<section class="homepage-product-carousel container-fluid px-md-6 px-lg-8 py-6">
  <div class="row no-gutters">
    <div class="col-12">
      <h2 class="mb-5 text-center">What&rsquo;s New at Kaplan</h2>
      <div class="carousel-wrapper new-products">
        <div class="swiper new-product-feed">
          <div class="swiper-wrapper">

            <!----------------------------------------
            BEGIN REPEATED CODE
            (use for each product; 12 products total in feed)
            ----------------------------------------->
            <div class="swiper-slide">
              <div class="card product-card bg-transparent">
                <div class="card-body">
                  <!-- Product image (link to product detail page). Use image size no less than 380x380 px -->
                  <a href="/product/123456/product-detail-page" class="product-image">
                    <img src="https://placehold.co/500x500?text=Product+Image" alt="" class="img-fluid">
                  </a>
                  <!-- Product title (link to product detail page) -->
                  <div class="product-card-title"><a href="/product/123456/product-detail-page">Product Title</a></div>
                  <!-- Item number -->
                  <div class="product-card-item">Item: 123456</div>
                  <!-- Ratings and Reviews -->
                  <div class="product-stars">
                    <!-- Insert Bazaarvoice rating here -->
                  </div>
                  <!-- Product price -->
                  <div class="product-card-price">
                    <!-- Add "strike" class to product price if there is a sale price -->
                    <div class="product-price">$123.95</div>
                    <!-- Sale price to show when product is discounted -->
                    <div class="product-sale-price">$99.95</div>
                  </div>
                </div>
                <div class="card-footer bg-transparent pt-0">
                  <!-- Add to cart button (Functions the same as "Add to Cart" on product detail page) -->
                  <!-- Change to "See Options <span class="sr-only">for Product Title</span>" and link to product detail page if product has child products -->
                  <!-- Change to "View Product<span class="sr-only">Information for Product Title</span>" and link to product detail page if product is not purchasable online -->
                  <button class="btn btn-sm btn-primary d-block w-100">Add <span class="sr-only">Product Title </span>to Cart</button>
                </div>
              </div>
            </div>
            <!----------------------------------------
            END REPEATED CODE
            ----------------------------------------->

            <div class="swiper-slide">
              <div class="card product-card bg-transparent">
                <div class="card-body">
                  <!-- Product image (link to product detail page). Use image size no less than 380x380 px -->
                  <a href="/product/123456/product-detail-page" class="product-image">
                    <img src="https://placehold.co/500x500?text=Product+Image" alt="" class="img-fluid">
                  </a>
                  <!-- Product title (link to product detail page) -->
                  <div class="product-card-title"><a href="/product/123456/product-detail-page">Product Title Product Title Product Title</a></div>
                  <!-- Item number -->
                  <div class="product-card-item">Item: 123456</div>
                  <!-- Ratings and Reviews -->
                  <div class="product-stars">
                    <!-- Insert Bazaarvoice rating here -->
                  </div>
                  <!-- Product price -->
                  <div class="product-card-price">
                    <!-- Add "strike" class to product price if there is a sale price -->
                    <div class="product-price strike">$123.95</div>
                    <!-- Sale price to show when product is discounted -->
                    <div class="product-sale-price">$99.95</div>
                  </div>
                </div>
                <div class="card-footer bg-transparent pt-0">
                  <!-- Add to cart button (Functions the same as "Add to Cart" on product detail page) -->
                  <!-- Change to "See Options <span class="sr-only">for Product Title</span>" and link to product detail page if product has child products -->
                  <!-- Change to "View Product<span class="sr-only">Information for Product Title</span>" and link to product detail page if product is not purchasable online -->
                  <button class="btn btn-sm btn-primary d-block w-100">Add <span class="sr-only">Product Title </span>to Cart</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="swiper-buttons">
          <button class="swiper-button-prev">
            <svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_264_12436)">
                <path
                  d="M25.2001 31.7537C24.7361 31.7537 24.2721 31.5457 23.9521 31.1457L17.5521 23.1457C17.0881 22.5537 17.0881 21.7377 17.5521 21.1457L23.9521 13.1457C24.5121 12.4577 25.5041 12.3457 26.2081 12.8897C26.8961 13.4337 27.0081 14.4417 26.4641 15.1457L20.8641 22.1537L26.4641 29.1617C27.0081 29.8497 26.8961 30.8577 26.2081 31.4177C25.9201 31.6577 25.5521 31.7697 25.2161 31.7697L25.2001 31.7537Z"
                  fill="#161F22" />
                <circle cx="22" cy="22" r="21.5" stroke="#161F22" />
              </g>
              <defs>
                <clipPath id="clip0_264_12436">
                  <rect width="44" height="44" fill="white" transform="translate(0 0.153809)" />
                </clipPath>
              </defs>
            </svg>
          </button>
          <button class="swiper-button-next">
            <svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_264_12455)">
                <path
                  d="M18.7998 31.7536C18.4478 31.7536 18.0958 31.6416 17.8078 31.4016C17.1198 30.8416 17.0078 29.8496 17.5518 29.1456L23.1518 22.1376L17.5518 15.1296C17.0078 14.4416 17.1198 13.4336 17.8078 12.8736C18.4958 12.3136 19.5038 12.4256 20.0638 13.1296L26.4638 21.1296C26.9278 21.7216 26.9278 22.5376 26.4638 23.1296L20.0638 31.1296C19.7438 31.5296 19.2798 31.7376 18.8158 31.7376L18.7998 31.7536Z"
                  fill="#161F22" />
                <circle cx="22" cy="22" r="21.5" stroke="#161F22" />
              </g>
              <defs>
                <clipPath id="clip0_264_12455">
                  <rect width="44" height="44" fill="white" transform="translate(0 0.153809)" />
                </clipPath>
              </defs>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</section>