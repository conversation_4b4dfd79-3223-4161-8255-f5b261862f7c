function _typeof2(e){return(_typeof2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}(e=>{"object"===("undefined"==typeof exports?"undefined":_typeof2(exports))&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):self.GLightbox=e()})(function(){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function e(e,t,i){t&&n(e.prototype,t),i&&n(e,i)}var a=Date.now();function h(e){var t={},i=!0,n=0,s=arguments.length;for("[object Boolean]"===Object.prototype.toString.call(e)&&(i=e,n++);n<s;n++){l=o=void 0;var l,o=arguments[n];for(l in o)Object.prototype.hasOwnProperty.call(o,l)&&(i&&"[object Object]"===Object.prototype.toString.call(o[l])?t[l]=h(!0,t[l],o[l]):t[l]=o[l])}return t}function u(e,t){if(0!=I(e=L(e=!B(e)&&e!==window&&e!==document?e:[e])||d(e)?e:[e]))if(L(e)&&!d(e))for(var i=e.length,n=0;n<i&&!1!==t.call(e[n],e[n],n,e);n++);else if(d(e))for(var s in e)if(b(e,s)&&!1===t.call(e[s],e[s],s,e))break}function C(e,t,i){var n=1<arguments.length&&void 0!==t?t:null,s=2<arguments.length&&void 0!==i?i:null,t=e[a]=e[a]||[],l={all:t,evt:null,found:null};return n&&s&&0<I(t)&&u(t,function(e,t){if(e.eventName==n&&e.fn.toString()==s.toString())return l.found=!0,l.evt=t,!1}),l}function z(i,e,t){var e=1<arguments.length&&void 0!==e?e:{},n=e.onElement,s=e.withCallback,l=e.avoidDuplicate,o=void 0===l||l,l=e.once,a=void 0!==l&&l,l=e.useCapture,r=void 0!==l&&l,h=2<arguments.length?t:void 0,d=n||[];function c(e){W(s)&&s.call(h,e,this),a&&c.destroy()}return x(d)&&(d=document.querySelectorAll(d)),c.destroy=function(){u(d,function(e){var t=C(e,i,c);t.found&&t.all.splice(t.evt,1),e.removeEventListener&&e.removeEventListener(i,c,r)})},u(d,function(e){var t=C(e,i,c);(e.addEventListener&&o&&!t.found||!o)&&(e.addEventListener(i,c,r),t.all.push({eventName:i,fn:c}))}),c}function X(t,e){u(e.split(" "),function(e){return t.classList.add(e)})}function Y(t,e){u(e.split(" "),function(e){return t.classList.remove(e)})}function q(e,t){return e.classList.contains(t)}function N(e,t){for(;e!==document.body;){if(!(e=e.parentElement))return!1;if("function"==typeof e.matches?e.matches(t):e.msMatchesSelector(t))return e}}function D(t,e,i){var n,e=1<arguments.length&&void 0!==e?e:"",s=2<arguments.length&&void 0!==i&&i;t&&""!==e&&("none"===e?W(s)&&s():(i=(()=>{var e,t=document.createElement("fakeelement"),i={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"animationend",WebkitAnimation:"webkitAnimationEnd"};for(e in i)if(void 0!==t.style[e])return i[e]})(),u(n=e.split(" "),function(e){X(t,"g"+e)}),z(i,{onElement:t,avoidDuplicate:!1,once:!0,withCallback:function(e,t){u(n,function(e){Y(t,"g"+e)}),W(s)&&s()}})))}function _(e,t){t=1<arguments.length&&void 0!==t?t:"";if(""===t)return e.style.webkitTransform="",e.style.MozTransform="",e.style.msTransform="",e.style.OTransform="",e.style.transform="",!1;e.style.webkitTransform=t,e.style.MozTransform=t,e.style.msTransform=t,e.style.OTransform=t,e.style.transform=t}function k(e){e.style.display="block"}function r(e){e.style.display="none"}function f(e){var t=document.createDocumentFragment(),i=document.createElement("div");for(i.innerHTML=e;i.firstChild;)t.appendChild(i.firstChild);return t}function F(){return{width:window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,height:window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight}}function m(e,t,i,n){var s,l;e()?t():(i=i||100,l=setInterval(function(){e()&&(clearInterval(l),s&&clearTimeout(s),t())},i),n&&(s=setTimeout(function(){clearInterval(l)},n)))}function E(e,t,i){var n,s,l;if(H(e))console.error("Inject assets error");else if(W(t)&&(i=t,t=!1),x(t)&&t in window)W(i)&&i();else if(-1!==e.indexOf(".css"))(n=document.querySelectorAll('link[href="'+e+'"]'))&&0<n.length||(o=(s=document.getElementsByTagName("head")[0]).querySelectorAll('link[rel="stylesheet"]'),(l=document.createElement("link")).rel="stylesheet",l.type="text/css",l.href=e,l.media="all",o?s.insertBefore(l,o[0]):s.appendChild(l)),W(i)&&i();else if((n=document.querySelectorAll('script[src="'+e+'"]'))&&0<n.length){if(W(i)){if(x(t))return void m(function(){return void 0!==window[t]},function(){i()});i()}}else{var o=document.createElement("script");o.type="text/javascript",o.src=e,o.onload=function(){if(W(i)){if(x(t))return m(function(){return void 0!==window[t]},function(){i()}),!1;i()}},document.body.appendChild(o)}}function y(){return"navigator"in window&&window.navigator.userAgent.match(/(iPad)|(iPhone)|(iPod)|(Android)|(PlayBook)|(BB10)|(BlackBerry)|(Opera Mini)|(IEMobile)|(webOS)|(MeeGo)/i)}function W(e){return"function"==typeof e}function x(e){return"string"==typeof e}function B(e){return e&&e.nodeType&&1==e.nodeType}function A(e){return Array.isArray(e)}function L(e){return e&&e.length&&isFinite(e.length)}function d(e){return"object"===t(e)&&null!=e&&!W(e)&&!A(e)}function H(e){return null==e}function b(e,t){return null!==e&&hasOwnProperty.call(e,t)}function I(e){if(d(e)){if(e.keys)return e.keys().length;var t,i=0;for(t in e)b(e,t)&&i++;return i}return e.length}function V(e){return!isNaN(parseFloat(e))&&isFinite(e)}function R(e){var e=0<arguments.length&&void 0!==e?e:-1,t=document.querySelectorAll(".gbtn[data-taborder]:not(.disabled)");if(!t.length)return!1;if(1==t.length)return t[0];"string"==typeof e&&(e=parseInt(e));var i=[],t=(u(t,function(e){i.push(e.getAttribute("data-taborder"))}),Math.max.apply(Math,i.map(function(e){return parseInt(e)}))),n=e<0?1:e+1;t<n&&(n="1");e=i.filter(function(e){return e>=parseInt(n)}).sort()[0];return document.querySelector('.gbtn[data-taborder="'.concat(e,'"]'))}function c(e){return Math.sqrt(e.x*e.x+e.y*e.y)}function O(e,t){s=t;var i,n,s=0==(n=c(i=e)*c(s))?0:(1<(i=(i.x*s.x+i.y*s.y)/n)&&(i=1),Math.acos(i));return 0<e.x*t.y-t.x*e.y&&(s*=-1),180*s/Math.PI}e(i,[{key:"add",value:function(e){this.handlers.push(e)}},{key:"del",value:function(e){e||(this.handlers=[]);for(var t=this.handlers.length;0<=t;t--)this.handlers[t]===e&&this.handlers.splice(t,1)}},{key:"dispatch",value:function(){for(var e=0,t=this.handlers.length;e<t;e++){var i=this.handlers[e];"function"==typeof i&&i.apply(this.el,arguments)}}}]);var P=i;function i(e){o(this,i),this.handlers=[],this.el=e}function s(e,t){e=new P(e);return e.add(t),e}e(l,[{key:"start",value:function(e){var t,i;e.touches&&(e.target&&e.target.nodeName&&0<=["a","button","input"].indexOf(e.target.nodeName.toLowerCase())?console.log("ignore drag for this touched element",e.target.nodeName.toLowerCase()):(this.now=Date.now(),this.x1=e.touches[0].pageX,this.y1=e.touches[0].pageY,this.delta=this.now-(this.last||this.now),this.touchStart.dispatch(e,this.element),null!==this.preTapPosition.x&&(this.isDoubleTap=0<this.delta&&this.delta<=250&&Math.abs(this.preTapPosition.x-this.x1)<30&&Math.abs(this.preTapPosition.y-this.y1)<30,this.isDoubleTap)&&clearTimeout(this.singleTapTimeout),this.preTapPosition.x=this.x1,this.preTapPosition.y=this.y1,this.last=this.now,t=this.preV,1<e.touches.length&&(this._cancelLongTap(),this._cancelSingleTap(),i={x:e.touches[1].pageX-this.x1,y:e.touches[1].pageY-this.y1},t.x=i.x,t.y=i.y,this.pinchStartLen=c(t),this.multipointStart.dispatch(e,this.element)),this._preventTap=!1,this.longTapTimeout=setTimeout(function(){this.longTap.dispatch(e,this.element),this._preventTap=!0}.bind(this),750)))}},{key:"move",value:function(e){var t,i,n,s,l,o,a;e.touches&&(o=this.preV,t=e.touches.length,i=e.touches[0].pageX,n=e.touches[0].pageY,this.isDoubleTap=!1,1<t?(s=e.touches[1].pageX,l=e.touches[1].pageY,a={x:e.touches[1].pageX-i,y:e.touches[1].pageY-n},null!==o.x&&(0<this.pinchStartLen&&(e.zoom=c(a)/this.pinchStartLen,this.pinch.dispatch(e,this.element)),e.angle=O(a,o),this.rotate.dispatch(e,this.element)),o.x=a.x,o.y=a.y,null!==this.x2&&null!==this.sx2?(e.deltaX=(i-this.x2+s-this.sx2)/2,e.deltaY=(n-this.y2+l-this.sy2)/2):(e.deltaX=0,e.deltaY=0),this.twoFingerPressMove.dispatch(e,this.element),this.sx2=s,this.sy2=l):(null!==this.x2?(e.deltaX=i-this.x2,e.deltaY=n-this.y2,o=Math.abs(this.x1-this.x2),a=Math.abs(this.y1-this.y2),(10<o||10<a)&&(this._preventTap=!0)):(e.deltaX=0,e.deltaY=0),this.pressMove.dispatch(e,this.element)),this.touchMove.dispatch(e,this.element),this._cancelLongTap(),this.x2=i,this.y2=n,1<t)&&e.preventDefault()}},{key:"end",value:function(e){var t;e.changedTouches&&(this._cancelLongTap(),t=this,e.touches.length<2&&(this.multipointEnd.dispatch(e,this.element),this.sx2=this.sy2=null),this.x2&&30<Math.abs(this.x1-this.x2)||this.y2&&30<Math.abs(this.y1-this.y2)?(e.direction=this._swipeDirection(this.x1,this.x2,this.y1,this.y2),this.swipeTimeout=setTimeout(function(){t.swipe.dispatch(e,t.element)},0)):(this.tapTimeout=setTimeout(function(){t._preventTap||t.tap.dispatch(e,t.element),t.isDoubleTap&&(t.doubleTap.dispatch(e,t.element),t.isDoubleTap=!1)},0),t.isDoubleTap||(t.singleTapTimeout=setTimeout(function(){t.singleTap.dispatch(e,t.element)},250))),this.touchEnd.dispatch(e,this.element),this.preV.x=0,this.preV.y=0,this.zoom=1,this.pinchStartLen=null,this.x1=this.x2=this.y1=this.y2=null)}},{key:"cancelAll",value:function(){this._preventTap=!0,clearTimeout(this.singleTapTimeout),clearTimeout(this.tapTimeout),clearTimeout(this.longTapTimeout),clearTimeout(this.swipeTimeout)}},{key:"cancel",value:function(e){this.cancelAll(),this.touchCancel.dispatch(e,this.element)}},{key:"_cancelLongTap",value:function(){clearTimeout(this.longTapTimeout)}},{key:"_cancelSingleTap",value:function(){clearTimeout(this.singleTapTimeout)}},{key:"_swipeDirection",value:function(e,t,i,n){return Math.abs(e-t)>=Math.abs(i-n)?0<e-t?"Left":"Right":0<i-n?"Up":"Down"}},{key:"on",value:function(e,t){this[e]&&this[e].add(t)}},{key:"off",value:function(e,t){this[e]&&this[e].del(t)}},{key:"destroy",value:function(){return this.singleTapTimeout&&clearTimeout(this.singleTapTimeout),this.tapTimeout&&clearTimeout(this.tapTimeout),this.longTapTimeout&&clearTimeout(this.longTapTimeout),this.swipeTimeout&&clearTimeout(this.swipeTimeout),this.element.removeEventListener("touchstart",this.start),this.element.removeEventListener("touchmove",this.move),this.element.removeEventListener("touchend",this.end),this.element.removeEventListener("touchcancel",this.cancel),this.rotate.del(),this.touchStart.del(),this.multipointStart.del(),this.multipointEnd.del(),this.pinch.del(),this.swipe.del(),this.tap.del(),this.doubleTap.del(),this.longTap.del(),this.singleTap.del(),this.pressMove.del(),this.twoFingerPressMove.del(),this.touchMove.del(),this.touchEnd.del(),this.touchCancel.del(),this.preV=this.pinchStartLen=this.zoom=this.isDoubleTap=this.delta=this.last=this.now=this.tapTimeout=this.singleTapTimeout=this.longTapTimeout=this.swipeTimeout=this.x1=this.x2=this.y1=this.y2=this.preTapPosition=this.rotate=this.touchStart=this.multipointStart=this.multipointEnd=this.pinch=this.swipe=this.tap=this.doubleTap=this.longTap=this.singleTap=this.pressMove=this.touchMove=this.touchEnd=this.touchCancel=this.twoFingerPressMove=null,window.removeEventListener("scroll",this._cancelAllHandler),null}}]);var G=l;function l(e,t){o(this,l),this.element="string"==typeof e?document.querySelector(e):e,this.start=this.start.bind(this),this.move=this.move.bind(this),this.end=this.end.bind(this),this.cancel=this.cancel.bind(this),this.element.addEventListener("touchstart",this.start,!1),this.element.addEventListener("touchmove",this.move,!1),this.element.addEventListener("touchend",this.end,!1),this.element.addEventListener("touchcancel",this.cancel,!1),this.preV={x:null,y:null},this.pinchStartLen=null,this.zoom=1,this.isDoubleTap=!1;function i(){}this.rotate=s(this.element,t.rotate||i),this.touchStart=s(this.element,t.touchStart||i),this.multipointStart=s(this.element,t.multipointStart||i),this.multipointEnd=s(this.element,t.multipointEnd||i),this.pinch=s(this.element,t.pinch||i),this.swipe=s(this.element,t.swipe||i),this.tap=s(this.element,t.tap||i),this.doubleTap=s(this.element,t.doubleTap||i),this.longTap=s(this.element,t.longTap||i),this.singleTap=s(this.element,t.singleTap||i),this.pressMove=s(this.element,t.pressMove||i),this.twoFingerPressMove=s(this.element,t.twoFingerPressMove||i),this.touchMove=s(this.element,t.touchMove||i),this.touchEnd=s(this.element,t.touchEnd||i),this.touchCancel=s(this.element,t.touchCancel||i),this.translateContainer=this.element,this._cancelAllHandler=this.cancelAll.bind(this),window.addEventListener("scroll",this._cancelAllHandler),this.delta=null,this.last=null,this.now=null,this.tapTimeout=null,this.singleTapTimeout=null,this.longTapTimeout=null,this.swipeTimeout=null,this.x1=this.x2=this.y1=this.y2=null,this.preTapPosition={x:null,y:null}}function j(e){var t=(()=>{var e,t=document.createElement("fakeelement"),i={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"};for(e in i)if(void 0!==t.style[e])return i[e]})(),i=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,n=q(e,"gslide-media")?e:e.querySelector(".gslide-media"),s=N(n,".ginner-container"),e=e.querySelector(".gslide-description");X(n=769<i?s:n,"greset"),_(n,"translate3d(0, 0, 0)"),z(t,{onElement:n,once:!0,withCallback:function(e,t){Y(n,"greset")}}),n.style.opacity="",e&&(e.style.opacity="")}e(g,[{key:"zoomIn",value:function(){var e,t=this.widowWidth();this.zoomedIn||t<=768||((e=this.img).setAttribute("data-style",e.getAttribute("style")),e.style.maxWidth=e.naturalWidth+"px",e.style.maxHeight=e.naturalHeight+"px",e.naturalWidth>t&&(t=t/2-e.naturalWidth/2,this.setTranslate(this.img.parentNode,t,0)),this.slide.classList.add("zoomed"),this.zoomedIn=!0)}},{key:"zoomOut",value:function(){this.img.parentNode.setAttribute("style",""),this.img.setAttribute("style",this.img.getAttribute("data-style")),this.slide.classList.remove("zoomed"),this.zoomedIn=!1,this.currentX=null,this.currentY=null,this.initialX=null,this.initialY=null,this.xOffset=0,this.yOffset=0,this.onclose&&"function"==typeof this.onclose&&this.onclose()}},{key:"dragStart",value:function(e){e.preventDefault(),this.zoomedIn?("touchstart"===e.type?(this.initialX=e.touches[0].clientX-this.xOffset,this.initialY=e.touches[0].clientY-this.yOffset):(this.initialX=e.clientX-this.xOffset,this.initialY=e.clientY-this.yOffset),e.target===this.img&&(this.active=!0,this.img.classList.add("dragging"))):this.active=!1}},{key:"dragEnd",value:function(e){var t=this;e.preventDefault(),this.initialX=this.currentX,this.initialY=this.currentY,this.active=!1,setTimeout(function(){t.dragging=!1,t.img.isDragging=!1,t.img.classList.remove("dragging")},100)}},{key:"drag",value:function(e){this.active&&(e.preventDefault(),"touchmove"===e.type?(this.currentX=e.touches[0].clientX-this.initialX,this.currentY=e.touches[0].clientY-this.initialY):(this.currentX=e.clientX-this.initialX,this.currentY=e.clientY-this.initialY),this.xOffset=this.currentX,this.yOffset=this.currentY,this.img.isDragging=!0,this.dragging=!0,this.setTranslate(this.img,this.currentX,this.currentY))}},{key:"onMove",value:function(e){var t;this.zoomedIn&&(t=e.clientX-this.img.naturalWidth/2,e=e.clientY-this.img.naturalHeight/2,this.setTranslate(this.img,t,e))}},{key:"setTranslate",value:function(e,t,i){e.style.transform="translate3d("+t+"px, "+i+"px, 0)"}},{key:"widowWidth",value:function(){return window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth}}]);var M=g;function g(e,t){var i=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(o(this,g),this.img=e,this.slide=t,this.onclose=n,this.img.setZoomEvents)return!1;this.active=!1,this.zoomedIn=!1,this.dragging=!1,this.currentX=null,this.currentY=null,this.initialX=null,this.initialY=null,this.xOffset=0,this.yOffset=0,this.img.addEventListener("mousedown",function(e){return i.dragStart(e)},!1),this.img.addEventListener("mouseup",function(e){return i.dragEnd(e)},!1),this.img.addEventListener("mousemove",function(e){return i.drag(e)},!1),this.img.addEventListener("click",function(e){return i.slide.classList.contains("dragging-nav")?(i.zoomOut(),!1):i.zoomedIn?void(i.zoomedIn&&!i.dragging&&i.zoomOut()):i.zoomIn()},!1),this.img.setZoomEvents=!0}e(p,[{key:"dragStart",value:function(e){var t;this.slide.classList.contains("zoomed")||("touchstart"===e.type?(this.initialX=e.touches[0].clientX-this.xOffset,this.initialY=e.touches[0].clientY-this.yOffset):(this.initialX=e.clientX-this.xOffset,this.initialY=e.clientY-this.yOffset),t=e.target.nodeName.toLowerCase(),e.target.classList.contains("nodrag"))||N(e.target,".nodrag")||-1!==["input","select","textarea","button","a"].indexOf(t)?this.active=!1:(e.preventDefault(),(e.target===this.el||"img"!==t&&N(e.target,".gslide-inline"))&&(this.active=!0,this.el.classList.add("dragging"),this.dragContainer=N(e.target,".ginner-container")))}},{key:"dragEnd",value:function(e){var t=this;e&&e.preventDefault(),this.initialX=0,this.initialY=0,this.currentX=null,this.currentY=null,this.initialX=null,this.initialY=null,this.xOffset=0,this.yOffset=0,this.active=!1,this.doSlideChange&&(this.instance.preventOutsideClick=!0,"right"==this.doSlideChange&&this.instance.prevSlide(),"left"==this.doSlideChange)&&this.instance.nextSlide(),this.doSlideClose&&this.instance.close(),this.toleranceReached||this.setTranslate(this.dragContainer,0,0,!0),setTimeout(function(){t.instance.preventOutsideClick=!1,t.toleranceReached=!1,t.lastDirection=null,t.dragging=!1,t.el.isDragging=!1,t.el.classList.remove("dragging"),t.slide.classList.remove("dragging-nav"),t.dragContainer.style.transform="",t.dragContainer.style.transition=""},100)}},{key:"drag",value:function(e){if(this.active){e.preventDefault(),this.slide.classList.add("dragging-nav"),"touchmove"===e.type?(this.currentX=e.touches[0].clientX-this.initialX,this.currentY=e.touches[0].clientY-this.initialY):(this.currentX=e.clientX-this.initialX,this.currentY=e.clientY-this.initialY),this.xOffset=this.currentX,this.yOffset=this.currentY,this.el.isDragging=!0,this.dragging=!0,this.doSlideChange=!1,this.doSlideClose=!1;var e=Math.abs(this.currentX),t=Math.abs(this.currentY);if(0<e&&e>=Math.abs(this.currentY)&&(!this.lastDirection||"x"==this.lastDirection)){this.yOffset=0,this.lastDirection="x",this.setTranslate(this.dragContainer,this.currentX,0);var i=this.shouldChange();if(!this.instance.settings.dragAutoSnap&&i&&(this.doSlideChange=i),this.instance.settings.dragAutoSnap&&i)return this.instance.preventOutsideClick=!0,this.toleranceReached=!0,this.active=!1,this.instance.preventOutsideClick=!0,this.dragEnd(null),"right"==i&&this.instance.prevSlide(),void("left"==i&&this.instance.nextSlide())}0<this.toleranceY&&0<t&&e<=t&&(!this.lastDirection||"y"==this.lastDirection)&&(this.xOffset=0,this.lastDirection="y",this.setTranslate(this.dragContainer,0,this.currentY),i=this.shouldClose(),!this.instance.settings.dragAutoSnap&&i&&(this.doSlideClose=!0),this.instance.settings.dragAutoSnap)&&i&&this.instance.close()}}},{key:"shouldChange",value:function(){var e,t=!1;return t=Math.abs(this.currentX)>=this.toleranceX&&("left"==(e=0<this.currentX?"right":"left")&&this.slide!==this.slide.parentNode.lastChild||"right"==e&&this.slide!==this.slide.parentNode.firstChild)?e:t}},{key:"shouldClose",value:function(){var e=!1;return e=Math.abs(this.currentY)>=this.toleranceY?!0:e}},{key:"setTranslate",value:function(e,t,i){e.style.transition=3<arguments.length&&void 0!==arguments[3]&&arguments[3]?"all .2s ease":"",e.style.transform="translate3d(".concat(t,"px, ").concat(i,"px, 0)")}}]);var Z=p;function p(){var t=this,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},i=(o(this,p),e.dragEl),n=e.toleranceX,n=void 0===n?40:n,s=e.toleranceY,s=void 0===s?65:s,l=e.slide,l=void 0===l?null:l,e=e.instance,e=void 0===e?null:e;this.el=i,this.active=!1,this.dragging=!1,this.currentX=null,this.currentY=null,this.initialX=null,this.initialY=null,this.xOffset=0,this.yOffset=0,this.direction=null,this.lastDirection=null,this.toleranceX=n,this.toleranceY=s,this.toleranceReached=!1,this.dragContainer=this.el,this.slide=l,this.instance=e,this.el.addEventListener("mousedown",function(e){return t.dragStart(e)},!1),this.el.addEventListener("mouseup",function(e){return t.dragEnd(e)},!1),this.el.addEventListener("mousemove",function(e){return t.drag(e)},!1)}function U(e){var t=N(e.target,".gslide-media");"enterfullscreen"===e.type&&X(t,"fullscreen"),"exitfullscreen"===e.type&&Y(t,"fullscreen")}function $(e,t,i,n){var s,l,o,e=e.querySelector(".gslide-media"),a=(n={url:t.href,callback:n},a=n.url,s=n.allow,l=n.callback,n=n.appendTo,(o=document.createElement("iframe")).className="vimeo-video gvideo",o.src=a,o.style.width="100%",o.style.height="100%",s&&o.setAttribute("allow",s),o.onload=function(){o.onload=null,X(o,"node-ready"),W(l)&&l()},n&&n.appendChild(o),o);e.parentNode.style.maxWidth=t.width,e.parentNode.style.height=t.height,e.appendChild(a)}e(v,[{key:"sourceType",value:function(e){var t=e;if(null!==(e=e.toLowerCase()).match(/\.(jpeg|jpg|jpe|gif|png|apn|webp|avif|svg)/))return"image";if(e.match(/(youtube\.com|youtube-nocookie\.com)\/watch\?v=([a-zA-Z0-9\-_]+)/)||e.match(/youtu\.be\/([a-zA-Z0-9\-_]+)/)||e.match(/(youtube\.com|youtube-nocookie\.com)\/embed\/([a-zA-Z0-9\-_]+)/))return"video";if(e.match(/vimeo\.com\/([0-9]*)/))return"video";if(null!==e.match(/\.(mp4|ogg|webm|mov)/))return"video";if(null!==e.match(/\.(mp3|wav|wma|aac|ogg)/))return"audio";if(-1<e.indexOf("#")&&""!==t.split("#").pop().trim())return"inline";return-1<e.indexOf("goajax=true")?"ajax":"external"}},{key:"parseConfig",value:function(n,s){var l=this,o=h({descPosition:s.descPosition},this.defaults);if(d(n)&&!B(n))return b(n,"type")||(b(n,"content")&&n.content?n.type="inline":b(n,"href")&&(n.type=this.sourceType(n.href))),t=h(o,n),this.setSize(t,s),t;var a,e,t="",r=n.getAttribute("data-glightbox"),i=n.nodeName.toLowerCase();if("a"===i&&(t=n.href),"img"===i&&(t=n.src,o.alt=n.alt),o.href=t,u(o,function(e,t){b(s,t)&&"width"!==t&&(o[t]=s[t]);var i=n.dataset[t];H(i)||(o[t]=l.sanitizeValue(i))}),o.content&&(o.type="inline"),!o.type&&t&&(o.type=this.sourceType(t)),H(r)?(o.title||"a"!=i||H(t=n.title)||""===t||(o.title=t),o.title||"img"!=i||H(t=n.alt)||""===t||(o.title=t)):(a=[],u(o,function(e,t){a.push(";\\s?"+t)}),a=a.join("\\s?:|"),""!==r.trim()&&u(o,function(e,t){var i=r,n=new RegExp("s?"+t+"s?:s?(.*?)("+a+"s?:|$)"),i=i.match(n);i&&i.length&&i[1]&&(n=i[1].trim().replace(/;\s*$/,""),o[t]=l.sanitizeValue(n))})),o.description&&"."===o.description.substring(0,1)){try{e=document.querySelector(o.description).innerHTML}catch(e){if(!(e instanceof DOMException))throw e}e&&(o.description=e)}return o.description||(i=n.querySelector(".glightbox-desc"))&&(o.description=i.innerHTML),this.setSize(o,s,n),this.slideConfig=o}},{key:"setSize",value:function(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,n="video"==e.type?this.checkSize(t.videosWidth):this.checkSize(t.width),t=this.checkSize(t.height);return e.width=b(e,"width")&&""!==e.width?this.checkSize(e.width):n,e.height=b(e,"height")&&""!==e.height?this.checkSize(e.height):t,i&&"image"==e.type&&(e._hasCustomWidth=!!i.dataset.width,e._hasCustomHeight=!!i.dataset.height),e}},{key:"checkSize",value:function(e){return V(e)?"".concat(e,"px"):e}},{key:"sanitizeValue",value:function(e){return"true"!==e&&"false"!==e?e:"true"===e}}]);var J=v;function v(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};o(this,v),this.defaults={href:"",sizes:"",srcset:"",title:"",type:"",videoProvider:"",description:"",alt:"",descPosition:"bottom",effect:"",width:"",height:"",content:!1,zoomable:!0,draggable:!0},d(e)&&(this.defaults=h(this.defaults,e))}e(w,[{key:"setContent",value:function(){var t=this,i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,e=1<arguments.length&&void 0!==arguments[1]&&arguments[1];if(q(i,"loaded"))return!1;var n,s=this.instance.settings,l=this.slideConfig,o=y(),a=(W(s.beforeSlideLoad)&&s.beforeSlideLoad({index:this.index,slide:i,player:!1}),l.type),r=l.descPosition,h=i.querySelector(".gslide-media"),d=i.querySelector(".gslide-title"),c=i.querySelector(".gslide-desc"),u=i.querySelector(".gdesc-inner"),g=e,p="gSlideTitle_"+this.index,v="gSlideDesc_"+this.index;W(s.afterSlideLoad)&&(g=function(){W(e)&&e(),s.afterSlideLoad({index:t.index,slide:i,player:t.instance.getSlidePlayerInstance(t.index)})}),""==l.title&&""==l.description?u&&u.parentNode.parentNode.removeChild(u.parentNode):(d&&""!==l.title?(d.id=p,d.innerHTML=l.title):d.parentNode.removeChild(d),c&&""!==l.description?(c.id=v,o&&0<s.moreLength?(l.smallDescription=this.slideShortDesc(l.description,s.moreLength,s.moreText),c.innerHTML=l.smallDescription,this.descriptionEvents(c,l)):c.innerHTML=l.description):c.parentNode.removeChild(c),X(h.parentNode,"desc-".concat(r)),X(u.parentNode,"description-".concat(r))),X(h,"gslide-".concat(a)),X(i,"loaded"),"video"===a?function(t,i,n,s){var l=this,e=t.querySelector(".ginner-container"),o="gvideo"+n,a=t.querySelector(".gslide-media"),r=this.getAllPlayers(),h=(X(e,"gvideo-container"),a.insertBefore(f('<div class="gvideo-wrapper"></div>'),a.firstChild),t.querySelector(".gvideo-wrapper")),d=(E(this.settings.plyr.css,"Plyr"),i.href),c=null==i?void 0:i.videoProvider,u=!1;a.style.maxWidth=i.width,E(this.settings.plyr.js,"Plyr",function(){"local"!==(c=!(c=!c&&d.match(/vimeo\.com\/([0-9]*)/)?"vimeo":c)&&(d.match(/(youtube\.com|youtube-nocookie\.com)\/watch\?v=([a-zA-Z0-9\-_]+)/)||d.match(/youtu\.be\/([a-zA-Z0-9\-_]+)/)||d.match(/(youtube\.com|youtube-nocookie\.com)\/embed\/([a-zA-Z0-9\-_]+)/))?"youtube":c)&&c||(c="local",e=(e=(e='<video id="'+o+'" ')+'style="background:#000; max-width: '.concat(i.width,';" ')+'preload="metadata" x-webkit-airplay="allow" playsinline controls class="gvideo-local">')+'<source src="'.concat(d,'">'),u=f(e+="</video>"));var e=u||f('<div id="'.concat(o,'" data-plyr-provider="').concat(c,'" data-plyr-embed-id="').concat(d,'"></div>')),e=(X(h,"".concat(c,"-video gvideo")),h.appendChild(e),h.setAttribute("data-id",o),h.setAttribute("data-index",n),b(l.settings.plyr,"config")?l.settings.plyr.config:{}),e=new Plyr("#"+o,e);e.on("ready",function(e){r[o]=e.detail.plyr,W(s)&&s()}),m(function(){return t.querySelector("iframe")&&"true"==t.querySelector("iframe").dataset.ready},function(){l.resize(t)}),e.on("enterfullscreen",U),e.on("exitfullscreen",U)})}.apply(this.instance,[i,l,this.index,g]):"external"===a?$.apply(this,[i,l,this.index,g]):"inline"===a?(function(e,t,i,n){var s,l=this,e=e.querySelector(".gslide-media"),o=!(!b(t,"href")||!t.href)&&t.href.split("#").pop().trim(),a=!(!b(t,"content")||!t.content)&&t.content;if(a&&(x(a)&&(s=f('<div class="ginlined-content">'.concat(a,"</div>"))),B(a))&&("none"==a.style.display&&(a.style.display="block"),(r=document.createElement("div")).className="ginlined-content",r.appendChild(a),s=r),o){a=document.getElementById(o);if(!a)return!1;var r=a.cloneNode(!0);r.style.height=t.height,r.style.maxWidth=t.width,X(r,"ginlined-content"),s=r}if(!s)return console.error("Unable to append inline slide content",t),!1;e.style.height=t.height,e.style.width=t.width,e.appendChild(s),this.events["inlineclose"+o]=z("click",{onElement:e.querySelectorAll(".gtrigger-close"),withCallback:function(e){e.preventDefault(),l.close()}}),W(n)&&n()}.apply(this.instance,[i,l,this.index,g]),l.draggable&&new Z({dragEl:i.querySelector(".gslide-inline"),toleranceX:s.dragToleranceX,toleranceY:s.dragToleranceY,slide:i,instance:this.instance})):"image"===a?(p=i,d=l,v=this.index,n=function(){var e=i.querySelector("img");l.draggable&&new Z({dragEl:e,toleranceX:s.dragToleranceX,toleranceY:s.dragToleranceY,slide:i,instance:t.instance}),l.zoomable&&e.naturalWidth>e.offsetWidth&&(X(e,"zoomable"),new M(e,i,function(){t.instance.resize()})),W(g)&&g()},p=p.querySelector(".gslide-media"),o=new Image,c="gSlideTitle_"+v,v="gSlideDesc_"+v,o.addEventListener("load",function(){W(n)&&n()},!1),o.src=d.href,""!=d.sizes&&""!=d.srcset&&(o.sizes=d.sizes,o.srcset=d.srcset),o.alt="",H(d.alt)||""===d.alt||(o.alt=d.alt),""!==d.title&&o.setAttribute("aria-labelledby",c),""!==d.description&&o.setAttribute("aria-describedby",v),d.hasOwnProperty("_hasCustomWidth")&&d._hasCustomWidth&&(o.style.width=d.width),d.hasOwnProperty("_hasCustomHeight")&&d._hasCustomHeight&&(o.style.height=d.height),p.insertBefore(o,p.firstChild)):W(g)&&g()}},{key:"slideShortDesc",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:50,i=2<arguments.length&&void 0!==arguments[2]&&arguments[2],n=document.createElement("div");n.innerHTML=e;var s=i;return!((e=n.innerText.trim()).length<=t)&&(e=e.substr(0,t-1),s)?(n=null,e+'... <a href="#" class="desc-more">'+i+"</a>"):e}},{key:"descriptionEvents",value:function(e,l){var o=this,e=e.querySelector(".desc-more");if(!e)return!1;z("click",{onElement:e,withCallback:function(e,t){e.preventDefault();var i=document.body,n=N(t,".gslide-desc");if(!n)return!1;n.innerHTML=l.description,X(i,"gdesc-open");var s=z("click",{onElement:[i,N(n,".gslide-description")],withCallback:function(e,t){"a"!==e.target.nodeName.toLowerCase()&&(Y(i,"gdesc-open"),X(i,"gdesc-closed"),n.innerHTML=l.smallDescription,o.descriptionEvents(n,l),setTimeout(function(){Y(i,"gdesc-closed")},400),s.destroy())}})}})}},{key:"create",value:function(){return f(this.instance.settings.slideHTML)}},{key:"getConfig",value:function(){B(this.element)||this.element.hasOwnProperty("draggable")||(this.element.draggable=this.instance.settings.draggable);var e=new J(this.instance.settings.slideExtraAttributes);return this.slideConfig=e.parseConfig(this.element,this.instance.settings),this.slideConfig}}]);var S=w;function w(e,t,i){o(this,w),this.element=e,this.instance=t,this.index=i}var K=y(),Q=null!==y()||void 0!==document.createTouch||"ontouchstart"in window||"onmsgesturechange"in window||navigator.msMaxTouchPoints,ee=document.getElementsByTagName("html")[0],te={selector:".glightbox",elements:null,skin:"clean",theme:"clean",closeButton:!0,startAt:null,autoplayVideos:!0,autofocusVideos:!0,descPosition:"bottom",width:"900px",height:"506px",videosWidth:"960px",beforeSlideChange:null,afterSlideChange:null,beforeSlideLoad:null,afterSlideLoad:null,slideInserted:null,slideRemoved:null,slideExtraAttributes:null,onOpen:null,onClose:null,loop:!1,zoomable:!0,draggable:!0,dragAutoSnap:!1,dragToleranceX:40,dragToleranceY:65,preload:!0,oneSlidePerOpen:!1,touchNavigation:!0,touchFollowAxis:!0,keyboardNavigation:!0,closeOnOutsideClick:!0,plugins:!1,plyr:{css:"https://cdn.plyr.io/3.6.12/plyr.css",js:"https://cdn.plyr.io/3.6.12/plyr.js",config:{ratio:"16:9",fullscreen:{enabled:!0,iosNative:!0},youtube:{noCookie:!0,rel:0,showinfo:0,iv_load_policy:3},vimeo:{byline:!1,portrait:!1,title:!1,transparent:!1}}},openEffect:"zoom",closeEffect:"zoom",slideEffect:"slide",moreText:"See more",moreLength:60,cssEfects:{fade:{in:"fadeIn",out:"fadeOut"},zoom:{in:"zoomIn",out:"zoomOut"},slide:{in:"slideInRight",out:"slideOutLeft"},slideBack:{in:"slideInLeft",out:"slideOutRight"},none:{in:"none",out:"none"}},svg:{close:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" xml:space="preserve"><g><g><path d="M505.943,6.058c-8.077-8.077-21.172-8.077-29.249,0L6.058,476.693c-8.077,8.077-8.077,21.172,0,29.249C10.096,509.982,15.39,512,20.683,512c5.293,0,10.586-2.019,14.625-6.059L505.943,35.306C514.019,27.23,514.019,14.135,505.943,6.058z"/></g></g><g><g><path d="M505.942,476.694L35.306,6.059c-8.076-8.077-21.172-8.077-29.248,0c-8.077,8.076-8.077,21.171,0,29.248l470.636,470.636c4.038,4.039,9.332,6.058,14.625,6.058c5.293,0,10.587-2.019,14.624-6.057C514.018,497.866,514.018,484.771,505.942,476.694z"/></g></g></svg>',next:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 477.175 477.175" xml:space="preserve"> <g><path d="M360.731,229.075l-225.1-225.1c-5.3-5.3-13.8-5.3-19.1,0s-5.3,13.8,0,19.1l215.5,215.5l-215.5,215.5c-5.3,5.3-5.3,13.8,0,19.1c2.6,2.6,6.1,4,9.5,4c3.4,0,6.9-1.3,9.5-4l225.1-225.1C365.931,242.875,365.931,234.275,360.731,229.075z"/></g></svg>',prev:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 477.175 477.175" xml:space="preserve"><g><path d="M145.188,238.575l215.5-215.5c5.3-5.3,5.3-13.8,0-19.1s-13.8-5.3-19.1,0l-225.1,225.1c-5.3,5.3-5.3,13.8,0,19.1l225.1,225c2.6,2.6,6.1,4,9.5,4s6.9-1.3,9.5-4c5.3-5.3,5.3-13.8,0-19.1L145.188,238.575z"/></g></svg>'},slideHTML:'<div class="gslide">\n    <div class="gslide-inner-content">\n        <div class="ginner-container">\n            <div class="gslide-media">\n            </div>\n            <div class="gslide-description">\n                <div class="gdesc-inner">\n                    <h4 class="gslide-title"></h4>\n                    <div class="gslide-desc"></div>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>',lightboxHTML:'<div id="glightbox-body" class="glightbox-container" tabindex="-1" role="dialog" aria-hidden="false">\n    <div class="gloader visible"></div>\n    <div class="goverlay"></div>\n    <div class="gcontainer">\n    <div id="glightbox-slider" class="gslider"></div>\n    <button class="gclose gbtn" aria-label="Close" data-taborder="3">{closeSVG}</button>\n    <button class="gprev gbtn" aria-label="Previous" data-taborder="2">{prevSVG}</button>\n    <button class="gnext gbtn" aria-label="Next" data-taborder="1">{nextSVG}</button>\n</div>\n</div>'},ie=(e(T,[{key:"init",value:function(){var i=this,e=this.getSelector();e&&(this.baseEvents=z("click",{onElement:e,withCallback:function(e,t){e.preventDefault(),i.open(t)}})),this.elements=this.getElements()}},{key:"open",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(0===this.elements.length)return!1;this.activeSlide=null,this.prevActiveSlideIndex=null,this.prevActiveSlide=null;var i,n,s,l,o,a,r,h,d,c,u,g,p,v,f,m,y,x,b,S,w,T,C,k,E,A,L,I,O,P,t=V(t)?t:this.settings.startAt,M=(V(t=B(e)&&((M=e.getAttribute("data-gallery"))&&(this.fullElementsList=this.elements,this.elements=this.getGalleryElements(this.elements,M)),H(t))&&(t=this.getElementIndex(e))<0?0:t)||(t=0),this.build(),D(this.overlay,"none"===this.settings.openEffect?"none":this.settings.cssEfects.fade.in),document.body),e=window.innerWidth-document.documentElement.clientWidth;0<e&&((i=document.createElement("style")).type="text/css",i.className="gcss-styles",i.innerText=".gscrollbar-fixer {margin-right: ".concat(e,"px}"),document.head.appendChild(i),X(M,"gscrollbar-fixer")),X(M,"glightbox-open"),X(ee,"glightbox-open"),K&&(X(document.body,"glightbox-mobile"),this.settings.slideEffect="slide"),this.showSlide(t,!0),(1===this.elements.length?(X(this.prevButton,"glightbox-button-hidden"),X):(Y(this.prevButton,"glightbox-button-hidden"),Y))(this.nextButton,"glightbox-button-hidden"),this.lightboxOpen=!0,this.trigger("open"),W(this.settings.onOpen)&&this.settings.onOpen(),Q&&this.settings.touchNavigation&&((n=this).events.hasOwnProperty("touch")||(e=F(),s=e.width,l=e.height,d=o=!1,y=m=f=v=h=r=a=null,C=T=p=g=!(u=c=1),k={},E={},L=A=w=S=0,e=document.getElementById("glightbox-slider"),O=document.querySelector(".goverlay"),e=new G(e,{touchStart:function(e){o=!0,(q(e.targetTouches[0].target,"ginner-container")||N(e.targetTouches[0].target,".gslide-desc")||"a"==e.targetTouches[0].target.nodeName.toLowerCase())&&(o=!1),(o=N(e.targetTouches[0].target,".gslide-inline")&&!q(e.targetTouches[0].target.parentNode,"gslide-inline")?!1:o)&&(E=e.targetTouches[0],k.pageX=e.targetTouches[0].pageX,k.pageY=e.targetTouches[0].pageY,A=e.targetTouches[0].clientX,L=e.targetTouches[0].clientY,a=n.activeSlide,r=a.querySelector(".gslide-media"),I=a.querySelector(".gslide-inline"),h=null,q(r,"gslide-image")&&(h=r.querySelector("img")),769<(window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth)&&(r=a.querySelector(".ginner-container")),Y(O,"greset"),20<e.pageX&&e.pageX<window.innerWidth-20||e.preventDefault())},touchMove:function(e){if(o&&(E=e.targetTouches[0],!g)&&!p){if(I&&I.offsetHeight>l){var t=k.pageX-E.pageX;if(Math.abs(t)<=13)return!1}d=!0;var i,t=e.targetTouches[0].clientX,e=e.targetTouches[0].clientY,t=A-t,e=L-e;if(Math.abs(t)>Math.abs(e)?C=!(T=!1):T=!(C=!1),x=E.pageX-k.pageX,S=100*x/s,b=E.pageY-k.pageY,w=100*b/l,T&&h&&(i=1-Math.abs(b)/l,O.style.opacity=i,n.settings.touchFollowAxis)&&(S=0),C&&(i=1-Math.abs(x)/s,r.style.opacity=i,n.settings.touchFollowAxis)&&(w=0),!h)return _(r,"translate3d(".concat(S,"%, 0, 0)"));_(r,"translate3d(".concat(S,"%, ").concat(w,"%, 0)"))}},touchEnd:function(){if(o)if(d=!1,p||g)m=v,y=f;else{var e=Math.abs(parseInt(w)),t=Math.abs(parseInt(S));if(!(29<e&&h))return e<29&&t<25?(X(O,"greset"),O.style.opacity=1,j(r)):void 0;n.close()}},multipointEnd:function(){setTimeout(function(){g=!1},50)},multipointStart:function(){g=!0,c=u||1},pinch:function(e){if(!h||d)return!1;g=!0,h.scaleX=h.scaleY=c*e.zoom;e=c*e.zoom;p=!0,e<=1?(p=!1,e=1,f=v=m=y=null,h.setAttribute("style","")):(h.style.transform="scale3d(".concat(e=4.5<e?4.5:e,", ").concat(e,", 1)"),u=e)},pressMove:function(e){var t,i;p&&!g&&(i=E.pageX-k.pageX,t=E.pageY-k.pageY,m&&(i+=m),y&&(t+=y),v=i,f=t,i="translate3d(".concat(i,"px, ").concat(t,"px, 0)"),u&&(i+=" scale3d(".concat(u,", ").concat(u,", 1)")),_(h,i))},swipe:function(e){if(!p)if(g)g=!1;else{if("Left"==e.direction){if(n.index==n.elements.length-1)return j(r);n.nextSlide()}if("Right"==e.direction){if(0==n.index)return j(r);n.prevSlide()}}}}),n.events.touch=e)),this.settings.keyboardNavigation&&!(P=this).events.hasOwnProperty("keyboard")&&(P.events.keyboard=z("keydown",{onElement:window,withCallback:function(e,t){var i=(e=e||window.event).keyCode;if(9==i){var n=document.querySelector(".gbtn.focused");if(!n){var s=!(!document.activeElement||!document.activeElement.nodeName)&&document.activeElement.nodeName.toLocaleLowerCase();if("input"==s||"textarea"==s||"button"==s)return}e.preventDefault();s=document.querySelectorAll(".gbtn[data-taborder]");if(!s||s.length<=0)return;if(!n)return void((e=R())&&(e.focus(),X(e,"focused")));s=R(n.getAttribute("data-taborder"));Y(n,"focused"),s&&(s.focus(),X(s,"focused"))}39==i&&P.nextSlide(),37==i&&P.prevSlide(),27==i&&P.close()}}))}},{key:"openAt",value:function(){this.open(null,0<arguments.length&&void 0!==arguments[0]?arguments[0]:0)}},{key:"showSlide",value:function(){var e,t=this,i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,n=1<arguments.length&&void 0!==arguments[1]&&arguments[1],s=(k(this.loader),this.index=parseInt(i),this.slidesContainer.querySelector(".current")),l=(s&&Y(s,"current"),this.slideAnimateOut(),this.slidesContainer.querySelectorAll(".gslide")[i]);q(l,"loaded")?(this.slideAnimateIn(l,n),r(this.loader)):(k(this.loader),s=this.elements[i],e={index:this.index,slide:l,slideNode:l,slideConfig:s.slideConfig,slideIndex:this.index,trigger:s.node,player:null},this.trigger("slide_before_load",e),s.instance.setContent(l,function(){r(t.loader),t.resize(),t.slideAnimateIn(l,n),t.trigger("slide_after_load",e)})),this.slideDescription=l.querySelector(".gslide-description"),this.slideDescriptionContained=this.slideDescription&&q(this.slideDescription.parentNode,"gslide-media"),this.settings.preload&&(this.preloadSlide(i+1),this.preloadSlide(i-1)),this.updateNavigationClasses(),this.activeSlide=l}},{key:"preloadSlide",value:function(e){var t,i,n,s,l=this;return!(e<0||e>this.elements.length-1||H(this.elements[e])||q(t=this.slidesContainer.querySelectorAll(".gslide")[e],"loaded"))&&(n=(i=this.elements[e]).type,s={index:e,slide:t,slideNode:t,slideConfig:i.slideConfig,slideIndex:e,trigger:i.node,player:null},this.trigger("slide_before_load",s),void("video"===n||"external"===n?setTimeout(function(){i.instance.setContent(t,function(){l.trigger("slide_after_load",s)})},200):i.instance.setContent(t,function(){l.trigger("slide_after_load",s)})))}},{key:"prevSlide",value:function(){this.goToSlide(this.index-1)}},{key:"nextSlide",value:function(){this.goToSlide(this.index+1)}},{key:"goToSlide",value:function(){var e=0<arguments.length&&void 0!==arguments[0]&&arguments[0];if(this.prevActiveSlide=this.activeSlide,this.prevActiveSlideIndex=this.index,!this.loop()&&(e<0||e>this.elements.length-1))return!1;e<0?e=this.elements.length-1:e>=this.elements.length&&(e=0),this.showSlide(e)}},{key:"insertSlide",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:-1,e=(t<0&&(t=this.elements.length),new S(e,this,t)),i=e.getConfig(),n=h({},i),s=e.create(),l=this.elements.length-1,e=(n.index=t,n.node=!1,n.instance=e,n.slideConfig=i,this.elements.splice(t,0,n),null),o=null;this.slidesContainer&&(l<t?this.slidesContainer.appendChild(s):(l=this.slidesContainer.querySelectorAll(".gslide")[t],this.slidesContainer.insertBefore(s,l)),(this.settings.preload&&0==this.index&&0==t||this.index-1==t||this.index+1==t)&&this.preloadSlide(t),0===this.index&&0===t&&(this.index=1),this.updateNavigationClasses(),e=this.slidesContainer.querySelectorAll(".gslide")[t],o=this.getSlidePlayerInstance(t),n.slideNode=e),this.trigger("slide_inserted",{index:t,slide:e,slideNode:e,slideConfig:i,slideIndex:t,trigger:null,player:o}),W(this.settings.slideInserted)&&this.settings.slideInserted({index:t,slide:e,player:o})}},{key:"removeSlide",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:-1;if(e<0||e>this.elements.length-1)return!1;var t=this.slidesContainer&&this.slidesContainer.querySelectorAll(".gslide")[e];t&&(this.getActiveSlideIndex()==e&&(e==this.elements.length-1?this.prevSlide():this.nextSlide()),t.parentNode.removeChild(t)),this.elements.splice(e,1),this.trigger("slide_removed",e),W(this.settings.slideRemoved)&&this.settings.slideRemoved(e)}},{key:"slideAnimateIn",value:function(e,t){var i=this,n=e.querySelector(".gslide-media"),s=e.querySelector(".gslide-description"),l={index:this.prevActiveSlideIndex,slide:this.prevActiveSlide,slideNode:this.prevActiveSlide,slideIndex:this.prevActiveSlide,slideConfig:H(this.prevActiveSlideIndex)?null:this.elements[this.prevActiveSlideIndex].slideConfig,trigger:H(this.prevActiveSlideIndex)?null:this.elements[this.prevActiveSlideIndex].node,player:this.getSlidePlayerInstance(this.prevActiveSlideIndex)},o={index:this.index,slide:this.activeSlide,slideNode:this.activeSlide,slideConfig:this.elements[this.index].slideConfig,slideIndex:this.index,trigger:this.elements[this.index].node,player:this.getSlidePlayerInstance(this.index)};0<n.offsetWidth&&s&&(r(s),s.style.display=""),Y(e,this.effectsClasses),t?D(e,this.settings.cssEfects[this.settings.openEffect].in,function(){i.settings.autoplayVideos&&i.slidePlayerPlay(e),i.trigger("slide_changed",{prev:l,current:o}),W(i.settings.afterSlideChange)&&i.settings.afterSlideChange.apply(i,[l,o])}):(s="none"!==(n=this.settings.slideEffect)?this.settings.cssEfects[n].in:n,this.prevActiveSlideIndex>this.index&&"slide"==this.settings.slideEffect&&(s=this.settings.cssEfects.slideBack.in),D(e,s,function(){i.settings.autoplayVideos&&i.slidePlayerPlay(e),i.trigger("slide_changed",{prev:l,current:o}),W(i.settings.afterSlideChange)&&i.settings.afterSlideChange.apply(i,[l,o])})),setTimeout(function(){i.resize(e)},100),X(e,"current")}},{key:"slideAnimateOut",value:function(){if(!this.prevActiveSlide)return!1;var n=this.prevActiveSlide,e=(Y(n,this.effectsClasses),X(n,"prev"),this.settings.slideEffect),e="none"!==e?this.settings.cssEfects[e].out:e;this.slidePlayerPause(n),this.trigger("slide_before_change",{prev:{index:this.prevActiveSlideIndex,slide:this.prevActiveSlide,slideNode:this.prevActiveSlide,slideIndex:this.prevActiveSlideIndex,slideConfig:H(this.prevActiveSlideIndex)?null:this.elements[this.prevActiveSlideIndex].slideConfig,trigger:H(this.prevActiveSlideIndex)?null:this.elements[this.prevActiveSlideIndex].node,player:this.getSlidePlayerInstance(this.prevActiveSlideIndex)},current:{index:this.index,slide:this.activeSlide,slideNode:this.activeSlide,slideIndex:this.index,slideConfig:this.elements[this.index].slideConfig,trigger:this.elements[this.index].node,player:this.getSlidePlayerInstance(this.index)}}),W(this.settings.beforeSlideChange)&&this.settings.beforeSlideChange.apply(this,[{index:this.prevActiveSlideIndex,slide:this.prevActiveSlide,player:this.getSlidePlayerInstance(this.prevActiveSlideIndex)},{index:this.index,slide:this.activeSlide,player:this.getSlidePlayerInstance(this.index)}]),this.prevActiveSlideIndex>this.index&&"slide"==this.settings.slideEffect&&(e=this.settings.cssEfects.slideBack.out),D(n,e,function(){var e=n.querySelector(".ginner-container"),t=n.querySelector(".gslide-media"),i=n.querySelector(".gslide-description");e.style.transform="",t.style.transform="",Y(t,"greset"),t.style.opacity="",i&&(i.style.opacity=""),Y(n,"prev")})}},{key:"getAllPlayers",value:function(){return this.videoPlayers}},{key:"getSlidePlayerInstance",value:function(e){var e="gvideo"+e,t=this.getAllPlayers();return!(!b(t,e)||!t[e])&&t[e]}},{key:"stopSlideVideo",value:function(e){B(e)&&(t=e.querySelector(".gvideo-wrapper"))&&(e=t.getAttribute("data-index")),console.log("stopSlideVideo is deprecated, use slidePlayerPause");var t=this.getSlidePlayerInstance(e);t&&t.playing&&t.pause()}},{key:"slidePlayerPause",value:function(e){B(e)&&(t=e.querySelector(".gvideo-wrapper"))&&(e=t.getAttribute("data-index"));var t=this.getSlidePlayerInstance(e);t&&t.playing&&t.pause()}},{key:"playSlideVideo",value:function(e){B(e)&&(t=e.querySelector(".gvideo-wrapper"))&&(e=t.getAttribute("data-index")),console.log("playSlideVideo is deprecated, use slidePlayerPlay");var t=this.getSlidePlayerInstance(e);t&&!t.playing&&t.play()}},{key:"slidePlayerPlay",value:function(e){var t;(!K||null!=(t=this.settings.plyr.config)&&t.muted)&&(B(e)&&(t=e.querySelector(".gvideo-wrapper"))&&(e=t.getAttribute("data-index")),t=this.getSlidePlayerInstance(e))&&!t.playing&&(t.play(),this.settings.autofocusVideos)&&t.elements.container.focus()}},{key:"setElements",value:function(e){var s=this,l=(this.settings.elements=!1,[]);e&&e.length&&u(e,function(e,t){var e=new S(e,s,t),i=e.getConfig(),n=h({},i);n.slideConfig=i,n.instance=e,n.index=t,l.push(n)}),this.elements=l,this.lightboxOpen&&(this.slidesContainer.innerHTML="",this.elements.length)&&(u(this.elements,function(){var e=f(s.settings.slideHTML);s.slidesContainer.appendChild(e)}),this.showSlide(0,!0))}},{key:"getElementIndex",value:function(i){var n=!1;return u(this.elements,function(e,t){if(b(e,"node")&&e.node==i)return n=t,!0}),n}},{key:"getElements",value:function(){var l=this,o=[],e=(this.elements=this.elements||[],!H(this.settings.elements)&&A(this.settings.elements)&&this.settings.elements.length&&u(this.settings.elements,function(e,t){var e=new S(e,l,t),i=e.getConfig(),n=h({},i);n.node=!1,n.index=t,n.instance=e,n.slideConfig=i,o.push(n)}),!1);return(e=this.getSelector()?document.querySelectorAll(this.getSelector()):e)&&u(e,function(e,t){var i=new S(e,l,t),n=i.getConfig(),s=h({},n);s.node=e,s.index=t,s.instance=i,s.slideConfig=n,s.gallery=e.getAttribute("data-gallery"),o.push(s)}),o}},{key:"getGalleryElements",value:function(e,t){return e.filter(function(e){return e.gallery==t})}},{key:"getSelector",value:function(){return!this.settings.elements&&(this.settings.selector&&"data-"==this.settings.selector.substring(0,5)?"*[".concat(this.settings.selector,"]"):this.settings.selector)}},{key:"getActiveSlide",value:function(){return this.slidesContainer.querySelectorAll(".gslide")[this.index]}},{key:"getActiveSlideIndex",value:function(){return this.index}},{key:"getAnimationClasses",value:function(){var e,t,i=[];for(e in this.settings.cssEfects)this.settings.cssEfects.hasOwnProperty(e)&&(t=this.settings.cssEfects[e],i.push("g".concat(t.in)),i.push("g".concat(t.out)));return i.join(" ")}},{key:"build",value:function(){var i=this;if(this.built)return!1;var e=document.body.childNodes,t=[],e=(u(e,function(e){e.parentNode==document.body&&"#"!==e.nodeName.charAt(0)&&e.hasAttribute&&!e.hasAttribute("aria-hidden")&&(t.push(e),e.setAttribute("aria-hidden","true"))}),b(this.settings.svg,"next")?this.settings.svg.next:""),n=b(this.settings.svg,"prev")?this.settings.svg.prev:"",s=b(this.settings.svg,"close")?this.settings.svg.close:"",l=this.settings.lightboxHTML,e=(l=f(l=(l=(l=l.replace(/{nextSVG}/g,e)).replace(/{prevSVG}/g,n)).replace(/{closeSVG}/g,s)),document.body.appendChild(l),document.getElementById("glightbox-body")),n=(this.modal=e).querySelector(".gclose");this.prevButton=e.querySelector(".gprev"),this.nextButton=e.querySelector(".gnext"),this.overlay=e.querySelector(".goverlay"),this.loader=e.querySelector(".gloader"),this.slidesContainer=document.getElementById("glightbox-slider"),this.bodyHiddenChildElms=t,this.events={},X(this.modal,"glightbox-"+this.settings.skin),this.settings.closeButton&&n&&(this.events.close=z("click",{onElement:n,withCallback:function(e,t){e.preventDefault(),i.close()}})),n&&!this.settings.closeButton&&n.parentNode.removeChild(n),this.nextButton&&(this.events.next=z("click",{onElement:this.nextButton,withCallback:function(e,t){e.preventDefault(),i.nextSlide()}})),this.prevButton&&(this.events.prev=z("click",{onElement:this.prevButton,withCallback:function(e,t){e.preventDefault(),i.prevSlide()}})),this.settings.closeOnOutsideClick&&(this.events.outClose=z("click",{onElement:e,withCallback:function(e,t){i.preventOutsideClick||q(document.body,"glightbox-mobile")||N(e.target,".ginner-container")||N(e.target,".gbtn")||q(e.target,"gnext")||q(e.target,"gprev")||i.close()}})),u(this.elements,function(e,t){i.slidesContainer.appendChild(e.instance.create()),e.slideNode=i.slidesContainer.querySelectorAll(".gslide")[t]}),Q&&X(document.body,"glightbox-touch"),this.events.resize=z("resize",{onElement:window,withCallback:function(){i.resize()}}),this.built=!0}},{key:"resize",value:function(){var e,t,i,n,s,l,o,a=(a=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null)||this.activeSlide;a&&!q(a,"zoomed")&&(i=F(),e=a.querySelector(".gvideo-wrapper"),a=a.querySelector(".gslide-image"),t=this.slideDescription,l=i.width,i=i.height,(l<=768?X:Y)(document.body,"glightbox-mobile"),e||a)&&(n=!1,t&&(q(t,"description-bottom")||q(t,"description-top"))&&!q(t,"gabsolute")&&(n=!0),a&&(l<=768?a.querySelector("img"):n&&(s=t.offsetHeight,(a=a.querySelector("img")).setAttribute("style","max-height: calc(100vh - ".concat(s,"px)")),t.setAttribute("style","max-width: ".concat(a.offsetWidth,"px;")))),e)&&((s=b(this.settings.plyr.config,"ratio")?this.settings.plyr.config.ratio:"")||(a=e.clientWidth,o=e.clientHeight,s="".concat(a/(a=a/o),":").concat(o/a)),o=s.split(":"),a=this.settings.videosWidth,s=this.settings.videosWidth,o=(s=V(a)||-1!==a.indexOf("px")?parseInt(a):-1!==a.indexOf("vw")?l*parseInt(a)/100:-1!==a.indexOf("vh")?i*parseInt(a)/100:-1!==a.indexOf("%")?l*parseInt(a)/100:parseInt(e.clientWidth))/(parseInt(o[0])/parseInt(o[1])),o=Math.floor(o),n&&(i-=t.offsetHeight),l<s||i<o||i<o&&s<l?(o=e.offsetWidth,s=e.offsetHeight,e.parentNode.setAttribute("style","max-width: ".concat((o={width:o*(l=i/s),height:s*l}).width,"px")),n&&t.setAttribute("style","max-width: ".concat(o.width,"px;"))):(e.parentNode.style.maxWidth="".concat(a),n&&t.setAttribute("style","max-width: ".concat(a,";"))))}},{key:"reload",value:function(){this.init()}},{key:"updateNavigationClasses",value:function(){var e=this.loop();Y(this.nextButton,"disabled"),Y(this.prevButton,"disabled"),0==this.index&&this.elements.length-1==0?(X(this.prevButton,"disabled"),X(this.nextButton,"disabled")):0!==this.index||e?this.index!==this.elements.length-1||e||X(this.nextButton,"disabled"):X(this.prevButton,"disabled")}},{key:"loop",value:function(){var e=b(this.settings,"loopAtEnd")?this.settings.loopAtEnd:null;return e=b(this.settings,"loop")?this.settings.loop:e}},{key:"close",value:function(){var i=this;if(!this.lightboxOpen){if(this.events){for(var e in this.events)this.events.hasOwnProperty(e)&&this.events[e].destroy();this.events=null}return!1}if(this.closing)return!1;this.closing=!0,this.slidePlayerPause(this.activeSlide),this.fullElementsList&&(this.elements=this.fullElementsList),this.bodyHiddenChildElms.length&&u(this.bodyHiddenChildElms,function(e){e.removeAttribute("aria-hidden")}),X(this.modal,"glightbox-closing"),D(this.overlay,"none"==this.settings.openEffect?"none":this.settings.cssEfects.fade.out),D(this.activeSlide,this.settings.cssEfects[this.settings.closeEffect].out,function(){if(i.activeSlide=null,i.prevActiveSlideIndex=null,i.prevActiveSlide=null,i.built=!1,i.events){for(var e in i.events)i.events.hasOwnProperty(e)&&i.events[e].destroy();i.events=null}var t=document.body,t=(Y(ee,"glightbox-open"),Y(t,"glightbox-open touching gdesc-open glightbox-touch glightbox-mobile gscrollbar-fixer"),i.modal.parentNode.removeChild(i.modal),i.trigger("close"),W(i.settings.onClose)&&i.settings.onClose(),document.querySelector(".gcss-styles"));t&&t.parentNode.removeChild(t),i.lightboxOpen=!1,i.closing=null})}},{key:"destroy",value:function(){this.close(),this.clearAllEvents(),this.baseEvents&&this.baseEvents.destroy()}},{key:"on",value:function(e,t){var i=2<arguments.length&&void 0!==arguments[2]&&arguments[2];if(!e||!W(t))throw new TypeError("Event name and callback must be defined");this.apiEvents.push({evt:e,once:i,callback:t})}},{key:"once",value:function(e,t){this.on(e,t,!0)}},{key:"trigger",value:function(s){var t=this,l=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,o=[];u(this.apiEvents,function(e,t){var i=e.evt,n=e.once;i==s&&((0,e.callback)(l),n)&&o.push(t)}),o.length&&u(o,function(e){return t.apiEvents.splice(e,1)})}},{key:"clearAllEvents",value:function(){this.apiEvents.splice(0,this.apiEvents.length)}},{key:"version",value:function(){return"3.1.0"}}]),T);function T(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};o(this,T),this.customOptions=e,this.settings=h(te,e),this.effectsClasses=this.getAnimationClasses(),this.videoPlayers={},this.apiEvents=[],this.fullElementsList=!1}return function(){var e=new ie(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{});return e.init(),e}});
//# sourceMappingURL=glightbox.min.js.map
