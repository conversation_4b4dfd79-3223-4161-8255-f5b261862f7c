"use strict";

/******************************
/* Responsive Site Navigation
/*****************************/
function setupResponsiveMenu() {
  // First, remove all previously attached event handlers to prevent overlap
  $('.navbar-nav .nav-pills .nav-link').off('click.bs.dropdown');
  $('.navbar-nav > .dropdown').off('mouseenter mouseleave');
  $('.navbar-nav > .dropdown > .dropdown-menu .nav-pills .nav-item .nav-link').off('mouseenter');
  $('.navbar-nav > .dropdown > .dropdown-menu .nav-pills .nav-item a[data-toggle="pill"]').off('shown.bs.tab');
  $('.navbar-toggler, #navMobileOverlay').off('click');
  $('.navbar-nav .tab-pane').off('click', 'a[data-toggle="collapse"]');

  // Desktop behavior (min-width: 992px)
  if (window.matchMedia("(min-width: 992px)").matches) {
    // Stop Menu Dropdown from closing on click
    $('.navbar-nav .nav-pills .nav-link').on('click.bs.dropdown', function (event) {
      $(this).tab('show');
      event.stopPropagation();
    });

    // Variable to store timeout ID
    var tabHideTimeout;

    // Open menu dropdown on hover with first submenu open by default
    $('.navbar-nav > .dropdown').on({
      mouseenter: function mouseenter() {
        var $dropdown = $(this);
        $dropdown.addClass('show');
        $dropdown.find('> .dropdown-toggle').attr('aria-expanded', 'true');
        $dropdown.find('> .dropdown-menu').addClass('show');

        // Always show the first tab when dropdown appears
        $dropdown.find('> .dropdown-menu .nav-pills .nav-item:first-child > .nav-link').tab('show');
      },
      mouseleave: function mouseleave() {
        var $dropdown = $(this);
        $dropdown.removeClass('show');
        $dropdown.find('> .dropdown-toggle').attr('aria-expanded', 'false');
        $dropdown.find('> .dropdown-menu').removeClass('show');
      }
    });

    // Open menu tabs on hover - but only for links that have data-toggle="pill"
    $('.navbar-nav > .dropdown > .dropdown-menu .nav-pills .nav-item .nav-link').on('mouseenter', function () {
      // Clear any pending timeouts to hide tabs
      clearTimeout(tabHideTimeout);

      // Only activate tab('show') if this link has a data-toggle attribute
      if ($(this).attr('data-toggle') === 'pill') {
        $(this).tab('show');
      } else {
        // For links without data-toggle="pill", ensure no tab is shown
        // First deactivate all tabs in this group
        $(this).closest('.nav-pills').find('.nav-link').removeClass('active').attr('aria-selected', 'false');
        // Then hide all tab content
        $(this).closest('.dropdown-menu').find('.tab-content .tab-pane').removeClass('show active');
      }
    });

    // Handle tab content closing when no tab is being hovered
    $('.navbar-nav > .dropdown > .dropdown-menu .nav-pills').on('mouseleave', function () {
      // We don't need to do anything here - tabs should stay active
      // when moving back to the parent dropdown
    });

    // Add handler for mouse entering tab content to keep it visible
    $('.navbar-nav > .dropdown > .dropdown-menu .tab-content').on('mouseenter', function () {
      // Clear any pending timeouts to hide tabs
      clearTimeout(tabHideTimeout);
    });

    // Add handler for mouse leaving tab content
    $('.navbar-nav > .dropdown > .dropdown-menu .tab-content').on('mouseleave', function () {
      // We don't need to do anything here - tabs should stay active
      // when moving back to the parent dropdown
    });
  }

  // Mobile behavior (max-width: 991.98px)
  else if (window.matchMedia("(max-width: 991.98px)").matches) {
    // Mobile menu toggle
    $('.navbar-toggler, #navMobileOverlay').on('click', function () {
      $('.navbar-toggler').toggleClass('is-active');
      $('#navMobileOverlay').toggleClass('show');
      $('#mainNavigation').toggleClass('slide');
      $('body').toggleClass('modal-open');
      $(this).attr('aria-expanded', function (i, attr) {
        return attr == 'true' ? 'false' : 'true';
      });

      // If closing the menu (when navbar-toggler is not active)
      if (!$('.navbar-toggler').hasClass('is-active')) {
        // Wait for animation to complete (300ms) before resetting submenu states
        setTimeout(function () {
          $('.navbar-nav .dropdown').removeClass('show');
          $('.navbar-nav .dropdown-toggle, .navbar-nav .tab-content .tab-pane .card-header a').addClass('collapsed');
          $('.navbar-nav .dropdown-toggle, .navbar-nav .tab-content .tab-pane .card-header a').attr('aria-expanded', 'false');
          $('.navbar-nav .dropdown-menu').removeClass('show');
          $('.navbar-nav .tab-content .tab-pane .card-header').removeClass('active');
          $('.navbar-nav .tab-content .tab-pane .collapse').removeClass('show');
        }, 300); // 300ms delay to match animation duration
      }
    });

    // Mobile tab pane handling
    $('.navbar-nav .tab-pane').on('click', 'a[data-toggle="collapse"]', function (event) {
      $($(this).attr('data-target')).collapse('toggle');
      event.stopPropagation();
    });
  }
}

// Initialize on document ready
$(document).ready(function () {
  setupResponsiveMenu();

  // Use throttled resize event to prevent performance issues
  var resizeTimer;
  $(window).on("resize", function () {
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(function () {
      setupResponsiveMenu();
    }, 250); // Wait 250ms after resize ends before running function
  });
});

/*******************************
/* Make navbar sticky on scroll
/******************************/
var $scrollHeight = $('#siteHeader').outerHeight();
$(window).on("load resize", function () {
  if (window.matchMedia("(min-width: 992px)").matches) {
    $('body').css('padding-top', "0");
    $('#mainNavigation').css('top', "0");
    $('#navMobileOverlay').css('top', "0");
    $(window).scroll(function () {
      if ($(this).scrollTop() > $scrollHeight + 60) {
        $('#mainNavigation').addClass("fixed-top shrink");
        // add padding top to show content behind navbar
        $('body').css('padding-top', $('#mainNavigation').outerHeight() - 8 + 'px');
      } else {
        $('#mainNavigation').removeClass("fixed-top shrink");
        $('body').css('padding-top', "0");
      }
    });
  } else {
    $('body').css('padding-top', $('#siteHeader').outerHeight() + 'px');
    $('#mainNavigation').css('top', $('#siteHeader').outerHeight() + 'px');
    $('#navMobileOverlay').css('top', $('#siteHeader').outerHeight() + 'px');
    $('#mainNavigation').removeClass("fixed-top shrink");
  }
});
$(window).on("load resize", function () {
  if (window.matchMedia("(max-width: 991.98px)").matches) {
    $('body').css('padding-top', $('#siteHeader').outerHeight() + 'px');
    $('#mainNavigation').css('top', $('#siteHeader').outerHeight() + 'px');
    $('#navMobileOverlay').css('top', $('#siteHeader').outerHeight() + 'px');
    $('#mainNavigation').removeClass("fixed-top shrink");
    $(window).scroll(function () {
      $('body').css('padding-top', $('#siteHeader').outerHeight() + 'px');
      $('#mainNavigation').css('top', $('#siteHeader').outerHeight() + 'px');
      $('#navMobileOverlay').css('top', $('#siteHeader').outerHeight() + 'px');
      $('#mainNavigation').removeClass("fixed-top shrink");
    });
  }
});

// Make blog navbar sticky on scroll
/*
var $scrollHeight = $('#siteHeader').outerHeight();
$(window).on("load resize", function() {
  if (window.matchMedia("(min-width: 992px)").matches) {
    $('body').css('padding-top', "0");
    $(window).scroll(function(){
      if ($(this).scrollTop() > $scrollHeight + 60) {
        $('.blognav.navbar').addClass("fixed-top shrink");
        // add padding top to show content behind navbar
        $('body').css('padding-top', $('.blognav.navbar').outerHeight() - 8 + 'px');
      } else {
        $('.blognav.navbar').removeClass("fixed-top shrink");
        $('body').css('padding-top', "0");
      }
    });
  } else {
    $('body').css('padding-top', $('#siteHeader').outerHeight() + 'px');
    $('.blognav.navbar').removeClass("fixed-top shrink");
  }
});
$(window).on("load resize", function() {
  if (window.matchMedia("(max-width: 991px)").matches) {
    $('body').css('padding-top', $('#siteHeader').outerHeight() + 'px');
    $('.blognav.navbar').removeClass("fixed-top shrink");
    $(window).scroll(function(){
      $('body').css('padding-top', $('#siteHeader').outerHeight() + 'px');
      $('.blognav.navbar').removeClass("fixed-top shrink");
    });
  }
});
*/

/*******************************
/* Floating Content Box
/******************************/

// Add scrollspy attributes to body if content box exists
$(window).on("load", function () {
  if ($('.content-box').length != 0) {
    $('body').addClass("position-relative");
    $('body').attr('data-spy', "scroll");
    $('body').attr('data-target', "#contents");
    $('body').attr('data-offset', "62");
  }
});

// Main floating content box functionality
$(document).ready(function () {
  var $headerHeight = $('.meganav.navbar').outerHeight();
  function setupContentBox() {
    // Clear any existing scroll handlers to prevent duplicates
    $(window).off('scroll.contentBox');

    // Scrollspy configuration
    if (window.matchMedia("(min-width: 992px)").matches) {
      if ($('body[data-spy="scroll"]').length > 0) {
        $('body').scrollspy('refresh');
        $('body').scrollspy({
          offset: $headerHeight
        });
      }
    } else if (window.matchMedia("(max-width: 991.98px)").matches) {
      if ($('body[data-spy="scroll"]').length > 0 && $('.content-box').length > 0) {
        $('body').scrollspy('refresh');
        $('body').scrollspy({
          offset: $('.content-box').outerHeight()
        });
      }
    }

    // Desktop behavior (992px and up)
    if (window.matchMedia("(min-width: 992px)").matches) {
      // Remove collapse functionality
      $('#contentsHeader').removeAttr('href data-toggle aria-expanded aria-controls');
      $("#contents").addClass("show");
      $("#contents").removeClass("collapse"); // Remove collapse class
      $('.content-box').removeClass('scroll-fix');
      $('#contentArea').css('padding-top', "0");
      $('.content-box').css({
        top: "calc(42px + 1.5rem)"
      });
      $("#contentsHeader").removeAttr('aria-expanded');
      $(".content-box .card-header > a").removeClass("collapsed");
      // Remove click handlers for mobile
      $(".content-box a.nav-link").off('click.contentBox');
    }
    // Mobile behavior (991.98px and below)
    else if (window.matchMedia("(max-width: 991.98px)").matches) {
      // Add collapse functionality
      $('#contentsHeader').attr('href', "#contents");
      $('#contentsHeader').attr('data-toggle', "collapse");
      $('#contentsHeader').attr('aria-expanded', "false");
      $('#contentsHeader').attr('aria-controls', "contents");
      $("#contents").removeClass("show");
      $("#contents").addClass("collapse"); // Add collapse class
      $("#contentsHeader").attr("aria-expanded", "false");
      $(".content-box .card-header > a").addClass("collapsed");

      // Add click handler for nav links to collapse content box
      $(".content-box a.nav-link").off('click.contentBox').on('click.contentBox', function () {
        $("#contentsHeader").attr("aria-expanded", "false");
        $("#contents").removeClass("show");
        $(".content-box .card-header > a").addClass("collapsed");
      });

      // Floating scroll behavior for mobile
      $(window).on('scroll.contentBox', function () {
        var $windowTop = $(window).scrollTop();
        var $scrollFixAnchor = $('.scroll-fix-anchor');
        var $footer = $('footer');

        // Safety checks for required elements
        if ($scrollFixAnchor.length === 0 || $footer.length === 0) {
          console.warn('Required elements not found: scroll-fix-anchor or footer');
          return;
        }
        var $divTop = $scrollFixAnchor.offset().top;
        var $footerTop = $footer.offset().top;
        var $contentHeight = $('.content-box .card-header').outerHeight();
        if ($windowTop + $contentHeight + 32 > $footerTop - 16) {
          $('.content-box').css({
            top: ($windowTop + $contentHeight - $footerTop + 32) * -1
          });
        } else if ($windowTop > $divTop - 16) {
          $('#contentArea').css('padding-top', $contentHeight + 16);
          $('.content-box').addClass('scroll-fix');
          $('.content-box').css({
            top: 1 + "rem"
          });
        } else {
          $('.content-box').removeClass('scroll-fix');
          $('#contentArea').css('padding-top', "0");
        }
      });
    }
  }

  // Run on load and resize
  $(window).on("load resize", setupContentBox);

  // Run immediately if DOM is ready
  if ($('.content-box').length > 0) {
    setupContentBox();
  }
});

/*******************************
/* Category Filter Sidebar
/******************************/
$(window).on("load resize", function () {
  if ($(window).width() < 973) {
    $("#categoryFilterHeading, #attributeFilterHeading").attr("aria-expanded", "false");
    $("#categoryFilter, #attributeFilter").removeClass("show");
  } else {
    $("#categoryFilterHeading, #attributeFilterHeading").attr("aria-expanded", "true");
    $("#categoryFilter, #attributeFilter").addClass("show");
  }
});
$(".filter-toggle").click(function () {
  $("#sidebar").addClass("slide");
});
$(".sidebar-close").click(function () {
  $("#sidebar").removeClass("slide");
});

/*******************************
/* Product List Control
/******************************/
$(".shop-view-grid").click(function () {
  $("#shopProductGrid").addClass("grid-view");
  $(".shop-view-grid").addClass("active");
  $("#shopProductGrid").removeClass("list-view");
  $(".shop-view-list").removeClass("active");
  $(".product-grid-column").addClass("col-6 col-sm-4");
  $(".product-grid-column").removeClass("col-12");
});
$(".shop-view-list").click(function () {
  $("#shopProductGrid").addClass("list-view");
  $(".shop-view-list").addClass("active");
  $("#shopProductGrid").removeClass("grid-view");
  $(".shop-view-grid").removeClass("active");
  $(".product-grid-column").removeClass("col-6 col-sm-4");
  $(".product-grid-column").addClass("col-12");
});

/*******************************
/* Accordian active state
/******************************/
$(window).on("load", function () {
  $('.accordion .collapse, .tab-content .collapse').on('shown.bs.collapse', function () {
    $(this).prev().addClass("active");
  });
  $('.accordion .collapse, .tab-content .collapse').on('hidden.bs.collapse', function () {
    $(this).prev().removeClass("active");
  });
});