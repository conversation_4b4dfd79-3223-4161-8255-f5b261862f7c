"use strict";for(var feedContainers=document.querySelectorAll("[data-rss-url]"),feeds=[].slice.call(feedContainers),i=0;i<feeds.length;i++){var container=feedContainers[i],url=container.getAttribute("data-rss-url"),max=container.getAttribute("data-rss-max")||10;!function(r,i){$.ajax(url,{accepts:{xml:"application/rss+xml"},dataType:"xml",success:function(e){var t,s=r.classList.contains("blog-feed");s&&((t=document.createElement("div")).className="swiper-wrapper",r.appendChild(t));var c=s?r.querySelector(".swiper-wrapper"):r;$(e).find("item").slice(0,i).each(function(){var e=$(this),t="";e.find("category").each(function(){t+=$(this).text()+", "});var i=$.trim(t).slice(0,-1),r=e.find("title").text(),a=e.find("link").text(),n=e.find("pubDate").text(),l=new Date(n),n=Array("January","February","March","April","May","June","July","August","September","October","November","December")[l.getMonth()]+" "+l.getDate()+", "+l.getFullYear(),l=e.find("description").text().match(/<img\b[^>]+?src\s*=\s*['"]?([^\s'"?#>]+)['"]\salt\s*=\s*['"]?([^'">]+)/),e=l&&l[1]?l[1]:"https://placehold.co/800x400?text=No+Image",l=l&&l[2]?l[2]:r,n=(s?'<div class="swiper-slide"><article class="card blog-article-card h-100 rounded-0 bg-transparent border-0" aria-label="Article summary: '.concat(r,'"><img src="').concat(e,'" alt="').concat(l,'" class="card-img-top rounded-0"><div class="card-body pt-3 pb-0 px-0"><div class="h4 mb-2 card-title blog-article-title"><a href="').concat(a,'" target="_blank" class="stretched-link text-reset">'):'<div class="col mb-5"><article class="blog-article-grid-item card h-100" aria-label="Article summary: '.concat(r,'"><img src="').concat(e,'" alt="').concat(l,'" class="card-img-top"><div class="card-body bg-light mx-4 mt-n5"><div class="card-title blog-article-tag">').concat(i,'</div><div class="card-title blog-article-title"><a href="').concat(a,'" target="_blank">')).concat(r,'</a></div><div class="card-text blog-article-date">').concat(n,"</div></div></article></div>");$(c).append(n)}),s&&(r.parentElement.querySelector(".swiper-buttons")||((e=document.createElement("div")).className="swiper-buttons",e.innerHTML='\n              <div class="swiper-button-prev"></div>\n              <div class="swiper-button-next"></div>\n            ',r.parentElement.appendChild(e)),0<r.querySelectorAll(".swiper-slide").length?setTimeout(function(){try{initializeBlogFeedSwiper(r)}catch(e){console.error("Error initializing Swiper:",e)}},100):console.warn("No slides found in blog feed container"))},error:function(e,t,i){console.error("RSS Feed Error:",i),$(r).append('<div class="alert alert-warning">Unable to load blog content. Please try again later.</div>')}})}(container,max)}function initializeBlogFeedSwiper(e){if(!e||!e.querySelector(".swiper-wrapper"))return console.error("Invalid container for Swiper initialization"),null;var t=e.parentElement;if(!t)return console.error("Container has no parent element"),null;var i=t.querySelector(".swiper-buttons .swiper-button-next"),t=t.querySelector(".swiper-buttons .swiper-button-prev");if(!i||!t)return console.error("Navigation buttons not found"),null;try{return new Swiper(e,{slidesPerView:1,spaceBetween:32,loop:!1,observer:!0,observeParents:!0,navigation:{nextEl:i,prevEl:t,hideOnClick:!1,disabledClass:"swiper-button-disabled"},breakpoints:{768:{slidesPerView:2},1232:{slidesPerView:4}},on:{init:function(){var e=this;setTimeout(function(){return equalizeSlideHeights(e)},100),updateNavigationVisibility(this)},resize:function(){var e=this;setTimeout(function(){return equalizeSlideHeights(e)},100),updateNavigationVisibility(this)},slideChange:function(){updateNavigationVisibility(this)}}})}catch(e){return console.error("Error creating Swiper instance:",e),null}}function updateNavigationVisibility(e){var t,i;e&&e.el&&e.el.parentElement&&(t=e.el.parentElement.querySelector(".swiper-buttons .swiper-button-prev"),i=e.el.parentElement.querySelector(".swiper-buttons .swiper-button-next"),t&&i&&(e.isBeginning?(t.style.visibility="hidden",t.style.opacity="0"):(t.style.visibility="visible",t.style.opacity="1"),e.isEnd?(i.style.visibility="hidden",i.style.opacity="0"):(i.style.visibility="visible",i.style.opacity="1")))}function equalizeSlideHeights(e){if(e&&e.el){e.el.classList.add("equal-height-slides");var t=e.el.querySelectorAll(".swiper-slide");if(t&&0!==t.length){t.forEach(function(e){e&&(e.style.height="",(e=e.querySelector(".card, .blog-article-card, .slide-content"))&&(e.style.height=""))}),e.el.offsetHeight;var i=0;t.forEach(function(e){e&&(e=e.offsetHeight,i=Math.max(i,e))}),0<i&&t.forEach(function(e){e&&(e.style.height="".concat(i,"px"),(e=e.querySelector(".card, .blog-article-card, .slide-content"))&&(e.style.height="100%"))});try{e.update()}catch(e){console.error("Error updating swiper:",e)}}}}
//# sourceMappingURL=rss-feed.min.js.map
