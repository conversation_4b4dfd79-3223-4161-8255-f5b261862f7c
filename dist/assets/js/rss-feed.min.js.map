{"version": 3, "sources": ["rss-feed.js"], "names": ["feedContainers", "document", "querySelectorAll", "feeds", "slice", "call", "i", "container", "url", "currentC<PERSON><PERSON>", "maxItems", "$", "ajax", "feedUrl", "accepts", "dataType", "data", "wrapper", "isBlogFeed", "classList", "contains", "createElement", "className", "append<PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "querySelector", "el", "categories", "each", "category", "title", "find", "pubDate", "formatDate", "Date", "months", "this", "text", "img", "description", "match", "imgSrc", "imgAlt", "template", "Array", "getMonth", "getDate", "getFullYear", "buttonsContainer", "link", "concat", "date", "slides", "setTimeout", "initializeBlogFeedSwiper", "innerHTML", "parentElement", "append", "error", "e", "console", "warn", "max", "nextButton", "prevButton", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "observeParents", "navigation", "prevEl", "disabledClass", "hideOnClick", "Swiper", "loop", "nextEl", "init", "breakpoints", "equalizeSlideHeights", "updateNavigationVisibility", "1232", "_this", "slideChange", "resize", "_this2", "swiper", "style", "visibility", "isBeginning", "isEnd", "opacity", "for<PERSON>ach", "add", "slide", "height", "content", "offsetHeight", "maxHeight", "update"], "mappings": "aACA,IADA,IAAMA,eAAiBC,SAASC,iBAAiB,kBAC3CC,MAAQ,GAAGC,MAAMC,KAAKL,gBAAjBM,EAAG,EAAGF,EAAHD,MAAcH,OAAAA,IAAAA,CAG1B,IAAIO,UAAYP,eAAeM,GAD5BE,IAAKD,UAAUJ,aAAe,gBAC7BI,IAAAA,UAAYP,aAAhB,iBAAA,IAKA,SAAUS,EAA2BC,GAApCC,EAAAC,KA8GAJ,IA9GSC,CACNG,QAAKC,CACLC,IAAO,uBAAEC,SADK,MAIdA,QAAQ,SAJMC,GAMZ,IAKQC,EALRC,EAAAT,EAAAU,UAAAC,SAAA,aAIIF,KACID,EAAUhB,SAASoB,cAAc,QAC/BC,UAAY,iBACpBb,EAAiBc,YAAYN,IAI/B,IAAMO,EAAkBN,EAItBF,EAAFS,cAEYf,mBAERD,EAGAiB,EAAAA,GACEC,KAAAA,QADFvB,MAAA,EAAAM,GAIAkB,KAAIC,WACJ,IAAIC,EAAQJ,EAAGK,MAEXC,EAAaD,GACjBL,EAAIO,KAAAA,YAAiBC,KAAKF,WACtBG,GAAcxB,EAACyB,MAAAC,OAAW,OAI9B,IAAIC,EAAMC,EAAAA,KAAYC,GACpBpC,MAAA,GAAA,GATE0B,EAAQJ,EAAGK,KAAK,SAASM,OAazBI,EAASH,EAAGP,KAAIO,QAASA,OACzBI,EAAYhB,EAAAK,KAAQ,WAAXM,OAETM,EAAJ,IAAAT,KAAAF,GAEId,EAdS0B,MAAM,UAAW,WAAY,QAAS,QAAS,MAAO,OAAQ,OAAQ,SAAU,YAAa,UAAW,WAAY,YAcjHX,EAAAY,YAAA,IAAAZ,EAAAa,UAAA,KAAAb,EAAAc,cAGdJ,EADKjB,EAAAK,KAAA,eAAAM,OACGG,MACT,0EATGC,EAASH,GAAOA,EAAI,GAAKA,EAAI,GAAK,6CAetCpB,EAAYoB,GAAAA,EAAA,GAAAA,EAAA,GAAAR,EAKZkB,GADAA,EACAA,0IAAAA,OAAAlB,EAAAkB,gBAAAA,OAAAP,EAAAO,WAAAA,OAAAN,EAAAM,uIAAAA,OAAAC,EAAAD,wDAOF,yGAAAE,OAAApB,EAAA,gBAAAoB,OAAAT,EAAA,WAAAS,OAAAR,EAAA,+GAAAQ,OAAArB,EAAA,8DAAAqB,OAAAD,EAAA,uBAPED,OAAAlB,EAAAkB,uDAAAA,OAAAG,EAAAH,gCAQFrC,EAAMyC,GAAS3C,OAAAA,KAGb4C,IAEIC,EAAAA,cAAyB7C,cAAzB,sBACAuC,EAAU/C,SAAAoB,cAAA,QACVC,UAAc,iBACf0B,EAAAO,UAAA,6HAIJ9C,EAAA+C,cAAAjC,YAAAyB,IAKiBS,EADNhD,EAAmBiD,iBAAjC,iBACEjD,OA3FN4C,WAAA,WADF,IAkGFC,yBAAA7C,GAjBgB,MAAOkD,GACPC,QAAQF,MAAM,6BAA8BC,KAkBrDpD,KAEIqD,QAAPC,KAAA,4CAIIL,MAAAA,SAAgBjD,EAAAA,EAAUiD,GAf1BI,QAAQF,MAAM,kBAAmBA,GAgBlCF,EAAAA,GAAeC,OAAfD,kGA3GL,CA8GCjD,UAAAuD,KAXH,SAASR,yBAAyB/C,GAgBhC,IAAKwD,IAAcxD,EAACyD,cAAY,mBAE9B,OADAJ,QAAQF,MAAM,+CACP,KAIT,IAAIF,EAAAjD,EAAAiD,cACF,IAAAA,EAEES,OADAC,QAAAA,MAAAA,mCACAD,KAGAE,IAAAA,EAAcX,EAL+B/B,cAAA,uCAM7C2C,EAAYZ,EAAA/B,cAAA,uCAEV4C,IAAAA,IAAQL,EAERM,OADAC,QAAAA,MAAAA,gCACAD,KAIA,IA+BR,OA9ByB,IAAAE,OAAAjE,EAAA,CADZ2D,cAFM,EAKXD,aAAA,GACAQ,MAAA,EACEP,UAAAA,EADIC,gBAAA,EANKC,WAZgC,CAsB3CM,OAAEX,EACFY,OAAMX,EAAYO,aAAA,EAblBD,cAAe,0BAcFM,YAAMC,CACjBC,IAAAA,CAHAZ,cAAA,GAJFa,KAAM,CAUJ1B,cAAW,IACXyB,GAAAA,CACDH,KARC,WAAA,IAAAK,EAAA5C,KASF6C,WAAa,WAAA,OAAAJ,qBAAYG,IAAA,KACvBF,2BAA2B1C,OAV3B8C,OAAA,WAAA,IAAAC,EAAA/C,KAtBNiB,WAAA,WAAA,OAAAwB,qBAAAM,IAAA,KAqCAL,2BAAA1C,OAEAwB,YAAc,WACdkB,2BAAA1C,UAMF,MAAKgD,GAEJ,OADCxB,QAAAF,MAAA,kCAAAC,GACD,MAKD,SAAIK,2BAA0BoB,GAC5B,IACApB,EAIEA,EALFoB,GAAAA,EAAA1D,IAAA0D,EAAA1D,GAAA8B,gBACAQ,EAGOoB,EAAA1D,GAAA8B,cAAA/B,cAAA,uCACLuC,EAAWqB,EAAMC,GAAAA,cAAjB7D,cAAA,uCAIFuC,GAAAD,IARIqB,EAAOG,aASXvB,EAAWwB,MAAOF,WAAA,SAChBvB,EAAWsB,MAAMC,QAAjB,MADFtB,EAGOqB,MAAAC,WAAA,UACLvB,EAAWsB,MAAMC,QAAjB,KAMNF,EAAAI,OATMzB,EAAWsB,MAAMC,WAAa,SAC9BvB,EAAWsB,MAAMI,QAAU,MAU3B1B,EAAWsB,MAACD,WAAW,UACzBrB,EAAAsB,MAAAI,QAAA,OAOF,SAAMrC,qBAAmBlD,GARzB,GAAKkF,GAAWA,EAAO1D,GAAvB,CAaA0B,EAAOsC,GAAAA,UAAQC,IAAA,uBALf,IAAMvC,EAASgC,EAAO1D,GAAGxB,iBAAiB,iBAStC,GAAAkD,GAAsB3B,IAAT2B,EAAGwC,OAAhB,CAJJxC,EAAOsC,QAAQ,SAAAE,GAUVR,IARDQ,EAAMP,MAAMQ,OAAS,IAYlBH,EAAQE,EAAAA,cAAS,gDACXE,EAAAT,MAAAQ,OAAA,OALRT,EAAO1D,GAAGqE,aAcX,IAAAC,EAAW,EACTJ,EAAAA,QAAMP,SAAAA,GAVNO,IAaME,EAAOF,EAAQG,aACrBC,EAAIF,KAASA,IAAAA,EAAcD,MAPjB,EAAZG,GAaJ5C,EAAIsC,QAAA,SAAAE,GACIA,IACNA,EAAUP,MAAAQ,OAAV,GAAA3C,OAAU8C,EAAV,OAQJF,EAAAF,EAAAnE,cAAA,gDAEAqE,EAAAT,MAAAQ,OAAA,WAWA,IACAT,EAAAa,SACA,MAAAtC,GACAC,QAAAF,MAAA,yBAAAC", "file": "rss-feed.min.js", "sourcesContent": ["const feedContainers = document.querySelectorAll('[data-rss-url]');\r\nconst feeds = [].slice.call(feedContainers);\r\n\r\nfor (var i = 0; i < feeds.length; i++) {\r\n  var container = feedContainers[i];\r\n  var url = container.getAttribute('data-rss-url');\r\n  var max = container.getAttribute('data-rss-max') || 10;\r\n  \r\n  // Create a closure to maintain the correct container reference\r\n  (function(currentContainer, feedUrl, maxItems) {\r\n    $.ajax(feedUrl, {\r\n      accepts: {\r\n        xml: \"application/rss+xml\",\r\n      },\r\n      dataType: \"xml\",\r\n      success: function (data) {\r\n        // Check if this is a blog feed container\r\n        const isBlogFeed = currentContainer.classList.contains('blog-feed');\r\n        \r\n        // If it's a blog feed, add the swiper-wrapper\r\n        if (isBlogFeed) {\r\n          const wrapper = document.createElement('div');\r\n          wrapper.className = 'swiper-wrapper';\r\n          currentContainer.appendChild(wrapper);\r\n        }\r\n        \r\n        // Get the container to append items to\r\n        const appendContainer = isBlogFeed ? \r\n          currentContainer.querySelector('.swiper-wrapper') : \r\n          currentContainer;\r\n        \r\n        $(data)\r\n          .find(\"item\")\r\n          .slice(0, maxItems)\r\n          .each(function () {\r\n            const el = $(this);\r\n            \r\n            var categories = '';\r\n            el.find(\"category\").each(function() {\r\n              categories += $(this).text() + \", \";\r\n            });\r\n            \r\n            var category = $.trim(categories).slice(0, -1);\r\n            var title = el.find(\"title\").text();\r\n            var link = el.find(\"link\").text();\r\n            var pubDate = el.find(\"pubDate\").text();\r\n            var formatDate = new Date(pubDate);\r\n            var months = Array(\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\");\r\n            var date = months[formatDate.getMonth()] + \" \" + formatDate.getDate() + \", \" + formatDate.getFullYear();\r\n            \r\n            var description = el.find(\"description\").text();\r\n            var img = description.match(\r\n              /<img\\b[^>]+?src\\s*=\\s*['\"]?([^\\s'\"?#>]+)['\"]\\salt\\s*=\\s*['\"]?([^'\">]+)/\r\n            );\r\n            \r\n            // Check if image was found in the description\r\n            var imgSrc = img && img[1] ? img[1] : 'https://placehold.co/800x400?text=No+Image';\r\n            var imgAlt = img && img[2] ? img[2] : title;\r\n            \r\n            let template;\r\n            \r\n            if (isBlogFeed) {\r\n              template = `<div class=\"swiper-slide\"><article class=\"card blog-article-card h-100 rounded-0 bg-transparent border-0\" aria-label=\"Article summary: ${title}\"><img src=\"${imgSrc}\" alt=\"${imgAlt}\" class=\"card-img-top rounded-0\"><div class=\"card-body pt-3 pb-0 px-0\"><div class=\"h4 mb-2 card-title blog-article-title\"><a href=\"${link}\" target=\"_blank\" class=\"stretched-link text-reset\">${title}</a></div><div class=\"card-text blog-article-date\">${date}</div></div></article></div>`;\r\n            } else {\r\n              template = `<div class=\"col mb-5\"><article class=\"blog-article-grid-item card h-100\" aria-label=\"Article summary: ${title}\"><img src=\"${imgSrc}\" alt=\"${imgAlt}\" class=\"card-img-top\"><div class=\"card-body bg-light mx-4 mt-n5\"><div class=\"card-title blog-article-tag\">${category}</div><div class=\"card-title blog-article-title\"><a href=\"${link}\" target=\"_blank\">${title}</a></div><div class=\"card-text blog-article-date\">${date}</div></div></article></div>`;\r\n            }\r\n            \r\n            $(appendContainer).append(template);\r\n          });\r\n          \r\n        // Initialize Swiper after content is loaded (if it's a blog feed)\r\n        if (isBlogFeed) {\r\n          // Add navigation buttons if they don't exist\r\n          if (!currentContainer.parentElement.querySelector('.swiper-buttons')) {\r\n            const buttonsContainer = document.createElement('div');\r\n            buttonsContainer.className = 'swiper-buttons';\r\n            buttonsContainer.innerHTML = `\r\n              <div class=\"swiper-button-prev\"></div>\r\n              <div class=\"swiper-button-next\"></div>\r\n            `;\r\n            currentContainer.parentElement.appendChild(buttonsContainer);\r\n          }\r\n          \r\n          // Make sure we have slides before initializing Swiper\r\n          const slides = currentContainer.querySelectorAll('.swiper-slide');\r\n          if (slides.length > 0) {\r\n            // Wait a bit to ensure DOM is fully updated\r\n            setTimeout(() => {\r\n              try {\r\n                initializeBlogFeedSwiper(currentContainer);\r\n              } catch (e) {\r\n                console.error(\"Error initializing Swiper:\", e);\r\n              }\r\n            }, 100);\r\n          } else {\r\n            console.warn(\"No slides found in blog feed container\");\r\n          }\r\n        }\r\n      },\r\n      error: function(xhr, status, error) {\r\n        console.error(\"RSS Feed Error:\", error);\r\n        $(currentContainer).append(`<div class=\"alert alert-warning\">Unable to load blog content. Please try again later.</div>`);\r\n      }\r\n    });\r\n  })(container, url, max);\r\n}\r\n\r\n// Function to initialize blog feed swiper\r\nfunction initializeBlogFeedSwiper(container) {\r\n  if (!container || !container.querySelector('.swiper-wrapper')) {\r\n    console.error(\"Invalid container for Swiper initialization\");\r\n    return null;\r\n  }\r\n  \r\n  // Make sure navigation elements exist\r\n  const parentElement = container.parentElement;\r\n  if (!parentElement) {\r\n    console.error(\"Container has no parent element\");\r\n    return null;\r\n  }\r\n  \r\n  const nextButton = parentElement.querySelector('.swiper-buttons .swiper-button-next');\r\n  const prevButton = parentElement.querySelector('.swiper-buttons .swiper-button-prev');\r\n  \r\n  if (!nextButton || !prevButton) {\r\n    console.error(\"Navigation buttons not found\");\r\n    return null;\r\n  }\r\n  \r\n  // Initialize Homepage Blog Feed Carousel\r\n  try {\r\n    const homepageBlogFeed = new Swiper(container, {\r\n      slidesPerView: 1,\r\n      spaceBetween: 32,\r\n      loop: false,\r\n      observer: true,\r\n      observeParents: true,\r\n      navigation: {\r\n        nextEl: nextButton,\r\n        prevEl: prevButton,\r\n        hideOnClick: false,\r\n        disabledClass: 'swiper-button-disabled',\r\n      },\r\n      breakpoints: {\r\n        // when window width is >= 768px\r\n        768: {\r\n          slidesPerView: 2,\r\n        },\r\n        // when window width is >= 1232px\r\n        1232: {\r\n          slidesPerView: 4,\r\n        },\r\n      },\r\n      on: {\r\n        init: function () {\r\n          setTimeout(() => equalizeSlideHeights(this), 100);\r\n          updateNavigationVisibility(this);\r\n        },\r\n        resize: function () {\r\n          setTimeout(() => equalizeSlideHeights(this), 100);\r\n          updateNavigationVisibility(this);\r\n        },\r\n        slideChange: function () {\r\n          updateNavigationVisibility(this);\r\n        }\r\n      }\r\n    });\r\n    \r\n    return homepageBlogFeed;\r\n  } catch (e) {\r\n    console.error(\"Error creating Swiper instance:\", e);\r\n    return null;\r\n  }\r\n}\r\n\r\n// Function to update navigation visibility\r\nfunction updateNavigationVisibility(swiper) {\r\n  if (!swiper || !swiper.el || !swiper.el.parentElement) {\r\n    return;\r\n  }\r\n  \r\n  const prevButton = swiper.el.parentElement.querySelector('.swiper-buttons .swiper-button-prev');\r\n  const nextButton = swiper.el.parentElement.querySelector('.swiper-buttons .swiper-button-next');\r\n  \r\n  if (prevButton && nextButton) {\r\n    // Hide prev button if at beginning\r\n    if (swiper.isBeginning) {\r\n      prevButton.style.visibility = 'hidden';\r\n      prevButton.style.opacity = '0';\r\n    } else {\r\n      prevButton.style.visibility = 'visible';\r\n      prevButton.style.opacity = '1';\r\n    }\r\n    \r\n    // Hide next button if at end\r\n    if (swiper.isEnd) {\r\n      nextButton.style.visibility = 'hidden';\r\n      nextButton.style.opacity = '0';\r\n    } else {\r\n      nextButton.style.visibility = 'visible';\r\n      nextButton.style.opacity = '1';\r\n    }\r\n  }\r\n}\r\n\r\n// Function to equalize slide heights\r\nfunction equalizeSlideHeights(swiper) {\r\n  if (!swiper || !swiper.el) {\r\n    return;\r\n  }\r\n  \r\n  // First, add a class to the swiper container to help with CSS targeting\r\n  swiper.el.classList.add('equal-height-slides');\r\n  \r\n  // Reset heights to natural height first\r\n  const slides = swiper.el.querySelectorAll('.swiper-slide');\r\n  if (!slides || slides.length === 0) {\r\n    return;\r\n  }\r\n  \r\n  slides.forEach(slide => {\r\n    if (slide) {\r\n      slide.style.height = '';\r\n      // Also reset any inner content that might need height adjustment\r\n      const content = slide.querySelector('.card, .blog-article-card, .slide-content');\r\n      if (content) content.style.height = '';\r\n    }\r\n  });\r\n  \r\n  // Force a reflow\r\n  void swiper.el.offsetHeight;\r\n  \r\n  // Find the tallest slide\r\n  let maxHeight = 0;\r\n  slides.forEach(slide => {\r\n    if (slide) {\r\n      const height = slide.offsetHeight;\r\n      maxHeight = Math.max(maxHeight, height);\r\n    }\r\n  });\r\n  \r\n  // Set all slides to the tallest height\r\n  if (maxHeight > 0) {\r\n    slides.forEach(slide => {\r\n      if (slide) {\r\n        slide.style.height = `${maxHeight}px`;\r\n        \r\n        // Also set height for inner content if needed\r\n        const content = slide.querySelector('.card, .blog-article-card, .slide-content');\r\n        if (content) content.style.height = '100%';\r\n      }\r\n    });\r\n  }\r\n  \r\n  // Update swiper\r\n  try {\r\n    swiper.update();\r\n  } catch (e) {\r\n    console.error(\"Error updating swiper:\", e);\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n// const feedContainers = document.querySelectorAll('[data-rss-url]');\r\n\r\n// const feeds = [].slice.call(feedContainers);\r\n\r\n// for (var i = 0; i < feeds.length; i++) {\r\n\r\n//   var container = feedContainers[i];\r\n  \r\n//   var url = container.getAttribute('data-rss-url');\r\n//   var max = container.getAttribute('data-rss-max') || 10;\r\n\r\n// }\r\n\r\n// $.ajax(url, {\r\n//   accepts: {\r\n//     xml: \"application/rss+xml\",\r\n//   },\r\n\r\n//   dataType: \"xml\",\r\n\r\n//   success: function (data) {\r\n//     $(data)\r\n//       .find(\"item\")\r\n//       .slice(0, max)\r\n//       .each(function () {\r\n//         const el = $(this);\r\n\r\n//         var categories = '';\r\n//         el.find(\"category\").each(function() {\r\n//           categories += $(this).text() + \", \";\r\n//         });\r\n\r\n//         var category = $.trim(categories).slice(0, -1);\r\n//         var title = el.find(\"title\").text();\r\n//         var link = el.find(\"link\").text();\r\n//         var pubDate = el.find(\"pubDate\").text();\r\n//         var formatDate = new Date(pubDate);\r\n//         var months = Array(\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\");\r\n//         var date = months[formatDate.getMonth()] + \" \" + formatDate.getDate() + \", \" + formatDate.getFullYear();\r\n        \r\n//         var description = el.find(\"description\").text();\r\n//         var img = description.match(\r\n//           /<img\\b[^>]+?src\\s*=\\s*['\"]?([^\\s'\"?#>]+)['\"]\\salt\\s*=\\s*['\"]?([^'\">]+)/\r\n//         );\r\n//         var imgSrc = img[1];\r\n//         var imgAlt = img[2];\r\n\r\n//         // const template = `<div class=\"col mb-5\"><article class=\"blog-article-grid-item card h-100\" aria-label=\"Article summary: ${title}\"><img src=\"${imgSrc}\" alt=\"${imgAlt}\" class=\"card-img-top\"><div class=\"card-body bg-light mx-4 mt-n5\"><div class=\"card-title blog-article-tag\">${category}</div><div class=\"card-title blog-article-title\"><a href=\"${link}\" target=\"_blank\">${title}</a></div><div class=\"card-text blog-article-date\">${date}</div></div></article></div>`;\r\n\r\n//         const template = `<div class=\"swiper-slide\"><article class=\"card blog-article-card h-100 rounded-0 bg-transparent border-0\" aria-label=\"Article summary: ${title}\"><img src=\"${imgSrc}\" alt=\"${imgAlt}\" class=\"card-img-top rounded-0\"><div class=\"card-body pt-3 pb-0 px-0\"><div class=\"h4 mb-2 card-title blog-article-title\"><a href=\"${link}\" target=\"_blank\" class=\"stretched-link text-reset\">Article Title 1</a></div><div class=\"card-text blog-article-date\">${date}</div></div></article></div>`;\r\n\r\n//         $(\".blog-feed\").append(template);\r\n//       });\r\n//   },\r\n// });\r\n\r\n"]}