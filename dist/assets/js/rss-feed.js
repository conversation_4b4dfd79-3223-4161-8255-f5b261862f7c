"use strict";

var feedContainers = document.querySelectorAll('[data-rss-url]');
var feeds = [].slice.call(feedContainers);
for (var i = 0; i < feeds.length; i++) {
  var container = feedContainers[i];
  var url = container.getAttribute('data-rss-url');
  var max = container.getAttribute('data-rss-max') || 10;

  // Create a closure to maintain the correct container reference
  (function (currentContainer, feedUrl, maxItems) {
    $.ajax(feedUrl, {
      accepts: {
        xml: "application/rss+xml"
      },
      dataType: "xml",
      success: function success(data) {
        // Check if this is a blog feed container
        var isBlogFeed = currentContainer.classList.contains('blog-feed');

        // If it's a blog feed, add the swiper-wrapper
        if (isBlogFeed) {
          var wrapper = document.createElement('div');
          wrapper.className = 'swiper-wrapper';
          currentContainer.appendChild(wrapper);
        }

        // Get the container to append items to
        var appendContainer = isBlogFeed ? currentContainer.querySelector('.swiper-wrapper') : currentContainer;
        $(data).find("item").slice(0, maxItems).each(function () {
          var el = $(this);
          var categories = '';
          el.find("category").each(function () {
            categories += $(this).text() + ", ";
          });
          var category = $.trim(categories).slice(0, -1);
          var title = el.find("title").text();
          var link = el.find("link").text();
          var pubDate = el.find("pubDate").text();
          var formatDate = new Date(pubDate);
          var months = Array("January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December");
          var date = months[formatDate.getMonth()] + " " + formatDate.getDate() + ", " + formatDate.getFullYear();
          var description = el.find("description").text();
          var img = description.match(/<img\b[^>]+?src\s*=\s*['"]?([^\s'"?#>]+)['"]\salt\s*=\s*['"]?([^'">]+)/);

          // Check if image was found in the description
          var imgSrc = img && img[1] ? img[1] : 'https://placehold.co/800x400?text=No+Image';
          var imgAlt = img && img[2] ? img[2] : title;
          var template;
          if (isBlogFeed) {
            template = "<div class=\"swiper-slide\"><article class=\"card blog-article-card h-100 rounded-0 bg-transparent border-0\" aria-label=\"Article summary: ".concat(title, "\"><img src=\"").concat(imgSrc, "\" alt=\"").concat(imgAlt, "\" class=\"card-img-top rounded-0\"><div class=\"card-body pt-3 pb-0 px-0\"><div class=\"h4 mb-2 card-title blog-article-title\"><a href=\"").concat(link, "\" target=\"_blank\" class=\"stretched-link text-reset\">").concat(title, "</a></div><div class=\"card-text blog-article-date\">").concat(date, "</div></div></article></div>");
          } else {
            template = "<div class=\"col mb-5\"><article class=\"blog-article-grid-item card h-100\" aria-label=\"Article summary: ".concat(title, "\"><img src=\"").concat(imgSrc, "\" alt=\"").concat(imgAlt, "\" class=\"card-img-top\"><div class=\"card-body bg-light mx-4 mt-n5\"><div class=\"card-title blog-article-tag\">").concat(category, "</div><div class=\"card-title blog-article-title\"><a href=\"").concat(link, "\" target=\"_blank\">").concat(title, "</a></div><div class=\"card-text blog-article-date\">").concat(date, "</div></div></article></div>");
          }
          $(appendContainer).append(template);
        });

        // Initialize Swiper after content is loaded (if it's a blog feed)
        if (isBlogFeed) {
          // Add navigation buttons if they don't exist
          if (!currentContainer.parentElement.querySelector('.swiper-buttons')) {
            var buttonsContainer = document.createElement('div');
            buttonsContainer.className = 'swiper-buttons';
            buttonsContainer.innerHTML = "\n              <div class=\"swiper-button-prev\"></div>\n              <div class=\"swiper-button-next\"></div>\n            ";
            currentContainer.parentElement.appendChild(buttonsContainer);
          }

          // Make sure we have slides before initializing Swiper
          var slides = currentContainer.querySelectorAll('.swiper-slide');
          if (slides.length > 0) {
            // Wait a bit to ensure DOM is fully updated
            setTimeout(function () {
              try {
                initializeBlogFeedSwiper(currentContainer);
              } catch (e) {
                console.error("Error initializing Swiper:", e);
              }
            }, 100);
          } else {
            console.warn("No slides found in blog feed container");
          }
        }
      },
      error: function error(xhr, status, _error) {
        console.error("RSS Feed Error:", _error);
        $(currentContainer).append("<div class=\"alert alert-warning\">Unable to load blog content. Please try again later.</div>");
      }
    });
  })(container, url, max);
}

// Function to initialize blog feed swiper
function initializeBlogFeedSwiper(container) {
  if (!container || !container.querySelector('.swiper-wrapper')) {
    console.error("Invalid container for Swiper initialization");
    return null;
  }

  // Make sure navigation elements exist
  var parentElement = container.parentElement;
  if (!parentElement) {
    console.error("Container has no parent element");
    return null;
  }
  var nextButton = parentElement.querySelector('.swiper-buttons .swiper-button-next');
  var prevButton = parentElement.querySelector('.swiper-buttons .swiper-button-prev');
  if (!nextButton || !prevButton) {
    console.error("Navigation buttons not found");
    return null;
  }

  // Initialize Homepage Blog Feed Carousel
  try {
    var homepageBlogFeed = new Swiper(container, {
      slidesPerView: 1,
      spaceBetween: 32,
      loop: false,
      observer: true,
      observeParents: true,
      navigation: {
        nextEl: nextButton,
        prevEl: prevButton,
        hideOnClick: false,
        disabledClass: 'swiper-button-disabled'
      },
      breakpoints: {
        // when window width is >= 768px
        768: {
          slidesPerView: 2
        },
        // when window width is >= 1232px
        1232: {
          slidesPerView: 4
        }
      },
      on: {
        init: function init() {
          var _this = this;
          setTimeout(function () {
            return equalizeSlideHeights(_this);
          }, 100);
          updateNavigationVisibility(this);
        },
        resize: function resize() {
          var _this2 = this;
          setTimeout(function () {
            return equalizeSlideHeights(_this2);
          }, 100);
          updateNavigationVisibility(this);
        },
        slideChange: function slideChange() {
          updateNavigationVisibility(this);
        }
      }
    });
    return homepageBlogFeed;
  } catch (e) {
    console.error("Error creating Swiper instance:", e);
    return null;
  }
}

// Function to update navigation visibility
function updateNavigationVisibility(swiper) {
  if (!swiper || !swiper.el || !swiper.el.parentElement) {
    return;
  }
  var prevButton = swiper.el.parentElement.querySelector('.swiper-buttons .swiper-button-prev');
  var nextButton = swiper.el.parentElement.querySelector('.swiper-buttons .swiper-button-next');
  if (prevButton && nextButton) {
    // Hide prev button if at beginning
    if (swiper.isBeginning) {
      prevButton.style.visibility = 'hidden';
      prevButton.style.opacity = '0';
    } else {
      prevButton.style.visibility = 'visible';
      prevButton.style.opacity = '1';
    }

    // Hide next button if at end
    if (swiper.isEnd) {
      nextButton.style.visibility = 'hidden';
      nextButton.style.opacity = '0';
    } else {
      nextButton.style.visibility = 'visible';
      nextButton.style.opacity = '1';
    }
  }
}

// Function to equalize slide heights
function equalizeSlideHeights(swiper) {
  if (!swiper || !swiper.el) {
    return;
  }

  // First, add a class to the swiper container to help with CSS targeting
  swiper.el.classList.add('equal-height-slides');

  // Reset heights to natural height first
  var slides = swiper.el.querySelectorAll('.swiper-slide');
  if (!slides || slides.length === 0) {
    return;
  }
  slides.forEach(function (slide) {
    if (slide) {
      slide.style.height = '';
      // Also reset any inner content that might need height adjustment
      var content = slide.querySelector('.card, .blog-article-card, .slide-content');
      if (content) content.style.height = '';
    }
  });

  // Force a reflow
  void swiper.el.offsetHeight;

  // Find the tallest slide
  var maxHeight = 0;
  slides.forEach(function (slide) {
    if (slide) {
      var height = slide.offsetHeight;
      maxHeight = Math.max(maxHeight, height);
    }
  });

  // Set all slides to the tallest height
  if (maxHeight > 0) {
    slides.forEach(function (slide) {
      if (slide) {
        slide.style.height = "".concat(maxHeight, "px");

        // Also set height for inner content if needed
        var content = slide.querySelector('.card, .blog-article-card, .slide-content');
        if (content) content.style.height = '100%';
      }
    });
  }

  // Update swiper
  try {
    swiper.update();
  } catch (e) {
    console.error("Error updating swiper:", e);
  }
}

// const feedContainers = document.querySelectorAll('[data-rss-url]');

// const feeds = [].slice.call(feedContainers);

// for (var i = 0; i < feeds.length; i++) {

//   var container = feedContainers[i];

//   var url = container.getAttribute('data-rss-url');
//   var max = container.getAttribute('data-rss-max') || 10;

// }

// $.ajax(url, {
//   accepts: {
//     xml: "application/rss+xml",
//   },

//   dataType: "xml",

//   success: function (data) {
//     $(data)
//       .find("item")
//       .slice(0, max)
//       .each(function () {
//         const el = $(this);

//         var categories = '';
//         el.find("category").each(function() {
//           categories += $(this).text() + ", ";
//         });

//         var category = $.trim(categories).slice(0, -1);
//         var title = el.find("title").text();
//         var link = el.find("link").text();
//         var pubDate = el.find("pubDate").text();
//         var formatDate = new Date(pubDate);
//         var months = Array("January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December");
//         var date = months[formatDate.getMonth()] + " " + formatDate.getDate() + ", " + formatDate.getFullYear();

//         var description = el.find("description").text();
//         var img = description.match(
//           /<img\b[^>]+?src\s*=\s*['"]?([^\s'"?#>]+)['"]\salt\s*=\s*['"]?([^'">]+)/
//         );
//         var imgSrc = img[1];
//         var imgAlt = img[2];

//         // const template = `<div class="col mb-5"><article class="blog-article-grid-item card h-100" aria-label="Article summary: ${title}"><img src="${imgSrc}" alt="${imgAlt}" class="card-img-top"><div class="card-body bg-light mx-4 mt-n5"><div class="card-title blog-article-tag">${category}</div><div class="card-title blog-article-title"><a href="${link}" target="_blank">${title}</a></div><div class="card-text blog-article-date">${date}</div></div></article></div>`;

//         const template = `<div class="swiper-slide"><article class="card blog-article-card h-100 rounded-0 bg-transparent border-0" aria-label="Article summary: ${title}"><img src="${imgSrc}" alt="${imgAlt}" class="card-img-top rounded-0"><div class="card-body pt-3 pb-0 px-0"><div class="h4 mb-2 card-title blog-article-title"><a href="${link}" target="_blank" class="stretched-link text-reset">Article Title 1</a></div><div class="card-text blog-article-date">${date}</div></div></article></div>`;

//         $(".blog-feed").append(template);
//       });
//   },
// });