"use strict";function _typeof2(e){return(_typeof2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function setupResponsiveMenu(){$(".navbar-nav .nav-pills .nav-link").off("click.bs.dropdown"),$(".navbar-nav > .dropdown").off("mouseenter mouseleave"),$(".navbar-nav > .dropdown > .dropdown-menu .nav-pills .nav-item .nav-link").off("mouseenter"),$('.navbar-nav > .dropdown > .dropdown-menu .nav-pills .nav-item a[data-toggle="pill"]').off("shown.bs.tab"),$(".navbar-toggler, #navMobileOverlay").off("click"),$(".navbar-nav .tab-pane").off("click",'a[data-toggle="collapse"]'),window.matchMedia("(min-width: 992px)").matches?($(".navbar-nav .nav-pills .nav-link").on("click.bs.dropdown",function(e){$(this).tab("show"),e.stopPropagation()}),$(".navbar-nav > .dropdown").on({mouseenter:function(){var e=$(this);e.addClass("show"),e.find("> .dropdown-toggle").attr("aria-expanded","true"),e.find("> .dropdown-menu").addClass("show"),e.find("> .dropdown-menu .nav-pills .nav-item:first-child > .nav-link").tab("show")},mouseleave:function(){var e=$(this);e.removeClass("show"),e.find("> .dropdown-toggle").attr("aria-expanded","false"),e.find("> .dropdown-menu").removeClass("show")}}),$(".navbar-nav > .dropdown > .dropdown-menu .nav-pills .nav-item .nav-link").on("mouseenter",function(){clearTimeout(void 0),"pill"===$(this).attr("data-toggle")?$(this).tab("show"):($(this).closest(".nav-pills").find(".nav-link").removeClass("active").attr("aria-selected","false"),$(this).closest(".dropdown-menu").find(".tab-content .tab-pane").removeClass("show active"))}),$(".navbar-nav > .dropdown > .dropdown-menu .nav-pills").on("mouseleave",function(){}),$(".navbar-nav > .dropdown > .dropdown-menu .tab-content").on("mouseenter",function(){clearTimeout(void 0)}),$(".navbar-nav > .dropdown > .dropdown-menu .tab-content").on("mouseleave",function(){})):window.matchMedia("(max-width: 991.98px)").matches&&($(".navbar-toggler, #navMobileOverlay").on("click",function(){$(".navbar-toggler").toggleClass("is-active"),$("#navMobileOverlay").toggleClass("show"),$("#mainNavigation").toggleClass("slide"),$("body").toggleClass("modal-open"),$(this).attr("aria-expanded",function(e,t){return"true"==t?"false":"true"}),$(".navbar-toggler").hasClass("is-active")||setTimeout(function(){$(".navbar-nav .dropdown").removeClass("show"),$(".navbar-nav .dropdown-toggle, .navbar-nav .tab-content .tab-pane .card-header a").addClass("collapsed"),$(".navbar-nav .dropdown-toggle, .navbar-nav .tab-content .tab-pane .card-header a").attr("aria-expanded","false"),$(".navbar-nav .dropdown-menu").removeClass("show"),$(".navbar-nav .tab-content .tab-pane .card-header").removeClass("active"),$(".navbar-nav .tab-content .tab-pane .collapse").removeClass("show")},300)}),$(".navbar-nav .tab-pane").on("click",'a[data-toggle="collapse"]',function(e){$($(this).attr("data-target")).collapse("toggle"),e.stopPropagation()}))}!function(e,t){"object"===("undefined"==typeof exports?"undefined":_typeof2(exports))&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).GLightbox=t()}(void 0,function(){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function e(e,t,i){return t&&n(e.prototype,t),i&&n(e,i),e}var o=Date.now();function d(e){var i={},n=!0,t=0,s=arguments.length;"[object Boolean]"===Object.prototype.toString.call(e)&&(n=e,t++);for(;t<s;t++)!function(e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n&&"[object Object]"===Object.prototype.toString.call(e[t])?i[t]=d(!0,i[t],e[t]):i[t]=e[t])}(arguments[t]);return i}function p(e,t){if(0!=f(e=!a(e=F(e)||e===window||e===document?[e]:e)&&!u(e)?[e]:e))if(a(e)&&!u(e))for(var i=e.length,n=0;n<i&&!1!==t.call(e[n],e[n],n,e);n++);else if(u(e))for(var s in e)if(S(e,s)&&!1===t.call(e[s],e[s],s,e))break}function h(e,t,i){var n=1<arguments.length&&void 0!==t?t:null,s=2<arguments.length&&void 0!==i?i:null,e=e[o]=e[o]||[],a={all:e,evt:null,found:null};return n&&s&&0<f(e)&&p(e,function(e,t){if(e.eventName==n&&e.fn.toString()==s.toString())return a.found=!0,a.evt=t,!1}),a}function I(i,e,t){var n=1<arguments.length&&void 0!==e?e:{},s=n.onElement,a=n.withCallback,e=n.avoidDuplicate,r=void 0===e||e,e=n.once,o=void 0!==e&&e,n=n.useCapture,l=void 0!==n&&n,d=2<arguments.length?t:void 0,c=s||[];function u(e){B(a)&&a.call(d,e,this),o&&u.destroy()}return w(c)&&(c=document.querySelectorAll(c)),u.destroy=function(){p(c,function(e){var t=h(e,i,u);t.found&&t.all.splice(t.evt,1),e.removeEventListener&&e.removeEventListener(i,u,l)})},p(c,function(e){var t=h(e,i,u);(e.addEventListener&&r&&!t.found||!r)&&(e.addEventListener(i,u,l),t.all.push({eventName:i,fn:u}))}),u}function D(t,e){p(e.split(" "),function(e){return t.classList.add(e)})}function $(t,e){p(e.split(" "),function(e){return t.classList.remove(e)})}function z(e,t){return e.classList.contains(t)}function N(e,t){for(;e!==document.body;){if(!(e=e.parentElement))return!1;if("function"==typeof e.matches?e.matches(t):e.msMatchesSelector(t))return e}}function H(t,e,i){var n,e=1<arguments.length&&void 0!==e?e:"",s=2<arguments.length&&void 0!==i&&i;t&&""!==e&&("none"!==e?(i=function(){var e,t=document.createElement("fakeelement"),i={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"animationend",WebkitAnimation:"webkitAnimationEnd"};for(e in i)if(void 0!==t.style[e])return i[e]}(),p(n=e.split(" "),function(e){D(t,"g"+e)}),I(i,{onElement:t,avoidDuplicate:!1,once:!0,withCallback:function(e,t){p(n,function(e){$(t,"g"+e)}),B(s)&&s()}})):B(s)&&s())}function q(e,t){t=1<arguments.length&&void 0!==t?t:"";if(""===t)return e.style.webkitTransform="",e.style.MozTransform="",e.style.msTransform="",e.style.OTransform="",e.style.transform="",!1;e.style.webkitTransform=t,e.style.MozTransform=t,e.style.msTransform=t,e.style.OTransform=t,e.style.transform=t}function l(e){e.style.display="block"}function c(e){e.style.display="none"}function g(e){var t=document.createDocumentFragment(),i=document.createElement("div");for(i.innerHTML=e;i.firstChild;)t.appendChild(i.firstChild);return t}function j(){return{width:window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,height:window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight}}function v(e,t,i,n){var s,a;e()?t():(i=i||100,a=setInterval(function(){e()&&(clearInterval(a),s&&clearTimeout(s),t())},i),n&&(s=setTimeout(function(){clearInterval(a)},n)))}function y(e,t,i){if(R(e))console.error("Inject assets error");else if(B(t)&&(i=t,t=!1),w(t)&&t in window)B(i)&&i();else{if(-1!==e.indexOf(".css")){if((r=document.querySelectorAll('link[href="'+e+'"]'))&&0<r.length)return void(B(i)&&i());var n=document.getElementsByTagName("head")[0],s=n.querySelectorAll('link[rel="stylesheet"]'),a=document.createElement("link");return a.rel="stylesheet",a.type="text/css",a.href=e,a.media="all",s?n.insertBefore(a,s[0]):n.appendChild(a),void(B(i)&&i())}if((r=document.querySelectorAll('script[src="'+e+'"]'))&&0<r.length){if(B(i)){if(w(t))return void v(function(){return void 0!==window[t]},function(){i()});i()}}else{var r=document.createElement("script");r.type="text/javascript",r.src=e,r.onload=function(){if(B(i)){if(w(t))return v(function(){return void 0!==window[t]},function(){i()}),!1;i()}},document.body.appendChild(r)}}}function b(){return"navigator"in window&&window.navigator.userAgent.match(/(iPad)|(iPhone)|(iPod)|(Android)|(PlayBook)|(BB10)|(BlackBerry)|(Opera Mini)|(IEMobile)|(webOS)|(MeeGo)/i)}function B(e){return"function"==typeof e}function w(e){return"string"==typeof e}function F(e){return e&&e.nodeType&&1==e.nodeType}function i(e){return Array.isArray(e)}function a(e){return e&&e.length&&isFinite(e.length)}function u(e){return"object"===t(e)&&null!=e&&!B(e)&&!i(e)}function R(e){return null==e}function S(e,t){return null!==e&&hasOwnProperty.call(e,t)}function f(e){if(u(e)){if(e.keys)return e.keys().length;var t,i=0;for(t in e)S(e,t)&&i++;return i}return e.length}function X(e){return!isNaN(parseFloat(e))&&isFinite(e)}function Y(e){var t=0<arguments.length&&void 0!==e?e:-1,e=document.querySelectorAll(".gbtn[data-taborder]:not(.disabled)");if(!e.length)return!1;if(1==e.length)return e[0];"string"==typeof t&&(t=parseInt(t));var i=[];p(e,function(e){i.push(e.getAttribute("data-taborder"))});var e=Math.max.apply(Math,i.map(function(e){return parseInt(e)})),n=t<0?1:t+1;e<n&&(n="1");e=i.filter(function(e){return e>=parseInt(n)}).sort()[0];return document.querySelector('.gbtn[data-taborder="'.concat(e,'"]'))}function m(e){return Math.sqrt(e.x*e.x+e.y*e.y)}function T(e,t){var i,n,s,s=(n=t,0==(s=m(i=e)*m(n))?0:(1<(s=(i.x*n.x+i.y*n.y)/s)&&(s=1),Math.acos(s)));return 0<e.x*t.y-t.x*e.y&&(s*=-1),180*s/Math.PI}var s=(e(x,[{key:"add",value:function(e){this.handlers.push(e)}},{key:"del",value:function(e){e||(this.handlers=[]);for(var t=this.handlers.length;0<=t;t--)this.handlers[t]===e&&this.handlers.splice(t,1)}},{key:"dispatch",value:function(){for(var e=0,t=this.handlers.length;e<t;e++){var i=this.handlers[e];"function"==typeof i&&i.apply(this.el,arguments)}}}]),x);function x(e){r(this,x),this.handlers=[],this.el=e}function E(e,t){e=new s(e);return e.add(t),e}var W=(e(C,[{key:"start",value:function(e){var t,i;e.touches&&(e.target&&e.target.nodeName&&0<=["a","button","input"].indexOf(e.target.nodeName.toLowerCase())?console.log("ignore drag for this touched element",e.target.nodeName.toLowerCase()):(this.now=Date.now(),this.x1=e.touches[0].pageX,this.y1=e.touches[0].pageY,this.delta=this.now-(this.last||this.now),this.touchStart.dispatch(e,this.element),null!==this.preTapPosition.x&&(this.isDoubleTap=0<this.delta&&this.delta<=250&&Math.abs(this.preTapPosition.x-this.x1)<30&&Math.abs(this.preTapPosition.y-this.y1)<30,this.isDoubleTap&&clearTimeout(this.singleTapTimeout)),this.preTapPosition.x=this.x1,this.preTapPosition.y=this.y1,this.last=this.now,t=this.preV,1<e.touches.length&&(this._cancelLongTap(),this._cancelSingleTap(),i={x:e.touches[1].pageX-this.x1,y:e.touches[1].pageY-this.y1},t.x=i.x,t.y=i.y,this.pinchStartLen=m(t),this.multipointStart.dispatch(e,this.element)),this._preventTap=!1,this.longTapTimeout=setTimeout(function(){this.longTap.dispatch(e,this.element),this._preventTap=!0}.bind(this),750)))}},{key:"move",value:function(e){var t,i,n,s,a,r,o;e.touches&&(t=this.preV,i=e.touches.length,n=e.touches[0].pageX,s=e.touches[0].pageY,this.isDoubleTap=!1,1<i?(r=e.touches[1].pageX,o=e.touches[1].pageY,a={x:e.touches[1].pageX-n,y:e.touches[1].pageY-s},null!==t.x&&(0<this.pinchStartLen&&(e.zoom=m(a)/this.pinchStartLen,this.pinch.dispatch(e,this.element)),e.angle=T(a,t),this.rotate.dispatch(e,this.element)),t.x=a.x,t.y=a.y,null!==this.x2&&null!==this.sx2?(e.deltaX=(n-this.x2+r-this.sx2)/2,e.deltaY=(s-this.y2+o-this.sy2)/2):(e.deltaX=0,e.deltaY=0),this.twoFingerPressMove.dispatch(e,this.element),this.sx2=r,this.sy2=o):(null!==this.x2?(e.deltaX=n-this.x2,e.deltaY=s-this.y2,r=Math.abs(this.x1-this.x2),o=Math.abs(this.y1-this.y2),(10<r||10<o)&&(this._preventTap=!0)):(e.deltaX=0,e.deltaY=0),this.pressMove.dispatch(e,this.element)),this.touchMove.dispatch(e,this.element),this._cancelLongTap(),this.x2=n,this.y2=s,1<i&&e.preventDefault())}},{key:"end",value:function(e){var t;e.changedTouches&&(this._cancelLongTap(),t=this,e.touches.length<2&&(this.multipointEnd.dispatch(e,this.element),this.sx2=this.sy2=null),this.x2&&30<Math.abs(this.x1-this.x2)||this.y2&&30<Math.abs(this.y1-this.y2)?(e.direction=this._swipeDirection(this.x1,this.x2,this.y1,this.y2),this.swipeTimeout=setTimeout(function(){t.swipe.dispatch(e,t.element)},0)):(this.tapTimeout=setTimeout(function(){t._preventTap||t.tap.dispatch(e,t.element),t.isDoubleTap&&(t.doubleTap.dispatch(e,t.element),t.isDoubleTap=!1)},0),t.isDoubleTap||(t.singleTapTimeout=setTimeout(function(){t.singleTap.dispatch(e,t.element)},250))),this.touchEnd.dispatch(e,this.element),this.preV.x=0,this.preV.y=0,this.zoom=1,this.pinchStartLen=null,this.x1=this.x2=this.y1=this.y2=null)}},{key:"cancelAll",value:function(){this._preventTap=!0,clearTimeout(this.singleTapTimeout),clearTimeout(this.tapTimeout),clearTimeout(this.longTapTimeout),clearTimeout(this.swipeTimeout)}},{key:"cancel",value:function(e){this.cancelAll(),this.touchCancel.dispatch(e,this.element)}},{key:"_cancelLongTap",value:function(){clearTimeout(this.longTapTimeout)}},{key:"_cancelSingleTap",value:function(){clearTimeout(this.singleTapTimeout)}},{key:"_swipeDirection",value:function(e,t,i,n){return Math.abs(e-t)>=Math.abs(i-n)?0<e-t?"Left":"Right":0<i-n?"Up":"Down"}},{key:"on",value:function(e,t){this[e]&&this[e].add(t)}},{key:"off",value:function(e,t){this[e]&&this[e].del(t)}},{key:"destroy",value:function(){return this.singleTapTimeout&&clearTimeout(this.singleTapTimeout),this.tapTimeout&&clearTimeout(this.tapTimeout),this.longTapTimeout&&clearTimeout(this.longTapTimeout),this.swipeTimeout&&clearTimeout(this.swipeTimeout),this.element.removeEventListener("touchstart",this.start),this.element.removeEventListener("touchmove",this.move),this.element.removeEventListener("touchend",this.end),this.element.removeEventListener("touchcancel",this.cancel),this.rotate.del(),this.touchStart.del(),this.multipointStart.del(),this.multipointEnd.del(),this.pinch.del(),this.swipe.del(),this.tap.del(),this.doubleTap.del(),this.longTap.del(),this.singleTap.del(),this.pressMove.del(),this.twoFingerPressMove.del(),this.touchMove.del(),this.touchEnd.del(),this.touchCancel.del(),this.preV=this.pinchStartLen=this.zoom=this.isDoubleTap=this.delta=this.last=this.now=this.tapTimeout=this.singleTapTimeout=this.longTapTimeout=this.swipeTimeout=this.x1=this.x2=this.y1=this.y2=this.preTapPosition=this.rotate=this.touchStart=this.multipointStart=this.multipointEnd=this.pinch=this.swipe=this.tap=this.doubleTap=this.longTap=this.singleTap=this.pressMove=this.touchMove=this.touchEnd=this.touchCancel=this.twoFingerPressMove=null,window.removeEventListener("scroll",this._cancelAllHandler),null}}]),C);function C(e,t){r(this,C),this.element="string"==typeof e?document.querySelector(e):e,this.start=this.start.bind(this),this.move=this.move.bind(this),this.end=this.end.bind(this),this.cancel=this.cancel.bind(this),this.element.addEventListener("touchstart",this.start,!1),this.element.addEventListener("touchmove",this.move,!1),this.element.addEventListener("touchend",this.end,!1),this.element.addEventListener("touchcancel",this.cancel,!1),this.preV={x:null,y:null},this.pinchStartLen=null,this.zoom=1,this.isDoubleTap=!1;e=function(){};this.rotate=E(this.element,t.rotate||e),this.touchStart=E(this.element,t.touchStart||e),this.multipointStart=E(this.element,t.multipointStart||e),this.multipointEnd=E(this.element,t.multipointEnd||e),this.pinch=E(this.element,t.pinch||e),this.swipe=E(this.element,t.swipe||e),this.tap=E(this.element,t.tap||e),this.doubleTap=E(this.element,t.doubleTap||e),this.longTap=E(this.element,t.longTap||e),this.singleTap=E(this.element,t.singleTap||e),this.pressMove=E(this.element,t.pressMove||e),this.twoFingerPressMove=E(this.element,t.twoFingerPressMove||e),this.touchMove=E(this.element,t.touchMove||e),this.touchEnd=E(this.element,t.touchEnd||e),this.touchCancel=E(this.element,t.touchCancel||e),this.translateContainer=this.element,this._cancelAllHandler=this.cancelAll.bind(this),window.addEventListener("scroll",this._cancelAllHandler),this.delta=null,this.last=null,this.now=null,this.tapTimeout=null,this.singleTapTimeout=null,this.longTapTimeout=null,this.swipeTimeout=null,this.x1=this.x2=this.y1=this.y2=null,this.preTapPosition={x:null,y:null}}function V(e){var t=function(){var e,t=document.createElement("fakeelement"),i={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"};for(e in i)if(void 0!==t.style[e])return i[e]}(),i=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,n=z(e,"gslide-media")?e:e.querySelector(".gslide-media"),s=N(n,".ginner-container"),e=e.querySelector(".gslide-description");D(n=769<i?s:n,"greset"),q(n,"translate3d(0, 0, 0)"),I(t,{onElement:n,once:!0,withCallback:function(e,t){$(n,"greset")}}),n.style.opacity="",e&&(e.style.opacity="")}var _=(e(k,[{key:"zoomIn",value:function(){var e,t=this.widowWidth();this.zoomedIn||t<=768||((e=this.img).setAttribute("data-style",e.getAttribute("style")),e.style.maxWidth=e.naturalWidth+"px",e.style.maxHeight=e.naturalHeight+"px",e.naturalWidth>t&&(e=t/2-e.naturalWidth/2,this.setTranslate(this.img.parentNode,e,0)),this.slide.classList.add("zoomed"),this.zoomedIn=!0)}},{key:"zoomOut",value:function(){this.img.parentNode.setAttribute("style",""),this.img.setAttribute("style",this.img.getAttribute("data-style")),this.slide.classList.remove("zoomed"),this.zoomedIn=!1,this.currentX=null,this.currentY=null,this.initialX=null,this.initialY=null,this.xOffset=0,this.yOffset=0,this.onclose&&"function"==typeof this.onclose&&this.onclose()}},{key:"dragStart",value:function(e){e.preventDefault(),this.zoomedIn?("touchstart"===e.type?(this.initialX=e.touches[0].clientX-this.xOffset,this.initialY=e.touches[0].clientY-this.yOffset):(this.initialX=e.clientX-this.xOffset,this.initialY=e.clientY-this.yOffset),e.target===this.img&&(this.active=!0,this.img.classList.add("dragging"))):this.active=!1}},{key:"dragEnd",value:function(e){var t=this;e.preventDefault(),this.initialX=this.currentX,this.initialY=this.currentY,this.active=!1,setTimeout(function(){t.dragging=!1,t.img.isDragging=!1,t.img.classList.remove("dragging")},100)}},{key:"drag",value:function(e){this.active&&(e.preventDefault(),"touchmove"===e.type?(this.currentX=e.touches[0].clientX-this.initialX,this.currentY=e.touches[0].clientY-this.initialY):(this.currentX=e.clientX-this.initialX,this.currentY=e.clientY-this.initialY),this.xOffset=this.currentX,this.yOffset=this.currentY,this.img.isDragging=!0,this.dragging=!0,this.setTranslate(this.img,this.currentX,this.currentY))}},{key:"onMove",value:function(e){var t;this.zoomedIn&&(t=e.clientX-this.img.naturalWidth/2,e=e.clientY-this.img.naturalHeight/2,this.setTranslate(this.img,t,e))}},{key:"setTranslate",value:function(e,t,i){e.style.transform="translate3d("+t+"px, "+i+"px, 0)"}},{key:"widowWidth",value:function(){return window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth}}]),k);function k(e,t){var i=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(r(this,k),this.img=e,this.slide=t,this.onclose=n,this.img.setZoomEvents)return!1;this.active=!1,this.zoomedIn=!1,this.dragging=!1,this.currentX=null,this.currentY=null,this.initialX=null,this.initialY=null,this.xOffset=0,this.yOffset=0,this.img.addEventListener("mousedown",function(e){return i.dragStart(e)},!1),this.img.addEventListener("mouseup",function(e){return i.dragEnd(e)},!1),this.img.addEventListener("mousemove",function(e){return i.drag(e)},!1),this.img.addEventListener("click",function(e){return i.slide.classList.contains("dragging-nav")?(i.zoomOut(),!1):i.zoomedIn?void(i.zoomedIn&&!i.dragging&&i.zoomOut()):i.zoomIn()},!1),this.img.setZoomEvents=!0}var M=(e(A,[{key:"dragStart",value:function(e){var t;this.slide.classList.contains("zoomed")?this.active=!1:("touchstart"===e.type?(this.initialX=e.touches[0].clientX-this.xOffset,this.initialY=e.touches[0].clientY-this.yOffset):(this.initialX=e.clientX-this.xOffset,this.initialY=e.clientY-this.yOffset),t=e.target.nodeName.toLowerCase(),e.target.classList.contains("nodrag")||N(e.target,".nodrag")||-1!==["input","select","textarea","button","a"].indexOf(t)?this.active=!1:(e.preventDefault(),(e.target===this.el||"img"!==t&&N(e.target,".gslide-inline"))&&(this.active=!0,this.el.classList.add("dragging"),this.dragContainer=N(e.target,".ginner-container"))))}},{key:"dragEnd",value:function(e){var t=this;e&&e.preventDefault(),this.initialX=0,this.initialY=0,this.currentX=null,this.currentY=null,this.initialX=null,this.initialY=null,this.xOffset=0,this.yOffset=0,this.active=!1,this.doSlideChange&&(this.instance.preventOutsideClick=!0,"right"==this.doSlideChange&&this.instance.prevSlide(),"left"==this.doSlideChange&&this.instance.nextSlide()),this.doSlideClose&&this.instance.close(),this.toleranceReached||this.setTranslate(this.dragContainer,0,0,!0),setTimeout(function(){t.instance.preventOutsideClick=!1,t.toleranceReached=!1,t.lastDirection=null,t.dragging=!1,t.el.isDragging=!1,t.el.classList.remove("dragging"),t.slide.classList.remove("dragging-nav"),t.dragContainer.style.transform="",t.dragContainer.style.transition=""},100)}},{key:"drag",value:function(e){if(this.active){e.preventDefault(),this.slide.classList.add("dragging-nav"),"touchmove"===e.type?(this.currentX=e.touches[0].clientX-this.initialX,this.currentY=e.touches[0].clientY-this.initialY):(this.currentX=e.clientX-this.initialX,this.currentY=e.clientY-this.initialY),this.xOffset=this.currentX,this.yOffset=this.currentY,this.el.isDragging=!0,this.dragging=!0,this.doSlideChange=!1,this.doSlideClose=!1;var t=Math.abs(this.currentX),i=Math.abs(this.currentY);if(0<t&&t>=Math.abs(this.currentY)&&(!this.lastDirection||"x"==this.lastDirection)){this.yOffset=0,this.lastDirection="x",this.setTranslate(this.dragContainer,this.currentX,0);e=this.shouldChange();if(!this.instance.settings.dragAutoSnap&&e&&(this.doSlideChange=e),this.instance.settings.dragAutoSnap&&e)return this.instance.preventOutsideClick=!0,this.toleranceReached=!0,this.active=!1,this.instance.preventOutsideClick=!0,this.dragEnd(null),"right"==e&&this.instance.prevSlide(),void("left"==e&&this.instance.nextSlide())}0<this.toleranceY&&0<i&&t<=i&&(!this.lastDirection||"y"==this.lastDirection)&&(this.xOffset=0,this.lastDirection="y",this.setTranslate(this.dragContainer,0,this.currentY),i=this.shouldClose(),!this.instance.settings.dragAutoSnap&&i&&(this.doSlideClose=!0),this.instance.settings.dragAutoSnap&&i&&this.instance.close())}}},{key:"shouldChange",value:function(){var e,t=!1;return Math.abs(this.currentX)>=this.toleranceX&&(("left"==(e=0<this.currentX?"right":"left")&&this.slide!==this.slide.parentNode.lastChild||"right"==e&&this.slide!==this.slide.parentNode.firstChild)&&(t=e)),t}},{key:"shouldClose",value:function(){var e=!1;return e=Math.abs(this.currentY)>=this.toleranceY?!0:e}},{key:"setTranslate",value:function(e,t,i){e.style.transition=3<arguments.length&&void 0!==arguments[3]&&arguments[3]?"all .2s ease":"",e.style.transform="translate3d(".concat(t,"px, ").concat(i,"px, 0)")}}]),A);function A(){var t=this,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};r(this,A);var i=e.dragEl,n=e.toleranceX,s=void 0===n?40:n,a=e.toleranceY,n=void 0===a?65:a,a=e.slide,a=void 0===a?null:a,e=e.instance,e=void 0===e?null:e;this.el=i,this.active=!1,this.dragging=!1,this.currentX=null,this.currentY=null,this.initialX=null,this.initialY=null,this.xOffset=0,this.yOffset=0,this.direction=null,this.lastDirection=null,this.toleranceX=s,this.toleranceY=n,this.toleranceReached=!1,this.dragContainer=this.el,this.slide=a,this.instance=e,this.el.addEventListener("mousedown",function(e){return t.dragStart(e)},!1),this.el.addEventListener("mouseup",function(e){return t.dragEnd(e)},!1),this.el.addEventListener("mousemove",function(e){return t.drag(e)},!1)}function P(e){var t=N(e.target,".gslide-media");"enterfullscreen"===e.type&&D(t,"fullscreen"),"exitfullscreen"===e.type&&$(t,"fullscreen")}function L(e,t,i,n){var s,a,r,o=e.querySelector(".gslide-media"),s=(s={url:t.href,callback:n},e=s.url,n=s.allow,a=s.callback,s=s.appendTo,(r=document.createElement("iframe")).className="vimeo-video gvideo",r.src=e,r.style.width="100%",r.style.height="100%",n&&r.setAttribute("allow",n),r.onload=function(){r.onload=null,D(r,"node-ready"),B(a)&&a()},s&&s.appendChild(r),r);o.parentNode.style.maxWidth=t.width,o.parentNode.style.height=t.height,o.appendChild(s)}var O=(e(G,[{key:"sourceType",value:function(e){var t=e;if(null!==(e=e.toLowerCase()).match(/\.(jpeg|jpg|jpe|gif|png|apn|webp|avif|svg)/))return"image";if(e.match(/(youtube\.com|youtube-nocookie\.com)\/watch\?v=([a-zA-Z0-9\-_]+)/)||e.match(/youtu\.be\/([a-zA-Z0-9\-_]+)/)||e.match(/(youtube\.com|youtube-nocookie\.com)\/embed\/([a-zA-Z0-9\-_]+)/))return"video";if(e.match(/vimeo\.com\/([0-9]*)/))return"video";if(null!==e.match(/\.(mp4|ogg|webm|mov)/))return"video";if(null!==e.match(/\.(mp3|wav|wma|aac|ogg)/))return"audio";if(-1<e.indexOf("#")&&""!==t.split("#").pop().trim())return"inline";return-1<e.indexOf("goajax=true")?"ajax":"external"}},{key:"parseConfig",value:function(n,s){var a=this,r=d({descPosition:s.descPosition},this.defaults);if(u(n)&&!F(n)){S(n,"type")||(S(n,"content")&&n.content?n.type="inline":S(n,"href")&&(n.type=this.sourceType(n.href)));var e=d(r,n);return this.setSize(e,s),e}var o,t,i="",l=n.getAttribute("data-glightbox"),e=n.nodeName.toLowerCase();if("a"===e&&(i=n.href),"img"===e&&(i=n.src,r.alt=n.alt),r.href=i,p(r,function(e,t){S(s,t)&&"width"!==t&&(r[t]=s[t]);var i=n.dataset[t];R(i)||(r[t]=a.sanitizeValue(i))}),r.content&&(r.type="inline"),!r.type&&i&&(r.type=this.sourceType(i)),R(l)?(r.title||"a"!=e||(R(i=n.title)||""===i||(r.title=i)),r.title||"img"!=e||(R(e=n.alt)||""===e||(r.title=e))):(o=[],p(r,function(e,t){o.push(";\\s?"+t)}),o=o.join("\\s?:|"),""!==l.trim()&&p(r,function(e,t){var i=l,n=new RegExp("s?"+t+"s?:s?(.*?)("+o+"s?:|$)"),n=i.match(n);n&&n.length&&n[1]&&(n=n[1].trim().replace(/;\s*$/,""),r[t]=a.sanitizeValue(n))})),r.description&&"."===r.description.substring(0,1)){try{t=document.querySelector(r.description).innerHTML}catch(e){if(!(e instanceof DOMException))throw e}t&&(r.description=t)}return r.description||(t=n.querySelector(".glightbox-desc"))&&(r.description=t.innerHTML),this.setSize(r,s,n),this.slideConfig=r}},{key:"setSize",value:function(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,n="video"==e.type?this.checkSize(t.videosWidth):this.checkSize(t.width),t=this.checkSize(t.height);return e.width=S(e,"width")&&""!==e.width?this.checkSize(e.width):n,e.height=S(e,"height")&&""!==e.height?this.checkSize(e.height):t,i&&"image"==e.type&&(e._hasCustomWidth=!!i.dataset.width,e._hasCustomHeight=!!i.dataset.height),e}},{key:"checkSize",value:function(e){return X(e)?"".concat(e,"px"):e}},{key:"sanitizeValue",value:function(e){return"true"!==e&&"false"!==e?e:"true"===e}}]),G);function G(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};r(this,G),this.defaults={href:"",sizes:"",srcset:"",title:"",type:"",videoProvider:"",description:"",alt:"",descPosition:"bottom",effect:"",width:"",height:"",content:!1,zoomable:!0,draggable:!0},u(e)&&(this.defaults=d(this.defaults,e))}var U=(e(Q,[{key:"setContent",value:function(){var t=this,i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,e=1<arguments.length&&void 0!==arguments[1]&&arguments[1];if(z(i,"loaded"))return!1;var n=this.instance.settings,s=this.slideConfig,a=b();B(n.beforeSlideLoad)&&n.beforeSlideLoad({index:this.index,slide:i,player:!1});var r,o=s.type,l=s.descPosition,d=i.querySelector(".gslide-media"),c=i.querySelector(".gslide-title"),u=i.querySelector(".gslide-desc"),p=i.querySelector(".gdesc-inner"),h=e,f="gSlideTitle_"+this.index,m="gSlideDesc_"+this.index;if(B(n.afterSlideLoad)&&(h=function(){B(e)&&e(),n.afterSlideLoad({index:t.index,slide:i,player:t.instance.getSlidePlayerInstance(t.index)})}),""==s.title&&""==s.description?p&&p.parentNode.parentNode.removeChild(p.parentNode):(c&&""!==s.title?(c.id=f,c.innerHTML=s.title):c.parentNode.removeChild(c),u&&""!==s.description?(u.id=m,a&&0<n.moreLength?(s.smallDescription=this.slideShortDesc(s.description,n.moreLength,n.moreText),u.innerHTML=s.smallDescription,this.descriptionEvents(u,s)):u.innerHTML=s.description):u.parentNode.removeChild(u),D(d.parentNode,"desc-".concat(l)),D(p.parentNode,"description-".concat(l))),D(d,"gslide-".concat(o)),D(i,"loaded"),"video"!==o){if("external"!==o)return"inline"===o?(function(e,t,i,n){var s,a=this,r=e.querySelector(".gslide-media"),o=!(!S(t,"href")||!t.href)&&t.href.split("#").pop().trim();if((e=!(!S(t,"content")||!t.content)&&t.content)&&(w(e)&&(s=g('<div class="ginlined-content">'.concat(e,"</div>"))),F(e)&&("none"==e.style.display&&(e.style.display="block"),(l=document.createElement("div")).className="ginlined-content",l.appendChild(e),s=l)),o){var l=document.getElementById(o);if(!l)return!1;l=l.cloneNode(!0);l.style.height=t.height,l.style.maxWidth=t.width,D(l,"ginlined-content"),s=l}if(!s)return console.error("Unable to append inline slide content",t),!1;r.style.height=t.height,r.style.width=t.width,r.appendChild(s),this.events["inlineclose"+o]=I("click",{onElement:r.querySelectorAll(".gtrigger-close"),withCallback:function(e){e.preventDefault(),a.close()}}),B(n)&&n()}.apply(this.instance,[i,s,this.index,h]),void(s.draggable&&new M({dragEl:i.querySelector(".gslide-inline"),toleranceX:n.dragToleranceX,toleranceY:n.dragToleranceY,slide:i,instance:this.instance}))):"image"===o?(u=i,p=s,l=this.index,r=function(){var e=i.querySelector("img");s.draggable&&new M({dragEl:e,toleranceX:n.dragToleranceX,toleranceY:n.dragToleranceY,slide:i,instance:t.instance}),s.zoomable&&e.naturalWidth>e.offsetWidth&&(D(e,"zoomable"),new _(e,i,function(){t.instance.resize()})),B(h)&&h()},d=u.querySelector(".gslide-media"),o=new Image,u="gSlideTitle_"+l,l="gSlideDesc_"+l,o.addEventListener("load",function(){B(r)&&r()},!1),o.src=p.href,""!=p.sizes&&""!=p.srcset&&(o.sizes=p.sizes,o.srcset=p.srcset),o.alt="",R(p.alt)||""===p.alt||(o.alt=p.alt),""!==p.title&&o.setAttribute("aria-labelledby",u),""!==p.description&&o.setAttribute("aria-describedby",l),p.hasOwnProperty("_hasCustomWidth")&&p._hasCustomWidth&&(o.style.width=p.width),p.hasOwnProperty("_hasCustomHeight")&&p._hasCustomHeight&&(o.style.height=p.height),void d.insertBefore(o,d.firstChild)):void(B(h)&&h());L.apply(this,[i,s,this.index,h])}else(function(t,i,n,s){var a=this,e=t.querySelector(".ginner-container"),r="gvideo"+n,o=t.querySelector(".gslide-media"),l=this.getAllPlayers();D(e,"gvideo-container"),o.insertBefore(g('<div class="gvideo-wrapper"></div>'),o.firstChild);var d=t.querySelector(".gvideo-wrapper");y(this.settings.plyr.css,"Plyr");var c=i.href,u=null==i?void 0:i.videoProvider,p=!1;o.style.maxWidth=i.width,y(this.settings.plyr.js,"Plyr",function(){"local"!==(u=!(u=!u&&c.match(/vimeo\.com\/([0-9]*)/)?"vimeo":u)&&(c.match(/(youtube\.com|youtube-nocookie\.com)\/watch\?v=([a-zA-Z0-9\-_]+)/)||c.match(/youtu\.be\/([a-zA-Z0-9\-_]+)/)||c.match(/(youtube\.com|youtube-nocookie\.com)\/embed\/([a-zA-Z0-9\-_]+)/))?"youtube":u)&&u||(u="local",e='<video id="'+r+'" ',e+='style="background:#000; max-width: '.concat(i.width,';" '),e+='preload="metadata" ',e+='x-webkit-airplay="allow" ',e+="playsinline ",e+="controls ",e+='class="gvideo-local">',e+='<source src="'.concat(c,'">'),p=g(e+="</video>"));var e=p||g('<div id="'.concat(r,'" data-plyr-provider="').concat(u,'" data-plyr-embed-id="').concat(c,'"></div>'));D(d,"".concat(u,"-video gvideo")),d.appendChild(e),d.setAttribute("data-id",r),d.setAttribute("data-index",n);e=S(a.settings.plyr,"config")?a.settings.plyr.config:{},e=new Plyr("#"+r,e);e.on("ready",function(e){l[r]=e.detail.plyr,B(s)&&s()}),v(function(){return t.querySelector("iframe")&&"true"==t.querySelector("iframe").dataset.ready},function(){a.resize(t)}),e.on("enterfullscreen",P),e.on("exitfullscreen",P)})}).apply(this.instance,[i,s,this.index,h])}},{key:"slideShortDesc",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:50,i=2<arguments.length&&void 0!==arguments[2]&&arguments[2],n=document.createElement("div");n.innerHTML=e;var s=i;if((e=n.innerText.trim()).length<=t)return e;t=e.substr(0,t-1);return s?(n=null,t+'... <a href="#" class="desc-more">'+i+"</a>"):t}},{key:"descriptionEvents",value:function(e,a){var r=this,e=e.querySelector(".desc-more");if(!e)return!1;I("click",{onElement:e,withCallback:function(e,t){e.preventDefault();var i=document.body,n=N(t,".gslide-desc");if(!n)return!1;n.innerHTML=a.description,D(i,"gdesc-open");var s=I("click",{onElement:[i,N(n,".gslide-description")],withCallback:function(e,t){"a"!==e.target.nodeName.toLowerCase()&&($(i,"gdesc-open"),D(i,"gdesc-closed"),n.innerHTML=a.smallDescription,r.descriptionEvents(n,a),setTimeout(function(){$(i,"gdesc-closed")},400),s.destroy())}})}})}},{key:"create",value:function(){return g(this.instance.settings.slideHTML)}},{key:"getConfig",value:function(){F(this.element)||this.element.hasOwnProperty("draggable")||(this.element.draggable=this.instance.settings.draggable);var e=new O(this.instance.settings.slideExtraAttributes);return this.slideConfig=e.parseConfig(this.element,this.instance.settings),this.slideConfig}}]),Q);function Q(e,t,i){r(this,Q),this.element=e,this.instance=t,this.index=i}var K=b(),Z=null!==b()||void 0!==document.createTouch||"ontouchstart"in window||"onmsgesturechange"in window||navigator.msMaxTouchPoints,J=document.getElementsByTagName("html")[0],ee={selector:".glightbox",elements:null,skin:"clean",theme:"clean",closeButton:!0,startAt:null,autoplayVideos:!0,autofocusVideos:!0,descPosition:"bottom",width:"900px",height:"506px",videosWidth:"960px",beforeSlideChange:null,afterSlideChange:null,beforeSlideLoad:null,afterSlideLoad:null,slideInserted:null,slideRemoved:null,slideExtraAttributes:null,onOpen:null,onClose:null,loop:!1,zoomable:!0,draggable:!0,dragAutoSnap:!1,dragToleranceX:40,dragToleranceY:65,preload:!0,oneSlidePerOpen:!1,touchNavigation:!0,touchFollowAxis:!0,keyboardNavigation:!0,closeOnOutsideClick:!0,plugins:!1,plyr:{css:"https://cdn.plyr.io/3.6.12/plyr.css",js:"https://cdn.plyr.io/3.6.12/plyr.js",config:{ratio:"16:9",fullscreen:{enabled:!0,iosNative:!0},youtube:{noCookie:!0,rel:0,showinfo:0,iv_load_policy:3},vimeo:{byline:!1,portrait:!1,title:!1,transparent:!1}}},openEffect:"zoom",closeEffect:"zoom",slideEffect:"slide",moreText:"See more",moreLength:60,cssEfects:{fade:{in:"fadeIn",out:"fadeOut"},zoom:{in:"zoomIn",out:"zoomOut"},slide:{in:"slideInRight",out:"slideOutLeft"},slideBack:{in:"slideInLeft",out:"slideOutRight"},none:{in:"none",out:"none"}},svg:{close:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" xml:space="preserve"><g><g><path d="M505.943,6.058c-8.077-8.077-21.172-8.077-29.249,0L6.058,476.693c-8.077,8.077-8.077,21.172,0,29.249C10.096,509.982,15.39,512,20.683,512c5.293,0,10.586-2.019,14.625-6.059L505.943,35.306C514.019,27.23,514.019,14.135,505.943,6.058z"/></g></g><g><g><path d="M505.942,476.694L35.306,6.059c-8.076-8.077-21.172-8.077-29.248,0c-8.077,8.076-8.077,21.171,0,29.248l470.636,470.636c4.038,4.039,9.332,6.058,14.625,6.058c5.293,0,10.587-2.019,14.624-6.057C514.018,497.866,514.018,484.771,505.942,476.694z"/></g></g></svg>',next:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 477.175 477.175" xml:space="preserve"> <g><path d="M360.731,229.075l-225.1-225.1c-5.3-5.3-13.8-5.3-19.1,0s-5.3,13.8,0,19.1l215.5,215.5l-215.5,215.5c-5.3,5.3-5.3,13.8,0,19.1c2.6,2.6,6.1,4,9.5,4c3.4,0,6.9-1.3,9.5-4l225.1-225.1C365.931,242.875,365.931,234.275,360.731,229.075z"/></g></svg>',prev:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 477.175 477.175" xml:space="preserve"><g><path d="M145.188,238.575l215.5-215.5c5.3-5.3,5.3-13.8,0-19.1s-13.8-5.3-19.1,0l-225.1,225.1c-5.3,5.3-5.3,13.8,0,19.1l225.1,225c2.6,2.6,6.1,4,9.5,4s6.9-1.3,9.5-4c5.3-5.3,5.3-13.8,0-19.1L145.188,238.575z"/></g></svg>'},slideHTML:'<div class="gslide">\n    <div class="gslide-inner-content">\n        <div class="ginner-container">\n            <div class="gslide-media">\n            </div>\n            <div class="gslide-description">\n                <div class="gdesc-inner">\n                    <h4 class="gslide-title"></h4>\n                    <div class="gslide-desc"></div>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>',lightboxHTML:'<div id="glightbox-body" class="glightbox-container" tabindex="-1" role="dialog" aria-hidden="false">\n    <div class="gloader visible"></div>\n    <div class="goverlay"></div>\n    <div class="gcontainer">\n    <div id="glightbox-slider" class="gslider"></div>\n    <button class="gclose gbtn" aria-label="Close" data-taborder="3">{closeSVG}</button>\n    <button class="gprev gbtn" aria-label="Previous" data-taborder="2">{prevSVG}</button>\n    <button class="gnext gbtn" aria-label="Next" data-taborder="1">{nextSVG}</button>\n</div>\n</div>'},te=(e(ie,[{key:"init",value:function(){var i=this,e=this.getSelector();e&&(this.baseEvents=I("click",{onElement:e,withCallback:function(e,t){e.preventDefault(),i.open(t)}})),this.elements=this.getElements()}},{key:"open",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(0===this.elements.length)return!1;this.activeSlide=null,this.prevActiveSlideIndex=null,this.prevActiveSlide=null;var i=X(t)?t:this.settings.startAt;F(e)&&((O=e.getAttribute("data-gallery"))&&(this.fullElementsList=this.elements,this.elements=this.getGalleryElements(this.elements,O)),R(i)&&(i=this.getElementIndex(e))<0&&(i=0)),X(i)||(i=0),this.build(),H(this.overlay,"none"===this.settings.openEffect?"none":this.settings.cssEfects.fade.in);var n,s,a,r,o,l,d,c,u,p,h,f,m,g,v,y,b,w,S,T,x,E,C,_,k,M,A,P,L,t=document.body,O=window.innerWidth-document.documentElement.clientWidth;0<O&&((e=document.createElement("style")).type="text/css",e.className="gcss-styles",e.innerText=".gscrollbar-fixer {margin-right: ".concat(O,"px}"),document.head.appendChild(e),D(t,"gscrollbar-fixer")),D(t,"glightbox-open"),D(J,"glightbox-open"),K&&(D(document.body,"glightbox-mobile"),this.settings.slideEffect="slide"),this.showSlide(i,!0),1===this.elements.length?(D(this.prevButton,"glightbox-button-hidden"),D(this.nextButton,"glightbox-button-hidden")):($(this.prevButton,"glightbox-button-hidden"),$(this.nextButton,"glightbox-button-hidden")),this.lightboxOpen=!0,this.trigger("open"),B(this.settings.onOpen)&&this.settings.onOpen(),Z&&this.settings.touchNavigation&&((n=this).events.hasOwnProperty("touch")||(i=j(),s=i.width,a=i.height,c=r=!1,y=v=g=m=d=l=o=null,E=x=f=h=!(p=u=1),C={},_={},M=k=T=S=0,i=document.getElementById("glightbox-slider"),P=document.querySelector(".goverlay"),i=new W(i,{touchStart:function(e){r=!0,(z(e.targetTouches[0].target,"ginner-container")||N(e.targetTouches[0].target,".gslide-desc")||"a"==e.targetTouches[0].target.nodeName.toLowerCase())&&(r=!1),(r=N(e.targetTouches[0].target,".gslide-inline")&&!z(e.targetTouches[0].target.parentNode,"gslide-inline")?!1:r)&&(_=e.targetTouches[0],C.pageX=e.targetTouches[0].pageX,C.pageY=e.targetTouches[0].pageY,k=e.targetTouches[0].clientX,M=e.targetTouches[0].clientY,o=n.activeSlide,l=o.querySelector(".gslide-media"),A=o.querySelector(".gslide-inline"),d=null,z(l,"gslide-image")&&(d=l.querySelector("img")),769<(window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth)&&(l=o.querySelector(".ginner-container")),$(P,"greset"),20<e.pageX&&e.pageX<window.innerWidth-20||e.preventDefault())},touchMove:function(e){if(r&&(_=e.targetTouches[0],!h&&!f)){if(A&&A.offsetHeight>a){var t=C.pageX-_.pageX;if(Math.abs(t)<=13)return!1}c=!0;var i,t=e.targetTouches[0].clientX,e=e.targetTouches[0].clientY,t=k-t,e=M-e;if(Math.abs(t)>Math.abs(e)?E=!(x=!1):x=!(E=!1),b=_.pageX-C.pageX,S=100*b/s,w=_.pageY-C.pageY,T=100*w/a,x&&d&&(i=1-Math.abs(w)/a,P.style.opacity=i,n.settings.touchFollowAxis&&(S=0)),E&&(i=1-Math.abs(b)/s,l.style.opacity=i,n.settings.touchFollowAxis&&(T=0)),!d)return q(l,"translate3d(".concat(S,"%, 0, 0)"));q(l,"translate3d(".concat(S,"%, ").concat(T,"%, 0)"))}},touchEnd:function(){if(r){if(c=!1,f||h)return v=m,void(y=g);var e=Math.abs(parseInt(T)),t=Math.abs(parseInt(S));if(!(29<e&&d))return e<29&&t<25?(D(P,"greset"),P.style.opacity=1,V(l)):void 0;n.close()}},multipointEnd:function(){setTimeout(function(){h=!1},50)},multipointStart:function(){h=!0,u=p||1},pinch:function(e){if(!d||c)return!1;h=!0,d.scaleX=d.scaleY=u*e.zoom;e=u*e.zoom;if(f=!0,e<=1)return f=!1,e=1,g=m=v=y=null,void d.setAttribute("style","");d.style.transform="scale3d(".concat(e=4.5<e?4.5:e,", ").concat(e,", 1)"),p=e},pressMove:function(e){var t,i;f&&!h&&(t=_.pageX-C.pageX,i=_.pageY-C.pageY,v&&(t+=v),y&&(i+=y),m=t,g=i,i="translate3d(".concat(t,"px, ").concat(i,"px, 0)"),p&&(i+=" scale3d(".concat(p,", ").concat(p,", 1)")),q(d,i))},swipe:function(e){if(!f)if(h)h=!1;else{if("Left"==e.direction){if(n.index==n.elements.length-1)return V(l);n.nextSlide()}if("Right"==e.direction){if(0==n.index)return V(l);n.prevSlide()}}}}),n.events.touch=i)),this.settings.keyboardNavigation&&((L=this).events.hasOwnProperty("keyboard")||(L.events.keyboard=I("keydown",{onElement:window,withCallback:function(e,t){var i=(e=e||window.event).keyCode;if(9==i){var n=document.querySelector(".gbtn.focused");if(!n){var s=!(!document.activeElement||!document.activeElement.nodeName)&&document.activeElement.nodeName.toLocaleLowerCase();if("input"==s||"textarea"==s||"button"==s)return}e.preventDefault();e=document.querySelectorAll(".gbtn[data-taborder]");if(!e||e.length<=0)return;if(!n){var a=Y();return void(a&&(a.focus(),D(a,"focused")))}a=Y(n.getAttribute("data-taborder"));$(n,"focused"),a&&(a.focus(),D(a,"focused"))}39==i&&L.nextSlide(),37==i&&L.prevSlide(),27==i&&L.close()}})))}},{key:"openAt",value:function(){this.open(null,0<arguments.length&&void 0!==arguments[0]?arguments[0]:0)}},{key:"showSlide",value:function(){var e=this,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,i=1<arguments.length&&void 0!==arguments[1]&&arguments[1];l(this.loader),this.index=parseInt(t);var n=this.slidesContainer.querySelector(".current");n&&$(n,"current"),this.slideAnimateOut();var s,a=this.slidesContainer.querySelectorAll(".gslide")[t];z(a,"loaded")?(this.slideAnimateIn(a,i),c(this.loader)):(l(this.loader),n=this.elements[t],s={index:this.index,slide:a,slideNode:a,slideConfig:n.slideConfig,slideIndex:this.index,trigger:n.node,player:null},this.trigger("slide_before_load",s),n.instance.setContent(a,function(){c(e.loader),e.resize(),e.slideAnimateIn(a,i),e.trigger("slide_after_load",s)})),this.slideDescription=a.querySelector(".gslide-description"),this.slideDescriptionContained=this.slideDescription&&z(this.slideDescription.parentNode,"gslide-media"),this.settings.preload&&(this.preloadSlide(t+1),this.preloadSlide(t-1)),this.updateNavigationClasses(),this.activeSlide=a}},{key:"preloadSlide",value:function(e){var t=this;if(e<0||e>this.elements.length-1)return!1;if(R(this.elements[e]))return!1;var i=this.slidesContainer.querySelectorAll(".gslide")[e];if(z(i,"loaded"))return!1;var n=this.elements[e],s=n.type,a={index:e,slide:i,slideNode:i,slideConfig:n.slideConfig,slideIndex:e,trigger:n.node,player:null};this.trigger("slide_before_load",a),"video"===s||"external"===s?setTimeout(function(){n.instance.setContent(i,function(){t.trigger("slide_after_load",a)})},200):n.instance.setContent(i,function(){t.trigger("slide_after_load",a)})}},{key:"prevSlide",value:function(){this.goToSlide(this.index-1)}},{key:"nextSlide",value:function(){this.goToSlide(this.index+1)}},{key:"goToSlide",value:function(){var e=0<arguments.length&&void 0!==arguments[0]&&arguments[0];if(this.prevActiveSlide=this.activeSlide,this.prevActiveSlideIndex=this.index,!this.loop()&&(e<0||e>this.elements.length-1))return!1;e<0?e=this.elements.length-1:e>=this.elements.length&&(e=0),this.showSlide(e)}},{key:"insertSlide",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:-1;t<0&&(t=this.elements.length);var i=new U(e,this,t),n=i.getConfig(),s=d({},n),a=i.create(),r=this.elements.length-1;s.index=t,s.node=!1,s.instance=i,s.slideConfig=n,this.elements.splice(t,0,s);e=null,i=null;this.slidesContainer&&(r<t?this.slidesContainer.appendChild(a):(r=this.slidesContainer.querySelectorAll(".gslide")[t],this.slidesContainer.insertBefore(a,r)),(this.settings.preload&&0==this.index&&0==t||this.index-1==t||this.index+1==t)&&this.preloadSlide(t),0===this.index&&0===t&&(this.index=1),this.updateNavigationClasses(),e=this.slidesContainer.querySelectorAll(".gslide")[t],i=this.getSlidePlayerInstance(t),s.slideNode=e),this.trigger("slide_inserted",{index:t,slide:e,slideNode:e,slideConfig:n,slideIndex:t,trigger:null,player:i}),B(this.settings.slideInserted)&&this.settings.slideInserted({index:t,slide:e,player:i})}},{key:"removeSlide",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:-1;if(e<0||e>this.elements.length-1)return!1;var t=this.slidesContainer&&this.slidesContainer.querySelectorAll(".gslide")[e];t&&(this.getActiveSlideIndex()==e&&(e==this.elements.length-1?this.prevSlide():this.nextSlide()),t.parentNode.removeChild(t)),this.elements.splice(e,1),this.trigger("slide_removed",e),B(this.settings.slideRemoved)&&this.settings.slideRemoved(e)}},{key:"slideAnimateIn",value:function(e,t){var i=this,n=e.querySelector(".gslide-media"),s=e.querySelector(".gslide-description"),a={index:this.prevActiveSlideIndex,slide:this.prevActiveSlide,slideNode:this.prevActiveSlide,slideIndex:this.prevActiveSlide,slideConfig:R(this.prevActiveSlideIndex)?null:this.elements[this.prevActiveSlideIndex].slideConfig,trigger:R(this.prevActiveSlideIndex)?null:this.elements[this.prevActiveSlideIndex].node,player:this.getSlidePlayerInstance(this.prevActiveSlideIndex)},r={index:this.index,slide:this.activeSlide,slideNode:this.activeSlide,slideConfig:this.elements[this.index].slideConfig,slideIndex:this.index,trigger:this.elements[this.index].node,player:this.getSlidePlayerInstance(this.index)};0<n.offsetWidth&&s&&(c(s),s.style.display=""),$(e,this.effectsClasses),t?H(e,this.settings.cssEfects[this.settings.openEffect].in,function(){i.settings.autoplayVideos&&i.slidePlayerPlay(e),i.trigger("slide_changed",{prev:a,current:r}),B(i.settings.afterSlideChange)&&i.settings.afterSlideChange.apply(i,[a,r])}):(t="none"!==(t=this.settings.slideEffect)?this.settings.cssEfects[t].in:t,this.prevActiveSlideIndex>this.index&&"slide"==this.settings.slideEffect&&(t=this.settings.cssEfects.slideBack.in),H(e,t,function(){i.settings.autoplayVideos&&i.slidePlayerPlay(e),i.trigger("slide_changed",{prev:a,current:r}),B(i.settings.afterSlideChange)&&i.settings.afterSlideChange.apply(i,[a,r])})),setTimeout(function(){i.resize(e)},100),D(e,"current")}},{key:"slideAnimateOut",value:function(){if(!this.prevActiveSlide)return!1;var n=this.prevActiveSlide;$(n,this.effectsClasses),D(n,"prev");var e=this.settings.slideEffect,e="none"!==e?this.settings.cssEfects[e].out:e;this.slidePlayerPause(n),this.trigger("slide_before_change",{prev:{index:this.prevActiveSlideIndex,slide:this.prevActiveSlide,slideNode:this.prevActiveSlide,slideIndex:this.prevActiveSlideIndex,slideConfig:R(this.prevActiveSlideIndex)?null:this.elements[this.prevActiveSlideIndex].slideConfig,trigger:R(this.prevActiveSlideIndex)?null:this.elements[this.prevActiveSlideIndex].node,player:this.getSlidePlayerInstance(this.prevActiveSlideIndex)},current:{index:this.index,slide:this.activeSlide,slideNode:this.activeSlide,slideIndex:this.index,slideConfig:this.elements[this.index].slideConfig,trigger:this.elements[this.index].node,player:this.getSlidePlayerInstance(this.index)}}),B(this.settings.beforeSlideChange)&&this.settings.beforeSlideChange.apply(this,[{index:this.prevActiveSlideIndex,slide:this.prevActiveSlide,player:this.getSlidePlayerInstance(this.prevActiveSlideIndex)},{index:this.index,slide:this.activeSlide,player:this.getSlidePlayerInstance(this.index)}]),this.prevActiveSlideIndex>this.index&&"slide"==this.settings.slideEffect&&(e=this.settings.cssEfects.slideBack.out),H(n,e,function(){var e=n.querySelector(".ginner-container"),t=n.querySelector(".gslide-media"),i=n.querySelector(".gslide-description");e.style.transform="",t.style.transform="",$(t,"greset"),t.style.opacity="",i&&(i.style.opacity=""),$(n,"prev")})}},{key:"getAllPlayers",value:function(){return this.videoPlayers}},{key:"getSlidePlayerInstance",value:function(e){var t="gvideo"+e,e=this.getAllPlayers();return!(!S(e,t)||!e[t])&&e[t]}},{key:"stopSlideVideo",value:function(e){var t;!F(e)||(t=e.querySelector(".gvideo-wrapper"))&&(e=t.getAttribute("data-index")),console.log("stopSlideVideo is deprecated, use slidePlayerPause");e=this.getSlidePlayerInstance(e);e&&e.playing&&e.pause()}},{key:"slidePlayerPause",value:function(e){var t;!F(e)||(t=e.querySelector(".gvideo-wrapper"))&&(e=t.getAttribute("data-index"));e=this.getSlidePlayerInstance(e);e&&e.playing&&e.pause()}},{key:"playSlideVideo",value:function(e){var t;!F(e)||(t=e.querySelector(".gvideo-wrapper"))&&(e=t.getAttribute("data-index")),console.log("playSlideVideo is deprecated, use slidePlayerPlay");e=this.getSlidePlayerInstance(e);e&&!e.playing&&e.play()}},{key:"slidePlayerPlay",value:function(e){var t;(!K||null!==(t=this.settings.plyr.config)&&void 0!==t&&t.muted)&&(!F(e)||(t=e.querySelector(".gvideo-wrapper"))&&(e=t.getAttribute("data-index")),(e=this.getSlidePlayerInstance(e))&&!e.playing&&(e.play(),this.settings.autofocusVideos&&e.elements.container.focus()))}},{key:"setElements",value:function(e){var s=this;this.settings.elements=!1;var a=[];e&&e.length&&p(e,function(e,t){var i=new U(e,s,t),n=i.getConfig(),e=d({},n);e.slideConfig=n,e.instance=i,e.index=t,a.push(e)}),this.elements=a,this.lightboxOpen&&(this.slidesContainer.innerHTML="",this.elements.length&&(p(this.elements,function(){var e=g(s.settings.slideHTML);s.slidesContainer.appendChild(e)}),this.showSlide(0,!0)))}},{key:"getElementIndex",value:function(i){var n=!1;return p(this.elements,function(e,t){if(S(e,"node")&&e.node==i)return n=t,!0}),n}},{key:"getElements",value:function(){var a=this,r=[];this.elements=this.elements||[],!R(this.settings.elements)&&i(this.settings.elements)&&this.settings.elements.length&&p(this.settings.elements,function(e,t){var i=new U(e,a,t),n=i.getConfig(),e=d({},n);e.node=!1,e.index=t,e.instance=i,e.slideConfig=n,r.push(e)});var e=!1;return(e=this.getSelector()?document.querySelectorAll(this.getSelector()):e)&&p(e,function(e,t){var i=new U(e,a,t),n=i.getConfig(),s=d({},n);s.node=e,s.index=t,s.instance=i,s.slideConfig=n,s.gallery=e.getAttribute("data-gallery"),r.push(s)}),r}},{key:"getGalleryElements",value:function(e,t){return e.filter(function(e){return e.gallery==t})}},{key:"getSelector",value:function(){return!this.settings.elements&&(this.settings.selector&&"data-"==this.settings.selector.substring(0,5)?"*[".concat(this.settings.selector,"]"):this.settings.selector)}},{key:"getActiveSlide",value:function(){return this.slidesContainer.querySelectorAll(".gslide")[this.index]}},{key:"getActiveSlideIndex",value:function(){return this.index}},{key:"getAnimationClasses",value:function(){var e,t,i=[];for(e in this.settings.cssEfects)this.settings.cssEfects.hasOwnProperty(e)&&(t=this.settings.cssEfects[e],i.push("g".concat(t.in)),i.push("g".concat(t.out)));return i.join(" ")}},{key:"build",value:function(){var i=this;if(this.built)return!1;var e=document.body.childNodes,t=[];p(e,function(e){e.parentNode==document.body&&"#"!==e.nodeName.charAt(0)&&e.hasAttribute&&!e.hasAttribute("aria-hidden")&&(t.push(e),e.setAttribute("aria-hidden","true"))});var n=S(this.settings.svg,"next")?this.settings.svg.next:"",s=S(this.settings.svg,"prev")?this.settings.svg.prev:"",a=S(this.settings.svg,"close")?this.settings.svg.close:"",e=this.settings.lightboxHTML;e=g(e=(e=(e=e.replace(/{nextSVG}/g,n)).replace(/{prevSVG}/g,s)).replace(/{closeSVG}/g,a)),document.body.appendChild(e);a=document.getElementById("glightbox-body"),e=(this.modal=a).querySelector(".gclose");this.prevButton=a.querySelector(".gprev"),this.nextButton=a.querySelector(".gnext"),this.overlay=a.querySelector(".goverlay"),this.loader=a.querySelector(".gloader"),this.slidesContainer=document.getElementById("glightbox-slider"),this.bodyHiddenChildElms=t,this.events={},D(this.modal,"glightbox-"+this.settings.skin),this.settings.closeButton&&e&&(this.events.close=I("click",{onElement:e,withCallback:function(e,t){e.preventDefault(),i.close()}})),e&&!this.settings.closeButton&&e.parentNode.removeChild(e),this.nextButton&&(this.events.next=I("click",{onElement:this.nextButton,withCallback:function(e,t){e.preventDefault(),i.nextSlide()}})),this.prevButton&&(this.events.prev=I("click",{onElement:this.prevButton,withCallback:function(e,t){e.preventDefault(),i.prevSlide()}})),this.settings.closeOnOutsideClick&&(this.events.outClose=I("click",{onElement:a,withCallback:function(e,t){i.preventOutsideClick||z(document.body,"glightbox-mobile")||N(e.target,".ginner-container")||N(e.target,".gbtn")||z(e.target,"gnext")||z(e.target,"gprev")||i.close()}})),p(this.elements,function(e,t){i.slidesContainer.appendChild(e.instance.create()),e.slideNode=i.slidesContainer.querySelectorAll(".gslide")[t]}),Z&&D(document.body,"glightbox-touch"),this.events.resize=I("resize",{onElement:window,withCallback:function(){i.resize()}}),this.built=!0}},{key:"resize",value:function(){var e,t,i,n,s,a,r,o,l,d=(d=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null)||this.activeSlide;d&&!z(d,"zoomed")&&(n=j(),e=d.querySelector(".gvideo-wrapper"),t=d.querySelector(".gslide-image"),i=this.slideDescription,r=n.width,o=n.height,(r<=768?D:$)(document.body,"glightbox-mobile"),(e||t)&&(d=!1,i&&(z(i,"description-bottom")||z(i,"description-top"))&&!z(i,"gabsolute")&&(d=!0),t&&(r<=768?t.querySelector("img"):d&&(n=i.offsetHeight,(a=t.querySelector("img")).setAttribute("style","max-height: calc(100vh - ".concat(n,"px)")),i.setAttribute("style","max-width: ".concat(a.offsetWidth,"px;")))),e&&((a=S(this.settings.plyr.config,"ratio")?this.settings.plyr.config.ratio:"")||(s=e.clientWidth,l=e.clientHeight,a="".concat(s/(s=s/l),":").concat(l/s)),l=a.split(":"),s=this.settings.videosWidth,a=this.settings.videosWidth,l=(a=X(s)||-1!==s.indexOf("px")?parseInt(s):-1!==s.indexOf("vw")?r*parseInt(s)/100:-1!==s.indexOf("vh")?o*parseInt(s)/100:-1!==s.indexOf("%")?r*parseInt(s)/100:parseInt(e.clientWidth))/(parseInt(l[0])/parseInt(l[1])),l=Math.floor(l),d&&(o-=i.offsetHeight),r<a||o<l||o<l&&a<r?(a=e.offsetWidth,r=e.offsetHeight,e.parentNode.setAttribute("style","max-width: ".concat((o={width:a*(o=o/r),height:r*o}).width,"px")),d&&i.setAttribute("style","max-width: ".concat(o.width,"px;"))):(e.parentNode.style.maxWidth="".concat(s),d&&i.setAttribute("style","max-width: ".concat(s,";"))))))}},{key:"reload",value:function(){this.init()}},{key:"updateNavigationClasses",value:function(){var e=this.loop();$(this.nextButton,"disabled"),$(this.prevButton,"disabled"),0==this.index&&this.elements.length-1==0?(D(this.prevButton,"disabled"),D(this.nextButton,"disabled")):0!==this.index||e?this.index!==this.elements.length-1||e||D(this.nextButton,"disabled"):D(this.prevButton,"disabled")}},{key:"loop",value:function(){var e=S(this.settings,"loopAtEnd")?this.settings.loopAtEnd:null;return e=S(this.settings,"loop")?this.settings.loop:e}},{key:"close",value:function(){var i=this;if(!this.lightboxOpen){if(this.events){for(var e in this.events)this.events.hasOwnProperty(e)&&this.events[e].destroy();this.events=null}return!1}if(this.closing)return!1;this.closing=!0,this.slidePlayerPause(this.activeSlide),this.fullElementsList&&(this.elements=this.fullElementsList),this.bodyHiddenChildElms.length&&p(this.bodyHiddenChildElms,function(e){e.removeAttribute("aria-hidden")}),D(this.modal,"glightbox-closing"),H(this.overlay,"none"==this.settings.openEffect?"none":this.settings.cssEfects.fade.out),H(this.activeSlide,this.settings.cssEfects[this.settings.closeEffect].out,function(){if(i.activeSlide=null,i.prevActiveSlideIndex=null,i.prevActiveSlide=null,i.built=!1,i.events){for(var e in i.events)i.events.hasOwnProperty(e)&&i.events[e].destroy();i.events=null}var t=document.body;$(J,"glightbox-open"),$(t,"glightbox-open touching gdesc-open glightbox-touch glightbox-mobile gscrollbar-fixer"),i.modal.parentNode.removeChild(i.modal),i.trigger("close"),B(i.settings.onClose)&&i.settings.onClose();t=document.querySelector(".gcss-styles");t&&t.parentNode.removeChild(t),i.lightboxOpen=!1,i.closing=null})}},{key:"destroy",value:function(){this.close(),this.clearAllEvents(),this.baseEvents&&this.baseEvents.destroy()}},{key:"on",value:function(e,t){var i=2<arguments.length&&void 0!==arguments[2]&&arguments[2];if(!e||!B(t))throw new TypeError("Event name and callback must be defined");this.apiEvents.push({evt:e,once:i,callback:t})}},{key:"once",value:function(e,t){this.on(e,t,!0)}},{key:"trigger",value:function(s){var t=this,a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,r=[];p(this.apiEvents,function(e,t){var i=e.evt,n=e.once,e=e.callback;i==s&&(e(a),n&&r.push(t))}),r.length&&p(r,function(e){return t.apiEvents.splice(e,1)})}},{key:"clearAllEvents",value:function(){this.apiEvents.splice(0,this.apiEvents.length)}},{key:"version",value:function(){return"3.1.0"}}]),ie);function ie(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};r(this,ie),this.customOptions=e,this.settings=d(ee,e),this.effectsClasses=this.getAnimationClasses(),this.videoPlayers={},this.apiEvents=[],this.fullElementsList=!1}return function(){var e=new te(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{});return e.init(),e}}),$(document).ready(function(){var e;setupResponsiveMenu(),$(window).on("resize",function(){clearTimeout(e),e=setTimeout(function(){setupResponsiveMenu()},250)})});var $scrollHeight=$("#siteHeader").outerHeight();$(window).on("load resize",function(){window.matchMedia("(min-width: 992px)").matches?($("body").css("padding-top","0"),$(window).scroll(function(){$(this).scrollTop()>$scrollHeight+60?($("#mainNavigation").addClass("fixed-top shrink"),$("body").css("padding-top",$("#mainNavigation").outerHeight()-8+"px")):($("#mainNavigation").removeClass("fixed-top shrink"),$("body").css("padding-top","0"))})):($("body").css("padding-top",$("#siteHeader").outerHeight()+"px"),$("#mainNavigation").removeClass("fixed-top shrink"))}),$(window).on("load resize",function(){window.matchMedia("(max-width: 991.98px)").matches&&($("body").css("padding-top",$("#siteHeader").outerHeight()+"px"),$("#mainNavigation").removeClass("fixed-top shrink"),$(window).scroll(function(){$("body").css("padding-top",$("#siteHeader").outerHeight()+"px"),$("#mainNavigation").removeClass("fixed-top shrink")}))});$scrollHeight=$("#siteHeader").outerHeight();$(window).on("load resize",function(){window.matchMedia("(min-width: 992px)").matches?($("body").css("padding-top","0"),$(window).scroll(function(){$(this).scrollTop()>$scrollHeight+60?($(".blognav.navbar").addClass("fixed-top shrink"),$("body").css("padding-top",$(".blognav.navbar").outerHeight()-8+"px")):($(".blognav.navbar").removeClass("fixed-top shrink"),$("body").css("padding-top","0"))})):($("body").css("padding-top",$("#siteHeader").outerHeight()+"px"),$(".blognav.navbar").removeClass("fixed-top shrink"))}),$(window).on("load resize",function(){window.matchMedia("(max-width: 991px)").matches&&($("body").css("padding-top",$("#siteHeader").outerHeight()+"px"),$(".blognav.navbar").removeClass("fixed-top shrink"),$(window).scroll(function(){$("body").css("padding-top",$("#siteHeader").outerHeight()+"px"),$(".blognav.navbar").removeClass("fixed-top shrink")}))}),$(window).on("load",function(){0!=$(".content-box").length&&($("body").addClass("position-relative"),$("body").attr("data-spy","scroll"),$("body").attr("data-target","#contents"),$("body").attr("data-offset","62"))}),$(document).ready(function(){var e=$(".meganav.navbar").outerHeight();function t(){$(window).off("scroll.contentBox"),window.matchMedia("(min-width: 992px)").matches?0<$('body[data-spy="scroll"]').length&&($("body").scrollspy("refresh"),$("body").scrollspy({offset:e})):window.matchMedia("(max-width: 991.98px)").matches&&0<$('body[data-spy="scroll"]').length&&0<$(".content-box").length&&($("body").scrollspy("refresh"),$("body").scrollspy({offset:$(".content-box").outerHeight()})),window.matchMedia("(min-width: 992px)").matches?($("#contentsHeader").removeAttr("href data-toggle aria-expanded aria-controls"),$("#contents").addClass("show"),$("#contents").removeClass("collapse"),$(".content-box").removeClass("scroll-fix"),$("#contentArea").css("padding-top","0"),$(".content-box").css({top:"calc(42px + 1.5rem)"}),$("#contentsHeader").removeAttr("aria-expanded"),$(".content-box .card-header > a").removeClass("collapsed"),$(".content-box a.nav-link").off("click.contentBox")):window.matchMedia("(max-width: 991.98px)").matches&&($("#contentsHeader").attr("href","#contents"),$("#contentsHeader").attr("data-toggle","collapse"),$("#contentsHeader").attr("aria-expanded","false"),$("#contentsHeader").attr("aria-controls","contents"),$("#contents").removeClass("show"),$("#contents").addClass("collapse"),$("#contentsHeader").attr("aria-expanded","false"),$(".content-box .card-header > a").addClass("collapsed"),$(".content-box a.nav-link").off("click.contentBox").on("click.contentBox",function(){$("#contentsHeader").attr("aria-expanded","false"),$("#contents").removeClass("show"),$(".content-box .card-header > a").addClass("collapsed")}),$(window).on("scroll.contentBox",function(){var e,t=$(window).scrollTop(),i=$(".scroll-fix-anchor"),n=$("footer");0!==i.length&&0!==n.length?(e=i.offset().top,(i=n.offset().top)-16<t+(n=$(".content-box .card-header").outerHeight())+32?$(".content-box").css({top:-1*(t+n-i+32)}):e-16<t?($("#contentArea").css("padding-top",n+16),$(".content-box").addClass("scroll-fix"),$(".content-box").css({top:"1rem"})):($(".content-box").removeClass("scroll-fix"),$("#contentArea").css("padding-top","0"))):console.warn("Required elements not found: scroll-fix-anchor or footer")}))}$(window).on("load resize",t),0<$(".content-box").length&&t()}),$(window).on("load resize",function(){$(window).width()<973?($("#categoryFilterHeading, #attributeFilterHeading").attr("aria-expanded","false"),$("#categoryFilter, #attributeFilter").removeClass("show")):($("#categoryFilterHeading, #attributeFilterHeading").attr("aria-expanded","true"),$("#categoryFilter, #attributeFilter").addClass("show"))}),$(".filter-toggle").click(function(){$("#sidebar").addClass("slide")}),$(".sidebar-close").click(function(){$("#sidebar").removeClass("slide")}),$(".shop-view-grid").click(function(){$("#shopProductGrid").addClass("grid-view"),$(".shop-view-grid").addClass("active"),$("#shopProductGrid").removeClass("list-view"),$(".shop-view-list").removeClass("active"),$(".product-grid-column").addClass("col-6 col-sm-4"),$(".product-grid-column").removeClass("col-12")}),$(".shop-view-list").click(function(){$("#shopProductGrid").addClass("list-view"),$(".shop-view-list").addClass("active"),$("#shopProductGrid").removeClass("grid-view"),$(".shop-view-grid").removeClass("active"),$(".product-grid-column").removeClass("col-6 col-sm-4"),$(".product-grid-column").addClass("col-12")}),$(window).on("load",function(){$(".accordion .collapse, .tab-content .collapse").on("shown.bs.collapse",function(){$(this).prev().addClass("active")}),$(".accordion .collapse, .tab-content .collapse").on("hidden.bs.collapse",function(){$(this).prev().removeClass("active")})});for(var feedContainers=document.querySelectorAll("[data-rss-url]"),feeds=[].slice.call(feedContainers),i=0;i<feeds.length;i++){var container=feedContainers[i],url=container.getAttribute("data-rss-url"),max=container.getAttribute("data-rss-max")||10;!function(n,i){$.ajax(url,{accepts:{xml:"application/rss+xml"},dataType:"xml",success:function(e){var t,o=n.classList.contains("blog-feed");o&&((t=document.createElement("div")).className="swiper-wrapper",n.appendChild(t));var l=o?n.querySelector(".swiper-wrapper"):n;$(e).find("item").slice(0,i).each(function(){var e=$(this),t="";e.find("category").each(function(){t+=$(this).text()+", "});var i=$.trim(t).slice(0,-1),n=e.find("title").text(),s=e.find("link").text(),a=e.find("pubDate").text(),r=new Date(a),a=Array("January","February","March","April","May","June","July","August","September","October","November","December")[r.getMonth()]+" "+r.getDate()+", "+r.getFullYear(),r=e.find("description").text().match(/<img\b[^>]+?src\s*=\s*['"]?([^\s'"?#>]+)['"]\salt\s*=\s*['"]?([^'">]+)/),e=r&&r[1]?r[1]:"https://placehold.co/800x400?text=No+Image",r=r&&r[2]?r[2]:n,a=(o?'<div class="swiper-slide"><article class="card blog-article-card h-100 rounded-0 bg-transparent border-0" aria-label="Article summary: '.concat(n,'"><img src="').concat(e,'" alt="').concat(r,'" class="card-img-top rounded-0"><div class="card-body pt-3 pb-0 px-0"><div class="h4 mb-2 card-title blog-article-title"><a href="').concat(s,'" target="_blank" class="stretched-link text-reset">'):'<div class="col mb-5"><article class="blog-article-grid-item card h-100" aria-label="Article summary: '.concat(n,'"><img src="').concat(e,'" alt="').concat(r,'" class="card-img-top"><div class="card-body bg-light mx-4 mt-n5"><div class="card-title blog-article-tag">').concat(i,'</div><div class="card-title blog-article-title"><a href="').concat(s,'" target="_blank">')).concat(n,'</a></div><div class="card-text blog-article-date">').concat(a,"</div></div></article></div>");$(l).append(a)}),o&&(n.parentElement.querySelector(".swiper-buttons")||((e=document.createElement("div")).className="swiper-buttons",e.innerHTML='\n              <div class="swiper-button-prev"></div>\n              <div class="swiper-button-next"></div>\n            ',n.parentElement.appendChild(e)),0<n.querySelectorAll(".swiper-slide").length?setTimeout(function(){try{initializeBlogFeedSwiper(n)}catch(e){console.error("Error initializing Swiper:",e)}},100):console.warn("No slides found in blog feed container"))},error:function(e,t,i){console.error("RSS Feed Error:",i),$(n).append('<div class="alert alert-warning">Unable to load blog content. Please try again later.</div>')}})}(container,max)}function initializeBlogFeedSwiper(e){if(!e||!e.querySelector(".swiper-wrapper"))return console.error("Invalid container for Swiper initialization"),null;var t=e.parentElement;if(!t)return console.error("Container has no parent element"),null;var i=t.querySelector(".swiper-buttons .swiper-button-next"),t=t.querySelector(".swiper-buttons .swiper-button-prev");if(!i||!t)return console.error("Navigation buttons not found"),null;try{return new Swiper(e,{slidesPerView:1,spaceBetween:32,loop:!1,observer:!0,observeParents:!0,navigation:{nextEl:i,prevEl:t,hideOnClick:!1,disabledClass:"swiper-button-disabled"},breakpoints:{768:{slidesPerView:2},1232:{slidesPerView:4}},on:{init:function(){var e=this;setTimeout(function(){return equalizeSlideHeights(e)},100),updateNavigationVisibility(this)},resize:function(){var e=this;setTimeout(function(){return equalizeSlideHeights(e)},100),updateNavigationVisibility(this)},slideChange:function(){updateNavigationVisibility(this)}}})}catch(e){return console.error("Error creating Swiper instance:",e),null}}function updateNavigationVisibility(e){var t,i;e&&e.el&&e.el.parentElement&&(t=e.el.parentElement.querySelector(".swiper-buttons .swiper-button-prev"),i=e.el.parentElement.querySelector(".swiper-buttons .swiper-button-next"),t&&i&&(e.isBeginning?(t.style.visibility="hidden",t.style.opacity="0"):(t.style.visibility="visible",t.style.opacity="1"),e.isEnd?(i.style.visibility="hidden",i.style.opacity="0"):(i.style.visibility="visible",i.style.opacity="1")))}function equalizeSlideHeights(e){if(e&&e.el){e.el.classList.add("equal-height-slides");var t=e.el.querySelectorAll(".swiper-slide");if(t&&0!==t.length){t.forEach(function(e){e&&(e.style.height="",(e=e.querySelector(".card, .blog-article-card, .slide-content"))&&(e.style.height=""))}),e.el.offsetHeight;var i=0;t.forEach(function(e){e&&(e=e.offsetHeight,i=Math.max(i,e))}),0<i&&t.forEach(function(e){e&&(e.style.height="".concat(i,"px"),(e=e.querySelector(".card, .blog-article-card, .slide-content"))&&(e.style.height="100%"))});try{e.update()}catch(e){console.error("Error updating swiper:",e)}}}}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _createClass(e,t,i){return t&&_defineProperties(e.prototype,t),i&&_defineProperties(e,i),e}function ownKeys(t,e){var i,n=Object.keys(t);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(t),e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,i)),n}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(i),!0).forEach(function(e){_defineProperty(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArrayLimit(e,t){var i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var n,s,a=[],r=!0,o=!1;try{for(i=i.call(e);!(r=(n=i.next()).done)&&(a.push(n.value),!t||a.length!==t);r=!0);}catch(e){o=!0,s=e}finally{try{r||null==i.return||i.return()}finally{if(o)throw s}}return a}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Map"===(i="Object"===i&&e.constructor?e.constructor.name:i)||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(e,t):void 0}}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function _defineProperty(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"undefined"!=typeof exports?module.exports=e(require("jquery")):e(jQuery)}(function(d){var n,r=window.Slick||{};n=0,(r=function(e,t){var i=this;i.defaults={accessibility:!0,adaptiveHeight:!1,appendArrows:d(e),appendDots:d(e),arrows:!0,asNavFor:null,prevArrow:'<button class="slick-prev" aria-label="Previous" type="button">Previous</button>',nextArrow:'<button class="slick-next" aria-label="Next" type="button">Next</button>',autoplay:!1,autoplaySpeed:3e3,centerMode:!1,centerPadding:"50px",cssEase:"ease",customPaging:function(e,t){return d('<button type="button" />').text(t+1)},dots:!1,dotsClass:"slick-dots",draggable:!0,easing:"linear",edgeFriction:.35,fade:!1,focusOnSelect:!1,focusOnChange:!1,infinite:!0,initialSlide:0,lazyLoad:"ondemand",mobileFirst:!1,pauseOnHover:!0,pauseOnFocus:!0,pauseOnDotsHover:!1,respondTo:"window",responsive:null,rows:1,rtl:!1,slide:"",slidesPerRow:1,slidesToShow:1,slidesToScroll:1,speed:500,swipe:!0,swipeToSlide:!1,touchMove:!0,touchThreshold:5,useCSS:!0,useTransform:!0,variableWidth:!1,vertical:!1,verticalSwiping:!1,waitForAnimate:!0,zIndex:1e3},i.initials={animating:!1,dragging:!1,autoPlayTimer:null,currentDirection:0,currentLeft:null,currentSlide:0,direction:1,$dots:null,listWidth:null,listHeight:null,loadIndex:0,$nextArrow:null,$prevArrow:null,scrolling:!1,slideCount:null,slideWidth:null,$slideTrack:null,$slides:null,sliding:!1,slideOffset:0,swipeLeft:null,swiping:!1,$list:null,touchObject:{},transformsEnabled:!1,unslicked:!1},d.extend(i,i.initials),i.activeBreakpoint=null,i.animType=null,i.animProp=null,i.breakpoints=[],i.breakpointSettings=[],i.cssTransitions=!1,i.focussed=!1,i.interrupted=!1,i.hidden="hidden",i.paused=!0,i.positionProp=null,i.respondTo=null,i.rowCount=1,i.shouldClick=!0,i.$slider=d(e),i.$slidesCache=null,i.transformType=null,i.transitionType=null,i.visibilityChange="visibilitychange",i.windowWidth=0,i.windowTimer=null,e=d(e).data("slick")||{},i.options=d.extend({},i.defaults,t,e),i.currentSlide=i.options.initialSlide,i.originalSettings=i.options,void 0!==document.mozHidden?(i.hidden="mozHidden",i.visibilityChange="mozvisibilitychange"):void 0!==document.webkitHidden&&(i.hidden="webkitHidden",i.visibilityChange="webkitvisibilitychange"),i.autoPlay=d.proxy(i.autoPlay,i),i.autoPlayClear=d.proxy(i.autoPlayClear,i),i.autoPlayIterator=d.proxy(i.autoPlayIterator,i),i.changeSlide=d.proxy(i.changeSlide,i),i.clickHandler=d.proxy(i.clickHandler,i),i.selectHandler=d.proxy(i.selectHandler,i),i.setPosition=d.proxy(i.setPosition,i),i.swipeHandler=d.proxy(i.swipeHandler,i),i.dragHandler=d.proxy(i.dragHandler,i),i.keyHandler=d.proxy(i.keyHandler,i),i.instanceUid=n++,i.htmlExpr=/^(?:\s*(<[\w\W]+>)[^>]*)$/,i.registerBreakpoints(),i.init(!0)}).prototype.activateADA=function(){this.$slideTrack.find(".slick-active").attr({"aria-hidden":"false"}).find("a, input, button, select").attr({tabindex:"0"})},r.prototype.addSlide=r.prototype.slickAdd=function(e,t,i){var n=this;if("boolean"==typeof t)i=t,t=null;else if(t<0||t>=n.slideCount)return!1;n.unload(),"number"==typeof t?0===t&&0===n.$slides.length?d(e).appendTo(n.$slideTrack):i?d(e).insertBefore(n.$slides.eq(t)):d(e).insertAfter(n.$slides.eq(t)):!0===i?d(e).prependTo(n.$slideTrack):d(e).appendTo(n.$slideTrack),n.$slides=n.$slideTrack.children(this.options.slide),n.$slideTrack.children(this.options.slide).detach(),n.$slideTrack.append(n.$slides),n.$slides.each(function(e,t){d(t).attr("data-slick-index",e)}),n.$slidesCache=n.$slides,n.reinit()},r.prototype.animateHeight=function(){var e,t=this;1===t.options.slidesToShow&&!0===t.options.adaptiveHeight&&!1===t.options.vertical&&(e=t.$slides.eq(t.currentSlide).outerHeight(!0),t.$list.animate({height:e},t.options.speed))},r.prototype.animateSlide=function(e,t){var i={},n=this;n.animateHeight(),!0===n.options.rtl&&!1===n.options.vertical&&(e=-e),!1===n.transformsEnabled?!1===n.options.vertical?n.$slideTrack.animate({left:e},n.options.speed,n.options.easing,t):n.$slideTrack.animate({top:e},n.options.speed,n.options.easing,t):!1===n.cssTransitions?(!0===n.options.rtl&&(n.currentLeft=-n.currentLeft),d({animStart:n.currentLeft}).animate({animStart:e},{duration:n.options.speed,easing:n.options.easing,step:function(e){e=Math.ceil(e),!1===n.options.vertical?i[n.animType]="translate("+e+"px, 0px)":i[n.animType]="translate(0px,"+e+"px)",n.$slideTrack.css(i)},complete:function(){t&&t.call()}})):(n.applyTransition(),e=Math.ceil(e),!1===n.options.vertical?i[n.animType]="translate3d("+e+"px, 0px, 0px)":i[n.animType]="translate3d(0px,"+e+"px, 0px)",n.$slideTrack.css(i),t&&setTimeout(function(){n.disableTransition(),t.call()},n.options.speed))},r.prototype.getNavTarget=function(){var e=this.options.asNavFor;return e=e&&null!==e?d(e).not(this.$slider):e},r.prototype.asNavFor=function(t){var e=this.getNavTarget();null!==e&&"object"===_typeof(e)&&e.each(function(){var e=d(this).slick("getSlick");e.unslicked||e.slideHandler(t,!0)})},r.prototype.applyTransition=function(e){var t=this,i={};!1===t.options.fade?i[t.transitionType]=t.transformType+" "+t.options.speed+"ms "+t.options.cssEase:i[t.transitionType]="opacity "+t.options.speed+"ms "+t.options.cssEase,(!1===t.options.fade?t.$slideTrack:t.$slides.eq(e)).css(i)},r.prototype.autoPlay=function(){var e=this;e.autoPlayClear(),e.slideCount>e.options.slidesToShow&&(e.autoPlayTimer=setInterval(e.autoPlayIterator,e.options.autoplaySpeed))},r.prototype.autoPlayClear=function(){this.autoPlayTimer&&clearInterval(this.autoPlayTimer)},r.prototype.autoPlayIterator=function(){var e=this,t=e.currentSlide+e.options.slidesToScroll;e.paused||e.interrupted||e.focussed||(!1===e.options.infinite&&(1===e.direction&&e.currentSlide+1===e.slideCount-1?e.direction=0:0===e.direction&&(t=e.currentSlide-e.options.slidesToScroll,e.currentSlide-1==0&&(e.direction=1))),e.slideHandler(t))},r.prototype.buildArrows=function(){var e=this;!0===e.options.arrows&&(e.$prevArrow=d(e.options.prevArrow).addClass("slick-arrow"),e.$nextArrow=d(e.options.nextArrow).addClass("slick-arrow"),e.slideCount>e.options.slidesToShow?(e.$prevArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"),e.$nextArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"),e.htmlExpr.test(e.options.prevArrow)&&e.$prevArrow.prependTo(e.options.appendArrows),e.htmlExpr.test(e.options.nextArrow)&&e.$nextArrow.appendTo(e.options.appendArrows),!0!==e.options.infinite&&e.$prevArrow.addClass("slick-disabled").attr("aria-disabled","true")):e.$prevArrow.add(e.$nextArrow).addClass("slick-hidden").attr({"aria-disabled":"true",tabindex:"-1"}))},r.prototype.buildDots=function(){var e,t,i=this;if(!0===i.options.dots&&i.slideCount>i.options.slidesToShow){for(i.$slider.addClass("slick-dotted"),t=d("<ul />").addClass(i.options.dotsClass),e=0;e<=i.getDotCount();e+=1)t.append(d("<li />").append(i.options.customPaging.call(this,i,e)));i.$dots=t.appendTo(i.options.appendDots),i.$dots.find("li").first().addClass("slick-active")}},r.prototype.buildOut=function(){var e=this;e.$slides=e.$slider.children(e.options.slide+":not(.slick-cloned)").addClass("slick-slide"),e.slideCount=e.$slides.length,e.$slides.each(function(e,t){d(t).attr("data-slick-index",e).data("originalStyling",d(t).attr("style")||"")}),e.$slider.addClass("slick-slider"),e.$slideTrack=0===e.slideCount?d('<div class="slick-track"/>').appendTo(e.$slider):e.$slides.wrapAll('<div class="slick-track"/>').parent(),e.$list=e.$slideTrack.wrap('<div class="slick-list"/>').parent(),e.$slideTrack.css("opacity",0),!0!==e.options.centerMode&&!0!==e.options.swipeToSlide||(e.options.slidesToScroll=1),d("img[data-lazy]",e.$slider).not("[src]").addClass("slick-loading"),e.setupInfinite(),e.buildArrows(),e.buildDots(),e.updateDots(),e.setSlideClasses("number"==typeof e.currentSlide?e.currentSlide:0),!0===e.options.draggable&&e.$list.addClass("draggable")},r.prototype.buildRows=function(){var e,t,i,n=this,s=document.createDocumentFragment(),a=n.$slider.children();if(0<n.options.rows){for(i=n.options.slidesPerRow*n.options.rows,t=Math.ceil(a.length/i),e=0;e<t;e++){for(var r=document.createElement("div"),o=0;o<n.options.rows;o++){for(var l=document.createElement("div"),d=0;d<n.options.slidesPerRow;d++){var c=e*i+(o*n.options.slidesPerRow+d);a.get(c)&&l.appendChild(a.get(c))}r.appendChild(l)}s.appendChild(r)}n.$slider.empty().append(s),n.$slider.children().children().children().css({width:100/n.options.slidesPerRow+"%",display:"inline-block"})}},r.prototype.checkResponsive=function(e,t){var i,n,s,a=this,r=!1,o=a.$slider.width(),l=window.innerWidth||d(window).width();if("window"===a.respondTo?s=l:"slider"===a.respondTo?s=o:"min"===a.respondTo&&(s=Math.min(l,o)),a.options.responsive&&a.options.responsive.length&&null!==a.options.responsive){for(i in n=null,a.breakpoints)a.breakpoints.hasOwnProperty(i)&&(!1===a.originalSettings.mobileFirst?s<a.breakpoints[i]&&(n=a.breakpoints[i]):s>a.breakpoints[i]&&(n=a.breakpoints[i]));null!==n?null!==a.activeBreakpoint&&n===a.activeBreakpoint&&!t||(a.activeBreakpoint=n,"unslick"===a.breakpointSettings[n]?a.unslick(n):(a.options=d.extend({},a.originalSettings,a.breakpointSettings[n]),!0===e&&(a.currentSlide=a.options.initialSlide),a.refresh(e)),r=n):null!==a.activeBreakpoint&&(a.activeBreakpoint=null,a.options=a.originalSettings,!0===e&&(a.currentSlide=a.options.initialSlide),a.refresh(e),r=n),e||!1===r||a.$slider.trigger("breakpoint",[a,r])}},r.prototype.changeSlide=function(e,t){var i,n=this,s=d(e.currentTarget);switch(s.is("a")&&e.preventDefault(),s.is("li")||(s=s.closest("li")),i=n.slideCount%n.options.slidesToScroll!=0?0:(n.slideCount-n.currentSlide)%n.options.slidesToScroll,e.data.message){case"previous":a=0==i?n.options.slidesToScroll:n.options.slidesToShow-i,n.slideCount>n.options.slidesToShow&&n.slideHandler(n.currentSlide-a,!1,t);break;case"next":a=0==i?n.options.slidesToScroll:i,n.slideCount>n.options.slidesToShow&&n.slideHandler(n.currentSlide+a,!1,t);break;case"index":var a=0===e.data.index?0:e.data.index||s.index()*n.options.slidesToScroll;n.slideHandler(n.checkNavigable(a),!1,t),s.children().trigger("focus");break;default:return}},r.prototype.checkNavigable=function(e){var t=this.getNavigableIndexes(),i=0;if(e>t[t.length-1])e=t[t.length-1];else for(var n in t){if(e<t[n]){e=i;break}i=t[n]}return e},r.prototype.cleanUpEvents=function(){var e=this;e.options.dots&&null!==e.$dots&&(d("li",e.$dots).off("click.slick",e.changeSlide).off("mouseenter.slick",d.proxy(e.interrupt,e,!0)).off("mouseleave.slick",d.proxy(e.interrupt,e,!1)),!0===e.options.accessibility&&e.$dots.off("keydown.slick",e.keyHandler)),e.$slider.off("focus.slick blur.slick"),!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow&&e.$prevArrow.off("click.slick",e.changeSlide),e.$nextArrow&&e.$nextArrow.off("click.slick",e.changeSlide),!0===e.options.accessibility&&(e.$prevArrow&&e.$prevArrow.off("keydown.slick",e.keyHandler),e.$nextArrow&&e.$nextArrow.off("keydown.slick",e.keyHandler))),e.$list.off("touchstart.slick mousedown.slick",e.swipeHandler),e.$list.off("touchmove.slick mousemove.slick",e.swipeHandler),e.$list.off("touchend.slick mouseup.slick",e.swipeHandler),e.$list.off("touchcancel.slick mouseleave.slick",e.swipeHandler),e.$list.off("click.slick",e.clickHandler),d(document).off(e.visibilityChange,e.visibility),e.cleanUpSlideEvents(),!0===e.options.accessibility&&e.$list.off("keydown.slick",e.keyHandler),!0===e.options.focusOnSelect&&d(e.$slideTrack).children().off("click.slick",e.selectHandler),d(window).off("orientationchange.slick.slick-"+e.instanceUid,e.orientationChange),d(window).off("resize.slick.slick-"+e.instanceUid,e.resize),d("[draggable!=true]",e.$slideTrack).off("dragstart",e.preventDefault),d(window).off("load.slick.slick-"+e.instanceUid,e.setPosition)},r.prototype.cleanUpSlideEvents=function(){var e=this;e.$list.off("mouseenter.slick",d.proxy(e.interrupt,e,!0)),e.$list.off("mouseleave.slick",d.proxy(e.interrupt,e,!1))},r.prototype.cleanUpRows=function(){var e;0<this.options.rows&&((e=this.$slides.children().children()).removeAttr("style"),this.$slider.empty().append(e))},r.prototype.clickHandler=function(e){!1===this.shouldClick&&(e.stopImmediatePropagation(),e.stopPropagation(),e.preventDefault())},r.prototype.destroy=function(e){var t=this;t.autoPlayClear(),t.touchObject={},t.cleanUpEvents(),d(".slick-cloned",t.$slider).detach(),t.$dots&&t.$dots.remove(),t.$prevArrow&&t.$prevArrow.length&&(t.$prevArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display",""),t.htmlExpr.test(t.options.prevArrow)&&t.$prevArrow.remove()),t.$nextArrow&&t.$nextArrow.length&&(t.$nextArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display",""),t.htmlExpr.test(t.options.nextArrow)&&t.$nextArrow.remove()),t.$slides&&(t.$slides.removeClass("slick-slide slick-active slick-center slick-visible slick-current").removeAttr("aria-hidden").removeAttr("data-slick-index").each(function(){d(this).attr("style",d(this).data("originalStyling"))}),t.$slideTrack.children(this.options.slide).detach(),t.$slideTrack.detach(),t.$list.detach(),t.$slider.append(t.$slides)),t.cleanUpRows(),t.$slider.removeClass("slick-slider"),t.$slider.removeClass("slick-initialized"),t.$slider.removeClass("slick-dotted"),t.unslicked=!0,e||t.$slider.trigger("destroy",[t])},r.prototype.disableTransition=function(e){var t={};t[this.transitionType]="",(!1===this.options.fade?this.$slideTrack:this.$slides.eq(e)).css(t)},r.prototype.fadeSlide=function(e,t){var i=this;!1===i.cssTransitions?(i.$slides.eq(e).css({zIndex:i.options.zIndex}),i.$slides.eq(e).animate({opacity:1},i.options.speed,i.options.easing,t)):(i.applyTransition(e),i.$slides.eq(e).css({opacity:1,zIndex:i.options.zIndex}),t&&setTimeout(function(){i.disableTransition(e),t.call()},i.options.speed))},r.prototype.fadeSlideOut=function(e){var t=this;!1===t.cssTransitions?t.$slides.eq(e).animate({opacity:0,zIndex:t.options.zIndex-2},t.options.speed,t.options.easing):(t.applyTransition(e),t.$slides.eq(e).css({opacity:0,zIndex:t.options.zIndex-2}))},r.prototype.filterSlides=r.prototype.slickFilter=function(e){var t=this;null!==e&&(t.$slidesCache=t.$slides,t.unload(),t.$slideTrack.children(this.options.slide).detach(),t.$slidesCache.filter(e).appendTo(t.$slideTrack),t.reinit())},r.prototype.focusHandler=function(){var i=this;i.$slider.off("focus.slick blur.slick").on("focus.slick","*",function(e){var t=d(this);setTimeout(function(){i.options.pauseOnFocus&&t.is(":focus")&&(i.focussed=!0,i.autoPlay())},0)}).on("blur.slick","*",function(e){d(this);i.options.pauseOnFocus&&(i.focussed=!1,i.autoPlay())})},r.prototype.getCurrent=r.prototype.slickCurrentSlide=function(){return this.currentSlide},r.prototype.getDotCount=function(){var e=this,t=0,i=0,n=0;if(!0===e.options.infinite)if(e.slideCount<=e.options.slidesToShow)++n;else for(;t<e.slideCount;)++n,t=i+e.options.slidesToScroll,i+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;else if(!0===e.options.centerMode)n=e.slideCount;else if(e.options.asNavFor)for(;t<e.slideCount;)++n,t=i+e.options.slidesToScroll,i+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;else n=1+Math.ceil((e.slideCount-e.options.slidesToShow)/e.options.slidesToScroll);return n-1},r.prototype.getLeft=function(e){var t,i,n=this,s=0;return n.slideOffset=0,t=n.$slides.first().outerHeight(!0),!0===n.options.infinite?(n.slideCount>n.options.slidesToShow&&(n.slideOffset=n.slideWidth*n.options.slidesToShow*-1,i=-1,!0===n.options.vertical&&!0===n.options.centerMode&&(2===n.options.slidesToShow?i=-1.5:1===n.options.slidesToShow&&(i=-2)),s=t*n.options.slidesToShow*i),n.slideCount%n.options.slidesToScroll!=0&&e+n.options.slidesToScroll>n.slideCount&&n.slideCount>n.options.slidesToShow&&(s=e>n.slideCount?(n.slideOffset=(n.options.slidesToShow-(e-n.slideCount))*n.slideWidth*-1,(n.options.slidesToShow-(e-n.slideCount))*t*-1):(n.slideOffset=n.slideCount%n.options.slidesToScroll*n.slideWidth*-1,n.slideCount%n.options.slidesToScroll*t*-1))):e+n.options.slidesToShow>n.slideCount&&(n.slideOffset=(e+n.options.slidesToShow-n.slideCount)*n.slideWidth,s=(e+n.options.slidesToShow-n.slideCount)*t),n.slideCount<=n.options.slidesToShow&&(s=n.slideOffset=0),!0===n.options.centerMode&&n.slideCount<=n.options.slidesToShow?n.slideOffset=n.slideWidth*Math.floor(n.options.slidesToShow)/2-n.slideWidth*n.slideCount/2:!0===n.options.centerMode&&!0===n.options.infinite?n.slideOffset+=n.slideWidth*Math.floor(n.options.slidesToShow/2)-n.slideWidth:!0===n.options.centerMode&&(n.slideOffset=0,n.slideOffset+=n.slideWidth*Math.floor(n.options.slidesToShow/2)),t=!1===n.options.vertical?e*n.slideWidth*-1+n.slideOffset:e*t*-1+s,!0===n.options.variableWidth&&(s=n.slideCount<=n.options.slidesToShow||!1===n.options.infinite?n.$slideTrack.children(".slick-slide").eq(e):n.$slideTrack.children(".slick-slide").eq(e+n.options.slidesToShow),t=!0===n.options.rtl?s[0]?-1*(n.$slideTrack.width()-s[0].offsetLeft-s.width()):0:s[0]?-1*s[0].offsetLeft:0,!0===n.options.centerMode&&(s=n.slideCount<=n.options.slidesToShow||!1===n.options.infinite?n.$slideTrack.children(".slick-slide").eq(e):n.$slideTrack.children(".slick-slide").eq(e+n.options.slidesToShow+1),t=!0===n.options.rtl?s[0]?-1*(n.$slideTrack.width()-s[0].offsetLeft-s.width()):0:s[0]?-1*s[0].offsetLeft:0,t+=(n.$list.width()-s.outerWidth())/2)),t},r.prototype.getOption=r.prototype.slickGetOption=function(e){return this.options[e]},r.prototype.getNavigableIndexes=function(){for(var e=this,t=0,i=0,n=[],s=!1===e.options.infinite?e.slideCount:(t=-1*e.options.slidesToScroll,i=-1*e.options.slidesToScroll,2*e.slideCount);t<s;)n.push(t),t=i+e.options.slidesToScroll,i+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;return n},r.prototype.getSlick=function(){return this},r.prototype.getSlideCount=function(){var s,a=this,e=!0===a.options.centerMode?Math.floor(a.$list.width()/2):0,r=-1*a.swipeLeft+e;return!0===a.options.swipeToSlide?(a.$slideTrack.find(".slick-slide").each(function(e,t){var i=d(t).outerWidth(),n=t.offsetLeft;if(!0!==a.options.centerMode&&(n+=i/2),r<n+i)return s=t,!1}),Math.abs(d(s).attr("data-slick-index")-a.currentSlide)||1):a.options.slidesToScroll},r.prototype.goTo=r.prototype.slickGoTo=function(e,t){this.changeSlide({data:{message:"index",index:parseInt(e)}},t)},r.prototype.init=function(e){var t=this;d(t.$slider).hasClass("slick-initialized")||(d(t.$slider).addClass("slick-initialized"),t.buildRows(),t.buildOut(),t.setProps(),t.startLoad(),t.loadSlider(),t.initializeEvents(),t.updateArrows(),t.updateDots(),t.checkResponsive(!0),t.focusHandler()),e&&t.$slider.trigger("init",[t]),!0===t.options.accessibility&&t.initADA(),t.options.autoplay&&(t.paused=!1,t.autoPlay())},r.prototype.initADA=function(){var i=this,n=Math.ceil(i.slideCount/i.options.slidesToShow),s=i.getNavigableIndexes().filter(function(e){return 0<=e&&e<i.slideCount});i.$slides.add(i.$slideTrack.find(".slick-cloned")).attr({"aria-hidden":"true",tabindex:"-1"}).find("a, input, button, select").attr({tabindex:"-1"}),null!==i.$dots&&(i.$slides.not(i.$slideTrack.find(".slick-cloned")).each(function(e){var t=s.indexOf(e);d(this).attr({role:"tabpanel",id:"slick-slide"+i.instanceUid+e,tabindex:-1}),-1!==t&&(t="slick-slide-control"+i.instanceUid+t,d("#"+t).length&&d(this).attr({"aria-describedby":t}))}),i.$dots.attr("role","tablist").find("li").each(function(e){var t=s[e];d(this).attr({role:"presentation"}),d(this).find("button").first().attr({role:"tab",id:"slick-slide-control"+i.instanceUid+e,"aria-controls":"slick-slide"+i.instanceUid+t,"aria-label":e+1+" of "+n,"aria-selected":null,tabindex:"-1"})}).eq(i.currentSlide).find("button").attr({"aria-selected":"true",tabindex:"0"}).end());for(var e=i.currentSlide,t=e+i.options.slidesToShow;e<t;e++)i.options.focusOnChange?i.$slides.eq(e).attr({tabindex:"0"}):i.$slides.eq(e).removeAttr("tabindex");i.activateADA()},r.prototype.initArrowEvents=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.off("click.slick").on("click.slick",{message:"previous"},e.changeSlide),e.$nextArrow.off("click.slick").on("click.slick",{message:"next"},e.changeSlide),!0===e.options.accessibility&&(e.$prevArrow.on("keydown.slick",e.keyHandler),e.$nextArrow.on("keydown.slick",e.keyHandler)))},r.prototype.initDotEvents=function(){var e=this;!0===e.options.dots&&e.slideCount>e.options.slidesToShow&&(d("li",e.$dots).on("click.slick",{message:"index"},e.changeSlide),!0===e.options.accessibility&&e.$dots.on("keydown.slick",e.keyHandler)),!0===e.options.dots&&!0===e.options.pauseOnDotsHover&&e.slideCount>e.options.slidesToShow&&d("li",e.$dots).on("mouseenter.slick",d.proxy(e.interrupt,e,!0)).on("mouseleave.slick",d.proxy(e.interrupt,e,!1))},r.prototype.initSlideEvents=function(){var e=this;e.options.pauseOnHover&&(e.$list.on("mouseenter.slick",d.proxy(e.interrupt,e,!0)),e.$list.on("mouseleave.slick",d.proxy(e.interrupt,e,!1)))},r.prototype.initializeEvents=function(){var e=this;e.initArrowEvents(),e.initDotEvents(),e.initSlideEvents(),e.$list.on("touchstart.slick mousedown.slick",{action:"start"},e.swipeHandler),e.$list.on("touchmove.slick mousemove.slick",{action:"move"},e.swipeHandler),e.$list.on("touchend.slick mouseup.slick",{action:"end"},e.swipeHandler),e.$list.on("touchcancel.slick mouseleave.slick",{action:"end"},e.swipeHandler),e.$list.on("click.slick",e.clickHandler),d(document).on(e.visibilityChange,d.proxy(e.visibility,e)),!0===e.options.accessibility&&e.$list.on("keydown.slick",e.keyHandler),!0===e.options.focusOnSelect&&d(e.$slideTrack).children().on("click.slick",e.selectHandler),d(window).on("orientationchange.slick.slick-"+e.instanceUid,d.proxy(e.orientationChange,e)),d(window).on("resize.slick.slick-"+e.instanceUid,d.proxy(e.resize,e)),d("[draggable!=true]",e.$slideTrack).on("dragstart",e.preventDefault),d(window).on("load.slick.slick-"+e.instanceUid,e.setPosition),d(e.setPosition)},r.prototype.initUI=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.show(),e.$nextArrow.show()),!0===e.options.dots&&e.slideCount>e.options.slidesToShow&&e.$dots.show()},r.prototype.keyHandler=function(e){var t=this;e.target.tagName.match("TEXTAREA|INPUT|SELECT")||(37===e.keyCode&&!0===t.options.accessibility?t.changeSlide({data:{message:!0===t.options.rtl?"next":"previous"}}):39===e.keyCode&&!0===t.options.accessibility&&t.changeSlide({data:{message:!0===t.options.rtl?"previous":"next"}}))},r.prototype.lazyLoad=function(){var e,t,i,a=this;function n(e){d("img[data-lazy]",e).each(function(){var e=d(this),t=d(this).attr("data-lazy"),i=d(this).attr("data-srcset"),n=d(this).attr("data-sizes")||a.$slider.attr("data-sizes"),s=document.createElement("img");s.onload=function(){e.animate({opacity:0},100,function(){i&&(e.attr("srcset",i),n&&e.attr("sizes",n)),e.attr("src",t).animate({opacity:1},200,function(){e.removeAttr("data-lazy data-srcset data-sizes").removeClass("slick-loading")}),a.$slider.trigger("lazyLoaded",[a,e,t])})},s.onerror=function(){e.removeAttr("data-lazy").removeClass("slick-loading").addClass("slick-lazyload-error"),a.$slider.trigger("lazyLoadError",[a,e,t])},s.src=t})}if(!0===a.options.centerMode?i=!0===a.options.infinite?(t=a.currentSlide+(a.options.slidesToShow/2+1))+a.options.slidesToShow+2:(t=Math.max(0,a.currentSlide-(a.options.slidesToShow/2+1)),a.options.slidesToShow/2+1+2+a.currentSlide):(t=a.options.infinite?a.options.slidesToShow+a.currentSlide:a.currentSlide,i=Math.ceil(t+a.options.slidesToShow),!0===a.options.fade&&(0<t&&t--,i<=a.slideCount&&i++)),e=a.$slider.find(".slick-slide").slice(t,i),"anticipated"===a.options.lazyLoad)for(var s=t-1,r=i,o=a.$slider.find(".slick-slide"),l=0;l<a.options.slidesToScroll;l++)s<0&&(s=a.slideCount-1),e=(e=e.add(o.eq(s))).add(o.eq(r)),s--,r++;n(e),a.slideCount<=a.options.slidesToShow?n(a.$slider.find(".slick-slide")):a.currentSlide>=a.slideCount-a.options.slidesToShow?n(a.$slider.find(".slick-cloned").slice(0,a.options.slidesToShow)):0===a.currentSlide&&n(a.$slider.find(".slick-cloned").slice(-1*a.options.slidesToShow))},r.prototype.loadSlider=function(){var e=this;e.setPosition(),e.$slideTrack.css({opacity:1}),e.$slider.removeClass("slick-loading"),e.initUI(),"progressive"===e.options.lazyLoad&&e.progressiveLazyLoad()},r.prototype.next=r.prototype.slickNext=function(){this.changeSlide({data:{message:"next"}})},r.prototype.orientationChange=function(){this.checkResponsive(),this.setPosition()},r.prototype.pause=r.prototype.slickPause=function(){this.autoPlayClear(),this.paused=!0},r.prototype.play=r.prototype.slickPlay=function(){var e=this;e.autoPlay(),e.options.autoplay=!0,e.paused=!1,e.focussed=!1,e.interrupted=!1},r.prototype.postSlide=function(e){var t=this;t.unslicked||(t.$slider.trigger("afterChange",[t,e]),t.animating=!1,t.slideCount>t.options.slidesToShow&&t.setPosition(),t.swipeLeft=null,t.options.autoplay&&t.autoPlay(),!0===t.options.accessibility&&(t.initADA(),t.options.focusOnChange&&d(t.$slides.get(t.currentSlide)).attr("tabindex",0).focus()))},r.prototype.prev=r.prototype.slickPrev=function(){this.changeSlide({data:{message:"previous"}})},r.prototype.preventDefault=function(e){e.preventDefault()},r.prototype.progressiveLazyLoad=function(e){e=e||1;var t,i,n,s,a=this,r=d("img[data-lazy]",a.$slider);r.length?(t=r.first(),i=t.attr("data-lazy"),n=t.attr("data-srcset"),s=t.attr("data-sizes")||a.$slider.attr("data-sizes"),(r=document.createElement("img")).onload=function(){n&&(t.attr("srcset",n),s&&t.attr("sizes",s)),t.attr("src",i).removeAttr("data-lazy data-srcset data-sizes").removeClass("slick-loading"),!0===a.options.adaptiveHeight&&a.setPosition(),a.$slider.trigger("lazyLoaded",[a,t,i]),a.progressiveLazyLoad()},r.onerror=function(){e<3?setTimeout(function(){a.progressiveLazyLoad(e+1)},500):(t.removeAttr("data-lazy").removeClass("slick-loading").addClass("slick-lazyload-error"),a.$slider.trigger("lazyLoadError",[a,t,i]),a.progressiveLazyLoad())},r.src=i):a.$slider.trigger("allImagesLoaded",[a])},r.prototype.refresh=function(e){var t=this,i=t.slideCount-t.options.slidesToShow;!t.options.infinite&&t.currentSlide>i&&(t.currentSlide=i),t.slideCount<=t.options.slidesToShow&&(t.currentSlide=0),i=t.currentSlide,t.destroy(!0),d.extend(t,t.initials,{currentSlide:i}),t.init(),e||t.changeSlide({data:{message:"index",index:i}},!1)},r.prototype.registerBreakpoints=function(){var e,t,i,n=this,s=n.options.responsive||null;if("array"===d.type(s)&&s.length){for(e in n.respondTo=n.options.respondTo||"window",s)if(i=n.breakpoints.length-1,s.hasOwnProperty(e)){for(t=s[e].breakpoint;0<=i;)n.breakpoints[i]&&n.breakpoints[i]===t&&n.breakpoints.splice(i,1),i--;n.breakpoints.push(t),n.breakpointSettings[t]=s[e].settings}n.breakpoints.sort(function(e,t){return n.options.mobileFirst?e-t:t-e})}},r.prototype.reinit=function(){var e=this;e.$slides=e.$slideTrack.children(e.options.slide).addClass("slick-slide"),e.slideCount=e.$slides.length,e.currentSlide>=e.slideCount&&0!==e.currentSlide&&(e.currentSlide=e.currentSlide-e.options.slidesToScroll),e.slideCount<=e.options.slidesToShow&&(e.currentSlide=0),e.registerBreakpoints(),e.setProps(),e.setupInfinite(),e.buildArrows(),e.updateArrows(),e.initArrowEvents(),e.buildDots(),e.updateDots(),e.initDotEvents(),e.cleanUpSlideEvents(),e.initSlideEvents(),e.checkResponsive(!1,!0),!0===e.options.focusOnSelect&&d(e.$slideTrack).children().on("click.slick",e.selectHandler),e.setSlideClasses("number"==typeof e.currentSlide?e.currentSlide:0),e.setPosition(),e.focusHandler(),e.paused=!e.options.autoplay,e.autoPlay(),e.$slider.trigger("reInit",[e])},r.prototype.resize=function(){var e=this;d(window).width()!==e.windowWidth&&(clearTimeout(e.windowDelay),e.windowDelay=window.setTimeout(function(){e.windowWidth=d(window).width(),e.checkResponsive(),e.unslicked||e.setPosition()},50))},r.prototype.removeSlide=r.prototype.slickRemove=function(e,t,i){var n=this;if(e="boolean"==typeof e?!0===(t=e)?0:n.slideCount-1:!0===t?--e:e,n.slideCount<1||e<0||e>n.slideCount-1)return!1;n.unload(),(!0===i?n.$slideTrack.children():n.$slideTrack.children(this.options.slide).eq(e)).remove(),n.$slides=n.$slideTrack.children(this.options.slide),n.$slideTrack.children(this.options.slide).detach(),n.$slideTrack.append(n.$slides),n.$slidesCache=n.$slides,n.reinit()},r.prototype.setCSS=function(e){var t,i,n=this,s={};!0===n.options.rtl&&(e=-e),t="left"==n.positionProp?Math.ceil(e)+"px":"0px",i="top"==n.positionProp?Math.ceil(e)+"px":"0px",s[n.positionProp]=e,!1===n.transformsEnabled||(!(s={})===n.cssTransitions?s[n.animType]="translate("+t+", "+i+")":s[n.animType]="translate3d("+t+", "+i+", 0px)"),n.$slideTrack.css(s)},r.prototype.setDimensions=function(){var e=this;!1===e.options.vertical?!0===e.options.centerMode&&e.$list.css({padding:"0px "+e.options.centerPadding}):(e.$list.height(e.$slides.first().outerHeight(!0)*e.options.slidesToShow),!0===e.options.centerMode&&e.$list.css({padding:e.options.centerPadding+" 0px"})),e.listWidth=e.$list.width(),e.listHeight=e.$list.height(),!1===e.options.vertical&&!1===e.options.variableWidth?(e.slideWidth=Math.ceil(e.listWidth/e.options.slidesToShow),e.$slideTrack.width(Math.ceil(e.slideWidth*e.$slideTrack.children(".slick-slide").length))):!0===e.options.variableWidth?e.$slideTrack.width(5e3*e.slideCount):(e.slideWidth=Math.ceil(e.listWidth),e.$slideTrack.height(Math.ceil(e.$slides.first().outerHeight(!0)*e.$slideTrack.children(".slick-slide").length)));var t=e.$slides.first().outerWidth(!0)-e.$slides.first().width();!1===e.options.variableWidth&&e.$slideTrack.children(".slick-slide").width(e.slideWidth-t)},r.prototype.setFade=function(){var i,n=this;n.$slides.each(function(e,t){i=n.slideWidth*e*-1,!0===n.options.rtl?d(t).css({position:"relative",right:i,top:0,zIndex:n.options.zIndex-2,opacity:0}):d(t).css({position:"relative",left:i,top:0,zIndex:n.options.zIndex-2,opacity:0})}),n.$slides.eq(n.currentSlide).css({zIndex:n.options.zIndex-1,opacity:1})},r.prototype.setHeight=function(){var e,t=this;1===t.options.slidesToShow&&!0===t.options.adaptiveHeight&&!1===t.options.vertical&&(e=t.$slides.eq(t.currentSlide).outerHeight(!0),t.$list.css("height",e))},r.prototype.setOption=r.prototype.slickSetOption=function(){var e,t,i,n,s,a=this,r=!1;if("object"===d.type(arguments[0])?(i=arguments[0],r=arguments[1],s="multiple"):"string"===d.type(arguments[0])&&(i=arguments[0],n=arguments[1],r=arguments[2],"responsive"===arguments[0]&&"array"===d.type(arguments[1])?s="responsive":void 0!==arguments[1]&&(s="single")),"single"===s)a.options[i]=n;else if("multiple"===s)d.each(i,function(e,t){a.options[e]=t});else if("responsive"===s)for(t in n)if("array"!==d.type(a.options.responsive))a.options.responsive=[n[t]];else{for(e=a.options.responsive.length-1;0<=e;)a.options.responsive[e].breakpoint===n[t].breakpoint&&a.options.responsive.splice(e,1),e--;a.options.responsive.push(n[t])}r&&(a.unload(),a.reinit())},r.prototype.setPosition=function(){var e=this;e.setDimensions(),e.setHeight(),!1===e.options.fade?e.setCSS(e.getLeft(e.currentSlide)):e.setFade(),e.$slider.trigger("setPosition",[e])},r.prototype.setProps=function(){var e=this,t=document.body.style;e.positionProp=!0===e.options.vertical?"top":"left","top"===e.positionProp?e.$slider.addClass("slick-vertical"):e.$slider.removeClass("slick-vertical"),void 0===t.WebkitTransition&&void 0===t.MozTransition&&void 0===t.msTransition||!0===e.options.useCSS&&(e.cssTransitions=!0),e.options.fade&&("number"==typeof e.options.zIndex?e.options.zIndex<3&&(e.options.zIndex=3):e.options.zIndex=e.defaults.zIndex),void 0!==t.OTransform&&(e.animType="OTransform",e.transformType="-o-transform",e.transitionType="OTransition",void 0===t.perspectiveProperty&&void 0===t.webkitPerspective&&(e.animType=!1)),void 0!==t.MozTransform&&(e.animType="MozTransform",e.transformType="-moz-transform",e.transitionType="MozTransition",void 0===t.perspectiveProperty&&void 0===t.MozPerspective&&(e.animType=!1)),void 0!==t.webkitTransform&&(e.animType="webkitTransform",e.transformType="-webkit-transform",e.transitionType="webkitTransition",void 0===t.perspectiveProperty&&void 0===t.webkitPerspective&&(e.animType=!1)),void 0!==t.msTransform&&(e.animType="msTransform",e.transformType="-ms-transform",e.transitionType="msTransition",void 0===t.msTransform&&(e.animType=!1)),void 0!==t.transform&&!1!==e.animType&&(e.animType="transform",e.transformType="transform",e.transitionType="transition"),e.transformsEnabled=e.options.useTransform&&null!==e.animType&&!1!==e.animType},r.prototype.setSlideClasses=function(e){var t,i,n,s=this,a=s.$slider.find(".slick-slide").removeClass("slick-active slick-center slick-current").attr("aria-hidden","true");s.$slides.eq(e).addClass("slick-current"),!0===s.options.centerMode?(i=s.options.slidesToShow%2==0?1:0,n=Math.floor(s.options.slidesToShow/2),!0===s.options.infinite&&(n<=e&&e<=s.slideCount-1-n?s.$slides.slice(e-n+i,e+n+1).addClass("slick-active").attr("aria-hidden","false"):(t=s.options.slidesToShow+e,a.slice(t-n+1+i,t+n+2).addClass("slick-active").attr("aria-hidden","false")),0===e?a.eq(s.options.slidesToShow+s.slideCount+1).addClass("slick-center"):e===s.slideCount-1&&a.eq(s.options.slidesToShow).addClass("slick-center")),s.$slides.eq(e).addClass("slick-center")):0<=e&&e<=s.slideCount-s.options.slidesToShow?s.$slides.slice(e,e+s.options.slidesToShow).addClass("slick-active").attr("aria-hidden","false"):a.length<=s.options.slidesToShow?a.addClass("slick-active").attr("aria-hidden","false"):(n=s.slideCount%s.options.slidesToShow,t=!0===s.options.infinite?s.options.slidesToShow+e:e,(s.options.slidesToShow==s.options.slidesToScroll&&s.slideCount-e<s.options.slidesToShow?a.slice(t-(s.options.slidesToShow-n),t+n):a.slice(t,t+s.options.slidesToShow)).addClass("slick-active").attr("aria-hidden","false")),"ondemand"!==s.options.lazyLoad&&"anticipated"!==s.options.lazyLoad||s.lazyLoad()},r.prototype.setupInfinite=function(){var e,t,i,n=this;if(!0===n.options.fade&&(n.options.centerMode=!1),!0===n.options.infinite&&!1===n.options.fade&&(t=null,n.slideCount>n.options.slidesToShow)){for(i=!0===n.options.centerMode?n.options.slidesToShow+1:n.options.slidesToShow,e=n.slideCount;e>n.slideCount-i;--e)d(n.$slides[t=e-1]).clone(!0).attr("id","").attr("data-slick-index",t-n.slideCount).prependTo(n.$slideTrack).addClass("slick-cloned");for(e=0;e<i+n.slideCount;e+=1)t=e,d(n.$slides[t]).clone(!0).attr("id","").attr("data-slick-index",t+n.slideCount).appendTo(n.$slideTrack).addClass("slick-cloned");n.$slideTrack.find(".slick-cloned").find("[id]").each(function(){d(this).attr("id","")})}},r.prototype.interrupt=function(e){e||this.autoPlay(),this.interrupted=e},r.prototype.selectHandler=function(e){e=d(e.target).is(".slick-slide")?d(e.target):d(e.target).parents(".slick-slide"),e=parseInt(e.attr("data-slick-index"))||0;this.slideCount<=this.options.slidesToShow?this.slideHandler(e,!1,!0):this.slideHandler(e)},r.prototype.slideHandler=function(e,t,i){var n,s,a,r,o=this;if(t=t||!1,!(!0===o.animating&&!0===o.options.waitForAnimate||!0===o.options.fade&&o.currentSlide===e))if(!1===t&&o.asNavFor(e),n=e,a=o.getLeft(n),t=o.getLeft(o.currentSlide),o.currentLeft=null===o.swipeLeft?t:o.swipeLeft,!1===o.options.infinite&&!1===o.options.centerMode&&(e<0||e>o.getDotCount()*o.options.slidesToScroll))!1===o.options.fade&&(n=o.currentSlide,!0!==i&&o.slideCount>o.options.slidesToShow?o.animateSlide(t,function(){o.postSlide(n)}):o.postSlide(n));else if(!1===o.options.infinite&&!0===o.options.centerMode&&(e<0||e>o.slideCount-o.options.slidesToScroll))!1===o.options.fade&&(n=o.currentSlide,!0!==i&&o.slideCount>o.options.slidesToShow?o.animateSlide(t,function(){o.postSlide(n)}):o.postSlide(n));else{if(o.options.autoplay&&clearInterval(o.autoPlayTimer),s=n<0?o.slideCount%o.options.slidesToScroll!=0?o.slideCount-o.slideCount%o.options.slidesToScroll:o.slideCount+n:n>=o.slideCount?o.slideCount%o.options.slidesToScroll!=0?0:n-o.slideCount:n,o.animating=!0,o.$slider.trigger("beforeChange",[o,o.currentSlide,s]),t=o.currentSlide,o.currentSlide=s,o.setSlideClasses(o.currentSlide),o.options.asNavFor&&(r=(r=o.getNavTarget()).slick("getSlick")).slideCount<=r.options.slidesToShow&&r.setSlideClasses(o.currentSlide),o.updateDots(),o.updateArrows(),!0===o.options.fade)return!0!==i?(o.fadeSlideOut(t),o.fadeSlide(s,function(){o.postSlide(s)})):o.postSlide(s),void o.animateHeight();!0!==i&&o.slideCount>o.options.slidesToShow?o.animateSlide(a,function(){o.postSlide(s)}):o.postSlide(s)}},r.prototype.startLoad=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.hide(),e.$nextArrow.hide()),!0===e.options.dots&&e.slideCount>e.options.slidesToShow&&e.$dots.hide(),e.$slider.addClass("slick-loading")},r.prototype.swipeDirection=function(){var e=this,t=e.touchObject.startX-e.touchObject.curX,i=e.touchObject.startY-e.touchObject.curY,t=Math.atan2(i,t),t=Math.round(180*t/Math.PI);return(t=t<0?360-Math.abs(t):t)<=45&&0<=t||t<=360&&315<=t?!1===e.options.rtl?"left":"right":135<=t&&t<=225?!1===e.options.rtl?"right":"left":!0===e.options.verticalSwiping?35<=t&&t<=135?"down":"up":"vertical"},r.prototype.swipeEnd=function(e){var t,i,n=this;if(n.dragging=!1,n.swiping=!1,n.scrolling)return n.scrolling=!1;if(n.interrupted=!1,n.shouldClick=!(10<n.touchObject.swipeLength),void 0===n.touchObject.curX)return!1;if(!0===n.touchObject.edgeHit&&n.$slider.trigger("edge",[n,n.swipeDirection()]),n.touchObject.swipeLength>=n.touchObject.minSwipe){switch(i=n.swipeDirection()){case"left":case"down":t=n.options.swipeToSlide?n.checkNavigable(n.currentSlide+n.getSlideCount()):n.currentSlide+n.getSlideCount(),n.currentDirection=0;break;case"right":case"up":t=n.options.swipeToSlide?n.checkNavigable(n.currentSlide-n.getSlideCount()):n.currentSlide-n.getSlideCount(),n.currentDirection=1}"vertical"!=i&&(n.slideHandler(t),n.touchObject={},n.$slider.trigger("swipe",[n,i]))}else n.touchObject.startX!==n.touchObject.curX&&(n.slideHandler(n.currentSlide),n.touchObject={})},r.prototype.swipeHandler=function(e){var t=this;if(!(!1===t.options.swipe||"ontouchend"in document&&!1===t.options.swipe||!1===t.options.draggable&&-1!==e.type.indexOf("mouse")))switch(t.touchObject.fingerCount=e.originalEvent&&void 0!==e.originalEvent.touches?e.originalEvent.touches.length:1,t.touchObject.minSwipe=t.listWidth/t.options.touchThreshold,!0===t.options.verticalSwiping&&(t.touchObject.minSwipe=t.listHeight/t.options.touchThreshold),e.data.action){case"start":t.swipeStart(e);break;case"move":t.swipeMove(e);break;case"end":t.swipeEnd(e)}},r.prototype.swipeMove=function(e){var t,i,n=this,s=void 0!==e.originalEvent?e.originalEvent.touches:null;return!(!n.dragging||n.scrolling||s&&1!==s.length)&&(t=n.getLeft(n.currentSlide),n.touchObject.curX=void 0!==s?s[0].pageX:e.clientX,n.touchObject.curY=void 0!==s?s[0].pageY:e.clientY,n.touchObject.swipeLength=Math.round(Math.sqrt(Math.pow(n.touchObject.curX-n.touchObject.startX,2))),i=Math.round(Math.sqrt(Math.pow(n.touchObject.curY-n.touchObject.startY,2))),!n.options.verticalSwiping&&!n.swiping&&4<i?!(n.scrolling=!0):(!0===n.options.verticalSwiping&&(n.touchObject.swipeLength=i),s=n.swipeDirection(),void 0!==e.originalEvent&&4<n.touchObject.swipeLength&&(n.swiping=!0,e.preventDefault()),i=(!1===n.options.rtl?1:-1)*(n.touchObject.curX>n.touchObject.startX?1:-1),!0===n.options.verticalSwiping&&(i=n.touchObject.curY>n.touchObject.startY?1:-1),e=n.touchObject.swipeLength,(n.touchObject.edgeHit=!1)===n.options.infinite&&(0===n.currentSlide&&"right"===s||n.currentSlide>=n.getDotCount()&&"left"===s)&&(e=n.touchObject.swipeLength*n.options.edgeFriction,n.touchObject.edgeHit=!0),!1===n.options.vertical?n.swipeLeft=t+e*i:n.swipeLeft=t+e*(n.$list.height()/n.listWidth)*i,!0===n.options.verticalSwiping&&(n.swipeLeft=t+e*i),!0!==n.options.fade&&!1!==n.options.touchMove&&(!0===n.animating?(n.swipeLeft=null,!1):void n.setCSS(n.swipeLeft))))},r.prototype.swipeStart=function(e){var t,i=this;if(i.interrupted=!0,1!==i.touchObject.fingerCount||i.slideCount<=i.options.slidesToShow)return!(i.touchObject={});void 0!==e.originalEvent&&void 0!==e.originalEvent.touches&&(t=e.originalEvent.touches[0]),i.touchObject.startX=i.touchObject.curX=void 0!==t?t.pageX:e.clientX,i.touchObject.startY=i.touchObject.curY=void 0!==t?t.pageY:e.clientY,i.dragging=!0},r.prototype.unfilterSlides=r.prototype.slickUnfilter=function(){var e=this;null!==e.$slidesCache&&(e.unload(),e.$slideTrack.children(this.options.slide).detach(),e.$slidesCache.appendTo(e.$slideTrack),e.reinit())},r.prototype.unload=function(){var e=this;d(".slick-cloned",e.$slider).remove(),e.$dots&&e.$dots.remove(),e.$prevArrow&&e.htmlExpr.test(e.options.prevArrow)&&e.$prevArrow.remove(),e.$nextArrow&&e.htmlExpr.test(e.options.nextArrow)&&e.$nextArrow.remove(),e.$slides.removeClass("slick-slide slick-active slick-visible slick-current").attr("aria-hidden","true").css("width","")},r.prototype.unslick=function(e){this.$slider.trigger("unslick",[this,e]),this.destroy()},r.prototype.updateArrows=function(){var e=this;Math.floor(e.options.slidesToShow/2);!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&!e.options.infinite&&(e.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false"),e.$nextArrow.removeClass("slick-disabled").attr("aria-disabled","false"),0===e.currentSlide?(e.$prevArrow.addClass("slick-disabled").attr("aria-disabled","true"),e.$nextArrow.removeClass("slick-disabled").attr("aria-disabled","false")):(e.currentSlide>=e.slideCount-e.options.slidesToShow&&!1===e.options.centerMode||e.currentSlide>=e.slideCount-1&&!0===e.options.centerMode)&&(e.$nextArrow.addClass("slick-disabled").attr("aria-disabled","true"),e.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false")))},r.prototype.updateDots=function(){var e=this;null!==e.$dots&&(e.$dots.find("li").removeClass("slick-active").end(),e.$dots.find("li").eq(Math.floor(e.currentSlide/e.options.slidesToScroll)).addClass("slick-active"))},r.prototype.visibility=function(){this.options.autoplay&&(document[this.hidden]?this.interrupted=!0:this.interrupted=!1)},d.fn.slick=function(){for(var e,t=this,i=arguments[0],n=Array.prototype.slice.call(arguments,1),s=t.length,a=0;a<s;a++)if("object"==_typeof(i)||void 0===i?t[a].slick=new r(t[a],i):e=t[a].slick[i].apply(t[a].slick,n),void 0!==e)return e;return t}}),$(document).ready(function(){$(".product-img-lg").slick({arrows:!1,slidesToShow:1,slidesToScroll:1,fade:!0,asNavFor:".product-img-nav"}),$(".product-img-nav").slick({slidesToShow:4,slidesToScroll:1,infinate:!1,focusOnSelect:!0,initialSlide:0,asNavFor:".product-img-lg",responsive:[{breakpoint:480,settings:{slidesToShow:3,slidesToScroll:1}}]}),$("#prodCarRelProd .product-row").slick({slidesToShow:3,slidesToScroll:1,centerMode:!1,infinite:!0,appendArrows:"#prodCarRelProd",responsive:[{breakpoint:600,settings:{slidesToShow:1,slidesToScroll:1,centerMode:!0}},{breakpoint:992,settings:{slidesToShow:2,slidesToScroll:1,centerMode:!1}}]}),$("#prodCarAlsoPurchased .product-row").slick({slidesToShow:3,slidesToScroll:1,centerMode:!1,infinite:!0,appendArrows:"#prodCarAlsoPurchased",responsive:[{breakpoint:600,settings:{slidesToShow:1,slidesToScroll:1,centerMode:!0}},{breakpoint:992,settings:{slidesToShow:2,slidesToScroll:1,centerMode:!1}}]}),$("#prodCarCartRecs .product-row").slick({slidesToShow:3,slidesToScroll:1,centerMode:!1,infinite:!0,appendArrows:"#prodCarCartRecs",responsive:[{breakpoint:600,settings:{slidesToShow:1,slidesToScroll:1,centerMode:!0}},{breakpoint:992,settings:{slidesToShow:2,slidesToScroll:1,centerMode:!1}}]}),$("#prodCarOrderConfRecs .product-row").slick({slidesToShow:3,slidesToScroll:1,centerMode:!1,infinite:!0,appendArrows:"#prodCarOrderConfRecs",responsive:[{breakpoint:600,settings:{slidesToShow:1,slidesToScroll:1,centerMode:!0}},{breakpoint:992,settings:{slidesToShow:2,slidesToScroll:1,centerMode:!1}}]}),$("#prodCarHomeRecs").slick({slidesToShow:3,slidesToScroll:1,centerMode:!1,infinite:!0,appendArrows:"#prodCarHomeRecs",responsive:[{breakpoint:600,settings:{slidesToShow:1,slidesToScroll:1,centerMode:!0}},{breakpoint:992,settings:{slidesToShow:2,slidesToScroll:1,centerMode:!1}}]}),$("#artCarNewArt .ii-article-row").slick({slidesToShow:3,slidesToScroll:1,centerMode:!1,infinite:!1,appendArrows:"#artCarNewArt",responsive:[{breakpoint:768,settings:{slidesToShow:1,slidesToScroll:1,centerMode:!0,centerPadding:"0",arrows:!1}},{breakpoint:992,settings:{slidesToShow:2,slidesToScroll:1,centerMode:!1,centerPadding:"50px"}}]}),$("#quoteCarousel").slick({dots:!0,centerMode:!0,slidesToShow:1,centerPadding:"24px",mobileFirst:!0,appendArrows:"#quoteCarousel",responsive:[{breakpoint:768,settings:{centerPadding:"32px"}},{breakpoint:992,settings:{centerPadding:"48px"}},{breakpoint:1232,settings:{centerPadding:"60px"}},{breakpoint:1472,settings:{centerPadding:"90px"}}]})});var Swiper=function(){function n(e){return null!==e&&"object"===_typeof(e)&&"constructor"in e&&e.constructor===Object}function s(t,i){void 0===t&&(t={}),void 0===i&&(i={}),Object.keys(i).forEach(function(e){void 0===t[e]?t[e]=i[e]:n(i[e])&&n(t[e])&&0<Object.keys(i[e]).length&&s(t[e],i[e])})}var t={body:{},addEventListener:function(){},removeEventListener:function(){},activeElement:{blur:function(){},nodeName:""},querySelector:function(){return null},querySelectorAll:function(){return[]},getElementById:function(){return null},createEvent:function(){return{initEvent:function(){}}},createElement:function(){return{children:[],childNodes:[],style:{},setAttribute:function(){},getElementsByTagName:function(){return[]}}},createElementNS:function(){return{}},importNode:function(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function L(){var e="undefined"!=typeof document?document:{};return s(e,t),e}var i,u,e,a={document:t,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState:function(){},pushState:function(){},go:function(){},back:function(){}},CustomEvent:function(){return this},addEventListener:function(){},removeEventListener:function(){},getComputedStyle:function(){return{getPropertyValue:function(){return""}}},Image:function(){},Date:function(){},screen:{},setTimeout:function(){},clearTimeout:function(){},matchMedia:function(){return{}},requestAnimationFrame:function(e){return"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0)},cancelAnimationFrame:function(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function z(){var e="undefined"!=typeof window?window:{};return s(e,a),e}function E(e){return(e=void 0===e?"":e).trim().split(" ").filter(function(e){return!!e.trim()})}function C(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function y(){return Date.now()}function N(e,t){void 0===t&&(t="x");var i,n,s,a,r,o=z(),r=(a=e,e=z(),r=(r=!(r=e.getComputedStyle?e.getComputedStyle(a,null):r)&&a.currentStyle?a.currentStyle:r)||a.style);return o.WebKitCSSMatrix?(6<(n=r.transform||r.webkitTransform).split(",").length&&(n=n.split(", ").map(function(e){return e.replace(",",".")}).join(", ")),s=new o.WebKitCSSMatrix("none"===n?"":n)):i=(s=r.MozTransform||r.OTransform||r.MsTransform||r.msTransform||r.transform||r.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(n=o.WebKitCSSMatrix?s.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),(n="y"===t?o.WebKitCSSMatrix?s.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5]):n)||0}function h(e){return"object"===_typeof(e)&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function p(e){for(var t,i=Object(arguments.length<=0?void 0:e),n=["__proto__","constructor","prototype"],s=1;s<arguments.length;s+=1){var a=s<0||arguments.length<=s?void 0:arguments[s];if(null!=a&&(t=a,!("undefined"!=typeof window&&void 0!==window.HTMLElement?t instanceof HTMLElement:t&&(1===t.nodeType||11===t.nodeType))))for(var r=Object.keys(Object(a)).filter(function(e){return n.indexOf(e)<0}),o=0,l=r.length;o<l;o+=1){var d=r[o],c=Object.getOwnPropertyDescriptor(a,d);void 0!==c&&c.enumerable&&(h(i[d])&&h(a[d])?a[d].__swiper__?i[d]=a[d]:p(i[d],a[d]):!h(i[d])&&h(a[d])?(i[d]={},a[d].__swiper__?i[d]=a[d]:p(i[d],a[d])):i[d]=a[d])}}return i}function K(e,t,i){e.style.setProperty(t,i)}function T(e){var n,s=e.swiper,a=e.targetPosition,r=e.side,o=z(),l=-s.translate,d=null,c=s.params.speed;s.wrapperEl.style.scrollSnapType="none",o.cancelAnimationFrame(s.cssModeFrameID);function u(e,t){return"next"===i&&t<=e||"prev"===i&&e<=t}var i=l<a?"next":"prev";(function e(){n=(new Date).getTime(),null===d&&(d=n);var t=Math.max(Math.min((n-d)/c,1),0),t=.5-Math.cos(t*Math.PI)/2,i=l+t*(a-l);if(u(i,a)&&(i=a),s.wrapperEl.scrollTo(_defineProperty({},r,i)),u(i,a))return s.wrapperEl.style.overflow="hidden",s.wrapperEl.style.scrollSnapType="",setTimeout(function(){s.wrapperEl.style.overflow="",s.wrapperEl.scrollTo(_defineProperty({},r,i))}),void o.cancelAnimationFrame(s.cssModeFrameID);s.cssModeFrameID=o.requestAnimationFrame(e)})()}function o(e){return e.querySelector(".swiper-slide-transform")||e.shadowRoot&&e.shadowRoot.querySelector(".swiper-slide-transform")||e}function Z(e,t){void 0===t&&(t="");var i=z(),n=_toConsumableArray(e.children);return i.HTMLSlotElement&&e instanceof HTMLSlotElement&&n.push.apply(n,_toConsumableArray(e.assignedElements())),t?n.filter(function(e){return e.matches(t)}):n}function d(e,t){var i=z(),n=t.contains(e);return n=!n&&i.HTMLSlotElement&&t instanceof HTMLSlotElement?(n=_toConsumableArray(t.assignedElements()).includes(e))||function(e,t){for(var i=[t];0<i.length;){var n,s=i.shift();if(e===s)return!0;i.push.apply(i,_toConsumableArray(s.children).concat(_toConsumableArray((null===(n=s.shadowRoot)||void 0===n?void 0:n.children)||[]),_toConsumableArray((null===(n=s.assignedElements)||void 0===n?void 0:n.call(s))||[])))}}(e,t):n}function $(e){try{return void console.warn(e)}catch(e){}}function _(e,t){void 0===t&&(t=[]);var i=document.createElement(e);return(e=i.classList).add.apply(e,_toConsumableArray(Array.isArray(t)?t:E(t))),i}function H(e){var t=z(),i=L(),n=e.getBoundingClientRect(),s=i.body,a=e.clientTop||s.clientTop||0,i=e.clientLeft||s.clientLeft||0,s=e===t?t.scrollY:e.scrollTop,e=e===t?t.scrollX:e.scrollLeft;return{top:n.top+s-a,left:n.left+e-i}}function J(e,t){return z().getComputedStyle(e,null).getPropertyValue(t)}function k(e){var t,i=e;if(i){for(t=0;null!==(i=i.previousSibling);)1===i.nodeType&&(t+=1);return t}}function q(e,t){for(var i=[],n=e.parentElement;n;)t&&!n.matches(t)||i.push(n),n=n.parentElement;return i}function b(i,n){n&&i.addEventListener("transitionend",function e(t){t.target===i&&(n.call(i,t),i.removeEventListener("transitionend",e))})}function ee(e,t,i){var n=z();return i?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(n.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(n.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function M(e){return(Array.isArray(e)?e:[e]).filter(function(e){return!!e})}function A(t){return function(e){return 0<Math.abs(e)&&t.browser&&t.browser.need3dFix&&Math.abs(e)%90==0?e+.001:e}}function f(){var e,t;return i||(e=z(),t=L(),i={smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}),i}function m(e){var t,i,n,s,a,r,o,l,d,c;return void 0===e&&(e={}),u||(i=(void 0===(t=e)?{}:t).userAgent,n=f(),s=(c=z()).navigator.platform,a=i||c.navigator.userAgent,r={ios:!1,android:!1},o=c.screen.width,l=c.screen.height,d=a.match(/(Android);?[\s\/]+([\d.]+)?/),e=a.match(/(iPad).*OS\s([\d_]+)/),t=a.match(/(iPod)(.*OS\s([\d_]+))?/),i=!e&&a.match(/(iPhone\sOS|iOS)\s([\d_]+)/),c="Win32"===s,s="MacIntel"===s,!e&&s&&n.touch&&0<=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf("".concat(o,"x").concat(l))&&(e=(e=a.match(/(Version)\/([\d.]+)/))||[0,1,"13_0_0"],s=!1),d&&!c&&(r.os="android",r.android=!0),(e||i||t)&&(r.os="ios",r.ios=!0),u=r),u}function g(){return e=e||function(){var e,t=z(),i=m(),n=!1;function s(){var e=t.navigator.userAgent.toLowerCase();return 0<=e.indexOf("safari")&&e.indexOf("chrome")<0&&e.indexOf("android")<0}!s()||(e=String(t.navigator.userAgent)).includes("Version/")&&(a=(r=_slicedToArray(e.split("Version/")[1].split(" ")[0].split(".").map(function(e){return Number(e)}),2))[0],r=r[1],n=a<16||16===a&&r<2);var a=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(t.navigator.userAgent),r=s();return{isSafari:n||r,needPerspectiveFix:n,need3dFix:r||a&&i.ios,isWebView:a}}()}function v(e,t,i){t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)}function w(e,t,i){t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)}function S(i){if(i&&!i.destroyed&&i.params){var e=i.params.lazyPreloadPrevNext,t=i.slides.length;if(t&&e&&!(e<0)){var e=Math.min(e,t),n="auto"===i.params.slidesPerView?i.slidesPerViewDynamic():Math.ceil(i.params.slidesPerView),s=i.activeIndex;if(i.params.grid&&1<i.params.grid.rows){var a=s,r=[a-e];return r.push.apply(r,_toConsumableArray(Array.from({length:e}).map(function(e,t){return a+n+t}))),i.slides.forEach(function(e,t){r.includes(e.column)&&x(i,t)}),0}var o=s+n-1;if(i.params.rewind||i.params.loop)for(var l=s-e;l<=o+e;l+=1){var d=(l%t+t)%t;(d<s||o<d)&&x(i,d)}else for(var c=Math.max(s-e,0);c<=Math.min(o+e,t-1);c+=1)c!==s&&(o<c||c<s)&&x(i,c)}}}var r=function(e,t){var i,n;!e||e.destroyed||!e.params||(i=t.closest(e.isElement?"swiper-slide":".".concat(e.params.slideClass)))&&(!(n=i.querySelector(".".concat(e.params.lazyPreloaderClass)))&&e.isElement&&(i.shadowRoot?n=i.shadowRoot.querySelector(".".concat(e.params.lazyPreloaderClass)):requestAnimationFrame(function(){i.shadowRoot&&(n=i.shadowRoot.querySelector(".".concat(e.params.lazyPreloaderClass)))&&n.remove()})),n&&n.remove())},x=function(e,t){!e.slides[t]||(t=e.slides[t].querySelector('[loading="lazy"]'))&&t.removeAttribute("loading")};function l(e){var t=e.swiper,i=e.runCallbacks,n=e.direction,s=e.step,a=t.activeIndex,e=t.previousIndex,n=(n=n)||(e<a?"next":a<e?"prev":"reset");t.emit("transition".concat(s)),i&&a!==e&&("reset"!==n?(t.emit("slideChangeTransition".concat(s)),"next"===n?t.emit("slideNextTransition".concat(s)):t.emit("slidePrevTransition".concat(s))):t.emit("slideResetTransition".concat(s)))}function c(n,e){return function e(t){if(!t||t===L()||t===z())return null;var i=(t=t.assignedSlot||t).closest(n);return i||t.getRootNode?i||e(t.getRootNode().host):null}(e=void 0===e?this:e)}function P(e,t,i){var n=z(),s=e.params,e=s.edgeSwipeDetection,s=s.edgeSwipeThreshold;return!e||!(i<=s||i>=n.innerWidth-s)||"prevent"===e&&(t.preventDefault(),1)}function O(){var e,t,i,n,s=this,a=s.params,r=s.el;r&&0===r.offsetWidth||(a.breakpoints&&s.setBreakpoint(),e=s.allowSlideNext,t=s.allowSlidePrev,i=s.snapGrid,n=s.virtual&&s.params.virtual.enabled,s.allowSlideNext=!0,s.allowSlidePrev=!0,s.updateSize(),s.updateSlides(),s.updateSlidesClasses(),r=n&&a.loop,!("auto"===a.slidesPerView||1<a.slidesPerView)||!s.isEnd||s.isBeginning||s.params.centeredSlides||r?s.params.loop&&!n?s.slideToLoop(s.realIndex,0,!1,!0):s.slideTo(s.activeIndex,0,!1,!0):s.slideTo(s.slides.length-1,0,!1,!0),s.autoplay&&s.autoplay.running&&s.autoplay.paused&&(clearTimeout(s.autoplay.resizeTimeout),s.autoplay.resizeTimeout=setTimeout(function(){s.autoplay&&s.autoplay.running&&s.autoplay.paused&&s.autoplay.resume()},500)),s.allowSlidePrev=t,s.allowSlideNext=e,s.params.watchOverflow&&i!==s.snapGrid&&s.checkOverflow())}function I(e,t){var i=L(),n=e.params,s=e.el,a=e.wrapperEl,r=e.device,o=!!n.nested,l="on"===t?"addEventListener":"removeEventListener",t=t;s&&"string"!=typeof s&&(i[l]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:o}),s[l]("touchstart",e.onTouchStart,{passive:!1}),s[l]("pointerdown",e.onTouchStart,{passive:!1}),i[l]("touchmove",e.onTouchMove,{passive:!1,capture:o}),i[l]("pointermove",e.onTouchMove,{passive:!1,capture:o}),i[l]("touchend",e.onTouchEnd,{passive:!0}),i[l]("pointerup",e.onTouchEnd,{passive:!0}),i[l]("pointercancel",e.onTouchEnd,{passive:!0}),i[l]("touchcancel",e.onTouchEnd,{passive:!0}),i[l]("pointerout",e.onTouchEnd,{passive:!0}),i[l]("pointerleave",e.onTouchEnd,{passive:!0}),i[l]("contextmenu",e.onTouchEnd,{passive:!0}),(n.preventClicks||n.preventClicksPropagation)&&s[l]("click",e.onClick,!0),n.cssMode&&a[l]("scroll",e.onScroll),n.updateOnWindowResize?e[t](r.ios||r.android?"resize orientationchange observerUpdate":"resize observerUpdate",O,!0):e[t]("observerUpdate",O,!0),s[l]("load",e.onLoad,{capture:!0}))}function D(e,t){return e.grid&&t.grid&&1<t.grid.rows}var j={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};var B={eventsEmitter:{on:function(e,t,i){var n=this;if(!n.eventsListeners||n.destroyed)return n;if("function"!=typeof t)return n;var s=i?"unshift":"push";return e.split(" ").forEach(function(e){n.eventsListeners[e]||(n.eventsListeners[e]=[]),n.eventsListeners[e][s](t)}),n},once:function(n,s,e){var a=this;return!a.eventsListeners||a.destroyed||"function"!=typeof s?a:(r.__emitterProxy=s,a.on(n,r,e));function r(){a.off(n,r),r.__emitterProxy&&delete r.__emitterProxy;for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];s.apply(a,t)}},onAny:function(e,t){var i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof e)return i;t=t?"unshift":"push";return i.eventsAnyListeners.indexOf(e)<0&&i.eventsAnyListeners[t](e),i},offAny:function(e){var t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsAnyListeners)return t;e=t.eventsAnyListeners.indexOf(e);return 0<=e&&t.eventsAnyListeners.splice(e,1),t},off:function(e,n){var s=this;return!s.eventsListeners||s.destroyed||s.eventsListeners&&e.split(" ").forEach(function(i){void 0===n?s.eventsListeners[i]=[]:s.eventsListeners[i]&&s.eventsListeners[i].forEach(function(e,t){(e===n||e.__emitterProxy&&e.__emitterProxy===n)&&s.eventsListeners[i].splice(t,1)})}),s},emit:function(){var e,i,n,s=this;if(!s.eventsListeners||s.destroyed)return s;if(!s.eventsListeners)return s;for(var t=arguments.length,a=new Array(t),r=0;r<t;r++)a[r]=arguments[r];return n="string"==typeof a[0]||Array.isArray(a[0])?(e=a[0],i=a.slice(1,a.length),s):(e=a[0].events,i=a[0].data,a[0].context||s),i.unshift(n),(Array.isArray(e)?e:e.split(" ")).forEach(function(t){s.eventsAnyListeners&&s.eventsAnyListeners.length&&s.eventsAnyListeners.forEach(function(e){e.apply(n,[t].concat(_toConsumableArray(i)))}),s.eventsListeners&&s.eventsListeners[t]&&s.eventsListeners[t].forEach(function(e){e.apply(n,i)})}),s}},update:{updateSize:function(){var e=this,t=e.el,i=void 0!==e.params.width&&null!==e.params.width?e.params.width:t.clientWidth,n=void 0!==e.params.height&&null!==e.params.height?e.params.height:t.clientHeight;0===i&&e.isHorizontal()||0===n&&e.isVertical()||(i=i-parseInt(J(t,"padding-left")||0,10)-parseInt(J(t,"padding-right")||0,10),n=n-parseInt(J(t,"padding-top")||0,10)-parseInt(J(t,"padding-bottom")||0,10),Number.isNaN(i)&&(i=0),Number.isNaN(n)&&(n=0),Object.assign(e,{width:i,height:n,size:e.isHorizontal()?i:n}))},updateSlides:function(){var i=this;function e(e,t){return parseFloat(e.getPropertyValue(i.getDirectionLabel(t))||0)}var n=i.params,t=i.wrapperEl,s=i.slidesEl,a=i.size,r=i.rtlTranslate,o=i.wrongRTL,l=i.virtual&&n.virtual.enabled,d=(l?i.virtual:i).slides.length,c=Z(s,".".concat(i.params.slideClass,", swiper-slide")),u=(l?i.virtual.slides:c).length,p=[],h=[],f=[],m=n.slidesOffsetBefore;"function"==typeof m&&(m=n.slidesOffsetBefore.call(i));var g=n.slidesOffsetAfter;"function"==typeof g&&(g=n.slidesOffsetAfter.call(i));var v=i.snapGrid.length,s=i.slidesGrid.length,y=n.spaceBetween,b=-m,w=0,S=0;if(void 0!==a){"string"==typeof y&&0<=y.indexOf("%")?y=parseFloat(y.replace("%",""))/100*a:"string"==typeof y&&(y=parseFloat(y)),i.virtualSize=-y,c.forEach(function(e){r?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),n.centeredSlides&&n.cssMode&&(K(t,"--swiper-centered-offset-before",""),K(t,"--swiper-centered-offset-after",""));var T=n.grid&&1<n.grid.rows&&i.grid;T?i.grid.initSlides(c):i.grid&&i.grid.unsetSlides();for(var x,E,C,_,k,M,A,P="auto"===n.slidesPerView&&n.breakpoints&&0<Object.keys(n.breakpoints).filter(function(e){return void 0!==n.breakpoints[e].slidesPerView}).length,L=0;L<u;L+=1){var O,I,D,$,z,N,H,q,j,B,F=void(B=0);c[L]&&(F=c[L]),T&&i.grid.updateSlide(L,F,c),c[L]&&"none"===J(F,"display")||("auto"===n.slidesPerView?(P&&(c[L].style[i.getDirectionLabel("width")]=""),q=getComputedStyle(F),O=F.style.transform,I=F.style.webkitTransform,O&&(F.style.transform="none"),I&&(F.style.webkitTransform="none"),B=n.roundLengths?i.isHorizontal()?ee(F,"width",!0):ee(F,"height",!0):(D=e(q,"width"),$=e(q,"padding-left"),z=e(q,"padding-right"),N=e(q,"margin-left"),H=e(q,"margin-right"),(j=q.getPropertyValue("box-sizing"))&&"border-box"===j?D+N+H:(j=(q=F).clientWidth,D+$+z+N+H+(q.offsetWidth-j))),O&&(F.style.transform=O),I&&(F.style.webkitTransform=I),n.roundLengths&&(B=Math.floor(B))):(B=(a-(n.slidesPerView-1)*y)/n.slidesPerView,n.roundLengths&&(B=Math.floor(B)),c[L]&&(c[L].style[i.getDirectionLabel("width")]="".concat(B,"px"))),c[L]&&(c[L].swiperSlideSize=B),f.push(B),n.centeredSlides?(b=b+B/2+w/2+y,0===w&&0!==L&&(b=b-a/2-y),0===L&&(b=b-a/2-y),Math.abs(b)<.001&&(b=0),n.roundLengths&&(b=Math.floor(b)),S%n.slidesPerGroup==0&&p.push(b),h.push(b)):(n.roundLengths&&(b=Math.floor(b)),(S-Math.min(i.params.slidesPerGroupSkip,S))%i.params.slidesPerGroup==0&&p.push(b),h.push(b),b=b+B+y),i.virtualSize+=B+y,w=B,S+=1)}if(i.virtualSize=Math.max(i.virtualSize,a)+g,r&&o&&("slide"===n.effect||"coverflow"===n.effect)&&(t.style.width="".concat(i.virtualSize+y,"px")),n.setWrapperSize&&(t.style[i.getDirectionLabel("width")]="".concat(i.virtualSize+y,"px")),T&&i.grid.updateWrapperSize(B,p),!n.centeredSlides){for(var R=[],X=0;X<p.length;X+=1){var Y=p[X];n.roundLengths&&(Y=Math.floor(Y)),p[X]<=i.virtualSize-a&&R.push(Y)}p=R,1<Math.floor(i.virtualSize-a)-Math.floor(p[p.length-1])&&p.push(i.virtualSize-a)}if(l&&n.loop){var W=f[0]+y;if(1<n.slidesPerGroup)for(var V=Math.ceil((i.virtual.slidesBefore+i.virtual.slidesAfter)/n.slidesPerGroup),G=W*n.slidesPerGroup,U=0;U<V;U+=1)p.push(p[p.length-1]+G);for(var Q=0;Q<i.virtual.slidesBefore+i.virtual.slidesAfter;Q+=1)1===n.slidesPerGroup&&p.push(p[p.length-1]+W),h.push(h[h.length-1]+W),i.virtualSize+=W}0===p.length&&(p=[0]),0!==y&&(x=i.isHorizontal()&&r?"marginLeft":i.getDirectionLabel("marginRight"),c.filter(function(e,t){return!(n.cssMode&&!n.loop)||t!==c.length-1}).forEach(function(e){e.style[x]="".concat(y,"px")})),n.centeredSlides&&n.centeredSlidesBounds&&(E=0,f.forEach(function(e){E+=e+(y||0)}),C=a<(E-=y)?E-a:0,p=p.map(function(e){return e<=0?-m:C<e?C+g:e})),n.centerInsufficientSlides&&(_=0,f.forEach(function(e){_+=e+(y||0)}),_-=y,o=(n.slidesOffsetBefore||0)+(n.slidesOffsetAfter||0),_+o<a&&(k=(a-_-o)/2,p.forEach(function(e,t){p[t]=e-k}),h.forEach(function(e,t){h[t]=e+k}))),Object.assign(i,{slides:c,snapGrid:p,slidesGrid:h,slidesSizesGrid:f}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds&&(K(t,"--swiper-centered-offset-before","".concat(-p[0],"px")),K(t,"--swiper-centered-offset-after","".concat(i.size/2-f[f.length-1]/2,"px")),M=-i.snapGrid[0],A=-i.slidesGrid[0],i.snapGrid=i.snapGrid.map(function(e){return e+M}),i.slidesGrid=i.slidesGrid.map(function(e){return e+A})),u!==d&&i.emit("slidesLengthChange"),p.length!==v&&(i.params.watchOverflow&&i.checkOverflow(),i.emit("snapGridLengthChange")),h.length!==s&&i.emit("slidesGridLengthChange"),n.watchSlidesProgress&&i.updateSlidesOffset(),i.emit("slidesUpdated"),l||n.cssMode||"slide"!==n.effect&&"fade"!==n.effect||(s="".concat(n.containerModifierClass,"backface-hidden"),l=i.el.classList.contains(s),u<=n.maxBackfaceHiddenSlides?l||i.el.classList.add(s):l&&i.el.classList.remove(s))}},updateAutoHeight:function(e){var t,i,n=this,s=[],a=n.virtual&&n.params.virtual.enabled,r=0;function o(e){return a?n.slides[n.getSlideIndexByData(e)]:n.slides[e]}if("number"==typeof e?n.setTransition(e):!0===e&&n.setTransition(n.params.speed),"auto"!==n.params.slidesPerView&&1<n.params.slidesPerView)if(n.params.centeredSlides)(n.visibleSlides||[]).forEach(function(e){s.push(e)});else for(t=0;t<Math.ceil(n.params.slidesPerView);t+=1){var l=n.activeIndex+t;if(l>n.slides.length&&!a)break;s.push(o(l))}else s.push(o(n.activeIndex));for(t=0;t<s.length;t+=1)void 0!==s[t]&&(r=r<(i=s[t].offsetHeight)?i:r);!r&&0!==r||(n.wrapperEl.style.height="".concat(r,"px"))},updateSlidesOffset:function(){for(var e=this,t=e.slides,i=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0,n=0;n<t.length;n+=1)t[n].swiperSlideOffset=(e.isHorizontal()?t[n].offsetLeft:t[n].offsetTop)-i-e.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);var t=this,i=t.params,n=t.slides,s=t.rtlTranslate,a=t.snapGrid;if(0!==n.length){void 0===n[0].swiperSlideOffset&&t.updateSlidesOffset();var r=s?e:-e;t.visibleSlidesIndexes=[],t.visibleSlides=[];var o=i.spaceBetween;"string"==typeof o&&0<=o.indexOf("%")?o=parseFloat(o.replace("%",""))/100*t.size:"string"==typeof o&&(o=parseFloat(o));for(var l=0;l<n.length;l+=1){var d=n[l],c=d.swiperSlideOffset;i.cssMode&&i.centeredSlides&&(c-=n[0].swiperSlideOffset);var u=(r+(i.centeredSlides?t.minTranslate():0)-c)/(d.swiperSlideSize+o),p=(r-a[0]+(i.centeredSlides?t.minTranslate():0)-c)/(d.swiperSlideSize+o),h=-(r-c),f=h+t.slidesSizesGrid[l],c=0<=h&&h<=t.size-t.slidesSizesGrid[l],f=0<=h&&h<t.size-1||1<f&&f<=t.size||h<=0&&f>=t.size;f&&(t.visibleSlides.push(d),t.visibleSlidesIndexes.push(l)),v(d,f,i.slideVisibleClass),v(d,c,i.slideFullyVisibleClass),d.progress=s?-u:u,d.originalProgress=s?-p:p}}},updateProgress:function(e){var t=this;void 0===e&&(s=t.rtlTranslate?-1:1,e=t&&t.translate&&t.translate*s||0);var i,n,s,a=t.params,r=t.maxTranslate()-t.minTranslate(),o=t.progress,l=t.isBeginning,d=t.isEnd,c=t.progressLoop,u=l,p=d;0==r?d=l=!(o=0):(o=(e-t.minTranslate())/r,l=(i=Math.abs(e-t.minTranslate())<1)||o<=0,d=(n=Math.abs(e-t.maxTranslate())<1)||1<=o,i&&(o=0),n&&(o=1)),a.loop&&(s=t.getSlideIndexByData(0),r=t.getSlideIndexByData(t.slides.length-1),i=t.slidesGrid[s],n=t.slidesGrid[r],s=t.slidesGrid[t.slidesGrid.length-1],1<(c=i<=(r=Math.abs(e))?(r-i)/s:(r+s-n)/s)&&--c),Object.assign(t,{progress:o,progressLoop:c,isBeginning:l,isEnd:d}),(a.watchSlidesProgress||a.centeredSlides&&a.autoHeight)&&t.updateSlidesProgress(e),l&&!u&&t.emit("reachBeginning toEdge"),d&&!p&&t.emit("reachEnd toEdge"),(u&&!l||p&&!d)&&t.emit("fromEdge"),t.emit("progress",o)},updateSlidesClasses:function(){function e(e){return Z(o,".".concat(r.slideClass).concat(e,", swiper-slide").concat(e))[0]}var t,i,n,s=this,a=s.slides,r=s.params,o=s.slidesEl,l=s.activeIndex,d=s.virtual&&r.virtual.enabled,c=s.grid&&r.grid&&1<r.grid.rows;d?n=r.loop?((d=(d=l-s.virtual.slidesBefore)<0?s.virtual.slides.length+d:d)>=s.virtual.slides.length&&(d-=s.virtual.slides.length),e('[data-swiper-slide-index="'.concat(d,'"]'))):e('[data-swiper-slide-index="'.concat(l,'"]')):c?(n=a.find(function(e){return e.column===l}),i=a.find(function(e){return e.column===l+1}),t=a.find(function(e){return e.column===l-1})):n=a[l],n&&(c||(i=function(e,t){for(var i=[];e.nextElementSibling;){var n=e.nextElementSibling;(!t||n.matches(t))&&i.push(n),e=n}return i}(n,".".concat(r.slideClass,", swiper-slide"))[0],r.loop&&!i&&(i=a[0]),t=function(e,t){for(var i=[];e.previousElementSibling;){var n=e.previousElementSibling;(!t||n.matches(t))&&i.push(n),e=n}return i}(n,".".concat(r.slideClass,", swiper-slide"))[0],r.loop&&0===!t&&(t=a[a.length-1]))),a.forEach(function(e){w(e,e===n,r.slideActiveClass),w(e,e===i,r.slideNextClass),w(e,e===t,r.slidePrevClass)}),s.emitSlidesClasses()},updateActiveIndex:function(e){var t,i,n=this,s=n.rtlTranslate?n.translate:-n.translate,a=n.snapGrid,r=n.params,o=n.activeIndex,l=n.realIndex,d=n.snapIndex,c=e,e=function(e){e-=n.virtual.slidesBefore;return(e=e<0?n.virtual.slides.length+e:e)>=n.virtual.slides.length&&(e-=n.virtual.slides.length),e};void 0===c&&(c=function(e){for(var t,i=e.slidesGrid,n=e.params,s=e.rtlTranslate?e.translate:-e.translate,a=0;a<i.length;a+=1)void 0!==i[a+1]?s>=i[a]&&s<i[a+1]-(i[a+1]-i[a])/2?t=a:s>=i[a]&&s<i[a+1]&&(t=a+1):s>=i[a]&&(t=a);return t=n.normalizeSlideIndex&&(t<0||void 0===t)?0:t}(n)),(t=0<=a.indexOf(s)?a.indexOf(s):(t=Math.min(r.slidesPerGroupSkip,c))+Math.floor((c-t)/r.slidesPerGroup))>=a.length&&(t=a.length-1),c!==o||n.params.loop?c===o&&n.params.loop&&n.virtual&&n.params.virtual.enabled?n.realIndex=e(c):(a=n.grid&&r.grid&&1<r.grid.rows,i=n.virtual&&r.virtual.enabled&&r.loop?e(c):a?(e=n.slides.find(function(e){return e.column===c}),a=parseInt(e.getAttribute("data-swiper-slide-index"),10),Number.isNaN(a)&&(a=Math.max(n.slides.indexOf(e),0)),Math.floor(a/r.grid.rows)):n.slides[c]&&(i=n.slides[c].getAttribute("data-swiper-slide-index"))?parseInt(i,10):c,Object.assign(n,{previousSnapIndex:d,snapIndex:t,previousRealIndex:l,realIndex:i,previousIndex:o,activeIndex:c}),n.initialized&&S(n),n.emit("activeIndexChange"),n.emit("snapIndexChange"),(n.initialized||n.params.runCallbacksOnInit)&&(l!==i&&n.emit("realIndexChange"),n.emit("slideChange"))):t!==d&&(n.snapIndex=t,n.emit("snapIndexChange"))},updateClickedSlide:function(e,t){var i=this,n=i.params,s=e.closest(".".concat(n.slideClass,", swiper-slide"));!s&&i.isElement&&t&&1<t.length&&t.includes(e)&&_toConsumableArray(t.slice(t.indexOf(e)+1,t.length)).forEach(function(e){!s&&e.matches&&e.matches(".".concat(n.slideClass,", swiper-slide"))&&(s=e)});var a,r=!1;if(s)for(var o=0;o<i.slides.length;o+=1)if(i.slides[o]===s){r=!0,a=o;break}if(!s||!r)return i.clickedSlide=void 0,void(i.clickedIndex=void 0);i.clickedSlide=s,i.virtual&&i.params.virtual.enabled?i.clickedIndex=parseInt(s.getAttribute("data-swiper-slide-index"),10):i.clickedIndex=a,n.slideToClickedSlide&&void 0!==i.clickedIndex&&i.clickedIndex!==i.activeIndex&&i.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");var t=this,i=t.params,n=t.rtlTranslate,s=t.translate,a=t.wrapperEl;return i.virtualTranslate?n?-s:s:i.cssMode?s:(e=N(a,e),e+=t.cssOverflowAdjustment(),(e=n?-e:e)||0)},setTranslate:function(e,t){var i=this,n=i.rtlTranslate,s=i.params,a=i.wrapperEl,r=i.progress,o=0,l=0;i.isHorizontal()?o=n?-e:e:l=e,s.roundLengths&&(o=Math.floor(o),l=Math.floor(l)),i.previousTranslate=i.translate,i.translate=i.isHorizontal()?o:l,s.cssMode?a[i.isHorizontal()?"scrollLeft":"scrollTop"]=i.isHorizontal()?-o:-l:s.virtualTranslate||(i.isHorizontal()?o-=i.cssOverflowAdjustment():l-=i.cssOverflowAdjustment(),a.style.transform="translate3d(".concat(o,"px, ").concat(l,"px, ").concat(0,"px)")),(0==(l=i.maxTranslate()-i.minTranslate())?0:(e-i.minTranslate())/l)!==r&&i.updateProgress(e),i.emit("setTranslate",i.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,n,s){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===n&&(n=!0);var a=this,r=a.params,o=a.wrapperEl;if(a.animating&&r.preventInteractionOnTransition)return!1;var l=a.minTranslate(),d=a.maxTranslate(),e=n&&l<e?l:n&&e<d?d:e;if(a.updateProgress(e),r.cssMode){r=a.isHorizontal();if(0===t)o[r?"scrollLeft":"scrollTop"]=-e;else{if(!a.support.smoothScroll)return T({swiper:a,targetPosition:-e,side:r?"left":"top"}),!0;o.scrollTo((_defineProperty(o={},r?"left":"top",-e),_defineProperty(o,"behavior","smooth"),o))}return!0}return 0===t?(a.setTransition(0),a.setTranslate(e),i&&(a.emit("beforeTransitionStart",t,s),a.emit("transitionEnd"))):(a.setTransition(t),a.setTranslate(e),i&&(a.emit("beforeTransitionStart",t,s),a.emit("transitionStart")),a.animating||(a.animating=!0,a.onTranslateToWrapperTransitionEnd||(a.onTranslateToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onTranslateToWrapperTransitionEnd),a.onTranslateToWrapperTransitionEnd=null,delete a.onTranslateToWrapperTransitionEnd,a.animating=!1,i&&a.emit("transitionEnd"))}),a.wrapperEl.addEventListener("transitionend",a.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||(this.wrapperEl.style.transitionDuration="".concat(e,"ms"),this.wrapperEl.style.transitionDelay=0===e?"0ms":""),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);var i=this.params;i.cssMode||(i.autoHeight&&this.updateAutoHeight(),l({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);var i=this.params;this.animating=!1,i.cssMode||(this.setTransition(0),l({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,i,n,s){void 0===i&&(i=!0),"string"==typeof(e=void 0===e?0:e)&&(e=parseInt(e,10));var a=this,r=e;r<0&&(r=0);var o=a.params,l=a.snapGrid,d=a.slidesGrid,c=a.previousIndex,u=a.activeIndex,p=a.rtlTranslate,h=a.wrapperEl;if(!a.enabled&&!n&&!s||a.destroyed||a.animating&&o.preventInteractionOnTransition)return!1;void 0===t&&(t=a.params.speed);var e=(e=Math.min(a.params.slidesPerGroupSkip,r))+Math.floor((r-e)/a.params.slidesPerGroup),f=-l[e=e>=l.length?l.length-1:e];if(o.normalizeSlideIndex)for(var m=0;m<d.length;m+=1){var g=-Math.floor(100*f),v=Math.floor(100*d[m]),y=Math.floor(100*d[m+1]);void 0!==d[m+1]?v<=g&&g<y-(y-v)/2?r=m:v<=g&&g<y&&(r=m+1):v<=g&&(r=m)}if(a.initialized&&r!==u){if(!a.allowSlideNext&&(p?f>a.translate&&f>a.minTranslate():f<a.translate&&f<a.minTranslate()))return!1;if(!a.allowSlidePrev&&f>a.translate&&f>a.maxTranslate()&&(u||0)!==r)return!1}r!==(c||0)&&i&&a.emit("beforeSlideChangeStart"),a.updateProgress(f);var b=u<r?"next":r<u?"prev":"reset";if(!((u=a.virtual&&a.params.virtual.enabled)&&s)&&(p&&-f===a.translate||!p&&f===a.translate))return a.updateActiveIndex(r),o.autoHeight&&a.updateAutoHeight(),a.updateSlidesClasses(),"slide"!==o.effect&&a.setTranslate(f),"reset"!==b&&(a.transitionStart(i,b),a.transitionEnd(i,b)),!1;if(o.cssMode){var w=a.isHorizontal(),S=p?f:-f;if(0===t)u&&(a.wrapperEl.style.scrollSnapType="none",a._immediateVirtual=!0),u&&!a._cssModeVirtualInitialSet&&0<a.params.initialSlide?(a._cssModeVirtualInitialSet=!0,requestAnimationFrame(function(){h[w?"scrollLeft":"scrollTop"]=S})):h[w?"scrollLeft":"scrollTop"]=S,u&&requestAnimationFrame(function(){a.wrapperEl.style.scrollSnapType="",a._immediateVirtual=!1});else{if(!a.support.smoothScroll)return T({swiper:a,targetPosition:S,side:w?"left":"top"}),!0;h.scrollTo((_defineProperty(u={},w?"left":"top",S),_defineProperty(u,"behavior","smooth"),u))}return!0}return a.setTransition(t),a.setTranslate(f),a.updateActiveIndex(r),a.updateSlidesClasses(),a.emit("beforeTransitionStart",t,n),a.transitionStart(i,b),0===t?a.transitionEnd(i,b):a.animating||(a.animating=!0,a.onSlideToWrapperTransitionEnd||(a.onSlideToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onSlideToWrapperTransitionEnd),a.onSlideToWrapperTransitionEnd=null,delete a.onSlideToWrapperTransitionEnd,a.transitionEnd(i,b))}),a.wrapperEl.addEventListener("transitionend",a.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,n){void 0===i&&(i=!0),"string"==typeof(e=void 0===e?0:e)&&(e=parseInt(e,10));var s=this;if(!s.destroyed){void 0===t&&(t=s.params.speed);var a,r,o,l,d,c,u=s.grid&&s.params.grid&&1<s.params.grid.rows,p=e;return s.params.loop&&(s.virtual&&s.params.virtual.enabled?p+=s.virtual.slidesBefore:(r=u?(a=p*s.params.grid.rows,s.slides.find(function(e){return+e.getAttribute("data-swiper-slide-index")==a}).column):s.getSlideIndexByData(p),o=u?Math.ceil(s.slides.length/s.params.grid.rows):s.slides.length,d=s.params.centeredSlides,"auto"===(l=s.params.slidesPerView)?l=s.slidesPerViewDynamic():(l=Math.ceil(parseFloat(s.params.slidesPerView,10)),d&&l%2==0&&(l+=1)),e=o-r<l,d&&(e=e||r<Math.ceil(l/2)),(e=n&&d&&"auto"!==s.params.slidesPerView&&!u?!1:e)&&(d=d?r<s.activeIndex?"prev":"next":r-s.activeIndex-1<s.params.slidesPerView?"next":"prev",s.loopFix({direction:d,slideTo:!0,activeSlideIndex:"next"==d?r+1:r-o+1,slideRealIndex:"next"==d?s.realIndex:void 0})),p=u?(c=p*s.params.grid.rows,s.slides.find(function(e){return+e.getAttribute("data-swiper-slide-index")==c}).column):s.getSlideIndexByData(p))),requestAnimationFrame(function(){s.slideTo(p,t,i,n)}),s}},slideNext:function(e,t,i){void 0===t&&(t=!0);var n=this,s=n.enabled,a=n.params,r=n.animating;if(!s||n.destroyed)return n;void 0===e&&(e=n.params.speed),s=a.slidesPerGroup,"auto"===a.slidesPerView&&1===a.slidesPerGroup&&a.slidesPerGroupAuto&&(s=Math.max(n.slidesPerViewDynamic("current",!0),1));var o=n.activeIndex<a.slidesPerGroupSkip?1:s,s=n.virtual&&a.virtual.enabled;if(a.loop){if(r&&!s&&a.loopPreventsSliding)return!1;if(n.loopFix({direction:"next"}),n._clientLeft=n.wrapperEl.clientLeft,n.activeIndex===n.slides.length-1&&a.cssMode)return requestAnimationFrame(function(){n.slideTo(n.activeIndex+o,e,t,i)}),!0}return a.rewind&&n.isEnd?n.slideTo(0,e,t,i):n.slideTo(n.activeIndex+o,e,t,i)},slidePrev:function(e,t,i){void 0===t&&(t=!0);var n=this,s=n.params,a=n.snapGrid,r=n.slidesGrid,o=n.rtlTranslate,l=n.enabled,d=n.animating;if(!l||n.destroyed)return n;if(void 0===e&&(e=n.params.speed),l=n.virtual&&s.virtual.enabled,s.loop){if(d&&!l&&s.loopPreventsSliding)return!1;n.loopFix({direction:"prev"}),n._clientLeft=n.wrapperEl.clientLeft}function c(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}var u,p=c(o?n.translate:-n.translate),o=a.map(c);void 0===(o=a[o.indexOf(p)-1])&&s.cssMode&&(a.forEach(function(e,t){e<=p&&(u=t)}),void 0!==u&&(o=a[0<u?u-1:u]));var h=0;if(void 0!==o&&((h=r.indexOf(o))<0&&(h=n.activeIndex-1),"auto"===s.slidesPerView&&1===s.slidesPerGroup&&s.slidesPerGroupAuto&&(h=h-n.slidesPerViewDynamic("previous",!0)+1,h=Math.max(h,0))),s.rewind&&n.isBeginning){o=n.params.virtual&&n.params.virtual.enabled&&n.virtual?n.virtual.slides.length-1:n.slides.length-1;return n.slideTo(o,e,t,i)}return s.loop&&0===n.activeIndex&&s.cssMode?(requestAnimationFrame(function(){n.slideTo(h,e,t,i)}),!0):n.slideTo(h,e,t,i)},slideReset:function(e,t,i){if(void 0===t&&(t=!0),!this.destroyed)return void 0===e&&(e=this.params.speed),this.slideTo(this.activeIndex,e,t,i)},slideToClosest:function(e,t,i,n){void 0===t&&(t=!0),void 0===n&&(n=.5);var s=this;if(!s.destroyed){void 0===e&&(e=s.params.speed);var a=s.activeIndex,r=Math.min(s.params.slidesPerGroupSkip,a),o=r+Math.floor((a-r)/s.params.slidesPerGroup),l=s.rtlTranslate?s.translate:-s.translate;return l>=s.snapGrid[o]?(r=s.snapGrid[o],(s.snapGrid[o+1]-r)*n<l-r&&(a+=s.params.slidesPerGroup)):l-(l=s.snapGrid[o-1])<=(s.snapGrid[o]-l)*n&&(a-=s.params.slidesPerGroup),a=Math.max(a,0),a=Math.min(a,s.slidesGrid.length-1),s.slideTo(a,e,t,i)}},slideToClickedSlide:function(){var e,t,i,n,s,a,r=this;r.destroyed||(e=r.params,t=r.slidesEl,i="auto"===e.slidesPerView?r.slidesPerViewDynamic():e.slidesPerView,n=r.clickedIndex,a=r.isElement?"swiper-slide":".".concat(e.slideClass),e.loop?r.animating||(s=parseInt(r.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?n<r.loopedSlides-i/2||n>r.slides.length-r.loopedSlides+i/2?(r.loopFix(),n=r.getSlideIndex(Z(t,"".concat(a,'[data-swiper-slide-index="').concat(s,'"]'))[0]),C(function(){r.slideTo(n)})):r.slideTo(n):n>r.slides.length-i?(r.loopFix(),n=r.getSlideIndex(Z(t,"".concat(a,'[data-swiper-slide-index="').concat(s,'"]'))[0]),C(function(){r.slideTo(n)})):r.slideTo(n)):r.slideTo(n))}},loop:{loopCreate:function(e){var t,i,n,s,a,r=this,o=r.params,l=r.slidesEl;!o.loop||r.virtual&&r.params.virtual.enabled||(t=function(){Z(l,".".concat(o.slideClass,", swiper-slide")).forEach(function(e,t){e.setAttribute("data-swiper-slide-index",t)})},a=r.grid&&o.grid&&1<o.grid.rows,i=o.slidesPerGroup*(a?o.grid.rows:1),n=r.slides.length%i!=0,s=a&&r.slides.length%o.grid.rows!=0,a=function(e){for(var t=0;t<e;t+=1){var i=r.isElement?_("swiper-slide",[o.slideBlankClass]):_("div",[o.slideClass,o.slideBlankClass]);r.slidesEl.append(i)}},n?o.loopAddBlankSlides?(a(i-r.slides.length%i),r.recalcSlides(),r.updateSlides()):$("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"):s&&(o.loopAddBlankSlides?(a(o.grid.rows-r.slides.length%o.grid.rows),r.recalcSlides(),r.updateSlides()):$("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)")),t(),r.loopFix({slideRealIndex:e,direction:o.centeredSlides?void 0:"next"}))},loopFix:function(e){var t=(_=void 0===e?{}:e).slideRealIndex,i=void 0===(C=_.slideTo)||C,n=_.direction,s=_.setTranslate,a=_.activeSlideIndex,r=_.byController,o=_.byMousewheel,l=this;if(l.params.loop){l.emit("beforeLoopFix");var d=l.slides,c=l.allowSlidePrev,u=l.allowSlideNext,p=l.slidesEl,h=l.params,f=h.centeredSlides;if(l.allowSlidePrev=!0,l.allowSlideNext=!0,l.virtual&&h.virtual.enabled)return i&&(h.centeredSlides||0!==l.snapIndex?h.centeredSlides&&l.snapIndex<h.slidesPerView?l.slideTo(l.virtual.slides.length+l.snapIndex,0,!1,!0):l.snapIndex===l.snapGrid.length-1&&l.slideTo(l.virtual.slidesBefore,0,!1,!0):l.slideTo(l.virtual.slides.length,0,!1,!0)),l.allowSlidePrev=c,l.allowSlideNext=u,void l.emit("loopFix");var m=h.slidesPerView;"auto"===m?m=l.slidesPerViewDynamic():(m=Math.ceil(parseFloat(h.slidesPerView,10)),f&&m%2==0&&(m+=1));var g=h.slidesPerGroupAuto?m:h.slidesPerGroup,v=g;v%g!=0&&(v+=g-v%g),v+=h.loopAdditionalSlides,l.loopedSlides=v;var y=l.grid&&h.grid&&1<h.grid.rows;d.length<m+v?$("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled and not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):y&&"row"===h.grid.fill&&$("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");var b=[],w=[],S=l.activeIndex;void 0===a?a=l.getSlideIndex(d.find(function(e){return e.classList.contains(h.slideActiveClass)})):S=a;var T,x,E,e="next"===n||!n,C="prev"===n||!n,_=0,k=0,M=y?Math.ceil(d.length/h.grid.rows):d.length,A=(y?d[a].column:a)+(f&&void 0===s?-m/2+.5:0);if(A<v)for(var _=Math.max(v-A,g),P=0;P<v-A;P+=1){var L=P-Math.floor(P/M)*M;if(y)for(var O=M-L-1,I=d.length-1;0<=I;--I)d[I].column===O&&b.push(I);else b.push(M-L-1)}else if(M-v<A+m)for(var k=Math.max(A-(M-2*v),g),D=0;D<k;D+=1)!function(e){var i=e-Math.floor(e/M)*M;y?d.forEach(function(e,t){e.column===i&&w.push(t)}):w.push(i)}(D);l.__preventObserver__=!0,requestAnimationFrame(function(){l.__preventObserver__=!1}),C&&b.forEach(function(e){d[e].swiperLoopMoveDOM=!0,p.prepend(d[e]),d[e].swiperLoopMoveDOM=!1}),e&&w.forEach(function(e){d[e].swiperLoopMoveDOM=!0,p.append(d[e]),d[e].swiperLoopMoveDOM=!1}),l.recalcSlides(),"auto"===h.slidesPerView?l.updateSlides():y&&(0<b.length&&C||0<w.length&&e)&&l.slides.forEach(function(e,t){l.grid.updateSlide(t,e,l.slides)}),h.watchSlidesProgress&&l.updateSlidesOffset(),i&&(0<b.length&&C?void 0===t?(T=l.slidesGrid[S],T=l.slidesGrid[S+_]-T,o?l.setTranslate(l.translate-T):(l.slideTo(S+Math.ceil(_),0,!1,!0),s&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-T,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-T))):s&&(T=y?b.length/h.grid.rows:b.length,l.slideTo(l.activeIndex+T,0,!1,!0),l.touchEventsData.currentTranslate=l.translate):0<w.length&&e&&(void 0===t?(x=l.slidesGrid[S],x=l.slidesGrid[S-k]-x,o?l.setTranslate(l.translate-x):(l.slideTo(S-k,0,!1,!0),s&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-x,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-x))):(x=y?w.length/h.grid.rows:w.length,l.slideTo(l.activeIndex-x,0,!1,!0)))),l.allowSlidePrev=c,l.allowSlideNext=u,l.controller&&l.controller.control&&!r&&(E={slideRealIndex:t,direction:n,setTranslate:s,activeSlideIndex:a,byController:!0},Array.isArray(l.controller.control)?l.controller.control.forEach(function(e){!e.destroyed&&e.params.loop&&e.loopFix(_objectSpread(_objectSpread({},E),{},{slideTo:e.params.slidesPerView===h.slidesPerView&&i}))}):l.controller.control instanceof l.constructor&&l.controller.control.params.loop&&l.controller.control.loopFix(_objectSpread(_objectSpread({},E),{},{slideTo:l.controller.control.params.slidesPerView===h.slidesPerView&&i}))),l.emit("loopFix")}},loopDestroy:function(){var i,e=this,t=e.params,n=e.slidesEl;!t.loop||e.virtual&&e.params.virtual.enabled||(e.recalcSlides(),i=[],e.slides.forEach(function(e){var t=void 0===e.swiperSlideIndex?+e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;i[t]=e}),e.slides.forEach(function(e){e.removeAttribute("data-swiper-slide-index")}),i.forEach(function(e){n.append(e)}),e.recalcSlides(),e.slideTo(e.realIndex,0))}},grabCursor:{setGrabCursor:function(e){var t,i=this;!i.params.simulateTouch||i.params.watchOverflow&&i.isLocked||i.params.cssMode||(t="container"===i.params.touchEventsTarget?i.el:i.wrapperEl,i.isElement&&(i.__preventObserver__=!0),t.style.cursor="move",t.style.cursor=e?"grabbing":"grab",i.isElement&&requestAnimationFrame(function(){i.__preventObserver__=!1}))},unsetGrabCursor:function(){var e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(function(){e.__preventObserver__=!1}))}},events:{attachEvents:function(){var e=this,t=e.params;e.onTouchStart=function(e){var t=this,i=L(),n=e;n.originalEvent&&(n=n.originalEvent);var s,a,r,o,l=t.touchEventsData;if("pointerdown"===n.type){if(null!==l.pointerId&&l.pointerId!==n.pointerId)return;l.pointerId=n.pointerId}else"touchstart"===n.type&&1===n.targetTouches.length&&(l.touchId=n.targetTouches[0].identifier);"touchstart"!==n.type?(s=t.params,a=t.touches,t.enabled&&(!s.simulateTouch&&"mouse"===n.pointerType||t.animating&&s.preventInteractionOnTransition||(!t.animating&&s.cssMode&&s.loop&&t.loopFix(),r=n.target,"wrapper"===s.touchEventsTarget&&!d(r,t.wrapperEl)||"which"in n&&3===n.which||"button"in n&&0<n.button||l.isTouched&&l.isMoved||(o=!!s.noSwipingClass&&""!==s.noSwipingClass,e=n.composedPath?n.composedPath():n.path,o&&n.target&&n.target.shadowRoot&&e&&(r=e[0]),o=s.noSwipingSelector||".".concat(s.noSwipingClass),e=!(!n.target||!n.target.shadowRoot),s.noSwiping&&(e?c(o,r):r.closest(o))?t.allowClick=!0:s.swipeHandler&&!r.closest(s.swipeHandler)||(a.currentX=n.pageX,a.currentY=n.pageY,e=a.currentX,o=a.currentY,P(t,n,e)&&(Object.assign(l,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),a.startX=e,a.startY=o,l.touchStartTime=y(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,0<s.threshold&&(l.allowThresholdMove=!1),o=!0,r.matches(l.focusableElements)&&(o=!1,"SELECT"===r.nodeName&&(l.isTouched=!1)),i.activeElement&&i.activeElement.matches(l.focusableElements)&&i.activeElement!==r&&("mouse"===n.pointerType||"mouse"!==n.pointerType&&!r.matches(l.focusableElements))&&i.activeElement.blur(),o=o&&t.allowTouchMove&&s.touchStartPreventDefault,!s.touchStartForcePreventDefault&&!o||r.isContentEditable||n.preventDefault(),s.freeMode&&s.freeMode.enabled&&t.freeMode&&t.animating&&!s.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",n))))))):P(t,n,n.targetTouches[0].pageX)}.bind(e),e.onTouchMove=function(e){var t=L(),i=this,n=i.touchEventsData,s=i.params,a=i.touches,r=i.rtlTranslate,o=i.enabled;if(o&&(s.simulateTouch||"mouse"!==e.pointerType)){var l=e;if("pointermove"===(l=l.originalEvent?l.originalEvent:l).type){if(null!==n.touchId)return;if(l.pointerId!==n.pointerId)return}if("touchmove"===l.type){if(!(c=_toConsumableArray(l.changedTouches).find(function(e){return e.identifier===n.touchId}))||c.identifier!==n.touchId)return}else c=l;if(n.isTouched){var d=c.pageX,o=c.pageY;if(l.preventedByNestedSwiper)return a.startX=d,void(a.startY=o);if(!i.allowTouchMove)return l.target.matches(n.focusableElements)||(i.allowClick=!1),void(n.isTouched&&(Object.assign(a,{startX:d,startY:o,currentX:d,currentY:o}),n.touchStartTime=y()));if(s.touchReleaseOnEdges&&!s.loop)if(i.isVertical()){if(o<a.startY&&i.translate<=i.maxTranslate()||o>a.startY&&i.translate>=i.minTranslate())return n.isTouched=!1,void(n.isMoved=!1)}else if(d<a.startX&&i.translate<=i.maxTranslate()||d>a.startX&&i.translate>=i.minTranslate())return;if(t.activeElement&&t.activeElement.matches(n.focusableElements)&&t.activeElement!==l.target&&"mouse"!==l.pointerType&&t.activeElement.blur(),t.activeElement&&l.target===t.activeElement&&l.target.matches(n.focusableElements))return n.isMoved=!0,void(i.allowClick=!1);n.allowTouchCallbacks&&i.emit("touchMove",l),a.previousX=a.currentX,a.previousY=a.currentY,a.currentX=d,a.currentY=o;var e=a.currentX-a.startX,c=a.currentY-a.startY;if(!(i.params.threshold&&Math.sqrt(Math.pow(e,2)+Math.pow(c,2))<i.params.threshold))if(void 0===n.isScrolling&&(i.isHorizontal()&&a.currentY===a.startY||i.isVertical()&&a.currentX===a.startX?n.isScrolling=!1:25<=e*e+c*c&&(u=180*Math.atan2(Math.abs(c),Math.abs(e))/Math.PI,n.isScrolling=i.isHorizontal()?u>s.touchAngle:90-u>s.touchAngle)),n.isScrolling&&i.emit("touchMoveOpposite",l),void 0===n.startMoving&&(a.currentX===a.startX&&a.currentY===a.startY||(n.startMoving=!0)),n.isScrolling||"touchmove"===l.type&&n.preventTouchMoveFromPointerMove)n.isTouched=!1;else if(n.startMoving){i.allowClick=!1,!s.cssMode&&l.cancelable&&l.preventDefault(),s.touchMoveStopPropagation&&!s.nested&&l.stopPropagation();var t=i.isHorizontal()?e:c,u=i.isHorizontal()?a.currentX-a.previousX:a.currentY-a.previousY;s.oneWayMovement&&(t=Math.abs(t)*(r?1:-1),u=Math.abs(u)*(r?1:-1)),a.diff=t,t*=s.touchRatio,r&&(t=-t,u=-u);e=i.touchesDirection;i.swipeDirection=0<t?"prev":"next",i.touchesDirection=0<u?"prev":"next";c=i.params.loop&&!s.cssMode,r="next"===i.touchesDirection&&i.allowSlideNext||"prev"===i.touchesDirection&&i.allowSlidePrev;if(n.isMoved||(c&&r&&i.loopFix({direction:i.swipeDirection}),n.startTranslate=i.getTranslate(),i.setTransition(0),i.animating&&(u=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}}),i.wrapperEl.dispatchEvent(u)),n.allowMomentumBounce=!1,!s.grabCursor||!0!==i.allowSlideNext&&!0!==i.allowSlidePrev||i.setGrabCursor(!0),i.emit("sliderFirstMove",l)),(new Date).getTime(),n.isMoved&&n.allowThresholdMove&&e!==i.touchesDirection&&c&&r&&1<=Math.abs(t))return Object.assign(a,{startX:d,startY:o,currentX:d,currentY:o,startTranslate:n.currentTranslate}),n.loopSwapReset=!0,void(n.startTranslate=n.currentTranslate);i.emit("sliderMove",l),n.isMoved=!0,n.currentTranslate=t+n.startTranslate;d=!0,o=s.resistanceRatio;if(s.touchReleaseOnEdges&&(o=0),0<t?(c&&r&&n.allowThresholdMove&&n.currentTranslate>(s.centeredSlides?i.minTranslate()-i.slidesSizesGrid[i.activeIndex+1]-("auto"!==s.slidesPerView&&2<=i.slides.length-s.slidesPerView?i.slidesSizesGrid[i.activeIndex+1]+i.params.spaceBetween:0)-i.params.spaceBetween:i.minTranslate())&&i.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),n.currentTranslate>i.minTranslate()&&(d=!1,s.resistance&&(n.currentTranslate=i.minTranslate()-1+Math.pow(-i.minTranslate()+n.startTranslate+t,o)))):t<0&&(c&&r&&n.allowThresholdMove&&n.currentTranslate<(s.centeredSlides?i.maxTranslate()+i.slidesSizesGrid[i.slidesSizesGrid.length-1]+i.params.spaceBetween+("auto"!==s.slidesPerView&&2<=i.slides.length-s.slidesPerView?i.slidesSizesGrid[i.slidesSizesGrid.length-1]+i.params.spaceBetween:0):i.maxTranslate())&&i.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:i.slides.length-("auto"===s.slidesPerView?i.slidesPerViewDynamic():Math.ceil(parseFloat(s.slidesPerView,10)))}),n.currentTranslate<i.maxTranslate()&&(d=!1,s.resistance&&(n.currentTranslate=i.maxTranslate()+1-Math.pow(i.maxTranslate()-n.startTranslate-t,o)))),d&&(l.preventedByNestedSwiper=!0),!i.allowSlideNext&&"next"===i.swipeDirection&&n.currentTranslate<n.startTranslate&&(n.currentTranslate=n.startTranslate),!i.allowSlidePrev&&"prev"===i.swipeDirection&&n.currentTranslate>n.startTranslate&&(n.currentTranslate=n.startTranslate),i.allowSlidePrev||i.allowSlideNext||(n.currentTranslate=n.startTranslate),0<s.threshold){if(!(Math.abs(t)>s.threshold||n.allowThresholdMove))return void(n.currentTranslate=n.startTranslate);if(!n.allowThresholdMove)return n.allowThresholdMove=!0,a.startX=a.currentX,a.startY=a.currentY,n.currentTranslate=n.startTranslate,void(a.diff=i.isHorizontal()?a.currentX-a.startX:a.currentY-a.startY)}s.followFinger&&!s.cssMode&&((s.freeMode&&s.freeMode.enabled&&i.freeMode||s.watchSlidesProgress)&&(i.updateActiveIndex(),i.updateSlidesClasses()),s.freeMode&&s.freeMode.enabled&&i.freeMode&&i.freeMode.onTouchMove(),i.updateProgress(n.currentTranslate),i.setTranslate(n.currentTranslate))}}else n.startMoving&&n.isScrolling&&i.emit("touchMoveOpposite",l)}}.bind(e),e.onTouchEnd=function(e){var t=this,i=t.touchEventsData,n=e;if("touchend"===(n=n.originalEvent?n.originalEvent:n).type||"touchcancel"===n.type){if(!(d=_toConsumableArray(n.changedTouches).find(function(e){return e.identifier===i.touchId}))||d.identifier!==i.touchId)return}else{if(null!==i.touchId)return;if(n.pointerId!==i.pointerId)return;d=n}if(!["pointercancel","pointerout","pointerleave","contextmenu"].includes(n.type)||["pointercancel","contextmenu"].includes(n.type)&&(t.browser.isSafari||t.browser.isWebView)){i.pointerId=null,i.touchId=null;var s=t.params,a=t.touches,r=t.rtlTranslate,o=t.slidesGrid,e=t.enabled;if(e&&(s.simulateTouch||"mouse"!==n.pointerType)){if(i.allowTouchCallbacks&&t.emit("touchEnd",n),i.allowTouchCallbacks=!1,!i.isTouched)return i.isMoved&&s.grabCursor&&t.setGrabCursor(!1),i.isMoved=!1,void(i.startMoving=!1);s.grabCursor&&i.isMoved&&i.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);var l,d=y(),e=d-i.touchStartTime;if(t.allowClick&&(m=n.path||n.composedPath&&n.composedPath(),t.updateClickedSlide(m&&m[0]||n.target,m),t.emit("tap click",n),e<300&&d-i.lastClickTime<300&&t.emit("doubleTap doubleClick",n)),i.lastClickTime=y(),C(function(){t.destroyed||(t.allowClick=!0)}),!i.isTouched||!i.isMoved||!t.swipeDirection||0===a.diff&&!i.loopSwapReset||i.currentTranslate===i.startTranslate&&!i.loopSwapReset)return i.isTouched=!1,i.isMoved=!1,void(i.startMoving=!1);if(i.isTouched=!1,i.isMoved=!1,i.startMoving=!1,l=s.followFinger?r?t.translate:-t.translate:-i.currentTranslate,!s.cssMode)if(s.freeMode&&s.freeMode.enabled)t.freeMode.onTouchEnd({currentPos:l});else{for(var c=l>=-t.maxTranslate()&&!t.params.loop,u=0,p=t.slidesSizesGrid[0],h=0;h<o.length;h+=h<s.slidesPerGroupSkip?1:s.slidesPerGroup){var f=h<s.slidesPerGroupSkip-1?1:s.slidesPerGroup;void 0!==o[h+f]?(c||l>=o[h]&&l<o[h+f])&&(p=o[(u=h)+f]-o[h]):(c||l>=o[h])&&(u=h,p=o[o.length-1]-o[o.length-2])}var m=null,d=null;s.rewind&&(t.isBeginning?d=s.virtual&&s.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(m=0));a=(l-o[u])/p,r=u<s.slidesPerGroupSkip-1?1:s.slidesPerGroup;e>s.longSwipesMs?s.longSwipes?("next"===t.swipeDirection&&(a>=s.longSwipesRatio?t.slideTo(s.rewind&&t.isEnd?m:u+r):t.slideTo(u)),"prev"===t.swipeDirection&&(a>1-s.longSwipesRatio?t.slideTo(u+r):null!==d&&a<0&&Math.abs(a)>s.longSwipesRatio?t.slideTo(d):t.slideTo(u))):t.slideTo(t.activeIndex):s.shortSwipes?t.navigation&&(n.target===t.navigation.nextEl||n.target===t.navigation.prevEl)?n.target===t.navigation.nextEl?t.slideTo(u+r):t.slideTo(u):("next"===t.swipeDirection&&t.slideTo(null!==m?m:u+r),"prev"===t.swipeDirection&&t.slideTo(null!==d?d:u)):t.slideTo(t.activeIndex)}}}}.bind(e),e.onDocumentTouchStart=function(){this.documentTouchHandlerProceeded||(this.documentTouchHandlerProceeded=!0,this.params.touchReleaseOnEdges&&(this.el.style.touchAction="auto"))}.bind(e),t.cssMode&&(e.onScroll=function(){var e=this,t=e.wrapperEl,i=e.rtlTranslate;e.enabled&&(e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses(),(0==(t=e.maxTranslate()-e.minTranslate())?0:(e.translate-e.minTranslate())/t)!==e.progress&&e.updateProgress(i?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1))}.bind(e)),e.onClick=function(e){var t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}.bind(e),e.onLoad=function(e){var t=this;r(t,e.target),t.params.cssMode||"auto"!==t.params.slidesPerView&&!t.params.autoHeight||t.update()}.bind(e),I(e,"on")},detachEvents:function(){I(this,"off")}},breakpoints:{setBreakpoint:function(){var e,n,t,i,s,a,r=this,o=r.realIndex,l=r.initialized,d=r.params,c=r.el,u=d.breakpoints;u&&0!==Object.keys(u).length&&(a=L(),i="window"!==d.breakpointsBase&&d.breakpointsBase?"container":d.breakpointsBase,s=["window","container"].includes(d.breakpointsBase)||!d.breakpointsBase?r.el:a.querySelector(d.breakpointsBase),(e=r.getBreakpoint(u,i,s))&&r.currentBreakpoint!==e&&(n=(e in u?u[e]:void 0)||r.originalParams,t=D(r,d),a=D(r,n),i=r.params.grabCursor,s=n.grabCursor,u=d.enabled,t&&!a?(c.classList.remove("".concat(d.containerModifierClass,"grid"),"".concat(d.containerModifierClass,"grid-column")),r.emitContainerClasses()):!t&&a&&(c.classList.add("".concat(d.containerModifierClass,"grid")),(n.grid.fill&&"column"===n.grid.fill||!n.grid.fill&&"column"===d.grid.fill)&&c.classList.add("".concat(d.containerModifierClass,"grid-column")),r.emitContainerClasses()),i&&!s?r.unsetGrabCursor():!i&&s&&r.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(function(e){var t,i;void 0!==n[e]&&(t=d[e]&&d[e].enabled,i=n[e]&&n[e].enabled,t&&!i&&r[e].disable(),!t&&i&&r[e].enable())}),a=n.direction&&n.direction!==d.direction,c=d.loop&&(n.slidesPerView!==d.slidesPerView||a),i=d.loop,a&&l&&r.changeDirection(),p(r.params,n),s=r.params.enabled,a=r.params.loop,Object.assign(r,{allowTouchMove:r.params.allowTouchMove,allowSlideNext:r.params.allowSlideNext,allowSlidePrev:r.params.allowSlidePrev}),u&&!s?r.disable():!u&&s&&r.enable(),r.currentBreakpoint=e,r.emit("_beforeBreakpoint",n),l&&(c?(r.loopDestroy(),r.loopCreate(o),r.updateSlides()):!i&&a?(r.loopCreate(o),r.updateSlides()):i&&!a&&r.loopDestroy()),r.emit("breakpoint",n)))},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),e&&("container"!==t||i)){var n=!1,s=z(),a="window"===t?s.innerHeight:i.clientHeight,r=Object.keys(e).map(function(e){if("string"!=typeof e||0!==e.indexOf("@"))return{value:e,point:e};var t=parseFloat(e.substr(1));return{value:a*t,point:e}});r.sort(function(e,t){return parseInt(e.value,10)-parseInt(t.value,10)});for(var o=0;o<r.length;o+=1){var l=r[o],d=l.point,l=l.value;"window"===t?s.matchMedia("(min-width: ".concat(l,"px)")).matches&&(n=d):l<=i.clientWidth&&(n=d)}return n||"max"}}},checkOverflow:{checkOverflow:function(){var e,t=this,i=t.isLocked,n=t.params,s=n.slidesOffsetBefore;s?(e=t.slides.length-1,s=t.slidesGrid[e]+t.slidesSizesGrid[e]+2*s,t.isLocked=t.size>s):t.isLocked=1===t.snapGrid.length,!0===n.allowSlideNext&&(t.allowSlideNext=!t.isLocked),!0===n.allowSlidePrev&&(t.allowSlidePrev=!t.isLocked),i&&i!==t.isLocked&&(t.isEnd=!1),i!==t.isLocked&&t.emit(t.isLocked?"lock":"unlock")}},classes:{addClasses:function(){var i,n,e=this,t=e.classNames,s=e.params,a=e.rtl,r=e.el,o=e.device,o=(o=["initialized",s.direction,{"free-mode":e.params.freeMode&&s.freeMode.enabled},{autoheight:s.autoHeight},{rtl:a},{grid:s.grid&&1<s.grid.rows},{"grid-column":s.grid&&1<s.grid.rows&&"column"===s.grid.fill},{android:o.android},{ios:o.ios},{"css-mode":s.cssMode},{centered:s.cssMode&&s.centeredSlides},{"watch-progress":s.watchSlidesProgress}],i=s.containerModifierClass,n=[],o.forEach(function(t){"object"===_typeof(t)?Object.keys(t).forEach(function(e){t[e]&&n.push(i+e)}):"string"==typeof t&&n.push(i+t)}),n);t.push.apply(t,_toConsumableArray(o)),(r=r.classList).add.apply(r,_toConsumableArray(t)),e.emitContainerClasses()},removeClasses:function(){var e=this.el,t=this.classNames;e&&"string"!=typeof e&&((e=e.classList).remove.apply(e,_toConsumableArray(t)),this.emitContainerClasses())}}},F={},R=function(){function c(){var e,t;_classCallCheck(this,c);for(var i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];t=p({},t=(t=1===n.length&&n[0].constructor&&"Object"===Object.prototype.toString.call(n[0]).slice(8,-1)?n[0]:(e=n[0],n[1]))||{}),e&&!t.el&&(t.el=e);var a=L();if(t.el&&"string"==typeof t.el&&1<a.querySelectorAll(t.el).length){var r=[];return a.querySelectorAll(t.el).forEach(function(e){e=p({},t,{el:e});r.push(new c(e))}),r}var o=this;o.__swiper__=!0,o.support=f(),o.device=m({userAgent:t.userAgent}),o.browser=g(),o.eventsListeners={},o.eventsAnyListeners=[],o.modules=_toConsumableArray(o.__modules__),t.modules&&Array.isArray(t.modules)&&(d=o.modules).push.apply(d,_toConsumableArray(t.modules));var l={};o.modules.forEach(function(e){var n,s;e({params:t,swiper:o,extendParams:(n=t,s=l,function(e){void 0===e&&(e={});var t=Object.keys(e)[0],i=e[t];"object"===_typeof(i)&&null!==i&&(!0===n[t]&&(n[t]={enabled:!0}),"navigation"===t&&n[t]&&n[t].enabled&&!n[t].prevEl&&!n[t].nextEl&&(n[t].auto=!0),0<=["pagination","scrollbar"].indexOf(t)&&n[t]&&n[t].enabled&&!n[t].el&&(n[t].auto=!0),t in n&&"enabled"in i&&("object"!==_typeof(n[t])||"enabled"in n[t]||(n[t].enabled=!0),n[t]||(n[t]={enabled:!1}))),p(s,e)}),on:o.on.bind(o),once:o.once.bind(o),off:o.off.bind(o),emit:o.emit.bind(o)})});var d=p({},j,l);return o.params=p({},d,F,t),o.originalParams=p({},o.params),o.passedParams=p({},t),o.params&&o.params.on&&Object.keys(o.params.on).forEach(function(e){o.on(e,o.params.on[e])}),o.params&&o.params.onAny&&o.onAny(o.params.onAny),Object.assign(o,{enabled:o.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:function(){return"horizontal"===o.params.direction},isVertical:function(){return"vertical"===o.params.direction},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment:function(){return Math.trunc(this.translate/Math.pow(2,23))*Math.pow(2,23)},allowSlideNext:o.params.allowSlideNext,allowSlidePrev:o.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:o.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:o.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),o.emit("_swiper"),o.params.init&&o.init(),o}return _createClass(c,[{key:"getDirectionLabel",value:function(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}},{key:"getSlideIndex",value:function(e){var t=this.slidesEl,i=this.params,i=k(Z(t,".".concat(i.slideClass,", swiper-slide"))[0]);return k(e)-i}},{key:"getSlideIndexByData",value:function(t){return this.getSlideIndex(this.slides.find(function(e){return+e.getAttribute("data-swiper-slide-index")===t}))}},{key:"recalcSlides",value:function(){var e=this.slidesEl,t=this.params;this.slides=Z(e,".".concat(t.slideClass,", swiper-slide"))}},{key:"enable",value:function(){var e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}},{key:"disable",value:function(){var e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}},{key:"setProgress",value:function(e,t){var i=this;e=Math.min(Math.max(e,0),1);var n=i.minTranslate(),s=i.maxTranslate();i.translateTo((s-n)*e+n,void 0===t?0:t),i.updateActiveIndex(),i.updateSlidesClasses()}},{key:"emitContainerClasses",value:function(){var e,t=this;t.params._emitClasses&&t.el&&(e=t.el.className.split(" ").filter(function(e){return 0===e.indexOf("swiper")||0===e.indexOf(t.params.containerModifierClass)}),t.emit("_containerClasses",e.join(" ")))}},{key:"getSlideClasses",value:function(e){var t=this;return t.destroyed?"":e.className.split(" ").filter(function(e){return 0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)}).join(" ")}},{key:"emitSlidesClasses",value:function(){var i,n=this;n.params._emitClasses&&n.el&&(i=[],n.slides.forEach(function(e){var t=n.getSlideClasses(e);i.push({slideEl:e,classNames:t}),n.emit("_slideClass",e,t)}),n.emit("_slideClasses",i))}},{key:"slidesPerViewDynamic",value:function(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);var i=this,n=i.params,s=i.slides,a=i.slidesGrid,r=i.slidesSizesGrid,o=i.size,l=i.activeIndex,d=1;if("number"==typeof n.slidesPerView)return n.slidesPerView;if(n.centeredSlides){for(var c,u=s[l]?Math.ceil(s[l].swiperSlideSize):0,p=l+1;p<s.length;p+=1)s[p]&&!c&&(d+=1,o<(u+=Math.ceil(s[p].swiperSlideSize))&&(c=!0));for(var h=l-1;0<=h;--h)s[h]&&!c&&(d+=1,o<(u+=s[h].swiperSlideSize)&&(c=!0))}else if("current"===e)for(var f=l+1;f<s.length;f+=1)(t?a[f]+r[f]-a[l]<o:a[f]-a[l]<o)&&(d+=1);else for(var m=l-1;0<=m;--m)a[l]-a[m]<o&&(d+=1);return d}},{key:"update",value:function(){var e,t,i,n=this;function s(){var e=n.rtlTranslate?-1*n.translate:n.translate,e=Math.min(Math.max(e,n.maxTranslate()),n.minTranslate());n.setTranslate(e),n.updateActiveIndex(),n.updateSlidesClasses()}n&&!n.destroyed&&(e=n.snapGrid,(t=n.params).breakpoints&&n.setBreakpoint(),_toConsumableArray(n.el.querySelectorAll('[loading="lazy"]')).forEach(function(e){e.complete&&r(n,e)}),n.updateSize(),n.updateSlides(),n.updateProgress(),n.updateSlidesClasses(),t.freeMode&&t.freeMode.enabled&&!t.cssMode?(s(),t.autoHeight&&n.updateAutoHeight()):(("auto"===t.slidesPerView||1<t.slidesPerView)&&n.isEnd&&!t.centeredSlides?(i=(n.virtual&&t.virtual.enabled?n.virtual:n).slides,n.slideTo(i.length-1,0,!1,!0)):n.slideTo(n.activeIndex,0,!1,!0))||s(),t.watchOverflow&&e!==n.snapGrid&&n.checkOverflow(),n.emit("update"))}},{key:"changeDirection",value:function(t,e){void 0===e&&(e=!0);var i=this,n=i.params.direction;return(t=t||("horizontal"===n?"vertical":"horizontal"))===n||"horizontal"!==t&&"vertical"!==t||(i.el.classList.remove("".concat(i.params.containerModifierClass).concat(n)),i.el.classList.add("".concat(i.params.containerModifierClass).concat(t)),i.emitContainerClasses(),i.params.direction=t,i.slides.forEach(function(e){"vertical"===t?e.style.width="":e.style.height=""}),i.emit("changeDirection"),e&&i.update()),i}},{key:"changeLanguageDirection",value:function(e){var t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.el.classList.add("".concat(t.params.containerModifierClass,"rtl")),t.el.dir="rtl"):(t.el.classList.remove("".concat(t.params.containerModifierClass,"rtl")),t.el.dir="ltr"),t.update())}},{key:"mount",value:function(e){var t=this;if(t.mounted)return!0;var i=e||t.params.el;if(!(i="string"==typeof i?document.querySelector(i):i))return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);var n=function(){return".".concat((t.params.wrapperClass||"").trim().split(" ").join("."))},s=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(n()):Z(i,n())[0];return!s&&t.params.createElements&&(s=_("div",t.params.wrapperClass),i.append(s),Z(i,".".concat(t.params.slideClass)).forEach(function(e){s.append(e)})),Object.assign(t,{el:i,wrapperEl:s,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:s,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===J(i,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===J(i,"direction")),wrongRTL:"-webkit-box"===J(s,"display")}),!0}},{key:"init",value:function(e){var t=this;if(t.initialized)return t;if(!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents();e=_toConsumableArray(t.el.querySelectorAll('[loading="lazy"]'));return t.isElement&&e.push.apply(e,_toConsumableArray(t.hostEl.querySelectorAll('[loading="lazy"]'))),e.forEach(function(e){e.complete?r(t,e):e.addEventListener("load",function(e){r(t,e.target)})}),S(t),t.initialized=!0,S(t),t.emit("init"),t.emit("afterInit"),t}},{key:"destroy",value:function(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);var i,n=this,s=n.params,a=n.el,r=n.wrapperEl,o=n.slides;return void 0===n.params||n.destroyed||(n.emit("beforeDestroy"),n.initialized=!1,n.detachEvents(),s.loop&&n.loopDestroy(),t&&(n.removeClasses(),a&&"string"!=typeof a&&a.removeAttribute("style"),r&&r.removeAttribute("style"),o&&o.length&&o.forEach(function(e){e.classList.remove(s.slideVisibleClass,s.slideFullyVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),n.emit("destroy"),Object.keys(n.eventsListeners).forEach(function(e){n.off(e)}),!1!==e&&(n.el&&"string"!=typeof n.el&&(n.el.swiper=null),i=n,Object.keys(i).forEach(function(e){try{i[e]=null}catch(e){}try{delete i[e]}catch(e){}})),n.destroyed=!0),null}}],[{key:"extendDefaults",value:function(e){p(F,e)}},{key:"extendedDefaults",get:function(){return F}},{key:"defaults",get:function(){return j}},{key:"installModule",value:function(e){var t=c.prototype.__modules__=!c.prototype.__modules__?[]:c.prototype.__modules__;"function"==typeof e&&t.indexOf(e)<0&&t.push(e)}},{key:"use",value:function(e){return Array.isArray(e)?e.forEach(function(e){return c.installModule(e)}):c.installModule(e),c}}]),c}();function X(i,n,s,a){return i.params.createElements&&Object.keys(a).forEach(function(e){var t;s[e]||!0!==s.auto||((t=Z(i.el,".".concat(a[e]))[0])||((t=_("div",a[e])).className=a[e],i.el.append(t)),s[e]=t,n[e]=t)}),s}function Y(e){return".".concat((e=void 0===e?"":e).trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,"."))}function W(e){var t=this.params,i=this.slidesEl;t.loop&&this.loopDestroy();function n(e){var t;"string"==typeof e?((t=document.createElement("div")).innerHTML=e,i.append(t.children[0]),t.innerHTML=""):i.append(e)}if("object"===_typeof(e)&&"length"in e)for(var s=0;s<e.length;s+=1)e[s]&&n(e[s]);else n(e);this.recalcSlides(),t.loop&&this.loopCreate(),t.observer&&!this.isElement||this.update()}function V(e){var t=this.params,i=this.activeIndex,n=this.slidesEl;t.loop&&this.loopDestroy();function s(e){var t;"string"==typeof e?((t=document.createElement("div")).innerHTML=e,n.prepend(t.children[0]),t.innerHTML=""):n.prepend(e)}var a=i+1;if("object"===_typeof(e)&&"length"in e){for(var r=0;r<e.length;r+=1)e[r]&&s(e[r]);a=i+e.length}else s(e);this.recalcSlides(),t.loop&&this.loopCreate(),t.observer&&!this.isElement||this.update(),this.slideTo(a,0,!1)}function G(e){var t,i=e.effect,n=e.swiper,s=e.on,a=e.setTranslate,r=e.setTransition,o=e.overwriteParams,l=e.perspective,d=e.recreateShadows,c=e.getEffectParams;s("beforeInit",function(){var e;n.params.effect===i&&(n.classNames.push("".concat(n.params.containerModifierClass).concat(i)),l&&l()&&n.classNames.push("".concat(n.params.containerModifierClass,"3d")),e=o?o():{},Object.assign(n.params,e),Object.assign(n.originalParams,e))}),s("setTranslate",function(){n.params.effect===i&&a()}),s("setTransition",function(e,t){n.params.effect===i&&r(t)}),s("transitionEnd",function(){n.params.effect===i&&d&&c&&c().slideShadows&&(n.slides.forEach(function(e){e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(function(e){return e.remove()})}),d())}),s("virtualUpdate",function(){n.params.effect===i&&(n.slides.length||(t=!0),requestAnimationFrame(function(){t&&n.slides&&n.slides.length&&(a(),t=!1)}))})}function U(e,t){var i=o(t);return i!==t&&(i.style.backfaceVisibility="hidden",i.style["-webkit-backface-visibility"]="hidden"),i}function Q(e){var t,i=e.swiper,n=e.duration,s=e.transformElements,e=e.allSlides,a=i.activeIndex;i.params.virtualTranslate&&0!==n&&(t=!1,(e?s:s.filter(function(e){var t,e=e.classList.contains("swiper-slide-transform")?(t=e).parentElement||i.slides.find(function(e){return e.shadowRoot&&e.shadowRoot===t.parentNode}):e;return i.getSlideIndex(e)===a})).forEach(function(e){b(e,function(){var e;t||i&&!i.destroyed&&(t=!0,i.animating=!1,e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0}),i.wrapperEl.dispatchEvent(e))})}))}function te(e,t,i){i="swiper-slide-shadow".concat(i?"-".concat(i):"").concat(e?" swiper-slide-shadow-".concat(e):""),e=o(t),t=e.querySelector(".".concat(i.split(" ").join(".")));return t||(t=_("div",i.split(" ")),e.append(t)),t}Object.keys(B).forEach(function(t){Object.keys(B[t]).forEach(function(e){R.prototype[e]=B[t][e]})}),R.use([function(e){function a(){r&&!r.destroyed&&r.initialized&&(n("beforeResize"),n("resize"))}function t(){r&&!r.destroyed&&r.initialized&&n("orientationchange")}var r=e.swiper,i=e.on,n=e.emit,s=z(),o=null,l=null;i("init",function(){r.params.resizeObserver&&void 0!==s.ResizeObserver?r&&!r.destroyed&&r.initialized&&(o=new ResizeObserver(function(i){l=s.requestAnimationFrame(function(){var e=r.width,t=r.height,n=e,s=t;i.forEach(function(e){var t=e.contentBoxSize,i=e.contentRect,e=e.target;e&&e!==r.el||(n=i?i.width:(t[0]||t).inlineSize,s=i?i.height:(t[0]||t).blockSize)}),n===e&&s===t||a()})})).observe(r.el):(s.addEventListener("resize",a),s.addEventListener("orientationchange",t))}),i("destroy",function(){l&&s.cancelAnimationFrame(l),o&&o.unobserve&&r.el&&(o.unobserve(r.el),o=null),s.removeEventListener("resize",a),s.removeEventListener("orientationchange",t)})},function(e){function i(e,t){void 0===t&&(t={});var i=new(o.MutationObserver||o.WebkitMutationObserver)(function(e){var t;n.__preventObserver__||(1!==e.length?(t=function(){a("observerUpdate",e[0])},o.requestAnimationFrame?o.requestAnimationFrame(t):o.setTimeout(t,0)):a("observerUpdate",e[0]))});i.observe(e,{attributes:void 0===t.attributes||t.attributes,childList:n.isElement||(void 0===t.childList||t).childList,characterData:void 0===t.characterData||t.characterData}),r.push(i)}var n=e.swiper,t=e.extendParams,s=e.on,a=e.emit,r=[],o=z();t({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",function(){if(n.params.observer){if(n.params.observeParents)for(var e=q(n.hostEl),t=0;t<e.length;t+=1)i(e[t]);i(n.hostEl,{childList:n.params.observeSlideChildren}),i(n.wrapperEl,{attributes:!1})}}),s("destroy",function(){r.forEach(function(e){e.disconnect()}),r.splice(0,r.length)})}]);var ie=[function(e){var t,M=e.swiper,i=e.extendParams,n=e.on,A=e.emit;i({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}}),i=L(),M.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]};var s=i.createElement("div");function P(e,t){var i,n=M.params.virtual;return n.cache&&M.virtual.cache[t]?M.virtual.cache[t]:(n.renderSlide?"string"==typeof(i=n.renderSlide.call(M,e,t))&&(s.innerHTML=i,i=s.children[0]):i=M.isElement?_("swiper-slide"):_("div",M.params.slideClass),i.setAttribute("data-swiper-slide-index",t),n.renderSlide||(i.innerHTML=e),n.cache&&(M.virtual.cache[t]=i),i)}function o(e,t){var i=M.params,n=i.slidesPerView,s=i.slidesPerGroup,a=i.centeredSlides,r=i.loop,o=i.initialSlide;if(!(t&&!r&&0<o)){var l=M.params.virtual,d=l.addSlidesBefore,i=l.addSlidesAfter,t=M.virtual,c=t.from,u=t.to,p=t.slides,o=t.slidesGrid,l=t.offset;M.params.cssMode||M.updateActiveIndex();var h,t=M.activeIndex||0,f=M.rtlTranslate?"right":M.isHorizontal()?"left":"top",d=a?(h=Math.floor(n/2)+s+i,Math.floor(n/2)+s+d):(h=n+(s-1)+i,(r?n:s)+d),m=t-d,g=t+h;r||(m=Math.max(m,0),g=Math.min(g,p.length-1));var v=(M.slidesGrid[m]||0)-(M.slidesGrid[0]||0);if(r&&d<=t?(m-=d,a||(v+=M.slidesGrid[0])):r&&t<d&&(m=-d,a&&(v+=M.slidesGrid[0])),Object.assign(M.virtual,{from:m,to:g,offset:v,slidesGrid:M.slidesGrid,slidesBefore:d,slidesAfter:h}),c===m&&u===g&&!e)return M.slidesGrid!==o&&v!==l&&M.slides.forEach(function(e){e.style[f]="".concat(v-Math.abs(M.cssOverflowAdjustment()),"px")}),M.updateProgress(),void A("virtualUpdate");if(M.params.virtual.renderExternal)return M.params.virtual.renderExternal.call(M,{offset:v,from:m,to:g,slides:function(){for(var e=[],t=m;t<=g;t+=1)e.push(p[t]);return e}()}),void(M.params.virtual.renderExternalUpdate?k():A("virtualUpdate"));var y=[],b=[],w=function(e){var t=e;return e<0?t=p.length+e:t>=p.length&&(t-=p.length),t};if(e)M.slides.filter(function(e){return e.matches(".".concat(M.params.slideClass,", swiper-slide"))}).forEach(function(e){e.remove()});else for(var S=c;S<=u;S+=1)(S<m||g<S)&&function(){var t=w(S);M.slides.filter(function(e){return e.matches(".".concat(M.params.slideClass,'[data-swiper-slide-index="').concat(t,'"], swiper-slide[data-swiper-slide-index="').concat(t,'"]'))}).forEach(function(e){e.remove()})}();for(var T,l=r?-p.length:0,x=r?2*p.length:p.length,E=l;E<x;E+=1)m<=E&&E<=g&&(T=w(E),void 0===u||e?b.push(T):(u<E&&b.push(T),E<c&&y.push(T)));if(b.forEach(function(e){M.slidesEl.append(P(p[e],e))}),r)for(var C=y.length-1;0<=C;--C){var _=y[C];M.slidesEl.prepend(P(p[_],_))}else y.sort(function(e,t){return t-e}),y.forEach(function(e){M.slidesEl.prepend(P(p[e],e))});Z(M.slidesEl,".swiper-slide, swiper-slide").forEach(function(e){e.style[f]="".concat(v-Math.abs(M.cssOverflowAdjustment()),"px")}),k()}function k(){M.updateSlides(),M.updateProgress(),M.updateSlidesClasses(),A("virtualUpdate")}}n("beforeInit",function(){var e,t;M.params.virtual.enabled&&(void 0!==M.passedParams.virtual.slides||(t=_toConsumableArray(M.slidesEl.children).filter(function(e){return e.matches(".".concat(M.params.slideClass,", swiper-slide"))}))&&t.length&&(M.virtual.slides=_toConsumableArray(t),e=!0,t.forEach(function(e,t){e.setAttribute("data-swiper-slide-index",t),(M.virtual.cache[t]=e).remove()})),e||(M.virtual.slides=M.params.virtual.slides),M.classNames.push("".concat(M.params.containerModifierClass,"virtual")),M.params.watchSlidesProgress=!0,M.originalParams.watchSlidesProgress=!0,o(!1,!0))}),n("setTranslate",function(){M.params.virtual.enabled&&(M.params.cssMode&&!M._immediateVirtual?(clearTimeout(t),t=setTimeout(function(){o()},100)):o())}),n("init update resize",function(){M.params.virtual.enabled&&M.params.cssMode&&K(M.wrapperEl,"--swiper-virtual-size","".concat(M.virtualSize,"px"))}),Object.assign(M.virtual,{appendSlide:function(e){if("object"===_typeof(e)&&"length"in e)for(var t=0;t<e.length;t+=1)e[t]&&M.virtual.slides.push(e[t]);else M.virtual.slides.push(e);o(!0)},prependSlide:function(e){var n,s,t=M.activeIndex,i=t+1,a=1;if(Array.isArray(e)){for(var r=0;r<e.length;r+=1)e[r]&&M.virtual.slides.unshift(e[r]);i=t+e.length,a=e.length}else M.virtual.slides.unshift(e);M.params.virtual.cache&&(n=M.virtual.cache,s={},Object.keys(n).forEach(function(e){var t=n[e],i=t.getAttribute("data-swiper-slide-index");i&&t.setAttribute("data-swiper-slide-index",parseInt(i,10)+a),s[parseInt(e,10)+a]=t}),M.virtual.cache=s),o(!0),M.slideTo(i,0)},removeSlide:function(t){if(null!=t){var e=M.activeIndex;if(Array.isArray(t))for(var i=t.length-1;0<=i;--i)M.params.virtual.cache&&(delete M.virtual.cache[t[i]],Object.keys(M.virtual.cache).forEach(function(e){t<e&&(M.virtual.cache[e-1]=M.virtual.cache[e],M.virtual.cache[e-1].setAttribute("data-swiper-slide-index",e-1),delete M.virtual.cache[e])})),M.virtual.slides.splice(t[i],1),t[i]<e&&--e,e=Math.max(e,0);else M.params.virtual.cache&&(delete M.virtual.cache[t],Object.keys(M.virtual.cache).forEach(function(e){t<e&&(M.virtual.cache[e-1]=M.virtual.cache[e],M.virtual.cache[e-1].setAttribute("data-swiper-slide-index",e-1),delete M.virtual.cache[e])})),M.virtual.slides.splice(t,1),t<e&&--e,e=Math.max(e,0);o(!0),M.slideTo(e,0)}},removeAllSlides:function(){M.virtual.slides=[],M.params.virtual.cache&&(M.virtual.cache={}),o(!0),M.slideTo(0,0)},update:o})},function(e){var b=e.swiper,t=e.extendParams,i=e.on,w=e.emit,S=L(),T=z();function n(e){if(b.enabled){var t=b.rtlTranslate,i=e,n=(i=i.originalEvent?i.originalEvent:i).keyCode||i.charCode,s=b.params.keyboard.pageUpDown,a=s&&33===n,r=s&&34===n,o=37===n,l=39===n,d=38===n,c=40===n;if(!b.allowSlideNext&&(b.isHorizontal()&&l||b.isVertical()&&c||r))return!1;if(!b.allowSlidePrev&&(b.isHorizontal()&&o||b.isVertical()&&d||a))return!1;if(!(i.shiftKey||i.altKey||i.ctrlKey||i.metaKey||S.activeElement&&S.activeElement.nodeName&&("input"===S.activeElement.nodeName.toLowerCase()||"textarea"===S.activeElement.nodeName.toLowerCase()))){if(b.params.keyboard.onlyInViewport&&(a||r||o||l||d||c)){var u=!1;if(0<q(b.el,".".concat(b.params.slideClass,", swiper-slide")).length&&0===q(b.el,".".concat(b.params.slideActiveClass)).length)return;var p=b.el,h=p.clientWidth,e=p.clientHeight,f=T.innerWidth,m=T.innerHeight,s=H(p);t&&(s.left-=p.scrollLeft);for(var g=[[s.left,s.top],[s.left+h,s.top],[s.left,s.top+e],[s.left+h,s.top+e]],v=0;v<g.length;v+=1){var y=g[v];0<=y[0]&&y[0]<=f&&0<=y[1]&&y[1]<=m&&(0===y[0]&&0===y[1]||(u=!0))}if(!u)return}b.isHorizontal()?((a||r||o||l)&&(i.preventDefault?i.preventDefault():i.returnValue=!1),((r||l)&&!t||(a||o)&&t)&&b.slideNext(),((a||o)&&!t||(r||l)&&t)&&b.slidePrev()):((a||r||d||c)&&(i.preventDefault?i.preventDefault():i.returnValue=!1),(r||c)&&b.slideNext(),(a||d)&&b.slidePrev()),w("keyPress",n)}}}function s(){b.keyboard.enabled||(S.addEventListener("keydown",n),b.keyboard.enabled=!0)}function a(){b.keyboard.enabled&&(S.removeEventListener("keydown",n),b.keyboard.enabled=!1)}t({keyboard:{enabled:!(b.keyboard={enabled:!1}),onlyInViewport:!0,pageUpDown:!0}}),i("init",function(){b.params.keyboard.enabled&&s()}),i("destroy",function(){b.keyboard.enabled&&a()}),Object.assign(b.keyboard,{enable:s,disable:a})},function(e){var p,h=e.swiper,t=e.extendParams,i=e.on,f=e.emit,n=z();t({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null,noMousewheelClass:"swiper-no-mousewheel"}}),h.mousewheel={enabled:!1};var m,s=y(),g=[];function a(){h.enabled&&(h.mouseEntered=!0)}function r(){h.enabled&&(h.mouseEntered=!1)}function v(e){if(!(h.params.mousewheel.thresholdDelta&&e.delta<h.params.mousewheel.thresholdDelta||h.params.mousewheel.thresholdTime&&y()-s<h.params.mousewheel.thresholdTime)){if(6<=e.delta&&y()-s<60)return 1;e.direction<0?h.isEnd&&!h.params.loop||h.animating||(h.slideNext(),f("scroll",e.raw)):h.isBeginning&&!h.params.loop||h.animating||(h.slidePrev(),f("scroll",e.raw)),s=(new n.Date).getTime()}}function o(e){var t=e;if(h.enabled&&!e.target.closest(".".concat(h.params.mousewheel.noMousewheelClass))){var i=h.params.mousewheel;h.params.cssMode&&t.preventDefault();var n=h.el,s=(n="container"!==h.params.mousewheel.eventsTarget?document.querySelector(h.params.mousewheel.eventsTarget):n)&&n.contains(t.target);if(!h.mouseEntered&&!s&&!i.releaseOnEdges)return!0;t.originalEvent&&(t=t.originalEvent);var a,r=0,o=h.rtlTranslate?-1:1,l=(n=d=l=a=0,"detail"in(s=t)&&(l=s.detail),"wheelDelta"in s&&(l=-s.wheelDelta/120),"wheelDeltaY"in s&&(l=-s.wheelDeltaY/120),"wheelDeltaX"in s&&(a=-s.wheelDeltaX/120),"axis"in s&&s.axis===s.HORIZONTAL_AXIS&&(a=l,l=0),d=10*a,n=10*l,"deltaY"in s&&(n=s.deltaY),"deltaX"in s&&(d=s.deltaX),s.shiftKey&&!d&&(d=n,n=0),(d||n)&&s.deltaMode&&(1===s.deltaMode?(d*=40,n*=40):(d*=800,n*=800)),{spinX:a=d&&!a?d<1?-1:1:a,spinY:l=n&&!l?n<1?-1:1:l,pixelX:d,pixelY:n});if(i.forceToAxis)if(h.isHorizontal()){if(!(Math.abs(l.pixelX)>Math.abs(l.pixelY)))return!0;r=-l.pixelX*o}else{if(!(Math.abs(l.pixelY)>Math.abs(l.pixelX)))return!0;r=-l.pixelY}else r=Math.abs(l.pixelX)>Math.abs(l.pixelY)?-l.pixelX*o:-l.pixelY;if(0===r)return!0;i.invert&&(r=-r);var d=h.getTranslate()+r*i.sensitivity;if((d=d>=h.minTranslate()?h.minTranslate():d)<=h.maxTranslate()&&(d=h.maxTranslate()),(!!h.params.loop||!(d===h.minTranslate()||d===h.maxTranslate()))&&h.params.nested&&t.stopPropagation(),h.params.freeMode&&h.params.freeMode.enabled){var c={time:y(),delta:Math.abs(r),direction:Math.sign(r)},n=m&&c.time<m.time+500&&c.delta<=m.delta&&c.direction===m.direction;if(!n){m=void 0;var l,u,o=h.getTranslate()+r*i.sensitivity,l=h.isBeginning,d=h.isEnd;if((o=o>=h.minTranslate()?h.minTranslate():o)<=h.maxTranslate()&&(o=h.maxTranslate()),h.setTransition(0),h.setTranslate(o),h.updateProgress(),h.updateActiveIndex(),h.updateSlidesClasses(),(!l&&h.isBeginning||!d&&h.isEnd)&&h.updateSlidesClasses(),h.params.loop&&h.loopFix({direction:c.direction<0?"next":"prev",byMousewheel:!0}),h.params.freeMode.sticky&&(clearTimeout(p),p=void 0,15<=g.length&&g.shift(),l=g.length?g[g.length-1]:void 0,d=g[0],g.push(c),l&&(c.delta>l.delta||c.direction!==l.direction)?g.splice(0):15<=g.length&&c.time-d.time<500&&1<=d.delta-c.delta&&c.delta<=6&&(u=0<r?.8:.2,m=c,g.splice(0),p=C(function(){!h.destroyed&&h.params&&h.slideToClosest(h.params.speed,!0,void 0,u)},0)),p=p||C(function(){!h.destroyed&&h.params&&(m=c,g.splice(0),h.slideToClosest(h.params.speed,!0,void 0,.5))},500)),n||f("scroll",t),h.params.autoplay&&h.params.autoplay.disableOnInteraction&&h.autoplay.stop(),i.releaseOnEdges&&(o===h.minTranslate()||o===h.maxTranslate()))return!0}}else{r={time:y(),delta:Math.abs(r),direction:Math.sign(r),raw:e};2<=g.length&&g.shift();e=g.length?g[g.length-1]:void 0;if(g.push(r),(!e||r.direction!==e.direction||r.delta>e.delta||r.time>e.time+150)&&v(r),function(e){var t=h.params.mousewheel;if(e.direction<0){if(h.isEnd&&!h.params.loop&&t.releaseOnEdges)return 1}else if(h.isBeginning&&!h.params.loop&&t.releaseOnEdges)return 1}(r))return!0}return t.preventDefault?t.preventDefault():t.returnValue=!1,!1}}function l(e){var t=h.el;(t="container"!==h.params.mousewheel.eventsTarget?document.querySelector(h.params.mousewheel.eventsTarget):t)[e]("mouseenter",a),t[e]("mouseleave",r),t[e]("wheel",o)}function d(){return h.params.cssMode?(h.wrapperEl.removeEventListener("wheel",o),!0):!h.mousewheel.enabled&&(l("addEventListener"),h.mousewheel.enabled=!0)}function c(){return h.params.cssMode?(h.wrapperEl.addEventListener(event,o),!0):!!h.mousewheel.enabled&&(l("removeEventListener"),!(h.mousewheel.enabled=!1))}i("init",function(){!h.params.mousewheel.enabled&&h.params.cssMode&&c(),h.params.mousewheel.enabled&&d()}),i("destroy",function(){h.params.cssMode&&d(),h.mousewheel.enabled&&c()}),Object.assign(h.mousewheel,{enable:d,disable:c})},function(e){var o=e.swiper,t=e.extendParams,i=e.on,l=e.emit;function s(e){var t;return e&&"string"==typeof e&&o.isElement&&(t=o.el.querySelector(e)||o.hostEl.querySelector(e))?t:(e&&("string"==typeof e&&(t=_toConsumableArray(document.querySelectorAll(e))),o.params.uniqueNavElements&&"string"==typeof e&&t&&1<t.length&&1===o.el.querySelectorAll(e).length?t=o.el.querySelector(e):t&&1===t.length&&(t=t[0])),e&&!t?e:t)}function n(e,i){var n=o.params.navigation;(e=M(e)).forEach(function(e){var t;e&&((t=e.classList)[i?"add":"remove"].apply(t,_toConsumableArray(n.disabledClass.split(" "))),"BUTTON"===e.tagName&&(e.disabled=i),o.params.watchOverflow&&o.enabled&&e.classList[o.isLocked?"add":"remove"](n.lockClass))})}function a(){var e=o.navigation,t=e.nextEl,e=e.prevEl;if(o.params.loop)return n(e,!1),void n(t,!1);n(e,o.isBeginning&&!o.params.rewind),n(t,o.isEnd&&!o.params.rewind)}function r(e){e.preventDefault(),o.isBeginning&&!o.params.loop&&!o.params.rewind||(o.slidePrev(),l("navigationPrev"))}function d(e){e.preventDefault(),o.isEnd&&!o.params.loop&&!o.params.rewind||(o.slideNext(),l("navigationNext"))}function c(){var e,t,i,n=o.params.navigation;o.params.navigation=X(o,o.originalParams.navigation,o.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),(n.nextEl||n.prevEl)&&(e=s(n.nextEl),t=s(n.prevEl),Object.assign(o.navigation,{nextEl:e,prevEl:t}),e=M(e),t=M(t),i=function(e,t){e&&e.addEventListener("click","next"===t?d:r),!o.enabled&&e&&(e=e.classList).add.apply(e,_toConsumableArray(n.lockClass.split(" ")))},e.forEach(function(e){return i(e,"next")}),t.forEach(function(e){return i(e,"prev")}))}function u(){function t(e,t){e.removeEventListener("click","next"===t?d:r),(e=e.classList).remove.apply(e,_toConsumableArray(o.params.navigation.disabledClass.split(" ")))}var e=o.navigation,i=e.nextEl,e=e.prevEl,i=M(i),e=M(e);i.forEach(function(e){return t(e,"next")}),e.forEach(function(e){return t(e,"prev")})}t({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),o.navigation={nextEl:null,prevEl:null},i("init",function(){!1===o.params.navigation.enabled?p():(c(),a())}),i("toEdge fromEdge lock unlock",function(){a()}),i("destroy",function(){u()}),i("enable disable",function(){var e=o.navigation,t=e.nextEl,e=e.prevEl,t=M(t),e=M(e);o.enabled?a():[].concat(_toConsumableArray(t),_toConsumableArray(e)).filter(function(e){return!!e}).forEach(function(e){return e.classList.add(o.params.navigation.lockClass)})}),i("click",function(e,t){var i,n=o.navigation,s=n.nextEl,a=n.prevEl,s=M(s),a=M(a),r=t.target,n=a.includes(r)||s.includes(r);!o.isElement||n||(t=t.path||t.composedPath&&t.composedPath())&&(n=t.find(function(e){return s.includes(e)||a.includes(e)})),o.params.navigation.hideOnClick&&!n&&(o.pagination&&o.params.pagination&&o.params.pagination.clickable&&(o.pagination.el===r||o.pagination.el.contains(r))||(s.length?i=s[0].classList.contains(o.params.navigation.hiddenClass):a.length&&(i=a[0].classList.contains(o.params.navigation.hiddenClass)),l(!0===i?"navigationShow":"navigationHide"),[].concat(_toConsumableArray(s),_toConsumableArray(a)).filter(function(e){return!!e}).forEach(function(e){return e.classList.toggle(o.params.navigation.hiddenClass)})))});var p=function(){var e;(e=o.el.classList).add.apply(e,_toConsumableArray(o.params.navigation.navigationDisabledClass.split(" "))),u()};Object.assign(o.navigation,{enable:function(){var e;(e=o.el.classList).remove.apply(e,_toConsumableArray(o.params.navigation.navigationDisabledClass.split(" "))),c(),a()},disable:p,update:a,init:c,destroy:u})},function(e){var v,y=e.swiper,t=e.extendParams,i=e.on,b=e.emit,e="swiper-pagination";t({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:function(e){return e},formatFractionTotal:function(e){return e},bulletClass:"".concat(e,"-bullet"),bulletActiveClass:"".concat(e,"-bullet-active"),modifierClass:"".concat(e,"-"),currentClass:"".concat(e,"-current"),totalClass:"".concat(e,"-total"),hiddenClass:"".concat(e,"-hidden"),progressbarFillClass:"".concat(e,"-progressbar-fill"),progressbarOppositeClass:"".concat(e,"-progressbar-opposite"),clickableClass:"".concat(e,"-clickable"),lockClass:"".concat(e,"-lock"),horizontalClass:"".concat(e,"-horizontal"),verticalClass:"".concat(e,"-vertical"),paginationDisabledClass:"".concat(e,"-disabled")}}),y.pagination={el:null,bullets:[]};var w=0;function S(){return!y.params.pagination.el||!y.pagination.el||Array.isArray(y.pagination.el)&&0===y.pagination.el.length}function T(e,t){var i=y.params.pagination.bulletActiveClass;(e=e&&e["".concat("prev"===t?"previous":"next","ElementSibling")])&&(e.classList.add("".concat(i,"-").concat(t)),(e=e["".concat("prev"===t?"previous":"next","ElementSibling")])&&e.classList.add("".concat(i,"-").concat(t,"-").concat(t)))}function n(e){var t,i,n=e.target.closest(Y(y.params.pagination.bulletClass));n&&(e.preventDefault(),t=k(n)*y.params.slidesPerGroup,y.params.loop?y.realIndex!==t&&(i=y.realIndex,e=t,n=y.slides.length,"next"===(i=(e%=n)===(i%=n)+1?"next":e===i-1?"previous":void 0)?y.slideNext():"previous"===i?y.slidePrev():y.slideToLoop(t)):y.slideTo(t))}function s(){var e=y.rtl,r=y.params.pagination;if(!S()){var o,t=M(t=y.pagination.el),i=(y.virtual&&y.params.virtual.enabled?y.virtual:y).slides.length,l=y.params.loop?Math.ceil(i/y.params.slidesPerGroup):y.snapGrid.length;if(y.params.loop?(p=y.previousRealIndex||0,o=1<y.params.slidesPerGroup?Math.floor(y.realIndex/y.params.slidesPerGroup):y.realIndex):void 0!==y.snapIndex?(o=y.snapIndex,p=y.previousSnapIndex):(p=y.previousIndex||0,o=y.activeIndex||0),"bullets"===r.type&&y.pagination.bullets&&0<y.pagination.bullets.length){var n,s,a,d,c,u=y.pagination.bullets;if(r.dynamicBullets&&(v=ee(u[0],y.isHorizontal()?"width":"height",!0),t.forEach(function(e){e.style[y.isHorizontal()?"width":"height"]="".concat(v*(r.dynamicMainBullets+4),"px")}),1<r.dynamicMainBullets&&void 0!==p&&((w+=o-(p||0))>r.dynamicMainBullets-1?w=r.dynamicMainBullets-1:w<0&&(w=0)),n=Math.max(o-w,0),a=((s=n+(Math.min(u.length,r.dynamicMainBullets)-1))+n)/2),u.forEach(function(e){var t=_toConsumableArray(["","-next","-next-next","-prev","-prev-prev","-main"].map(function(e){return"".concat(r.bulletActiveClass).concat(e)})).map(function(e){return"string"==typeof e&&e.includes(" ")?e.split(" "):e}).flat();(e=e.classList).remove.apply(e,_toConsumableArray(t))}),1<t.length)u.forEach(function(e){var t,i=k(e);i===o?(t=e.classList).add.apply(t,_toConsumableArray(r.bulletActiveClass.split(" "))):y.isElement&&e.setAttribute("part","bullet"),r.dynamicBullets&&(n<=i&&i<=s&&(t=e.classList).add.apply(t,_toConsumableArray("".concat(r.bulletActiveClass,"-main").split(" "))),i===n&&T(e,"prev"),i===s&&T(e,"next"))});else{var p=u[o];if(p&&(f=p.classList).add.apply(f,_toConsumableArray(r.bulletActiveClass.split(" "))),y.isElement&&u.forEach(function(e,t){e.setAttribute("part",t===o?"bullet-active":"bullet")}),r.dynamicBullets){for(var h,f=u[n],m=u[s],g=n;g<=s;g+=1)u[g]&&(h=u[g].classList).add.apply(h,_toConsumableArray("".concat(r.bulletActiveClass,"-main").split(" ")));T(f,"prev"),T(m,"next")}}r.dynamicBullets&&(m=Math.min(u.length,r.dynamicMainBullets+4),d=(v*m-v)/2-a*v,c=e?"right":"left",u.forEach(function(e){e.style[y.isHorizontal()?c:"top"]="".concat(d,"px")}))}t.forEach(function(e,t){var i,n,s,a;"fraction"===r.type&&(e.querySelectorAll(Y(r.currentClass)).forEach(function(e){e.textContent=r.formatFractionCurrent(o+1)}),e.querySelectorAll(Y(r.totalClass)).forEach(function(e){e.textContent=r.formatFractionTotal(l)})),"progressbar"===r.type&&(i=r.progressbarOpposite?y.isHorizontal()?"vertical":"horizontal":y.isHorizontal()?"horizontal":"vertical",n=(o+1)/l,a=s=1,"horizontal"===i?s=n:a=n,e.querySelectorAll(Y(r.progressbarFillClass)).forEach(function(e){e.style.transform="translate3d(0,0,0) scaleX(".concat(s,") scaleY(").concat(a,")"),e.style.transitionDuration="".concat(y.params.speed,"ms")})),"custom"===r.type&&r.renderCustom?(e.innerHTML=r.renderCustom(y,o+1,l),0===t&&b("paginationRender",e)):(0===t&&b("paginationRender",e),b("paginationUpdate",e)),y.params.watchOverflow&&y.enabled&&e.classList[y.isLocked?"add":"remove"](r.lockClass)})}}function a(){var i=y.params.pagination;if(!S()){var e=y.virtual&&y.params.virtual.enabled?y.virtual.slides.length:y.grid&&1<y.params.grid.rows?y.slides.length/Math.ceil(y.params.grid.rows):y.slides.length,t=M(t=y.pagination.el),n="";if("bullets"===i.type){var s=y.params.loop?Math.ceil(e/y.params.slidesPerGroup):y.snapGrid.length;y.params.freeMode&&y.params.freeMode.enabled&&e<s&&(s=e);for(var a=0;a<s;a+=1)i.renderBullet?n+=i.renderBullet.call(y,a,i.bulletClass):n+="<".concat(i.bulletElement," ").concat(y.isElement?'part="bullet"':"",' class="').concat(i.bulletClass,'"></').concat(i.bulletElement,">")}"fraction"===i.type&&(n=i.renderFraction?i.renderFraction.call(y,i.currentClass,i.totalClass):'<span class="'.concat(i.currentClass,'"></span>')+" / "+'<span class="'.concat(i.totalClass,'"></span>')),"progressbar"===i.type&&(n=i.renderProgressbar?i.renderProgressbar.call(y,i.progressbarFillClass):'<span class="'.concat(i.progressbarFillClass,'"></span>')),y.pagination.bullets=[],t.forEach(function(e){var t;"custom"!==i.type&&(e.innerHTML=n||""),"bullets"===i.type&&(t=y.pagination.bullets).push.apply(t,_toConsumableArray(e.querySelectorAll(Y(i.bulletClass))))}),"custom"!==i.type&&b("paginationRender",t[0])}}function r(){y.params.pagination=X(y,y.originalParams.pagination,y.params.pagination,{el:"swiper-pagination"});var e,i=y.params.pagination;i.el&&(e=(e=!(e="string"==typeof i.el&&y.isElement?y.el.querySelector(i.el):e)&&"string"==typeof i.el?_toConsumableArray(document.querySelectorAll(i.el)):e)||i.el)&&0!==e.length&&(y.params.uniqueNavElements&&"string"==typeof i.el&&Array.isArray(e)&&1<e.length&&1<(e=_toConsumableArray(y.el.querySelectorAll(i.el))).length&&(e=e.find(function(e){return q(e,".swiper")[0]===y.el})),Array.isArray(e)&&1===e.length&&(e=e[0]),Object.assign(y.pagination,{el:e}),(e=M(e)).forEach(function(e){var t;"bullets"===i.type&&i.clickable&&(t=e.classList).add.apply(t,_toConsumableArray((i.clickableClass||"").split(" "))),e.classList.add(i.modifierClass+i.type),e.classList.add(y.isHorizontal()?i.horizontalClass:i.verticalClass),"bullets"===i.type&&i.dynamicBullets&&(e.classList.add("".concat(i.modifierClass).concat(i.type,"-dynamic")),w=0,i.dynamicMainBullets<1&&(i.dynamicMainBullets=1)),"progressbar"===i.type&&i.progressbarOpposite&&e.classList.add(i.progressbarOppositeClass),i.clickable&&e.addEventListener("click",n),y.enabled||e.classList.add(i.lockClass)}))}function o(){var e,i=y.params.pagination;S()||((e=y.pagination.el)&&(e=M(e)).forEach(function(e){var t;e.classList.remove(i.hiddenClass),e.classList.remove(i.modifierClass+i.type),e.classList.remove(y.isHorizontal()?i.horizontalClass:i.verticalClass),i.clickable&&((t=e.classList).remove.apply(t,_toConsumableArray((i.clickableClass||"").split(" "))),e.removeEventListener("click",n))}),y.pagination.bullets&&y.pagination.bullets.forEach(function(e){return(e=e.classList).remove.apply(e,_toConsumableArray(i.bulletActiveClass.split(" ")))}))}i("changeDirection",function(){var t;y.pagination&&y.pagination.el&&(t=y.params.pagination,M(y.pagination.el).forEach(function(e){e.classList.remove(t.horizontalClass,t.verticalClass),e.classList.add(y.isHorizontal()?t.horizontalClass:t.verticalClass)}))}),i("init",function(){!1===y.params.pagination.enabled?l():(r(),a(),s())}),i("activeIndexChange",function(){void 0===y.snapIndex&&s()}),i("snapIndexChange",function(){s()}),i("snapGridLengthChange",function(){a(),s()}),i("destroy",function(){o()}),i("enable disable",function(){var e=y.pagination.el;e&&(e=M(e)).forEach(function(e){return e.classList[y.enabled?"remove":"add"](y.params.pagination.lockClass)})}),i("lock unlock",function(){s()}),i("click",function(e,t){var i=t.target,t=M(y.pagination.el);y.params.pagination.el&&y.params.pagination.hideOnClick&&t&&0<t.length&&!i.classList.contains(y.params.pagination.bulletClass)&&(y.navigation&&(y.navigation.nextEl&&i===y.navigation.nextEl||y.navigation.prevEl&&i===y.navigation.prevEl)||(i=t[0].classList.contains(y.params.pagination.hiddenClass),b(!0===i?"paginationShow":"paginationHide"),t.forEach(function(e){return e.classList.toggle(y.params.pagination.hiddenClass)})))});var l=function(){y.el.classList.add(y.params.pagination.paginationDisabledClass);var e=y.pagination.el;e&&(e=M(e)).forEach(function(e){return e.classList.add(y.params.pagination.paginationDisabledClass)}),o()};Object.assign(y.pagination,{enable:function(){y.el.classList.remove(y.params.pagination.paginationDisabledClass);var e=y.pagination.el;e&&(e=M(e)).forEach(function(e){return e.classList.remove(y.params.pagination.paginationDisabledClass)}),r(),a(),s()},disable:l,render:a,update:s,init:r,destroy:o})},function(e){var a,r,o,n,l=e.swiper,t=e.extendParams,i=e.on,d=e.emit,c=L(),u=!1,p=null,h=null;function s(){var e,t,i,n,s,a;l.params.scrollbar.el&&l.scrollbar.el&&(s=l.scrollbar,e=l.rtlTranslate,t=s.dragEl,i=s.el,n=l.params.scrollbar,a=l.params.loop?l.progressLoop:l.progress,a=(o-(s=r))*a,e?0<(a=-a)?(s=r-a,a=0):o<-a+r&&(s=o+a):a<0?(s=r+a,a=0):o<a+r&&(s=o-a),l.isHorizontal()?(t.style.transform="translate3d(".concat(a,"px, 0, 0)"),t.style.width="".concat(s,"px")):(t.style.transform="translate3d(0px, ".concat(a,"px, 0)"),t.style.height="".concat(s,"px")),n.hide&&(clearTimeout(p),i.style.opacity=1,p=setTimeout(function(){i.style.opacity=0,i.style.transitionDuration="400ms"},1e3)))}function f(){var e,t,i;l.params.scrollbar.el&&l.scrollbar.el&&(t=(e=l.scrollbar).dragEl,i=e.el,t.style.width="",t.style.height="",o=l.isHorizontal()?i.offsetWidth:i.offsetHeight,n=l.size/(l.virtualSize+l.params.slidesOffsetBefore-(l.params.centeredSlides?l.snapGrid[0]:0)),r="auto"===l.params.scrollbar.dragSize?o*n:parseInt(l.params.scrollbar.dragSize,10),l.isHorizontal()?t.style.width="".concat(r,"px"):t.style.height="".concat(r,"px"),i.style.display=1<=n?"none":"",l.params.scrollbar.hide&&(i.style.opacity=0),l.params.watchOverflow&&l.enabled&&e.el.classList[l.isLocked?"add":"remove"](l.params.scrollbar.lockClass))}function m(e){return l.isHorizontal()?e.clientX:e.clientY}function g(e){var t=l.scrollbar,i=l.rtlTranslate,t=t.el,t=(m(e)-H(t)[l.isHorizontal()?"left":"top"]-(null!==a?a:r/2))/(o-r);t=Math.max(Math.min(t,1),0),i&&(t=1-t);t=l.minTranslate()+(l.maxTranslate()-l.minTranslate())*t;l.updateProgress(t),l.setTranslate(t),l.updateActiveIndex(),l.updateSlidesClasses()}function v(e){var t=l.params.scrollbar,i=l.scrollbar,n=l.wrapperEl,s=i.el,i=i.dragEl;u=!0,a=e.target===i?m(e)-e.target.getBoundingClientRect()[l.isHorizontal()?"left":"top"]:null,e.preventDefault(),e.stopPropagation(),n.style.transitionDuration="100ms",i.style.transitionDuration="100ms",g(e),clearTimeout(h),s.style.transitionDuration="0ms",t.hide&&(s.style.opacity=1),l.params.cssMode&&(l.wrapperEl.style["scroll-snap-type"]="none"),d("scrollbarDragStart",e)}function y(e){var t=l.scrollbar,i=l.wrapperEl,n=t.el,t=t.dragEl;u&&(e.preventDefault&&e.cancelable?e.preventDefault():e.returnValue=!1,g(e),i.style.transitionDuration="0ms",n.style.transitionDuration="0ms",t.style.transitionDuration="0ms",d("scrollbarDragMove",e))}function b(e){var t=l.params.scrollbar,i=l.scrollbar,n=l.wrapperEl,s=i.el;u&&(u=!1,l.params.cssMode&&(l.wrapperEl.style["scroll-snap-type"]="",n.style.transitionDuration=""),t.hide&&(clearTimeout(h),h=C(function(){s.style.opacity=0,s.style.transitionDuration="400ms"},1e3)),d("scrollbarDragEnd",e),t.snapOnRelease&&l.slideToClosest())}function w(e){var t=l.scrollbar,i=l.params,n=t.el;n&&(t=!!i.passiveListeners&&{passive:!1,capture:!1},i=!!i.passiveListeners&&{passive:!0,capture:!1},n&&(n[e="on"===e?"addEventListener":"removeEventListener"]("pointerdown",v,t),c[e]("pointermove",y,t),c[e]("pointerup",b,i)))}function S(){var e=l.scrollbar,t=l.el;l.params.scrollbar=X(l,l.originalParams.scrollbar,l.params.scrollbar,{el:"swiper-scrollbar"});var i,n,s=l.params.scrollbar;if(s.el){if((n="string"==typeof s.el&&l.isElement?l.el.querySelector(s.el):n)||"string"!=typeof s.el)n=n||s.el;else if(!(n=c.querySelectorAll(s.el)).length)return;(n=0<(n=l.params.uniqueNavElements&&"string"==typeof s.el&&1<n.length&&1===t.querySelectorAll(s.el).length?t.querySelector(s.el):n).length?n[0]:n).classList.add(l.isHorizontal()?s.horizontalClass:s.verticalClass),n&&((i=n.querySelector(Y(l.params.scrollbar.dragClass)))||(i=_("div",l.params.scrollbar.dragClass),n.append(i))),Object.assign(e,{el:n,dragEl:i}),s.draggable&&l.params.scrollbar.el&&l.scrollbar.el&&w("on"),n&&(n=n.classList)[l.enabled?"remove":"add"].apply(n,_toConsumableArray(E(l.params.scrollbar.lockClass)))}}function T(){var e=l.params.scrollbar,t=l.scrollbar.el;t&&(t=t.classList).remove.apply(t,_toConsumableArray(E(l.isHorizontal()?e.horizontalClass:e.verticalClass))),l.params.scrollbar.el&&l.scrollbar.el&&w("off")}t({scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag",scrollbarDisabledClass:"swiper-scrollbar-disabled",horizontalClass:"swiper-scrollbar-horizontal",verticalClass:"swiper-scrollbar-vertical"}}),l.scrollbar={el:null,dragEl:null},i("changeDirection",function(){var t;l.scrollbar&&l.scrollbar.el&&(t=l.params.scrollbar,M(l.scrollbar.el).forEach(function(e){e.classList.remove(t.horizontalClass,t.verticalClass),e.classList.add(l.isHorizontal()?t.horizontalClass:t.verticalClass)}))}),i("init",function(){!1===l.params.scrollbar.enabled?x():(S(),f(),s())}),i("update resize observerUpdate lock unlock changeDirection",function(){f()}),i("setTranslate",function(){s()}),i("setTransition",function(e,t){t=t,l.params.scrollbar.el&&l.scrollbar.el&&(l.scrollbar.dragEl.style.transitionDuration="".concat(t,"ms"))}),i("enable disable",function(){var e=l.scrollbar.el;e&&(e=e.classList)[l.enabled?"remove":"add"].apply(e,_toConsumableArray(E(l.params.scrollbar.lockClass)))}),i("destroy",function(){T()});var x=function(){var e;(e=l.el.classList).add.apply(e,_toConsumableArray(E(l.params.scrollbar.scrollbarDisabledClass))),l.scrollbar.el&&(e=l.scrollbar.el.classList).add.apply(e,_toConsumableArray(E(l.params.scrollbar.scrollbarDisabledClass))),T()};Object.assign(l.scrollbar,{enable:function(){var e;(e=l.el.classList).remove.apply(e,_toConsumableArray(E(l.params.scrollbar.scrollbarDisabledClass))),l.scrollbar.el&&(e=l.scrollbar.el.classList).remove.apply(e,_toConsumableArray(E(l.params.scrollbar.scrollbarDisabledClass))),S(),f(),s()},disable:x,updateSize:f,setTranslate:s,init:S,destroy:T})},function(e){var d=e.swiper,t=e.extendParams,e=e.on;function a(e,t){var i=d.rtl?-1:1,n=e.getAttribute("data-swiper-parallax")||"0",s=e.getAttribute("data-swiper-parallax-x"),a=e.getAttribute("data-swiper-parallax-y"),r=e.getAttribute("data-swiper-parallax-scale"),o=e.getAttribute("data-swiper-parallax-opacity"),l=e.getAttribute("data-swiper-parallax-rotate");s||a?(s=s||"0",a=a||"0"):d.isHorizontal()?(s=n,a="0"):(a=n,s="0"),s=0<=s.indexOf("%")?"".concat(parseInt(s,10)*t*i,"%"):"".concat(s*t*i,"px"),a=0<=a.indexOf("%")?"".concat(parseInt(a,10)*t,"%"):"".concat(a*t,"px"),null!=o&&(o=o-(o-1)*(1-Math.abs(t)),e.style.opacity=o),a="translate3d(".concat(s,", ").concat(a,", 0px)"),null!=r&&(r=r-(r-1)*(1-Math.abs(t)),a+=" scale(".concat(r,")")),l&&null!=l&&(a+=" rotate(".concat(l*t*-1,"deg)")),e.style.transform=a}function i(){var e=d.el,t=d.slides,n=d.progress,s=d.snapGrid,e=(d.isElement,Z(e,r));d.isElement&&e.push.apply(e,_toConsumableArray(Z(d.hostEl,r))),e.forEach(function(e){a(e,n)}),t.forEach(function(e,t){var i=e.progress;1<d.params.slidesPerGroup&&"auto"!==d.params.slidesPerView&&(i+=Math.ceil(t/2)-n*(s.length-1)),i=Math.min(Math.max(i,-1),1),e.querySelectorAll("".concat(r,", [data-swiper-parallax-rotate]")).forEach(function(e){a(e,i)})})}t({parallax:{enabled:!1}});var r="[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]";e("beforeInit",function(){d.params.parallax.enabled&&(d.params.watchSlidesProgress=!0,d.originalParams.watchSlidesProgress=!0)}),e("init",function(){d.params.parallax.enabled&&i()}),e("setTranslate",function(){d.params.parallax.enabled&&i()}),e("setTransition",function(e,t){d.params.parallax.enabled&&function(i){void 0===i&&(i=d.params.speed);var e=d.el,t=d.hostEl,e=_toConsumableArray(e.querySelectorAll(r));d.isElement&&e.push.apply(e,_toConsumableArray(t.querySelectorAll(r))),e.forEach(function(e){var t=parseInt(e.getAttribute("data-swiper-parallax-duration"),10)||i;0===i&&(t=0),e.style.transitionDuration="".concat(t,"ms")})}(t)})},function(e){var c=e.swiper,t=e.extendParams,i=e.on,n=e.emit,u=z();t({zoom:{enabled:!1,limitToOriginalSize:!1,maxRatio:3,minRatio:1,panOnMouseMove:!1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}}),c.zoom={enabled:!1};var s,a,r,p=1,o=!1,l=!1,d={x:0,y:0},h=-3,f=[],m={originX:0,originY:0,slideEl:void 0,slideWidth:void 0,slideHeight:void 0,imageEl:void 0,imageWrapEl:void 0,maxRatio:3},g={isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},v={x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0},y=1;function b(){if(f.length<2)return 1;var e=f[0].pageX,t=f[0].pageY,i=f[1].pageX,n=f[1].pageY;return Math.sqrt(Math.pow(i-e,2)+Math.pow(n-t,2))}function w(){var e=c.params.zoom,t=m.imageWrapEl.getAttribute("data-swiper-zoom")||e.maxRatio;if(e.limitToOriginalSize&&m.imageEl&&m.imageEl.naturalWidth){e=m.imageEl.naturalWidth/m.imageEl.offsetWidth;return Math.min(e,t)}return t}function S(t){var e=c.isElement?"swiper-slide":".".concat(c.params.slideClass);return t.target.matches(e)||0<c.slides.filter(function(e){return e.contains(t.target)}).length}function T(t){var e=".".concat(c.params.zoom.containerClass);return t.target.matches(e)||0<_toConsumableArray(c.hostEl.querySelectorAll(e)).filter(function(e){return e.contains(t.target)}).length}function x(e){if("mouse"===e.pointerType&&f.splice(0,f.length),S(e)){var t=c.params.zoom;if(a=s=!1,f.push(e),!(f.length<2)){if(s=!0,m.scaleStart=b(),!m.slideEl){m.slideEl=e.target.closest(".".concat(c.params.slideClass,", swiper-slide")),m.slideEl||(m.slideEl=c.slides[c.activeIndex]);var i=(i=m.slideEl.querySelector(".".concat(t.containerClass)))&&i.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0];if(m.imageEl=i,m.imageWrapEl=i?q(m.imageEl,".".concat(t.containerClass))[0]:void 0,!m.imageWrapEl)return void(m.imageEl=void 0);m.maxRatio=w()}m.imageEl&&(t=(i=_slicedToArray(function(){if(f.length<2)return{x:null,y:null};var e=m.imageEl.getBoundingClientRect();return[(f[0].pageX+(f[1].pageX-f[0].pageX)/2-e.x-u.scrollX)/p,(f[0].pageY+(f[1].pageY-f[0].pageY)/2-e.y-u.scrollY)/p]}(),2))[0],i=i[1],m.originX=t,m.originY=i,m.imageEl.style.transitionDuration="0ms"),o=!0}}}function E(t){var e,i,n;S(t)&&(e=c.params.zoom,i=c.zoom,0<=(n=f.findIndex(function(e){return e.pointerId===t.pointerId}))&&(f[n]=t),f.length<2||(a=!0,m.scaleMove=b(),m.imageEl&&(i.scale=m.scaleMove/m.scaleStart*p,i.scale>m.maxRatio&&(i.scale=m.maxRatio-1+Math.pow(i.scale-m.maxRatio+1,.5)),i.scale<e.minRatio&&(i.scale=e.minRatio+1-Math.pow(e.minRatio-i.scale+1,.5)),m.imageEl.style.transform="translate3d(0,0,0) scale(".concat(i.scale,")"))))}function C(t){var e,i,n;S(t)&&("mouse"===t.pointerType&&"pointerout"===t.type||(e=c.params.zoom,i=c.zoom,0<=(n=f.findIndex(function(e){return e.pointerId===t.pointerId}))&&f.splice(n,1),s&&a&&(a=s=!1,m.imageEl&&(i.scale=Math.max(Math.min(i.scale,m.maxRatio),e.minRatio),m.imageEl.style.transitionDuration="".concat(c.params.speed,"ms"),m.imageEl.style.transform="translate3d(0,0,0) scale(".concat(i.scale,")"),p=i.scale,o=!1,1<i.scale&&m.slideEl?m.slideEl.classList.add("".concat(e.zoomedSlideClass)):i.scale<=1&&m.slideEl&&m.slideEl.classList.remove("".concat(e.zoomedSlideClass)),1===i.scale&&(m.originX=0,m.originY=0,m.slideEl=void 0)))))}function _(){c.touchEventsData.preventTouchMoveFromPointerMove=!1}function k(e){var t="mouse"===e.pointerType&&c.params.zoom.panOnMouseMove;if(S(e)&&T(e)){var i=c.zoom;if(m.imageEl)if(g.isTouched&&m.slideEl)if(t)A(e);else{g.isMoved||(g.width=m.imageEl.offsetWidth||m.imageEl.clientWidth,g.height=m.imageEl.offsetHeight||m.imageEl.clientHeight,g.startX=N(m.imageWrapEl,"x")||0,g.startY=N(m.imageWrapEl,"y")||0,m.slideWidth=m.slideEl.offsetWidth,m.slideHeight=m.slideEl.offsetHeight,m.imageWrapEl.style.transitionDuration="0ms");var n=g.width*i.scale,s=g.height*i.scale;if(g.minX=Math.min(m.slideWidth/2-n/2,0),g.maxX=-g.minX,g.minY=Math.min(m.slideHeight/2-s/2,0),g.maxY=-g.minY,g.touchesCurrent.x=(0<f.length?f[0]:e).pageX,g.touchesCurrent.y=(0<f.length?f[0]:e).pageY,5<Math.max(Math.abs(g.touchesCurrent.x-g.touchesStart.x),Math.abs(g.touchesCurrent.y-g.touchesStart.y))&&(c.allowClick=!1),!g.isMoved&&!o){if(c.isHorizontal()&&(Math.floor(g.minX)===Math.floor(g.startX)&&g.touchesCurrent.x<g.touchesStart.x||Math.floor(g.maxX)===Math.floor(g.startX)&&g.touchesCurrent.x>g.touchesStart.x))return g.isTouched=!1,void _();if(!c.isHorizontal()&&(Math.floor(g.minY)===Math.floor(g.startY)&&g.touchesCurrent.y<g.touchesStart.y||Math.floor(g.maxY)===Math.floor(g.startY)&&g.touchesCurrent.y>g.touchesStart.y))return g.isTouched=!1,void _()}e.cancelable&&e.preventDefault(),e.stopPropagation(),clearTimeout(r),c.touchEventsData.preventTouchMoveFromPointerMove=!0,r=setTimeout(function(){c.destroyed||_()}),g.isMoved=!0;n=(i.scale-p)/(m.maxRatio-c.params.zoom.minRatio),s=m.originX,i=m.originY;g.currentX=g.touchesCurrent.x-g.touchesStart.x+g.startX+n*(g.width-2*s),g.currentY=g.touchesCurrent.y-g.touchesStart.y+g.startY+n*(g.height-2*i),g.currentX<g.minX&&(g.currentX=g.minX+1-Math.pow(g.minX-g.currentX+1,.8)),g.currentX>g.maxX&&(g.currentX=g.maxX-1+Math.pow(g.currentX-g.maxX+1,.8)),g.currentY<g.minY&&(g.currentY=g.minY+1-Math.pow(g.minY-g.currentY+1,.8)),g.currentY>g.maxY&&(g.currentY=g.maxY-1+Math.pow(g.currentY-g.maxY+1,.8)),v.prevPositionX||(v.prevPositionX=g.touchesCurrent.x),v.prevPositionY||(v.prevPositionY=g.touchesCurrent.y),v.prevTime||(v.prevTime=Date.now()),v.x=(g.touchesCurrent.x-v.prevPositionX)/(Date.now()-v.prevTime)/2,v.y=(g.touchesCurrent.y-v.prevPositionY)/(Date.now()-v.prevTime)/2,Math.abs(g.touchesCurrent.x-v.prevPositionX)<2&&(v.x=0),Math.abs(g.touchesCurrent.y-v.prevPositionY)<2&&(v.y=0),v.prevPositionX=g.touchesCurrent.x,v.prevPositionY=g.touchesCurrent.y,v.prevTime=Date.now(),m.imageWrapEl.style.transform="translate3d(".concat(g.currentX,"px, ").concat(g.currentY,"px,0)")}else t&&A(e)}}function M(){var e=c.zoom;m.slideEl&&c.activeIndex!==c.slides.indexOf(m.slideEl)&&(m.imageEl&&(m.imageEl.style.transform="translate3d(0,0,0) scale(1)"),m.imageWrapEl&&(m.imageWrapEl.style.transform="translate3d(0,0,0)"),m.slideEl.classList.remove("".concat(c.params.zoom.zoomedSlideClass)),e.scale=1,p=1,m.slideEl=void 0,m.imageEl=void 0,m.imageWrapEl=void 0,m.originX=0,m.originY=0)}function A(e){if(!(p<=1)&&m.imageWrapEl&&S(e)&&T(e)){var t=u.getComputedStyle(m.imageWrapEl).transform,i=new u.DOMMatrix(t);if(!l)return l=!0,d.x=e.clientX,d.y=e.clientY,g.startX=i.e,g.startY=i.f,g.width=m.imageEl.offsetWidth||m.imageEl.clientWidth,g.height=m.imageEl.offsetHeight||m.imageEl.clientHeight,m.slideWidth=m.slideEl.offsetWidth,void(m.slideHeight=m.slideEl.offsetHeight);var n=(e.clientX-d.x)*h,s=(e.clientY-d.y)*h,a=g.width*p,r=g.height*p,t=m.slideWidth,i=m.slideHeight,t=Math.min(t/2-a/2,0),a=-t,i=Math.min(i/2-r/2,0),r=-i,t=Math.max(Math.min(g.startX+n,a),t),i=Math.max(Math.min(g.startY+s,r),i);m.imageWrapEl.style.transitionDuration="0ms",m.imageWrapEl.style.transform="translate3d(".concat(t,"px, ").concat(i,"px, 0)"),d.x=e.clientX,d.y=e.clientY,g.startX=t,g.startY=i}}function P(e){var t,i,n,s,a,r,o,l=c.zoom,d=c.params.zoom;m.slideEl||(e&&e.target&&(m.slideEl=e.target.closest(".".concat(c.params.slideClass,", swiper-slide"))),m.slideEl||(c.params.virtual&&c.params.virtual.enabled&&c.virtual?m.slideEl=Z(c.slidesEl,".".concat(c.params.slideActiveClass))[0]:m.slideEl=c.slides[c.activeIndex]),s=(s=m.slideEl.querySelector(".".concat(d.containerClass)))&&s.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0],m.imageEl=s,m.imageWrapEl=s?q(m.imageEl,".".concat(d.containerClass))[0]:void 0),m.imageEl&&m.imageWrapEl&&(c.params.cssMode&&(c.wrapperEl.style.overflow="hidden",c.wrapperEl.style.touchAction="none"),m.slideEl.classList.add("".concat(d.zoomedSlideClass)),a=void 0===g.touchesStart.x&&e?(n=e.pageX,e.pageY):(n=g.touchesStart.x,g.touchesStart.y),r="number"==typeof e?e:null,1===p&&r&&(g.touchesStart.x=a=n=void 0,g.touchesStart.y=void 0),o=w(),l.scale=r||o,p=r||o,!e||1===p&&r?i=t=0:(s=m.slideEl.offsetWidth,d=m.slideEl.offsetHeight,o=H(m.slideEl).left+u.scrollX+s/2-n,e=H(m.slideEl).top+u.scrollY+d/2-a,n=m.imageEl.offsetWidth||m.imageEl.clientWidth,a=m.imageEl.offsetHeight||m.imageEl.clientHeight,n=n*l.scale,a=a*l.scale,n=Math.min(s/2-n/2,0),d=Math.min(d/2-a/2,0),(a=-n)<(t=(t=o*l.scale)<n?n:t)&&(t=a),(a=-d)<(i=(i=e*l.scale)<d?d:i)&&(i=a)),r&&1===l.scale&&(m.originX=0,m.originY=0),m.imageWrapEl.style.transitionDuration="300ms",m.imageWrapEl.style.transform="translate3d(".concat(t,"px, ").concat(i,"px,0)"),m.imageEl.style.transitionDuration="300ms",m.imageEl.style.transform="translate3d(0,0,0) scale(".concat(l.scale,")"))}function L(){var e,t=c.zoom,i=c.params.zoom;m.slideEl||(c.params.virtual&&c.params.virtual.enabled&&c.virtual?m.slideEl=Z(c.slidesEl,".".concat(c.params.slideActiveClass))[0]:m.slideEl=c.slides[c.activeIndex],e=(e=m.slideEl.querySelector(".".concat(i.containerClass)))&&e.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0],m.imageEl=e,m.imageWrapEl=e?q(m.imageEl,".".concat(i.containerClass))[0]:void 0),m.imageEl&&m.imageWrapEl&&(c.params.cssMode&&(c.wrapperEl.style.overflow="",c.wrapperEl.style.touchAction=""),t.scale=1,p=1,g.touchesStart.x=void 0,g.touchesStart.y=void 0,m.imageWrapEl.style.transitionDuration="300ms",m.imageWrapEl.style.transform="translate3d(0,0,0)",m.imageEl.style.transitionDuration="300ms",m.imageEl.style.transform="translate3d(0,0,0) scale(1)",m.slideEl.classList.remove("".concat(i.zoomedSlideClass)),m.slideEl=void 0,m.originX=0,m.originY=0,c.params.zoom.panOnMouseMove&&(d={x:0,y:0},l&&(l=!1,g.startX=0,g.startY=0)))}function O(e){var t=c.zoom;t.scale&&1!==t.scale?L():P(e)}function I(){return{passiveListener:!!c.params.passiveListeners&&{passive:!0,capture:!1},activeListenerWithCapture:!c.params.passiveListeners||{passive:!1,capture:!0}}}function D(){var t,e=c.zoom;e.enabled||(e.enabled=!0,e=I(),t=e.passiveListener,e=e.activeListenerWithCapture,c.wrapperEl.addEventListener("pointerdown",x,t),c.wrapperEl.addEventListener("pointermove",E,e),["pointerup","pointercancel","pointerout"].forEach(function(e){c.wrapperEl.addEventListener(e,C,t)}),c.wrapperEl.addEventListener("pointermove",k,e))}function $(){var t,e=c.zoom;e.enabled&&(e.enabled=!1,e=I(),t=e.passiveListener,e=e.activeListenerWithCapture,c.wrapperEl.removeEventListener("pointerdown",x,t),c.wrapperEl.removeEventListener("pointermove",E,e),["pointerup","pointercancel","pointerout"].forEach(function(e){c.wrapperEl.removeEventListener(e,C,t)}),c.wrapperEl.removeEventListener("pointermove",k,e))}Object.defineProperty(c.zoom,"scale",{get:function(){return y},set:function(e){var t,i;y!==e&&(t=m.imageEl,i=m.slideEl,n("zoomChange",e,t,i)),y=e}}),i("init",function(){c.params.zoom.enabled&&D()}),i("destroy",function(){$()}),i("touchStart",function(e,t){var i;c.zoom.enabled&&(i=t,t=c.device,m.imageEl&&(g.isTouched||(t.android&&i.cancelable&&i.preventDefault(),g.isTouched=!0,i=0<f.length?f[0]:i,g.touchesStart.x=i.pageX,g.touchesStart.y=i.pageY)))}),i("touchEnd",function(e,t){c.zoom.enabled&&function(){var e=c.zoom;if(f.length=0,m.imageEl){if(!g.isTouched||!g.isMoved)return g.isTouched=!1,g.isMoved=!1;g.isTouched=!1,g.isMoved=!1;var t=300,i=300,n=v.x*t,s=g.currentX+n,n=v.y*i,n=g.currentY+n;0!==v.x&&(t=Math.abs((s-g.currentX)/v.x)),0!==v.y&&(i=Math.abs((n-g.currentY)/v.y));i=Math.max(t,i);g.currentX=s,g.currentY=n;n=g.width*e.scale,e=g.height*e.scale;g.minX=Math.min(m.slideWidth/2-n/2,0),g.maxX=-g.minX,g.minY=Math.min(m.slideHeight/2-e/2,0),g.maxY=-g.minY,g.currentX=Math.max(Math.min(g.currentX,g.maxX),g.minX),g.currentY=Math.max(Math.min(g.currentY,g.maxY),g.minY),m.imageWrapEl.style.transitionDuration="".concat(i,"ms"),m.imageWrapEl.style.transform="translate3d(".concat(g.currentX,"px, ").concat(g.currentY,"px,0)")}}()}),i("doubleTap",function(e,t){!c.animating&&c.params.zoom.enabled&&c.zoom.enabled&&c.params.zoom.toggle&&O(t)}),i("transitionEnd",function(){c.zoom.enabled&&c.params.zoom.enabled&&M()}),i("slideChange",function(){c.zoom.enabled&&c.params.zoom.enabled&&c.params.cssMode&&M()}),Object.assign(c.zoom,{enable:D,disable:$,in:P,out:L,toggle:O})},function(e){var l=e.swiper,t=e.extendParams,e=e.on;function d(e,t){var i,n,s,a,r,o=function(e,t){for(n=-1,i=e.length;1<i-n;)e[s=i+n>>1]<=t?n=s:i=s;return i};return this.x=e,this.y=t,this.lastIndex=e.length-1,this.interpolate=function(e){return e?(r=o(this.x,e),a=r-1,(e-this.x[a])*(this.y[r]-this.y[a])/(this.x[r]-this.x[a])+this.y[a]):0},this}function i(){l.controller.control&&l.controller.spline&&(l.controller.spline=void 0,delete l.controller.spline)}t({controller:{control:void 0,inverse:!1,by:"slide"}}),l.controller={control:void 0},e("beforeInit",function(){"undefined"!=typeof window&&("string"==typeof l.params.controller.control||l.params.controller.control instanceof HTMLElement)?("string"==typeof l.params.controller.control?_toConsumableArray(document.querySelectorAll(l.params.controller.control)):[l.params.controller.control]).forEach(function(i){var n,e;l.controller.control||(l.controller.control=[]),i&&i.swiper?l.controller.control.push(i.swiper):i&&(n="".concat(l.params.eventsPrefix,"init"),e=function e(t){l.controller.control.push(t.detail[0]),l.update(),i.removeEventListener(n,e)},i.addEventListener(n,e))}):l.controller.control=l.params.controller.control}),e("update",function(){i()}),e("resize",function(){i()}),e("observerUpdate",function(){i()}),e("setTranslate",function(e,t,i){l.controller.control&&!l.controller.control.destroyed&&l.controller.setTranslate(t,i)}),e("setTransition",function(e,t,i){l.controller.control&&!l.controller.control.destroyed&&l.controller.setTransition(t,i)}),Object.assign(l.controller,{setTranslate:function(e,t){var n,s,i=l.controller.control,a=l.constructor;function r(e){var t,i;e.destroyed||(t=l.rtlTranslate?-l.translate:l.translate,"slide"===l.params.controller.by&&(i=e,l.controller.spline=l.params.loop?new d(l.slidesGrid,i.slidesGrid):new d(l.snapGrid,i.snapGrid),s=-l.controller.spline.interpolate(-t)),s&&"container"!==l.params.controller.by||(n=(e.maxTranslate()-e.minTranslate())/(l.maxTranslate()-l.minTranslate()),!Number.isNaN(n)&&Number.isFinite(n)||(n=1),s=(t-l.minTranslate())*n+e.minTranslate()),l.params.controller.inverse&&(s=e.maxTranslate()-s),e.updateProgress(s),e.setTranslate(s,l),e.updateActiveIndex(),e.updateSlidesClasses())}if(Array.isArray(i))for(var o=0;o<i.length;o+=1)i[o]!==t&&i[o]instanceof a&&r(i[o]);else i instanceof a&&t!==i&&r(i)},setTransition:function(t,e){var i,n=l.constructor,s=l.controller.control;function a(e){e.destroyed||(e.setTransition(t,l),0!==t&&(e.transitionStart(),e.params.autoHeight&&C(function(){e.updateAutoHeight()}),b(e.wrapperEl,function(){s&&e.transitionEnd()})))}if(Array.isArray(s))for(i=0;i<s.length;i+=1)s[i]!==e&&s[i]instanceof n&&a(s[i]);else s instanceof n&&e!==s&&a(s)}})},function(e){var r=e.swiper,t=e.extendParams,e=e.on;t({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,containerRole:null,itemRoleDescriptionMessage:null,slideRole:"group",id:null,scrollOnFocus:!0}}),r.a11y={clicked:!1};var s,a,o=null,l=(new Date).getTime();function n(e){var t=o;0!==t.length&&(t.innerHTML="",t.innerHTML=e)}function d(e){(e=M(e)).forEach(function(e){e.setAttribute("tabIndex","0")})}function i(e){(e=M(e)).forEach(function(e){e.setAttribute("tabIndex","-1")})}function c(e,t){(e=M(e)).forEach(function(e){e.setAttribute("role",t)})}function u(e,t){(e=M(e)).forEach(function(e){e.setAttribute("aria-roledescription",t)})}function p(e,t){(e=M(e)).forEach(function(e){e.setAttribute("aria-label",t)})}function h(e){(e=M(e)).forEach(function(e){e.setAttribute("aria-disabled",!0)})}function f(e){(e=M(e)).forEach(function(e){e.setAttribute("aria-disabled",!1)})}function m(e){var t,i;13!==e.keyCode&&32!==e.keyCode||(t=r.params.a11y,i=e.target,r.pagination&&r.pagination.el&&(i===r.pagination.el||r.pagination.el.contains(e.target))&&!e.target.matches(Y(r.params.pagination.bulletClass))||(r.navigation&&r.navigation.prevEl&&r.navigation.nextEl&&(e=M(r.navigation.prevEl),M(r.navigation.nextEl).includes(i)&&(r.isEnd&&!r.params.loop||r.slideNext(),r.isEnd?n(t.lastSlideMessage):n(t.nextSlideMessage)),e.includes(i)&&(r.isBeginning&&!r.params.loop||r.slidePrev(),r.isBeginning?n(t.firstSlideMessage):n(t.prevSlideMessage))),r.pagination&&i.matches(Y(r.params.pagination.bulletClass))&&i.click()))}function g(){return r.pagination&&r.pagination.bullets&&r.pagination.bullets.length}function v(){return g()&&r.params.pagination.clickable}function y(e,t,i){var n;d(e),"BUTTON"!==e.tagName&&(c(e,"button"),e.addEventListener("keydown",m)),p(e,i),n=t,M(e).forEach(function(e){e.setAttribute("aria-controls",n)})}function b(e){a&&a!==e.target&&!a.contains(e.target)&&(s=!0),r.a11y.clicked=!0}function w(){s=!1,requestAnimationFrame(function(){requestAnimationFrame(function(){r.destroyed||(r.a11y.clicked=!1)})})}function S(e){l=(new Date).getTime()}function T(e){var t,i,n;!r.a11y.clicked&&r.params.a11y.scrollOnFocus&&((new Date).getTime()-l<100||(t=e.target.closest(".".concat(r.params.slideClass,", swiper-slide")))&&r.slides.includes(t)&&(a=t,i=r.slides.indexOf(t)===r.activeIndex,n=r.params.watchSlidesProgress&&r.visibleSlides&&r.visibleSlides.includes(t),i||n||e.sourceCapabilities&&e.sourceCapabilities.firesTouchEvents||(r.isHorizontal()?r.el.scrollLeft=0:r.el.scrollTop=0,requestAnimationFrame(function(){s||(r.params.loop?r.slideToLoop(parseInt(t.getAttribute("data-swiper-slide-index")),0):r.slideTo(r.slides.indexOf(t),0),s=!1)}))))}function x(){var i=r.params.a11y;i.itemRoleDescriptionMessage&&u(r.slides,i.itemRoleDescriptionMessage),i.slideRole&&c(r.slides,i.slideRole);var n=r.slides.length;i.slideLabelMessage&&r.slides.forEach(function(e,t){t=r.params.loop?parseInt(e.getAttribute("data-swiper-slide-index"),10):t;p(e,i.slideLabelMessage.replace(/\{\{index\}\}/,t+1).replace(/\{\{slidesLength\}\}/,n))})}function E(){var t=r.params.a11y;r.el.append(o);var e=r.el;t.containerRoleDescriptionMessage&&u(e,t.containerRoleDescriptionMessage),t.containerMessage&&p(e,t.containerMessage),t.containerRole&&c(e,t.containerRole);var i,n,e=r.wrapperEl,s=t.id||e.getAttribute("id")||"swiper-wrapper-".concat("x".repeat(a=void 0===(a=16)?16:a).replace(/x/g,function(){return Math.round(16*Math.random()).toString(16)})),a=r.params.autoplay&&r.params.autoplay.enabled?"off":"polite";i=s,M(e).forEach(function(e){e.setAttribute("id",i)}),n=a,M(e).forEach(function(e){e.setAttribute("aria-live",n)}),x(),a=r.navigation||{},e=a.nextEl,a=a.prevEl,e=M(e),a=M(a),e&&e.forEach(function(e){return y(e,s,t.nextSlideMessage)}),a&&a.forEach(function(e){return y(e,s,t.prevSlideMessage)}),v()&&M(r.pagination.el).forEach(function(e){e.addEventListener("keydown",m)}),L().addEventListener("visibilitychange",S),r.el.addEventListener("focus",T,!0),r.el.addEventListener("focus",T,!0),r.el.addEventListener("pointerdown",b,!0),r.el.addEventListener("pointerup",w,!0)}e("beforeInit",function(){(o=_("span",r.params.a11y.notificationClass)).setAttribute("aria-live","assertive"),o.setAttribute("aria-atomic","true")}),e("afterInit",function(){r.params.a11y.enabled&&E()}),e("slidesLengthChange snapGridLengthChange slidesGridLengthChange",function(){r.params.a11y.enabled&&x()}),e("fromEdge toEdge afterInit lock unlock",function(){var e,t;r.params.a11y.enabled&&(r.params.loop||r.params.rewind||!r.navigation||(e=(t=r.navigation).nextEl,(t=t.prevEl)&&(r.isBeginning?(h(t),i(t)):(f(t),d(t))),e&&(r.isEnd?(h(e),i(e)):(f(e),d(e)))))}),e("paginationUpdate",function(){var t;r.params.a11y.enabled&&(t=r.params.a11y,g()&&r.pagination.bullets.forEach(function(e){r.params.pagination.clickable&&(d(e),r.params.pagination.renderBullet||(c(e,"button"),p(e,t.paginationBulletMessage.replace(/\{\{index\}\}/,k(e)+1)))),e.matches(Y(r.params.pagination.bulletActiveClass))?e.setAttribute("aria-current","true"):e.removeAttribute("aria-current")}))}),e("destroy",function(){r.params.a11y.enabled&&function(){o&&o.remove();var e=(t=r.navigation||{}).nextEl,t=t.prevEl,e=M(e),t=M(t);e&&e.forEach(function(e){return e.removeEventListener("keydown",m)}),t&&t.forEach(function(e){return e.removeEventListener("keydown",m)}),v()&&M(r.pagination.el).forEach(function(e){e.removeEventListener("keydown",m)}),L().removeEventListener("visibilitychange",S),r.el&&"string"!=typeof r.el&&(r.el.removeEventListener("focus",T,!0),r.el.removeEventListener("pointerdown",b,!0),r.el.removeEventListener("pointerup",w,!0))}()})},function(e){var r=e.swiper,t=e.extendParams,e=e.on;function i(e,t){var i,n,s=z();a&&r.params.history.enabled&&(n=r.params.url?new URL(r.params.url):s.location,i=r.virtual&&r.params.virtual.enabled?r.slidesEl.querySelector('[data-swiper-slide-index="'.concat(t,'"]')):r.slides[t],t=o(i.getAttribute("data-history")),0<r.params.history.root.length?("/"===(i=r.params.history.root)[i.length-1]&&(i=i.slice(0,i.length-1)),t="".concat(i,"/").concat(e?"".concat(e,"/"):"").concat(t)):n.pathname.includes(e)||(t="".concat(e?"".concat(e,"/"):"").concat(t)),r.params.history.keepQuery&&(t+=n.search),(n=s.history.state)&&n.value===t||(r.params.history.replaceState?s.history.replaceState({value:t},null,t):s.history.pushState({value:t},null,t)))}function n(){s=l(r.params.url),d(r.params.speed,s.value,!1)}t({history:{enabled:!1,root:"",replaceState:!1,key:"slides",keepQuery:!1}});var a=!1,s={},o=function(e){return e.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,"")},l=function(e){var t=z(),e=e?new URL(e):t.location,t=e.pathname.slice(1).split("/").filter(function(e){return""!==e}),e=t.length;return{key:t[e-2],value:t[e-1]}},d=function(e,t,i){if(t)for(var n=0,s=r.slides.length;n<s;n+=1){var a=r.slides[n];o(a.getAttribute("data-history"))===t&&(a=r.getSlideIndex(a),r.slideTo(a,e,i))}else r.slideTo(0,e,i)};e("init",function(){r.params.history.enabled&&function(){var e=z();if(r.params.history){if(!e.history||!e.history.pushState)return r.params.history.enabled=!1,r.params.hashNavigation.enabled=!0;a=!0,((s=l(r.params.url)).key||s.value)&&d(0,s.value,r.params.runCallbacksOnInit),r.params.history.replaceState||e.addEventListener("popstate",n)}}()}),e("destroy",function(){var e;r.params.history.enabled&&(e=z(),r.params.history.replaceState||e.removeEventListener("popstate",n))}),e("transitionEnd _freeModeNoMomentumRelease",function(){a&&i(r.params.history.key,r.activeIndex)}),e("slideChange",function(){a&&r.params.cssMode&&i(r.params.history.key,r.activeIndex)})},function(e){var n=e.swiper,t=e.extendParams,i=e.emit,e=e.on,s=!1,a=L(),r=z();function o(){i("hashChange");var e=a.location.hash.replace("#",""),t=n.virtual&&n.params.virtual.enabled?n.slidesEl.querySelector('[data-swiper-slide-index="'.concat(n.activeIndex,'"]')):n.slides[n.activeIndex];e!==(t?t.getAttribute("data-hash"):"")&&(void 0===(e=n.params.hashNavigation.getSlideIndex(n,e))||Number.isNaN(e)||n.slideTo(e))}function l(){var e;s&&n.params.hashNavigation.enabled&&(e=(e=n.virtual&&n.params.virtual.enabled?n.slidesEl.querySelector('[data-swiper-slide-index="'.concat(n.activeIndex,'"]')):n.slides[n.activeIndex])?e.getAttribute("data-hash")||e.getAttribute("data-history"):"",n.params.hashNavigation.replaceState&&r.history&&r.history.replaceState?r.history.replaceState(null,null,"#".concat(e)||""):a.location.hash=e||"",i("hashSet"))}t({hashNavigation:{enabled:!1,replaceState:!1,watchState:!1,getSlideIndex:function(e,t){if(n.virtual&&n.params.virtual.enabled){var i=n.slides.find(function(e){return e.getAttribute("data-hash")===t});return i?parseInt(i.getAttribute("data-swiper-slide-index"),10):0}return n.getSlideIndex(Z(n.slidesEl,".".concat(n.params.slideClass,'[data-hash="').concat(t,'"], swiper-slide[data-hash="').concat(t,'"]'))[0])}}}),e("init",function(){var e;n.params.hashNavigation.enabled&&(!n.params.hashNavigation.enabled||n.params.history&&n.params.history.enabled||(s=!0,(e=a.location.hash.replace("#",""))&&(e=n.params.hashNavigation.getSlideIndex(n,e),n.slideTo(e||0,0,n.params.runCallbacksOnInit,!0)),n.params.hashNavigation.watchState&&r.addEventListener("hashchange",o)))}),e("destroy",function(){n.params.hashNavigation.enabled&&n.params.hashNavigation.watchState&&r.removeEventListener("hashchange",o)}),e("transitionEnd _freeModeNoMomentumRelease",function(){s&&l()}),e("slideChange",function(){s&&n.params.cssMode&&l()})},function(e){var a,r,o=e.swiper,t=e.extendParams,i=e.on,l=e.emit,e=e.params;t({autoplay:{enabled:!(o.autoplay={running:!1,paused:!1,timeLeft:0}),delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});var d,n,s,c,u,p,h,f,m=e&&e.autoplay?e.autoplay.delay:3e3,g=e&&e.autoplay?e.autoplay.delay:3e3,v=(new Date).getTime();function y(e){o&&!o.destroyed&&o.wrapperEl&&e.target===o.wrapperEl&&(o.wrapperEl.removeEventListener("transitionend",y),f||e.detail&&e.detail.bySwiperTouchMove||M())}function b(e){if(!o.destroyed&&o.autoplay.running){cancelAnimationFrame(r),C();var t=void 0===e?o.params.autoplay.delay:e;m=o.params.autoplay.delay,g=o.params.autoplay.delay;var i=_();!Number.isNaN(i)&&0<i&&void 0===e&&(g=m=t=i),d=t;var n=o.params.speed,s=function(){o&&!o.destroyed&&(o.params.autoplay.reverseDirection?!o.isBeginning||o.params.loop||o.params.rewind?(o.slidePrev(n,!0,!0),l("autoplay")):o.params.autoplay.stopOnLastSlide||(o.slideTo(o.slides.length-1,n,!0,!0),l("autoplay")):!o.isEnd||o.params.loop||o.params.rewind?(o.slideNext(n,!0,!0),l("autoplay")):o.params.autoplay.stopOnLastSlide||(o.slideTo(0,n,!0,!0),l("autoplay")),o.params.cssMode&&(v=(new Date).getTime(),requestAnimationFrame(function(){b()})))};return 0<t?(clearTimeout(a),a=setTimeout(function(){s()},t)):requestAnimationFrame(function(){s()}),t}}function w(){v=(new Date).getTime(),o.autoplay.running=!0,b(),l("autoplayStart")}function S(){o.autoplay.running=!1,clearTimeout(a),cancelAnimationFrame(r),l("autoplayStop")}function T(){var e;!o.destroyed&&o.autoplay.running&&("hidden"===(e=L()).visibilityState&&k(h=!0),"visible"===e.visibilityState&&M())}function x(e){"mouse"===e.pointerType&&(f=h=!0,o.animating||o.autoplay.paused||k(!0))}function E(e){"mouse"===e.pointerType&&(f=!1,o.autoplay.paused&&M())}var C=function e(){var t;!o.destroyed&&o.autoplay.running&&(o.autoplay.paused?n=!0:n&&(g=d,n=!1),t=o.autoplay.paused?d:v+g-(new Date).getTime(),o.autoplay.timeLeft=t,l("autoplayTimeLeft",t,t/m),r=requestAnimationFrame(function(){e()}))},_=function(){var e=o.virtual&&o.params.virtual.enabled?o.slides.find(function(e){return e.classList.contains("swiper-slide-active")}):o.slides[o.activeIndex];if(e)return parseInt(e.getAttribute("data-swiper-autoplay"),10)},k=function(e,t){if(!o.destroyed&&o.autoplay.running){clearTimeout(a),e||(h=!0);e=function(){l("autoplayPause"),o.params.autoplay.waitForTransition?o.wrapperEl.addEventListener("transitionend",y):M()};if(o.autoplay.paused=!0,t)return p&&(d=o.params.autoplay.delay),p=!1,void e();t=d||o.params.autoplay.delay;d=t-((new Date).getTime()-v),o.isEnd&&d<0&&!o.params.loop||(d<0&&(d=0),e())}},M=function(){o.isEnd&&d<0&&!o.params.loop||o.destroyed||!o.autoplay.running||(v=(new Date).getTime(),h?(h=!1,b(d)):b(),o.autoplay.paused=!1,l("autoplayResume"))};i("init",function(){o.params.autoplay.enabled&&(o.params.autoplay.pauseOnMouseEnter&&(o.el.addEventListener("pointerenter",x),o.el.addEventListener("pointerleave",E)),L().addEventListener("visibilitychange",T),w())}),i("destroy",function(){o.el&&"string"!=typeof o.el&&(o.el.removeEventListener("pointerenter",x),o.el.removeEventListener("pointerleave",E)),L().removeEventListener("visibilitychange",T),o.autoplay.running&&S()}),i("_freeModeStaticRelease",function(){(c||h)&&M()}),i("_freeModeNoMomentumRelease",function(){o.params.autoplay.disableOnInteraction?S():k(!0,!0)}),i("beforeTransitionStart",function(e,t,i){!o.destroyed&&o.autoplay.running&&(i||!o.params.autoplay.disableOnInteraction?k(!0,!0):S())}),i("sliderFirstMove",function(){!o.destroyed&&o.autoplay.running&&(o.params.autoplay.disableOnInteraction?S():(h=c=!(s=!0),u=setTimeout(function(){k(c=h=!0)},200)))}),i("touchEnd",function(){!o.destroyed&&o.autoplay.running&&s&&(clearTimeout(u),clearTimeout(a),s=c=(o.params.autoplay.disableOnInteraction||c&&o.params.cssMode&&M(),!1))}),i("slideChange",function(){!o.destroyed&&o.autoplay.running&&(p=!0)}),Object.assign(o.autoplay,{start:w,stop:S,pause:k,resume:M})},function(e){var p=e.swiper,t=e.extendParams,e=e.on;t({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}});var i=!1,n=!1;function s(){var e,t,i=p.thumbs.swiper;i&&!i.destroyed&&(t=i.clickedIndex,(e=i.clickedSlide)&&e.classList.contains(p.params.thumbs.slideThumbActiveClass)||null!=t&&(t=i.params.loop?parseInt(i.clickedSlide.getAttribute("data-swiper-slide-index"),10):t,p.params.loop?p.slideToLoop(t):p.slideTo(t)))}function a(){var e=p.params.thumbs;if(i)return!1;i=!0;var t=p.constructor;return e.swiper instanceof t?(p.thumbs.swiper=e.swiper,Object.assign(p.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(p.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1}),p.thumbs.swiper.update()):h(e.swiper)&&(e=Object.assign({},e.swiper),Object.assign(e,{watchSlidesProgress:!0,slideToClickedSlide:!1}),p.thumbs.swiper=new t(e),n=!0),p.thumbs.swiper.el.classList.add(p.params.thumbs.thumbsContainerClass),p.thumbs.swiper.on("tap",s),!0}function r(e){var t=p.thumbs.swiper;if(t&&!t.destroyed){var i="auto"===t.params.slidesPerView?t.slidesPerViewDynamic():t.params.slidesPerView,n=1,s=p.params.thumbs.slideThumbActiveClass;if(1<p.params.slidesPerView&&!p.params.centeredSlides&&(n=p.params.slidesPerView),p.params.thumbs.multipleActiveThumbs||(n=1),n=Math.floor(n),t.slides.forEach(function(e){return e.classList.remove(s)}),t.params.loop||t.params.virtual&&t.params.virtual.enabled)for(var a=0;a<n;a+=1)Z(t.slidesEl,'[data-swiper-slide-index="'.concat(p.realIndex+a,'"]')).forEach(function(e){e.classList.add(s)});else for(var r=0;r<n;r+=1)t.slides[p.realIndex+r]&&t.slides[p.realIndex+r].classList.add(s);var o,l,d,c=p.params.thumbs.autoScrollOffset,u=c&&!t.params.loop;p.realIndex===t.realIndex&&!u||(o=t.activeIndex,d=t.params.loop?(d=t.slides.find(function(e){return e.getAttribute("data-swiper-slide-index")==="".concat(p.realIndex)}),l=t.slides.indexOf(d),p.activeIndex>p.previousIndex?"next":"prev"):(l=p.realIndex)>p.previousIndex?"next":"prev",u&&(l+="next"===d?c:-1*c),t.visibleSlidesIndexes&&t.visibleSlidesIndexes.indexOf(l)<0&&(t.params.centeredSlides?l=o<l?l-Math.floor(i/2)+1:l+Math.floor(i/2)-1:o<l&&t.params.slidesPerGroup,t.slideTo(l,e?0:void 0)))}}p.thumbs={swiper:null},e("beforeInit",function(){var t,i,e,s=p.params.thumbs;s&&s.swiper&&("string"==typeof s.swiper||s.swiper instanceof HTMLElement?(t=L(),i=function(){var i,e,n="string"==typeof s.swiper?t.querySelector(s.swiper):s.swiper;return n&&n.swiper?(s.swiper=n.swiper,a(),r(!0)):n&&(i="".concat(p.params.eventsPrefix,"init"),e=function e(t){s.swiper=t.detail[0],n.removeEventListener(i,e),a(),r(!0),s.swiper.update(),p.update()},n.addEventListener(i,e)),n},e=function e(){p.destroyed||i()||requestAnimationFrame(e)},requestAnimationFrame(e)):(a(),r(!0)))}),e("slideChange update resize observerUpdate",function(){r()}),e("setTransition",function(e,t){var i=p.thumbs.swiper;i&&!i.destroyed&&i.setTransition(t)}),e("beforeDestroy",function(){var e=p.thumbs.swiper;e&&!e.destroyed&&n&&e.destroy()}),Object.assign(p.thumbs,{init:a,update:r})},function(e){var m=e.swiper,t=e.extendParams,g=e.emit,v=e.once;t({freeMode:{enabled:!1,momentum:!0,momentumRatio:1,momentumBounce:!0,momentumBounceRatio:1,momentumVelocityRatio:1,sticky:!1,minimumVelocity:.02}}),Object.assign(m,{freeMode:{onTouchStart:function(){var e;m.params.cssMode||(e=m.getTranslate(),m.setTranslate(e),m.setTransition(0),m.touchEventsData.velocities.length=0,m.freeMode.onTouchEnd({currentPos:m.rtl?m.translate:-m.translate}))},onTouchMove:function(){var e,t;m.params.cssMode||(e=m.touchEventsData,t=m.touches,0===e.velocities.length&&e.velocities.push({position:t[m.isHorizontal()?"startX":"startY"],time:e.touchStartTime}),e.velocities.push({position:t[m.isHorizontal()?"currentX":"currentY"],time:y()}))},onTouchEnd:function(e){var t=e.currentPos;if(!m.params.cssMode){var i=m.params,n=m.wrapperEl,s=m.rtlTranslate,a=m.snapGrid,r=m.touchEventsData,e=y()-r.touchStartTime;if(t<-m.minTranslate())m.slideTo(m.activeIndex);else if(t>-m.maxTranslate())m.slides.length<a.length?m.slideTo(a.length-1):m.slideTo(m.slides.length-1);else{if(i.freeMode.momentum){1<r.velocities.length?(p=r.velocities.pop(),l=r.velocities.pop(),o=p.position-l.position,l=p.time-l.time,m.velocity=o/l,m.velocity/=2,Math.abs(m.velocity)<i.freeMode.minimumVelocity&&(m.velocity=0),(150<l||300<y()-p.time)&&(m.velocity=0)):m.velocity=0,m.velocity*=i.freeMode.momentumVelocityRatio,r.velocities.length=0;var o=1e3*i.freeMode.momentumRatio,l=m.velocity*o,d=m.translate+l;s&&(d=-d);var c,u,p=!1,l=20*Math.abs(m.velocity)*i.freeMode.momentumBounceRatio;if(d<m.maxTranslate())i.freeMode.momentumBounce?(d+m.maxTranslate()<-l&&(d=m.maxTranslate()-l),c=m.maxTranslate(),r.allowMomentumBounce=p=!0):d=m.maxTranslate(),i.loop&&i.centeredSlides&&(u=!0);else if(d>m.minTranslate())i.freeMode.momentumBounce?(d-m.minTranslate()>l&&(d=m.minTranslate()+l),c=m.minTranslate(),r.allowMomentumBounce=p=!0):d=m.minTranslate(),i.loop&&i.centeredSlides&&(u=!0);else if(i.freeMode.sticky){for(var h,f=0;f<a.length;f+=1)if(a[f]>-d){h=f;break}d=-(d=Math.abs(a[h]-d)<Math.abs(a[h-1]-d)||"next"===m.swipeDirection?a[h]:a[h-1])}if(u&&v("transitionEnd",function(){m.loopFix()}),0!==m.velocity)o=s?Math.abs((-d-m.translate)/m.velocity):Math.abs((d-m.translate)/m.velocity),i.freeMode.sticky&&(o=(u=Math.abs((s?-d:d)-m.translate))<(s=m.slidesSizesGrid[m.activeIndex])?i.speed:u<2*s?1.5*i.speed:2.5*i.speed);else if(i.freeMode.sticky)return void m.slideToClosest();i.freeMode.momentumBounce&&p?(m.updateProgress(c),m.setTransition(o),m.setTranslate(d),m.transitionStart(!0,m.swipeDirection),m.animating=!0,b(n,function(){m&&!m.destroyed&&r.allowMomentumBounce&&(g("momentumBounce"),m.setTransition(i.speed),setTimeout(function(){m.setTranslate(c),b(n,function(){m&&!m.destroyed&&m.transitionEnd()})},0))})):m.velocity?(g("_freeModeNoMomentumRelease"),m.updateProgress(d),m.setTransition(o),m.setTranslate(d),m.transitionStart(!0,m.swipeDirection),m.animating||(m.animating=!0,b(n,function(){m&&!m.destroyed&&m.transitionEnd()}))):m.updateProgress(d),m.updateActiveIndex(),m.updateSlidesClasses()}else{if(i.freeMode.sticky)return void m.slideToClosest();i.freeMode&&g("_freeModeNoMomentumRelease")}(!i.freeMode.momentum||e>=i.longSwipesMs)&&(g("_freeModeStaticRelease"),m.updateProgress(),m.updateActiveIndex(),m.updateSlidesClasses())}}}}})},function(e){var d,c,u,n,p=e.swiper,t=e.extendParams,e=e.on;function h(){var e=p.params.spaceBetween;return"string"==typeof e&&0<=e.indexOf("%")?e=parseFloat(e.replace("%",""))/100*p.size:"string"==typeof e&&(e=parseFloat(e)),e}t({grid:{rows:1,fill:"column"}}),e("init",function(){n=p.params.grid&&1<p.params.grid.rows}),e("update",function(){var e=p.params,t=p.el,i=e.grid&&1<e.grid.rows;n&&!i?(t.classList.remove("".concat(e.containerModifierClass,"grid"),"".concat(e.containerModifierClass,"grid-column")),u=1,p.emitContainerClasses()):!n&&i&&(t.classList.add("".concat(e.containerModifierClass,"grid")),"column"===e.grid.fill&&t.classList.add("".concat(e.containerModifierClass,"grid-column")),p.emitContainerClasses()),n=i}),p.grid={initSlides:function(e){var t=p.params.slidesPerView,i=p.params.grid,n=i.rows,i=i.fill,e=(p.virtual&&p.params.virtual.enabled?p.virtual.slides:e).length;u=Math.floor(e/n),d=Math.floor(e/n)===e/n?e:Math.ceil(e/n)*n,"auto"!==t&&"row"===i&&(d=Math.max(d,t*n)),c=d/n},unsetSlides:function(){p.slides&&p.slides.forEach(function(e){e.swiperSlideGridSet&&(e.style.height="",e.style[p.getDirectionLabel("margin-top")]="")})},updateSlide:function(e,t,i){var n,s,a=p.params.slidesPerGroup,r=h(),o=p.params.grid,l=o.rows,o=o.fill,i=(p.virtual&&p.params.virtual.enabled?p.virtual.slides:i).length;"row"===o&&1<a?(n=e-l*a*(s=Math.floor(e/(a*l))),i=0===s?a:Math.min(Math.ceil((i-s*l*a)/l),a),a=(s=n-(n=Math.floor(n/i))*i+s*a)+n*d/l,t.style.order=a):"column"===o?(n=e-(s=Math.floor(e/l))*l,(u<s||s===u&&n===l-1)&&l<=(n+=1)&&(n=0,s+=1)):s=e-(n=Math.floor(e/c))*c,t.row=n,t.column=s,t.style.height="calc((100% - ".concat((l-1)*r,"px) / ").concat(l,")"),t.style[p.getDirectionLabel("margin-top")]=0!==n?r&&"".concat(r,"px"):"",t.swiperSlideGridSet=!0},updateWrapperSize:function(e,t){var i=p.params,n=i.centeredSlides,s=i.roundLengths,a=h(),i=p.params.grid.rows;if(p.virtualSize=(e+a)*d,p.virtualSize=Math.ceil(p.virtualSize/i)-a,p.params.cssMode||(p.wrapperEl.style[p.getDirectionLabel("width")]="".concat(p.virtualSize+a,"px")),n){for(var r=[],o=0;o<t.length;o+=1){var l=t[o];s&&(l=Math.floor(l)),t[o]<p.virtualSize+t[0]&&r.push(l)}t.splice(0,t.length),t.push.apply(t,r)}}}},function(e){e=e.swiper,Object.assign(e,{appendSlide:W.bind(e),prependSlide:V.bind(e),addSlide:function(e,t){var i=this,n=i.params,s=i.activeIndex,a=i.slidesEl,r=s;n.loop&&(r-=i.loopedSlides,i.loopDestroy(),i.recalcSlides());var o=i.slides.length;if(e<=0)i.prependSlide(t);else if(o<=e)i.appendSlide(t);else{for(var s=e<r?r+1:r,l=[],d=o-1;e<=d;--d){var c=i.slides[d];c.remove(),l.unshift(c)}if("object"===_typeof(t)&&"length"in t){for(var u=0;u<t.length;u+=1)t[u]&&a.append(t[u]);s=e<r?r+t.length:r}else a.append(t);for(var p=0;p<l.length;p+=1)a.append(l[p]);i.recalcSlides(),n.loop&&i.loopCreate(),n.observer&&!i.isElement||i.update(),n.loop?i.slideTo(s+i.loopedSlides,0,!1):i.slideTo(s,0,!1)}}.bind(e),removeSlide:function(e){var t=this,i=t.params,n=t.activeIndex;i.loop&&(n-=t.loopedSlides,t.loopDestroy());var s,a=n;if("object"===_typeof(e)&&"length"in e){for(var r=0;r<e.length;r+=1)s=e[r],t.slides[s]&&t.slides[s].remove(),s<a&&--a;a=Math.max(a,0)}else t.slides[s=e]&&t.slides[s].remove(),s<a&&--a,a=Math.max(a,0);t.recalcSlides(),i.loop&&t.loopCreate(),i.observer&&!t.isElement||t.update(),i.loop?t.slideTo(a+t.loopedSlides,0,!1):t.slideTo(a,0,!1)}.bind(e),removeAllSlides:function(){for(var e=[],t=0;t<this.slides.length;t+=1)e.push(t);this.removeSlide(e)}.bind(e)})},function(e){var r=e.swiper,t=e.extendParams,e=e.on;t({fadeEffect:{crossFade:!1}}),G({effect:"fade",swiper:r,on:e,setTranslate:function(){for(var e=r.slides,t=(r.params.fadeEffect,0);t<e.length;t+=1){var i=r.slides[t],n=-i.swiperSlideOffset;r.params.virtualTranslate||(n-=r.translate);var s=0;r.isHorizontal()||(s=n,n=0);var a=r.params.fadeEffect.crossFade?Math.max(1-Math.abs(i.progress),0):1+Math.min(Math.max(i.progress,-1),0),i=U(0,i);i.style.opacity=a,i.style.transform="translate3d(".concat(n,"px, ").concat(s,"px, 0px)")}},setTransition:function(t){var e=r.slides.map(o);e.forEach(function(e){e.style.transitionDuration="".concat(t,"ms")}),Q({swiper:r,duration:t,transformElements:e,allSlides:!0})},overwriteParams:function(){return{slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!r.params.cssMode}}})},function(e){var E=e.swiper,t=e.extendParams,e=e.on;function C(e,t,i){var n=i?e.querySelector(".swiper-slide-shadow-left"):e.querySelector(".swiper-slide-shadow-top"),s=i?e.querySelector(".swiper-slide-shadow-right"):e.querySelector(".swiper-slide-shadow-bottom");n||(n=_("div","swiper-slide-shadow-cube swiper-slide-shadow-".concat(i?"left":"top").split(" ")),e.append(n)),s||(s=_("div","swiper-slide-shadow-cube swiper-slide-shadow-".concat(i?"right":"bottom").split(" ")),e.append(s)),n&&(n.style.opacity=Math.max(-t,0)),s&&(s.style.opacity=Math.max(t,0))}t({cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}}),G({effect:"cube",swiper:E,on:e,setTranslate:function(){var e,t=E.el,i=E.wrapperEl,n=E.slides,s=E.width,a=E.height,r=E.rtlTranslate,o=E.size,l=E.browser,d=A(E),c=E.params.cubeEffect,u=E.isHorizontal(),p=E.virtual&&E.params.virtual.enabled,h=0;c.shadow&&(u?((e=E.wrapperEl.querySelector(".swiper-cube-shadow"))||(e=_("div","swiper-cube-shadow"),E.wrapperEl.append(e)),e.style.height="".concat(s,"px")):(e=t.querySelector(".swiper-cube-shadow"))||(e=_("div","swiper-cube-shadow"),t.append(e)));for(var f,m=0;m<n.length;m+=1){var g=n[m],v=m,y=90*(v=p?parseInt(g.getAttribute("data-swiper-slide-index"),10):v),b=Math.floor(y/360);r&&(y=-y,b=Math.floor(-y/360));var w=Math.max(Math.min(g.progress,1),-1),S=0,T=0,x=0;v%4==0?(S=4*-b*o,x=0):(v-1)%4==0?(S=0,x=4*-b*o):(v-2)%4==0?(S=o+4*b*o,x=o):(v-3)%4==0&&(S=-o,x=3*o+4*o*b),r&&(S=-S),u||(T=S,S=0);x="rotateX(".concat(d(u?0:-y),"deg) rotateY(").concat(d(u?y:0),"deg) translate3d(").concat(S,"px, ").concat(T,"px, ").concat(x,"px)");w<=1&&-1<w&&(h=r?90*-v-90*w:90*v+90*w),g.style.transform=x,c.slideShadows&&C(g,w,u)}i.style.transformOrigin="50% 50% -".concat(o/2,"px"),i.style["-webkit-transform-origin"]="50% 50% -".concat(o/2,"px"),c.shadow&&(u?e.style.transform="translate3d(0px, ".concat(s/2+c.shadowOffset,"px, ").concat(-s/2,"px) rotateX(89.99deg) rotateZ(0deg) scale(").concat(c.shadowScale,")"):(f=Math.abs(h)-90*Math.floor(Math.abs(h)/90),t=1.5-(Math.sin(2*f*Math.PI/360)/2+Math.cos(2*f*Math.PI/360)/2),s=c.shadowScale,f=c.shadowScale/t,t=c.shadowOffset,e.style.transform="scale3d(".concat(s,", 1, ").concat(f,") translate3d(0px, ").concat(a/2+t,"px, ").concat(-a/2/f,"px) rotateX(-89.99deg)")));l=(l.isSafari||l.isWebView)&&l.needPerspectiveFix?-o/2:0;i.style.transform="translate3d(0px,0,".concat(l,"px) rotateX(").concat(d(E.isHorizontal()?0:h),"deg) rotateY(").concat(d(E.isHorizontal()?-h:0),"deg)"),i.style.setProperty("--swiper-cube-translate-z","".concat(l,"px"))},setTransition:function(t){var e=E.el;E.slides.forEach(function(e){e.style.transitionDuration="".concat(t,"ms"),e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(function(e){e.style.transitionDuration="".concat(t,"ms")})}),!E.params.cubeEffect.shadow||E.isHorizontal()||(e=e.querySelector(".swiper-cube-shadow"))&&(e.style.transitionDuration="".concat(t,"ms"))},recreateShadows:function(){var i=E.isHorizontal();E.slides.forEach(function(e){var t=Math.max(Math.min(e.progress,1),-1);C(e,t,i)})},getEffectParams:function(){return E.params.cubeEffect},perspective:function(){return!0},overwriteParams:function(){return{slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0}}})},function(e){var u=e.swiper,t=e.extendParams,e=e.on;function p(e,t){var i=u.isHorizontal()?e.querySelector(".swiper-slide-shadow-left"):e.querySelector(".swiper-slide-shadow-top"),n=u.isHorizontal()?e.querySelector(".swiper-slide-shadow-right"):e.querySelector(".swiper-slide-shadow-bottom"),i=i||te("flip",e,u.isHorizontal()?"left":"top"),n=n||te("flip",e,u.isHorizontal()?"right":"bottom");i&&(i.style.opacity=Math.max(-t,0)),n&&(n.style.opacity=Math.max(t,0))}t({flipEffect:{slideShadows:!0,limitRotation:!0}}),G({effect:"flip",swiper:u,on:e,setTranslate:function(){for(var e=u.slides,t=u.rtlTranslate,i=u.params.flipEffect,n=A(u),s=0;s<e.length;s+=1){var a=e[s],r=a.progress;u.params.flipEffect.limitRotation&&(r=Math.max(Math.min(a.progress,1),-1));var o=a.swiperSlideOffset,l=-180*r,d=0,c=u.params.cssMode?-o-u.translate:-o,o=0;u.isHorizontal()?t&&(l=-l):(o=c,d=-l,l=c=0),a.style.zIndex=-Math.abs(Math.round(r))+e.length,i.slideShadows&&p(a,r);l="translate3d(".concat(c,"px, ").concat(o,"px, 0px) rotateX(").concat(n(d),"deg) rotateY(").concat(n(l),"deg)");U(0,a).style.transform=l}},setTransition:function(t){var e=u.slides.map(o);e.forEach(function(e){e.style.transitionDuration="".concat(t,"ms"),e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(function(e){e.style.transitionDuration="".concat(t,"ms")})}),Q({swiper:u,duration:t,transformElements:e})},recreateShadows:function(){u.params.flipEffect,u.slides.forEach(function(e){var t=e.progress;u.params.flipEffect.limitRotation&&(t=Math.max(Math.min(e.progress,1),-1)),p(e,t)})},getEffectParams:function(){return u.params.flipEffect},perspective:function(){return!0},overwriteParams:function(){return{slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!u.params.cssMode}}})},function(e){var S=e.swiper,t=e.extendParams,e=e.on;t({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}}),G({effect:"coverflow",swiper:S,on:e,setTranslate:function(){for(var e=S.width,t=S.height,i=S.slides,n=S.slidesSizesGrid,s=S.params.coverflowEffect,a=S.isHorizontal(),r=S.translate,o=a?e/2-r:t/2-r,l=a?s.rotate:-s.rotate,d=s.depth,c=A(S),u=0,p=i.length;u<p;u+=1){var h=i[u],f=n[u],m=(o-h.swiperSlideOffset-f/2)/f,g="function"==typeof s.modifier?s.modifier(m):m*s.modifier,v=a?l*g:0,y=a?0:l*g,b=-d*Math.abs(g),w=s.stretch;"string"==typeof w&&-1!==w.indexOf("%")&&(w=parseFloat(s.stretch)/100*f);m=a?0:w*g,f=a?w*g:0,w=1-(1-s.scale)*Math.abs(g);Math.abs(f)<.001&&(f=0),Math.abs(m)<.001&&(m=0),Math.abs(b)<.001&&(b=0),Math.abs(v)<.001&&(v=0),Math.abs(y)<.001&&(y=0),Math.abs(w)<.001&&(w=0);v="translate3d(".concat(f,"px,").concat(m,"px,").concat(b,"px)  rotateX(").concat(c(y),"deg) rotateY(").concat(c(v),"deg) scale(").concat(w,")");U(0,h).style.transform=v,h.style.zIndex=1-Math.abs(Math.round(g)),s.slideShadows&&(w=a?h.querySelector(".swiper-slide-shadow-left"):h.querySelector(".swiper-slide-shadow-top"),v=a?h.querySelector(".swiper-slide-shadow-right"):h.querySelector(".swiper-slide-shadow-bottom"),w=w||te("coverflow",h,a?"left":"top"),v=v||te("coverflow",h,a?"right":"bottom"),w&&(w.style.opacity=0<g?g:0),v&&(v.style.opacity=0<-g?-g:0))}},setTransition:function(t){S.slides.map(o).forEach(function(e){e.style.transitionDuration="".concat(t,"ms"),e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(function(e){e.style.transitionDuration="".concat(t,"ms")})})},perspective:function(){return!0},overwriteParams:function(){return{watchSlidesProgress:!0}}})},function(e){var m=e.swiper,t=e.extendParams,e=e.on;t({creativeEffect:{limitProgress:1,shadowPerProgress:!1,progressMultiplier:1,perspective:!0,prev:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1},next:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1}}}),G({effect:"creative",swiper:m,on:e,setTranslate:function(){var c=m.slides,e=m.wrapperEl,t=m.slidesSizesGrid,u=m.params.creativeEffect,p=u.progressMultiplier,h=m.params.centeredSlides,f=A(m);h&&(t=t[0]/2-m.params.slidesOffsetBefore||0,e.style.transform="translateX(calc(50% - ".concat(t,"px))"));for(var i=function(e){var t=c[e],i=t.progress,n=Math.min(Math.max(t.progress,-u.limitProgress),u.limitProgress),s=n;h||(s=Math.min(Math.max(t.originalProgress,-u.limitProgress),u.limitProgress));var a=t.swiperSlideOffset,r=[m.params.cssMode?-a-m.translate:-a,0,0],o=[0,0,0],l=!1;m.isHorizontal()||(r[1]=r[0],r[0]=0);var d={translate:[0,0,0],rotate:[0,0,0],scale:1,opacity:1};n<0?(d=u.next,l=!0):0<n&&(d=u.prev,l=!0),r.forEach(function(e,t){r[t]="calc(".concat(e,"px + (").concat("string"==typeof(t=d.translate[t])?t:"".concat(t,"px")," * ").concat(Math.abs(n*p),"))")}),o.forEach(function(e,t){var i=d.rotate[t]*Math.abs(n*p);o[t]=i}),t.style.zIndex=-Math.abs(Math.round(i))+c.length;e=r.join(", "),a="rotateX(".concat(f(o[0]),"deg) rotateY(").concat(f(o[1]),"deg) rotateZ(").concat(f(o[2]),"deg)"),i="scale(".concat(s<0?1+(1-d.scale)*s*p:1-(1-d.scale)*s*p,")"),s=s<0?1+(1-d.opacity)*s*p:1-(1-d.opacity)*s*p,a="translate3d(".concat(e,") ").concat(a," ").concat(i);!(l&&d.shadow||!l)||(i=!(i=t.querySelector(".swiper-slide-shadow"))&&d.shadow?te("creative",t):i)&&(l=u.shadowPerProgress?n*(1/u.limitProgress):n,i.style.opacity=Math.min(Math.max(Math.abs(l),0),1));t=U(0,t);t.style.transform=a,t.style.opacity=s,d.origin&&(t.style.transformOrigin=d.origin)},n=0;n<c.length;n+=1)i(n)},setTransition:function(t){var e=m.slides.map(o);e.forEach(function(e){e.style.transitionDuration="".concat(t,"ms"),e.querySelectorAll(".swiper-slide-shadow").forEach(function(e){e.style.transitionDuration="".concat(t,"ms")})}),Q({swiper:m,duration:t,transformElements:e,allSlides:!0})},perspective:function(){return m.params.creativeEffect.perspective},overwriteParams:function(){return{watchSlidesProgress:!0,virtualTranslate:!m.params.cssMode}}})},function(e){var w=e.swiper,t=e.extendParams,e=e.on;t({cardsEffect:{slideShadows:!0,rotate:!0,perSlideRotate:2,perSlideOffset:8}}),G({effect:"cards",swiper:w,on:e,setTranslate:function(){for(var e=w.slides,t=w.activeIndex,i=w.rtlTranslate,n=w.params.cardsEffect,s=w.touchEventsData,a=s.startTranslate,r=s.isTouched,o=i?-w.translate:w.translate,l=0;l<e.length;l+=1){var d=e[l],c=d.progress,u=Math.min(Math.max(c,-4),4),p=d.swiperSlideOffset;w.params.centeredSlides&&!w.params.cssMode&&(w.wrapperEl.style.transform="translateX(".concat(w.minTranslate(),"px)")),w.params.centeredSlides&&w.params.cssMode&&(p-=e[0].swiperSlideOffset);var h=w.params.cssMode?-p-w.translate:-p,f=0,m=-100*Math.abs(u),g=1,v=-n.perSlideRotate*u,y=n.perSlideOffset-.75*Math.abs(u),b=w.virtual&&w.params.virtual.enabled?w.virtual.from+l:l,p=(b===t||b===t-1)&&0<u&&u<1&&(r||w.params.cssMode)&&o<a,b=(b===t||b===t+1)&&u<0&&-1<u&&(r||w.params.cssMode)&&a<o;(p||b)&&(v+=-28*u*(b=Math.pow(1-Math.abs((Math.abs(u)-.5)/.5),.5)),g+=-.5*b,y+=96*b,f="".concat(-25*b*Math.abs(u),"%")),h=u<0?"calc(".concat(h,"px ").concat(i?"-":"+"," (").concat(y*Math.abs(u),"%))"):0<u?"calc(".concat(h,"px ").concat(i?"-":"+"," (-").concat(y*Math.abs(u),"%))"):"".concat(h,"px"),w.isHorizontal()||(y=f,f=h,h=y);g="".concat(u<0?1+(1-g)*u:1-(1-g)*u),v="\n        translate3d(".concat(h,", ").concat(f,", ").concat(m,"px)\n        rotateZ(").concat(n.rotate?i?-v:v:0,"deg)\n        scale(").concat(g,")\n      ");!n.slideShadows||(g=(g=d.querySelector(".swiper-slide-shadow"))||te("cards",d))&&(g.style.opacity=Math.min(Math.max((Math.abs(u)-.5)/.5,0),1)),d.style.zIndex=-Math.abs(Math.round(c))+e.length,U(0,d).style.transform=v}},setTransition:function(t){var e=w.slides.map(o);e.forEach(function(e){e.style.transitionDuration="".concat(t,"ms"),e.querySelectorAll(".swiper-slide-shadow").forEach(function(e){e.style.transitionDuration="".concat(t,"ms")})}),Q({swiper:w,duration:t,transformElements:e})},perspective:function(){return!0},overwriteParams:function(){return{watchSlidesProgress:!0,virtualTranslate:!w.params.cssMode}}})}];return R.use(ie),R}(),brandTabSwiper=new Swiper(".brand-tab-nav",{slidesPerView:2.9,spaceBetween:0,loop:!1,freeMode:!0,keyboard:{enabled:!0},grabCursor:!0,pagination:!1,navigation:{nextEl:".brand-tab-nav-next",prevEl:".brand-tab-nav-prev"},breakpoints:{0:{slidesPerView:2.9},480:{slidesPerView:3.5},600:{slidesPerView:4},768:{slidesPerView:4,allowSlideNext:!1,allowSlidePrev:!1,grabCursor:!1}}}),homepageNewProducts=new Swiper(".new-product-feed",{slidesPerView:1,spaceBetween:16,loop:!0,autoHeight:!1,updateOnWindowResize:!0,navigation:{nextEl:".new-products .swiper-buttons .swiper-button-next",prevEl:".new-products .swiper-buttons .swiper-button-prev",disabledClass:"swiper-button-disabled"},breakpoints:{460:{slidesPerView:2},768:{slidesPerView:2},992:{slidesPerView:3},1232:{slidesPerView:4},1472:{slidesPerView:6}}}),homepageRecommendedProducts=new Swiper(".recommended-product-feed",{slidesPerView:1,spaceBetween:16,loop:!0,autoHeight:!1,updateOnWindowResize:!0,navigation:{nextEl:".recommended-products .swiper-buttons .swiper-button-next",prevEl:".recommended-products .swiper-buttons .swiper-button-prev"},breakpoints:{460:{slidesPerView:2},768:{slidesPerView:2},992:{slidesPerView:3},1232:{slidesPerView:4},1472:{slidesPerView:6}}});function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(e,t){"object"===("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?t(exports,require("jquery")):"function"==typeof define&&define.amd?define(["exports","jquery"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).bootstrap={},e.jQuery)}(void 0,function(e,t){function i(e){return e&&"object"===_typeof(e)&&"default"in e?e:{default:e}}var c=i(t);function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function r(e,t,i){return t&&n(e.prototype,t),i&&n(e,i),e}function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i,n=arguments[t];for(i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var s="transitionend";function a(e){var t=this,i=!1;return c.default(this).one(u.TRANSITION_END,function(){i=!0}),setTimeout(function(){i||u.triggerTransitionEnd(t)},e),this}var u={TRANSITION_END:"bsTransitionEnd",getUID:function(e){for(;e+=~~(1e6*Math.random()),document.getElementById(e););return e},getSelectorFromElement:function(e){var t=e.getAttribute("data-target");t&&"#"!==t||(t=(e=e.getAttribute("href"))&&"#"!==e?e.trim():"");try{return document.querySelector(t)?t:null}catch(e){return null}},getTransitionDurationFromElement:function(e){if(!e)return 0;var t=c.default(e).css("transition-duration"),i=c.default(e).css("transition-delay"),n=parseFloat(t),e=parseFloat(i);return n||e?(t=t.split(",")[0],i=i.split(",")[0],1e3*(parseFloat(t)+parseFloat(i))):0},reflow:function(e){return e.offsetHeight},triggerTransitionEnd:function(e){c.default(e).trigger(s)},supportsTransitionEnd:function(){return Boolean(s)},isElement:function(e){return(e[0]||e).nodeType},typeCheckConfig:function(e,t,i){for(var n in i)if(Object.prototype.hasOwnProperty.call(i,n)){var s=i[n],a=t[n],r=a&&u.isElement(a)?"element":null==(r=a)?""+r:{}.toString.call(r).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(s).test(r))throw new Error(e.toUpperCase()+': Option "'+n+'" provided type "'+r+'" but expected type "'+s+'".')}var r},findShadowRoot:function(e){if(!document.documentElement.attachShadow)return null;if("function"!=typeof e.getRootNode)return e instanceof ShadowRoot?e:e.parentNode?u.findShadowRoot(e.parentNode):null;e=e.getRootNode();return e instanceof ShadowRoot?e:null},jQueryDetection:function(){if(void 0===c.default)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var e=c.default.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||4<=e[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};u.jQueryDetection(),c.default.fn.emulateTransitionEnd=a,c.default.event.special[u.TRANSITION_END]={bindType:s,delegateType:s,handle:function(e){if(c.default(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}};var l="alert",d="bs.alert",p="."+d,h=c.default.fn[l],f=function(){function n(e){this._element=e}var e=n.prototype;return e.close=function(e){var t=this._element;e&&(t=this._getRootElement(e)),this._triggerCloseEvent(t).isDefaultPrevented()||this._removeElement(t)},e.dispose=function(){c.default.removeData(this._element,d),this._element=null},e._getRootElement=function(e){var t=u.getSelectorFromElement(e),i=!1;return i=(i=t?document.querySelector(t):i)||c.default(e).closest(".alert")[0]},e._triggerCloseEvent=function(e){var t=c.default.Event("close.bs.alert");return c.default(e).trigger(t),t},e._removeElement=function(t){var e,i=this;c.default(t).removeClass("show"),c.default(t).hasClass("fade")?(e=u.getTransitionDurationFromElement(t),c.default(t).one(u.TRANSITION_END,function(e){return i._destroyElement(t,e)}).emulateTransitionEnd(e)):this._destroyElement(t)},e._destroyElement=function(e){c.default(e).detach().trigger("closed.bs.alert").remove()},n._jQueryInterface=function(i){return this.each(function(){var e=c.default(this),t=e.data(d);t||(t=new n(this),e.data(d,t)),"close"===i&&t[i](this)})},n._handleDismiss=function(t){return function(e){e&&e.preventDefault(),t.close(this)}},r(n,null,[{key:"VERSION",get:function(){return"4.6.0"}}]),n}();c.default(document).on("click.bs.alert.data-api",'[data-dismiss="alert"]',f._handleDismiss(new f)),c.default.fn[l]=f._jQueryInterface,c.default.fn[l].Constructor=f,c.default.fn[l].noConflict=function(){return c.default.fn[l]=h,f._jQueryInterface};var m="button",g="bs.button",t=".data-api",v=c.default.fn[m],y="active",p='[data-toggle^="button"]',b='input:not([type="hidden"])',w=function(){function s(e){this._element=e,this.shouldAvoidTriggerChange=!1}var e=s.prototype;return e.toggle=function(){var e,t=!0,i=!0,n=c.default(this._element).closest('[data-toggle="buttons"]')[0];!n||(e=this._element.querySelector(b))&&("radio"===e.type&&(e.checked&&this._element.classList.contains(y)?t=!1:(n=n.querySelector(".active"))&&c.default(n).removeClass(y)),t&&("checkbox"!==e.type&&"radio"!==e.type||(e.checked=!this._element.classList.contains(y)),this.shouldAvoidTriggerChange||c.default(e).trigger("change")),e.focus(),i=!1),this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(i&&this._element.setAttribute("aria-pressed",!this._element.classList.contains(y)),t&&c.default(this._element).toggleClass(y))},e.dispose=function(){c.default.removeData(this._element,g),this._element=null},s._jQueryInterface=function(i,n){return this.each(function(){var e=c.default(this),t=e.data(g);t||(t=new s(this),e.data(g,t)),t.shouldAvoidTriggerChange=n,"toggle"===i&&t[i]()})},r(s,null,[{key:"VERSION",get:function(){return"4.6.0"}}]),s}();c.default(document).on("click.bs.button.data-api",p,function(e){var t,i=e.target,n=i;!(i=!c.default(i).hasClass("btn")?c.default(i).closest(".btn")[0]:i)||i.hasAttribute("disabled")||i.classList.contains("disabled")||(t=i.querySelector(b))&&(t.hasAttribute("disabled")||t.classList.contains("disabled"))?e.preventDefault():"INPUT"!==n.tagName&&"LABEL"===i.tagName||w._jQueryInterface.call(c.default(i),"toggle","INPUT"===n.tagName)}).on("focus.bs.button.data-api blur.bs.button.data-api",p,function(e){var t=c.default(e.target).closest(".btn")[0];c.default(t).toggleClass("focus",/^focus(in)?$/.test(e.type))}),c.default(window).on("load.bs.button.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),t=0,i=e.length;t<i;t++){var n=e[t],s=n.querySelector(b);s.checked||s.hasAttribute("checked")?n.classList.add(y):n.classList.remove(y)}for(var a=0,r=(e=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;a<r;a++){var o=e[a];"true"===o.getAttribute("aria-pressed")?o.classList.add(y):o.classList.remove(y)}}),c.default.fn[m]=w._jQueryInterface,c.default.fn[m].Constructor=w,c.default.fn[m].noConflict=function(){return c.default.fn[m]=v,w._jQueryInterface};var S="carousel",T="bs.carousel",x="."+T,E=c.default.fn[S],C={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},_={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},k="next",M="prev",A="slid"+x,P="active",L=".active.carousel-item",O={TOUCH:"touch",PEN:"pen"},I=function(){function s(e,t){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(t),this._element=e,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}var e=s.prototype;return e.next=function(){this._isSliding||this._slide(k)},e.nextWhenVisible=function(){var e=c.default(this._element);!document.hidden&&e.is(":visible")&&"hidden"!==e.css("visibility")&&this.next()},e.prev=function(){this._isSliding||this._slide(M)},e.pause=function(e){e||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(u.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},e.cycle=function(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._updateInterval(),this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},e.to=function(e){var t=this;this._activeElement=this._element.querySelector(L);var i=this._getItemIndex(this._activeElement);if(!(e>this._items.length-1||e<0))if(this._isSliding)c.default(this._element).one(A,function(){return t.to(e)});else{if(i===e)return this.pause(),void this.cycle();this._slide(i<e?k:M,this._items[e])}},e.dispose=function(){c.default(this._element).off(x),c.default.removeData(this._element,T),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},e._getConfig=function(e){return e=o({},C,e),u.typeCheckConfig(S,e,_),e},e._handleSwipe=function(){var e=Math.abs(this.touchDeltaX);e<=40||(e=e/this.touchDeltaX,(this.touchDeltaX=0)<e&&this.prev(),e<0&&this.next())},e._addEventListeners=function(){var t=this;this._config.keyboard&&c.default(this._element).on("keydown.bs.carousel",function(e){return t._keydown(e)}),"hover"===this._config.pause&&c.default(this._element).on("mouseenter.bs.carousel",function(e){return t.pause(e)}).on("mouseleave.bs.carousel",function(e){return t.cycle(e)}),this._config.touch&&this._addTouchEventListeners()},e._addTouchEventListeners=function(){var e,t,i=this;this._touchSupported&&(e=function(e){i._pointerEvent&&O[e.originalEvent.pointerType.toUpperCase()]?i.touchStartX=e.originalEvent.clientX:i._pointerEvent||(i.touchStartX=e.originalEvent.touches[0].clientX)},t=function(e){i._pointerEvent&&O[e.originalEvent.pointerType.toUpperCase()]&&(i.touchDeltaX=e.originalEvent.clientX-i.touchStartX),i._handleSwipe(),"hover"===i._config.pause&&(i.pause(),i.touchTimeout&&clearTimeout(i.touchTimeout),i.touchTimeout=setTimeout(function(e){return i.cycle(e)},500+i._config.interval))},c.default(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel",function(e){return e.preventDefault()}),this._pointerEvent?(c.default(this._element).on("pointerdown.bs.carousel",e),c.default(this._element).on("pointerup.bs.carousel",t),this._element.classList.add("pointer-event")):(c.default(this._element).on("touchstart.bs.carousel",e),c.default(this._element).on("touchmove.bs.carousel",function(e){(e=e).originalEvent.touches&&1<e.originalEvent.touches.length?i.touchDeltaX=0:i.touchDeltaX=e.originalEvent.touches[0].clientX-i.touchStartX}),c.default(this._element).on("touchend.bs.carousel",t)))},e._keydown=function(e){if(!/input|textarea/i.test(e.target.tagName))switch(e.which){case 37:e.preventDefault(),this.prev();break;case 39:e.preventDefault(),this.next()}},e._getItemIndex=function(e){return this._items=e&&e.parentNode?[].slice.call(e.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(e)},e._getItemByDirection=function(e,t){var i=e===k,n=e===M,s=this._getItemIndex(t),a=this._items.length-1;if((n&&0===s||i&&s===a)&&!this._config.wrap)return t;e=(s+(e===M?-1:1))%this._items.length;return-1==e?this._items[this._items.length-1]:this._items[e]},e._triggerSlideEvent=function(e,t){var i=this._getItemIndex(e),n=this._getItemIndex(this._element.querySelector(L)),i=c.default.Event("slide.bs.carousel",{relatedTarget:e,direction:t,from:n,to:i});return c.default(this._element).trigger(i),i},e._setActiveIndicatorElement=function(e){var t;this._indicatorsElement&&(t=[].slice.call(this._indicatorsElement.querySelectorAll(".active")),c.default(t).removeClass(P),(e=this._indicatorsElement.children[this._getItemIndex(e)])&&c.default(e).addClass(P))},e._updateInterval=function(){var e=this._activeElement||this._element.querySelector(L);e&&((e=parseInt(e.getAttribute("data-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=e):this._config.interval=this._config.defaultInterval||this._config.interval)},e._slide=function(e,t){var i,n,s,a=this,r=this._element.querySelector(L),o=this._getItemIndex(r),l=t||r&&this._getItemByDirection(e,r),d=this._getItemIndex(l),t=Boolean(this._interval),e=e===k?(i="carousel-item-left",n="carousel-item-next","left"):(i="carousel-item-right",n="carousel-item-prev","right");l&&c.default(l).hasClass(P)?this._isSliding=!1:this._triggerSlideEvent(l,e).isDefaultPrevented()||r&&l&&(this._isSliding=!0,t&&this.pause(),this._setActiveIndicatorElement(l),this._activeElement=l,s=c.default.Event(A,{relatedTarget:l,direction:e,from:o,to:d}),c.default(this._element).hasClass("slide")?(c.default(l).addClass(n),u.reflow(l),c.default(r).addClass(i),c.default(l).addClass(i),d=u.getTransitionDurationFromElement(r),c.default(r).one(u.TRANSITION_END,function(){c.default(l).removeClass(i+" "+n).addClass(P),c.default(r).removeClass(P+" "+n+" "+i),a._isSliding=!1,setTimeout(function(){return c.default(a._element).trigger(s)},0)}).emulateTransitionEnd(d)):(c.default(r).removeClass(P),c.default(l).addClass(P),this._isSliding=!1,c.default(this._element).trigger(s)),t&&this.cycle())},s._jQueryInterface=function(n){return this.each(function(){var e=c.default(this).data(T),t=o({},C,c.default(this).data());"object"===_typeof(n)&&(t=o({},t,n));var i="string"==typeof n?n:t.slide;if(e||(e=new s(this,t),c.default(this).data(T,e)),"number"==typeof n)e.to(n);else if("string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}else t.interval&&t.ride&&(e.pause(),e.cycle())})},s._dataApiClickHandler=function(e){var t,i,n=u.getSelectorFromElement(this);!n||(t=c.default(n)[0])&&c.default(t).hasClass("carousel")&&(i=o({},c.default(t).data(),c.default(this).data()),(n=this.getAttribute("data-slide-to"))&&(i.interval=!1),s._jQueryInterface.call(c.default(t),i),n&&c.default(t).data(T).to(n),e.preventDefault())},r(s,null,[{key:"VERSION",get:function(){return"4.6.0"}},{key:"Default",get:function(){return C}}]),s}();c.default(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",I._dataApiClickHandler),c.default(window).on("load.bs.carousel.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),t=0,i=e.length;t<i;t++){var n=c.default(e[t]);I._jQueryInterface.call(n,n.data())}}),c.default.fn[S]=I._jQueryInterface,c.default.fn[S].Constructor=I,c.default.fn[S].noConflict=function(){return c.default.fn[S]=E,I._jQueryInterface};var D="collapse",$="bs.collapse",z=c.default.fn[D],N={toggle:!0,parent:""},H={toggle:"boolean",parent:"(string|element)"},q="show",j="collapse",B="collapsing",F="collapsed",R='[data-toggle="collapse"]',X=function(){function a(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+t.id+'"],[data-toggle="collapse"][data-target="#'+t.id+'"]'));for(var i=[].slice.call(document.querySelectorAll(R)),n=0,s=i.length;n<s;n++){var a=i[n],r=u.getSelectorFromElement(a),o=[].slice.call(document.querySelectorAll(r)).filter(function(e){return e===t});null!==r&&0<o.length&&(this._selector=r,this._triggerArray.push(a))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var e=a.prototype;return e.toggle=function(){c.default(this._element).hasClass(q)?this.hide():this.show()},e.show=function(){var e,t,i,n,s=this;this._isTransitioning||c.default(this._element).hasClass(q)||(n=this._parent&&0===(n=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(e){return"string"==typeof s._config.parent?e.getAttribute("data-parent")===s._config.parent:e.classList.contains(j)})).length?null:n)&&(i=c.default(n).not(this._selector).data($))&&i._isTransitioning||(e=c.default.Event("show.bs.collapse"),c.default(this._element).trigger(e),e.isDefaultPrevented()||(n&&(a._jQueryInterface.call(c.default(n).not(this._selector),"hide"),i||c.default(n).data($,null)),t=this._getDimension(),c.default(this._element).removeClass(j).addClass(B),this._element.style[t]=0,this._triggerArray.length&&c.default(this._triggerArray).removeClass(F).attr("aria-expanded",!0),this.setTransitioning(!0),i="scroll"+(t[0].toUpperCase()+t.slice(1)),n=u.getTransitionDurationFromElement(this._element),c.default(this._element).one(u.TRANSITION_END,function(){c.default(s._element).removeClass(B).addClass(j+" "+q),s._element.style[t]="",s.setTransitioning(!1),c.default(s._element).trigger("shown.bs.collapse")}).emulateTransitionEnd(n),this._element.style[t]=this._element[i]+"px"))},e.hide=function(){var e=this;if(!this._isTransitioning&&c.default(this._element).hasClass(q)){var t=c.default.Event("hide.bs.collapse");if(c.default(this._element).trigger(t),!t.isDefaultPrevented()){t=this._getDimension();this._element.style[t]=this._element.getBoundingClientRect()[t]+"px",u.reflow(this._element),c.default(this._element).addClass(B).removeClass(j+" "+q);var i=this._triggerArray.length;if(0<i)for(var n=0;n<i;n++){var s=this._triggerArray[n],a=u.getSelectorFromElement(s);null!==a&&(c.default([].slice.call(document.querySelectorAll(a))).hasClass(q)||c.default(s).addClass(F).attr("aria-expanded",!1))}this.setTransitioning(!0);this._element.style[t]="";t=u.getTransitionDurationFromElement(this._element);c.default(this._element).one(u.TRANSITION_END,function(){e.setTransitioning(!1),c.default(e._element).removeClass(B).addClass(j).trigger("hidden.bs.collapse")}).emulateTransitionEnd(t)}}},e.setTransitioning=function(e){this._isTransitioning=e},e.dispose=function(){c.default.removeData(this._element,$),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},e._getConfig=function(e){return(e=o({},N,e)).toggle=Boolean(e.toggle),u.typeCheckConfig(D,e,H),e},e._getDimension=function(){return c.default(this._element).hasClass("width")?"width":"height"},e._getParent=function(){var e,i=this;u.isElement(this._config.parent)?(e=this._config.parent,void 0!==this._config.parent.jquery&&(e=this._config.parent[0])):e=document.querySelector(this._config.parent);var t='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',t=[].slice.call(e.querySelectorAll(t));return c.default(t).each(function(e,t){i._addAriaAndCollapsedClass(a._getTargetFromElement(t),[t])}),e},e._addAriaAndCollapsedClass=function(e,t){e=c.default(e).hasClass(q);t.length&&c.default(t).toggleClass(F,!e).attr("aria-expanded",e)},a._getTargetFromElement=function(e){e=u.getSelectorFromElement(e);return e?document.querySelector(e):null},a._jQueryInterface=function(n){return this.each(function(){var e=c.default(this),t=e.data($),i=o({},N,e.data(),"object"===_typeof(n)&&n?n:{});if(!t&&i.toggle&&"string"==typeof n&&/show|hide/.test(n)&&(i.toggle=!1),t||(t=new a(this,i),e.data($,t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},r(a,null,[{key:"VERSION",get:function(){return"4.6.0"}},{key:"Default",get:function(){return N}}]),a}();c.default(document).on("click.bs.collapse.data-api",R,function(e){"A"===e.currentTarget.tagName&&e.preventDefault();var i=c.default(this),e=u.getSelectorFromElement(this),e=[].slice.call(document.querySelectorAll(e));c.default(e).each(function(){var e=c.default(this),t=e.data($)?"toggle":i.data();X._jQueryInterface.call(e,t)})}),c.default.fn[D]=X._jQueryInterface,c.default.fn[D].Constructor=X,c.default.fn[D].noConflict=function(){return c.default.fn[D]=z,X._jQueryInterface};var Y="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,W=function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(Y&&0<=navigator.userAgent.indexOf(e[t]))return 1;return 0}();var V=Y&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},W))}};function G(e){return e&&"[object Function]"==={}.toString.call(e)}function U(e,t){if(1!==e.nodeType)return[];e=e.ownerDocument.defaultView.getComputedStyle(e,null);return t?e[t]:e}function Q(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function K(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=U(e),i=t.overflow,n=t.overflowX,t=t.overflowY;return/(auto|scroll|overlay)/.test(i+t+n)?e:K(Q(e))}function Z(e){return e&&e.referenceNode?e.referenceNode:e}var J=Y&&!(!window.MSInputMethodContext||!document.documentMode),ee=Y&&/MSIE 10/.test(navigator.userAgent);function te(e){return 11===e?J:10!==e&&J||ee}function ie(e){if(!e)return document.documentElement;for(var t=te(10)?document.body:null,i=e.offsetParent||null;i===t&&e.nextElementSibling;)i=(e=e.nextElementSibling).offsetParent;var n=i&&i.nodeName;return n&&"BODY"!==n&&"HTML"!==n?-1!==["TH","TD","TABLE"].indexOf(i.nodeName)&&"static"===U(i,"position")?ie(i):i:(e?e.ownerDocument:document).documentElement}function ne(e){return null!==e.parentNode?ne(e.parentNode):e}function se(e,t){if(!(e&&e.nodeType&&t&&t.nodeType))return document.documentElement;var i=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,n=i?e:t,s=i?t:e,i=document.createRange();i.setStart(n,0),i.setEnd(s,0);i=i.commonAncestorContainer;if(e!==i&&t!==i||n.contains(s))return"BODY"===(s=(n=i).nodeName)||"HTML"!==s&&ie(n.firstElementChild)!==n?ie(i):i;i=ne(e);return i.host?se(i.host,t):se(e,ne(t).host)}function ae(e,t){var i="top"===(1<arguments.length&&void 0!==t?t:"top")?"scrollTop":"scrollLeft",t=e.nodeName;if("BODY"!==t&&"HTML"!==t)return e[i];t=e.ownerDocument.documentElement;return(e.ownerDocument.scrollingElement||t)[i]}function re(e,t){var i="x"===t?"Left":"Top",t="Left"==i?"Right":"Bottom";return parseFloat(e["border"+i+"Width"])+parseFloat(e["border"+t+"Width"])}function oe(e,t,i,n){return Math.max(t["offset"+e],t["scroll"+e],i["client"+e],i["offset"+e],i["scroll"+e],te(10)?parseInt(i["offset"+e])+parseInt(n["margin"+("Height"===e?"Top":"Left")])+parseInt(n["margin"+("Height"===e?"Bottom":"Right")]):0)}function le(e){var t=e.body,i=e.documentElement,e=te(10)&&getComputedStyle(i);return{height:oe("Height",t,i,e),width:oe("Width",t,i,e)}}t=function(e,t,i){return t&&de(e.prototype,t),i&&de(e,i),e};function de(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ce(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}var ue=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i,n=arguments[t];for(i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e};function pe(e){return ue({},e,{right:e.left+e.width,bottom:e.top+e.height})}function he(e){var t={};try{te(10)?(t=e.getBoundingClientRect(),i=ae(e,"top"),n=ae(e,"left"),t.top+=i,t.left+=n,t.bottom+=i,t.right+=n):t=e.getBoundingClientRect()}catch(e){}var i={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},n="HTML"===e.nodeName?le(e.ownerDocument):{},t=n.width||e.clientWidth||i.width,n=n.height||e.clientHeight||i.height,t=e.offsetWidth-t,n=e.offsetHeight-n;return(t||n)&&(t-=re(e=U(e),"x"),n-=re(e,"y"),i.width-=t,i.height-=n),pe(i)}function fe(e,t,i){var n=2<arguments.length&&void 0!==i&&i,s=te(10),a="HTML"===t.nodeName,r=he(e),o=he(t),l=K(e),d=U(t),i=parseFloat(d.borderTopWidth),e=parseFloat(d.borderLeftWidth);n&&a&&(o.top=Math.max(o.top,0),o.left=Math.max(o.left,0));r=pe({top:r.top-o.top-i,left:r.left-o.left-e,width:r.width,height:r.height});return r.marginTop=0,r.marginLeft=0,!s&&a&&(a=parseFloat(d.marginTop),d=parseFloat(d.marginLeft),r.top-=i-a,r.bottom-=i-a,r.left-=e-d,r.right-=e-d,r.marginTop=a,r.marginLeft=d),r=(s&&!n?t.contains(l):t===l&&"BODY"!==l.nodeName)?function(e,t,i){var n=2<arguments.length&&void 0!==i&&i,i=ae(t,"top"),t=ae(t,"left"),n=n?-1:1;return e.top+=i*n,e.bottom+=i*n,e.left+=t*n,e.right+=t*n,e}(r,t):r}function me(e){if(!e||!e.parentElement||te())return document.documentElement;for(var t=e.parentElement;t&&"none"===U(t,"transform");)t=t.parentElement;return t||document.documentElement}function ge(e,t,i,n,s){var a=4<arguments.length&&void 0!==s&&s,r={top:0,left:0},o=a?me(e):se(e,Z(t));"viewport"===n?r=function(e,t){var i=1<arguments.length&&void 0!==t&&t,n=e.ownerDocument.documentElement,s=fe(e,n),a=Math.max(n.clientWidth,window.innerWidth||0),t=Math.max(n.clientHeight,window.innerHeight||0),e=i?0:ae(n),n=i?0:ae(n,"left");return pe({top:e-s.top+s.marginTop,left:n-s.left+s.marginLeft,width:a,height:t})}(o,a):(s=void 0,"scrollParent"===n?"BODY"===(s=K(Q(t))).nodeName&&(s=e.ownerDocument.documentElement):s="window"===n?e.ownerDocument.documentElement:n,l=fe(s,o,a),"HTML"!==s.nodeName||function e(t){var i=t.nodeName;if("BODY"===i||"HTML"===i)return!1;if("fixed"===U(t,"position"))return!0;t=Q(t);return!!t&&e(t)}(o)?r=l:(e=(o=le(e.ownerDocument)).height,o=o.width,r.top+=l.top-l.marginTop,r.bottom=e+l.top,r.left+=l.left-l.marginLeft,r.right=o+l.left));var l="number"==typeof(i=i||0);return r.left+=l?i:i.left||0,r.top+=l?i:i.top||0,r.right-=l?i:i.right||0,r.bottom-=l?i:i.bottom||0,r}function ve(e,t,i,n,s,a){a=5<arguments.length&&void 0!==a?a:0;if(-1===e.indexOf("auto"))return e;var s=ge(i,n,a,s),r={top:{width:s.width,height:t.top-s.top},right:{width:s.right-t.right,height:s.height},bottom:{width:s.width,height:s.bottom-t.bottom},left:{width:t.left-s.left,height:s.height}},t=Object.keys(r).map(function(e){return ue({key:e},r[e],{area:(e=r[e]).width*e.height})}).sort(function(e,t){return t.area-e.area}),s=t.filter(function(e){var t=e.width,e=e.height;return t>=i.clientWidth&&e>=i.clientHeight}),t=(0<s.length?s:t)[0].key,e=e.split("-")[1];return t+(e?"-"+e:"")}function ye(e,t,i,n){n=3<arguments.length&&void 0!==n?n:null;return fe(i,n?me(t):se(t,Z(i)),n)}function be(e){var t=e.ownerDocument.defaultView.getComputedStyle(e),i=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),t=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:e.offsetWidth+t,height:e.offsetHeight+i}}function we(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function Se(e,t,i){i=i.split("-")[0];var n=be(e),s={width:n.width,height:n.height},a=-1!==["right","left"].indexOf(i),r=a?"top":"left",o=a?"left":"top",e=a?"height":"width",a=a?"width":"height";return s[r]=t[r]+t[e]/2-n[e]/2,s[o]=i===o?t[o]-n[a]:t[we(o)],s}function Te(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function xe(e,i,t){return(void 0===t?e:e.slice(0,function(e,t,i){if(Array.prototype.findIndex)return e.findIndex(function(e){return e[t]===i});var n=Te(e,function(e){return e[t]===i});return e.indexOf(n)}(e,"name",t))).forEach(function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var t=e.function||e.fn;e.enabled&&G(t)&&(i.offsets.popper=pe(i.offsets.popper),i.offsets.reference=pe(i.offsets.reference),i=t(i,e))}),i}function Ee(e,i){return e.some(function(e){var t=e.name;return e.enabled&&t===i})}function Ce(e){for(var t=[!1,"ms","Webkit","Moz","O"],i=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<t.length;n++){var s=t[n],s=s?""+s+i:e;if(void 0!==document.body.style[s])return s}return null}function _e(e){e=e.ownerDocument;return e?e.defaultView:window}function ke(e,t,i,n){i.updateBound=n,_e(e).addEventListener("resize",i.updateBound,{passive:!0});e=K(e);return function e(t,i,n,s){var a="BODY"===t.nodeName,t=a?t.ownerDocument.defaultView:t;t.addEventListener(i,n,{passive:!0}),a||e(K(t.parentNode),i,n,s),s.push(t)}(e,"scroll",i.updateBound,i.scrollParents),i.scrollElement=e,i.eventsEnabled=!0,i}function Me(){var e,t;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(e=this.reference,t=this.state,_e(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t))}function Ae(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function Pe(i,n){Object.keys(n).forEach(function(e){var t="";-1!==["width","height","top","right","bottom","left"].indexOf(e)&&Ae(n[e])&&(t="px"),i.style[e]=n[e]+t})}var Le=Y&&/Firefox/i.test(navigator.userAgent);function Oe(e,t,i){var n=Te(e,function(e){return e.name===t}),s=!!n&&e.some(function(e){return e.name===i&&e.enabled&&e.order<n.order});return s||(e="`"+t+"`",console.warn("`"+i+"`"+" modifier is required by "+e+" modifier in order to work, be sure to include it before "+e+"!")),s}var p=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Ie=p.slice(3);function De(e,t){t=1<arguments.length&&void 0!==t&&t,e=Ie.indexOf(e),e=Ie.slice(e+1).concat(Ie.slice(0,e));return t?e.reverse():e}var $e="flip",ze="clockwise",Ne="counterclockwise";function He(e,o,l,t){var s=[0,0],n=-1!==["right","left"].indexOf(t),i=e.split(/(\+|\-)/).map(function(e){return e.trim()}),t=i.indexOf(Te(i,function(e){return-1!==e.search(/,|\s/)}));i[t]&&-1===i[t].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");e=/\s*,\s*|\s+/;return(-1!==t?[i.slice(0,t).concat([i[t].split(e)[0]]),[i[t].split(e)[1]].concat(i.slice(t+1))]:[i]).map(function(e,t){var r=(1===t?!n:n)?"height":"width",i=!1;return e.reduce(function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,i=!0,e):i?(e[e.length-1]+=t,i=!1,e):e.concat(t)},[]).map(function(e){return i=r,n=o,s=l,e=+(a=(t=e).match(/((?:\-|\+)?\d*\.?\d*)(.*)/))[1],a=a[2],e?0===a.indexOf("%")?pe("%p"===a?n:s)[i]/100*e:"vh"!==a&&"vw"!==a?e:("vh"===a?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*e:t;var t,i,n,s,a})}).forEach(function(i,n){i.forEach(function(e,t){Ae(e)&&(s[n]+=e*("-"===i[t-1]?-1:1))})}),s}var qe={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t,i,n=e.placement,s=n.split("-")[0],a=n.split("-")[1];return a&&(t=(i=e.offsets).reference,n=i.popper,s=(i=-1!==["bottom","top"].indexOf(s))?"width":"height",s={start:ce({},i=i?"left":"top",t[i]),end:ce({},i,t[i]+t[s]-n[s])},e.offsets.popper=ue({},n,s[a])),e}},offset:{order:200,enabled:!0,fn:function(e,t){var i=t.offset,n=e.placement,s=(a=e.offsets).popper,t=a.reference,a=n.split("-")[0],n=void 0,n=Ae(+i)?[+i,0]:He(i,s,t,a);return"left"===a?(s.top+=n[0],s.left-=n[1]):"right"===a?(s.top+=n[0],s.left+=n[1]):"top"===a?(s.left+=n[0],s.top-=n[1]):"bottom"===a&&(s.left+=n[0],s.top+=n[1]),e.popper=s,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,n){var t=n.boundariesElement||ie(e.instance.popper);e.instance.reference===t&&(t=ie(t));var i=Ce("transform"),s=e.instance.popper.style,a=s.top,r=s.left,o=s[i];s.top="",s.left="",s[i]="";var l=ge(e.instance.popper,e.instance.reference,n.padding,t,e.positionFixed);s.top=a,s.left=r,s[i]=o,n.boundaries=l;var o=n.priority,d=e.offsets.popper,c={primary:function(e){var t=d[e];return d[e]<l[e]&&!n.escapeWithReference&&(t=Math.max(d[e],l[e])),ce({},e,t)},secondary:function(e){var t="right"===e?"left":"top",i=d[t];return d[e]>l[e]&&!n.escapeWithReference&&(i=Math.min(d[t],l[e]-("right"===e?d.width:d.height))),ce({},t,i)}};return o.forEach(function(e){var t=-1!==["left","top"].indexOf(e)?"primary":"secondary";d=ue({},d,c[t](e))}),e.offsets.popper=d,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=(a=e.offsets).popper,i=a.reference,n=e.placement.split("-")[0],s=Math.floor,a=(r=-1!==["top","bottom"].indexOf(n))?"right":"bottom",n=r?"left":"top",r=r?"width":"height";return t[a]<s(i[n])&&(e.offsets.popper[n]=s(i[n])-t[r]),t[n]>s(i[a])&&(e.offsets.popper[n]=s(i[a])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){if(!Oe(e.instance.modifiers,"arrow","keepTogether"))return e;var i=t.element;if("string"==typeof i){if(!(i=e.instance.popper.querySelector(i)))return e}else if(!e.instance.popper.contains(i))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var n=e.placement.split("-")[0],s=(c=e.offsets).popper,a=c.reference,r=-1!==["left","right"].indexOf(n),o=r?"height":"width",l=r?"Top":"Left",d=l.toLowerCase(),t=r?"left":"top",c=r?"bottom":"right",n=be(i)[o];return a[c]-n<s[d]&&(e.offsets.popper[d]-=s[d]-(a[c]-n)),a[d]+n>s[c]&&(e.offsets.popper[d]+=a[d]+n-s[c]),e.offsets.popper=pe(e.offsets.popper),r=a[d]+a[o]/2-n/2,c=U(e.instance.popper),a=parseFloat(c["margin"+l]),l=parseFloat(c["border"+l+"Width"]),l=r-e.offsets.popper[d]-a-l,l=Math.max(Math.min(s[o]-n,l),0),e.arrowElement=i,e.offsets.arrow=(ce(i={},d,Math.round(l)),ce(i,t,""),i),e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(d,c){if(Ee(d.instance.modifiers,"inner"))return d;if(d.flipped&&d.placement===d.originalPlacement)return d;var u=ge(d.instance.popper,d.instance.reference,c.padding,c.boundariesElement,d.positionFixed),p=d.placement.split("-")[0],h=we(p),f=d.placement.split("-")[1]||"",m=[];switch(c.behavior){case $e:m=[p,h];break;case ze:m=De(p);break;case Ne:m=De(p,!0);break;default:m=c.behavior}return m.forEach(function(e,t){if(p!==e||m.length===t+1)return d;p=d.placement.split("-")[0],h=we(p);var i=d.offsets.popper,n=d.offsets.reference,s=Math.floor,a="left"===p&&s(i.right)>s(n.left)||"right"===p&&s(i.left)<s(n.right)||"top"===p&&s(i.bottom)>s(n.top)||"bottom"===p&&s(i.top)<s(n.bottom),r=s(i.left)<s(u.left),o=s(i.right)>s(u.right),l=s(i.top)<s(u.top),e=s(i.bottom)>s(u.bottom),n="left"===p&&r||"right"===p&&o||"top"===p&&l||"bottom"===p&&e,i=-1!==["top","bottom"].indexOf(p),s=!!c.flipVariations&&(i&&"start"===f&&r||i&&"end"===f&&o||!i&&"start"===f&&l||!i&&"end"===f&&e),l=!!c.flipVariationsByContent&&(i&&"start"===f&&o||i&&"end"===f&&r||!i&&"start"===f&&e||!i&&"end"===f&&l),l=s||l;(a||n||l)&&(d.flipped=!0,(a||n)&&(p=m[t+1]),l&&(f="end"===(l=f)?"start":"start"===l?"end":l),d.placement=p+(f?"-"+f:""),d.offsets.popper=ue({},d.offsets.popper,Se(d.instance.popper,d.offsets.reference,d.placement)),d=xe(d.instance.modifiers,d,"flip"))}),d},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,i=t.split("-")[0],n=(r=e.offsets).popper,s=r.reference,a=-1!==["left","right"].indexOf(i),r=-1===["top","left"].indexOf(i);return n[a?"left":"top"]=s[i]-(r?n[a?"width":"height"]:0),e.placement=we(t),e.offsets.popper=pe(n),e}},hide:{order:800,enabled:!0,fn:function(e){if(!Oe(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,i=Te(e.instance.modifiers,function(e){return"preventOverflow"===e.name}).boundaries;if(t.bottom<i.top||t.left>i.right||t.top>i.bottom||t.right<i.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var i=t.x,n=t.y,s=e.offsets.popper;void 0!==(f=Te(e.instance.modifiers,function(e){return"applyStyle"===e.name}).gpuAcceleration)&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var a,r,o,l,d=void 0!==f?f:t.gpuAcceleration,c=ie(e.instance.popper),u=he(c),p={position:s.position},h=(a=e,h=window.devicePixelRatio<2||!Le,m=(l=a.offsets).popper,r=l.reference,o=Math.round,f=Math.floor,t=function(e){return e},s=o(r.width),l=o(m.width),r=-1!==["left","right"].indexOf(a.placement),a=-1!==a.placement.indexOf("-"),f=h?r||a||s%2==l%2?o:f:t,t=h?o:t,{left:f(s%2==1&&l%2==1&&!a&&h?m.left-1:m.left),top:t(m.top),bottom:t(m.bottom),right:f(m.right)}),t="bottom"===i?"top":"bottom",f="right"===n?"left":"right",m=Ce("transform"),i=void 0,n=void 0,n="bottom"==t?"HTML"===c.nodeName?-c.clientHeight+h.bottom:-u.height+h.bottom:h.top,i="right"==f?"HTML"===c.nodeName?-c.clientWidth+h.right:-u.width+h.right:h.left;return d&&m?(p[m]="translate3d("+i+"px, "+n+"px, 0)",p[t]=0,p[f]=0,p.willChange="transform"):(m="right"==f?-1:1,p[t]=n*("bottom"==t?-1:1),p[f]=i*m,p.willChange=t+", "+f),f={"x-placement":e.placement},e.attributes=ue({},f,e.attributes),e.styles=ue({},p,e.styles),e.arrowStyles=ue({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){var t,i;return Pe(e.instance.popper,e.styles),t=e.instance.popper,i=e.attributes,Object.keys(i).forEach(function(e){!1!==i[e]?t.setAttribute(e,i[e]):t.removeAttribute(e)}),e.arrowElement&&Object.keys(e.arrowStyles).length&&Pe(e.arrowElement,e.arrowStyles),e},onLoad:function(e,t,i,n,s){return s=ye(s,t,e,i.positionFixed),e=ve(i.placement,s,t,e,i.modifiers.flip.boundariesElement,i.modifiers.flip.padding),t.setAttribute("x-placement",e),Pe(t,{position:i.positionFixed?"fixed":"absolute"}),i},gpuAcceleration:void 0}}},je=(t(Be,[{key:"update",value:function(){return function(){var e;this.state.isDestroyed||((e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}}).offsets.reference=ye(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=ve(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=Se(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=xe(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e)))}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,Ee(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[Ce("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=ke(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return Me.call(this)}}]),Be);function Be(e,t){var i=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Be),this.scheduleUpdate=function(){return requestAnimationFrame(i.update)},this.update=V(this.update.bind(this)),this.options=ue({},Be.Defaults,n),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e&&e.jquery?e[0]:e,this.popper=t&&t.jquery?t[0]:t,this.options.modifiers={},Object.keys(ue({},Be.Defaults.modifiers,n.modifiers)).forEach(function(e){i.options.modifiers[e]=ue({},Be.Defaults.modifiers[e]||{},n.modifiers?n.modifiers[e]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return ue({name:e},i.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&G(e.onLoad)&&e.onLoad(i.reference,i.popper,i.options,e,i.state)}),this.update();t=this.options.eventsEnabled;t&&this.enableEventListeners(),this.state.eventsEnabled=t}je.Utils=("undefined"!=typeof window?window:global).PopperUtils,je.placements=p,je.Defaults=qe;var Fe="dropdown",Re="bs.dropdown",Xe="."+Re,t=".data-api",Ye=c.default.fn[Fe],We=new RegExp("38|40|27"),Ve="hide"+Xe,Ge="hidden"+Xe,p="click"+Xe+t,qe="keydown"+Xe+t,Ue="disabled",Qe="show",Ke="dropdown-menu-right",Ze='[data-toggle="dropdown"]',Je=".dropdown-menu",et={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},tt={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},it=function(){function d(e,t){this._element=e,this._popper=null,this._config=this._getConfig(t),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var e=d.prototype;return e.toggle=function(){var e;this._element.disabled||c.default(this._element).hasClass(Ue)||(e=c.default(this._menu).hasClass(Qe),d._clearMenus(),e||this.show(!0))},e.show=function(e){if(void 0===e&&(e=!1),!(this._element.disabled||c.default(this._element).hasClass(Ue)||c.default(this._menu).hasClass(Qe))){var t={relatedTarget:this._element},i=c.default.Event("show.bs.dropdown",t),n=d._getParentFromElement(this._element);if(c.default(n).trigger(i),!i.isDefaultPrevented()){if(!this._inNavbar&&e){if(void 0===je)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");e=this._element;"parent"===this._config.reference?e=n:u.isElement(this._config.reference)&&(e=this._config.reference,void 0!==this._config.reference.jquery&&(e=this._config.reference[0])),"scrollParent"!==this._config.boundary&&c.default(n).addClass("position-static"),this._popper=new je(e,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===c.default(n).closest(".navbar-nav").length&&c.default(document.body).children().on("mouseover",null,c.default.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),c.default(this._menu).toggleClass(Qe),c.default(n).toggleClass(Qe).trigger(c.default.Event("shown.bs.dropdown",t))}}},e.hide=function(){var e,t,i;this._element.disabled||c.default(this._element).hasClass(Ue)||!c.default(this._menu).hasClass(Qe)||(e={relatedTarget:this._element},t=c.default.Event(Ve,e),i=d._getParentFromElement(this._element),c.default(i).trigger(t),t.isDefaultPrevented()||(this._popper&&this._popper.destroy(),c.default(this._menu).toggleClass(Qe),c.default(i).toggleClass(Qe).trigger(c.default.Event(Ge,e))))},e.dispose=function(){c.default.removeData(this._element,Re),c.default(this._element).off(Xe),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},e.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},e._addEventListeners=function(){var t=this;c.default(this._element).on("click.bs.dropdown",function(e){e.preventDefault(),e.stopPropagation(),t.toggle()})},e._getConfig=function(e){return e=o({},this.constructor.Default,c.default(this._element).data(),e),u.typeCheckConfig(Fe,e,this.constructor.DefaultType),e},e._getMenuElement=function(){var e;return this._menu||(e=d._getParentFromElement(this._element))&&(this._menu=e.querySelector(Je)),this._menu},e._getPlacement=function(){var e=c.default(this._element.parentNode),t="bottom-start";return e.hasClass("dropup")?t=c.default(this._menu).hasClass(Ke)?"top-end":"top-start":e.hasClass("dropright")?t="right-start":e.hasClass("dropleft")?t="left-start":c.default(this._menu).hasClass(Ke)&&(t="bottom-end"),t},e._detectNavbar=function(){return 0<c.default(this._element).closest(".navbar").length},e._getOffset=function(){var t=this,e={};return"function"==typeof this._config.offset?e.fn=function(e){return e.offsets=o({},e.offsets,t._config.offset(e.offsets,t._element)||{}),e}:e.offset=this._config.offset,e},e._getPopperConfig=function(){var e={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(e.modifiers.applyStyle={enabled:!1}),o({},e,this._config.popperConfig)},d._jQueryInterface=function(i){return this.each(function(){var e=c.default(this).data(Re),t="object"===_typeof(i)?i:null;if(e||(e=new d(this,t),c.default(this).data(Re,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}})},d._clearMenus=function(e){if(!e||3!==e.which&&("keyup"!==e.type||9===e.which))for(var t=[].slice.call(document.querySelectorAll(Ze)),i=0,n=t.length;i<n;i++){var s,a,r=d._getParentFromElement(t[i]),o=c.default(t[i]).data(Re),l={relatedTarget:t[i]};e&&"click"===e.type&&(l.clickEvent=e),o&&(s=o._menu,c.default(r).hasClass(Qe)&&(e&&("click"===e.type&&/input|textarea/i.test(e.target.tagName)||"keyup"===e.type&&9===e.which)&&c.default.contains(r,e.target)||(a=c.default.Event(Ve,l),c.default(r).trigger(a),a.isDefaultPrevented()||("ontouchstart"in document.documentElement&&c.default(document.body).children().off("mouseover",null,c.default.noop),t[i].setAttribute("aria-expanded","false"),o._popper&&o._popper.destroy(),c.default(s).removeClass(Qe),c.default(r).removeClass(Qe).trigger(c.default.Event(Ge,l))))))}},d._getParentFromElement=function(e){var t,i=u.getSelectorFromElement(e);return(t=i?document.querySelector(i):t)||e.parentNode},d._dataApiKeydownHandler=function(e){if((/input|textarea/i.test(e.target.tagName)?!(32===e.which||27!==e.which&&(40!==e.which&&38!==e.which||c.default(e.target).closest(Je).length)):We.test(e.which))&&!this.disabled&&!c.default(this).hasClass(Ue)){var t=d._getParentFromElement(this),i=c.default(t).hasClass(Qe);if(i||27!==e.which){if(e.preventDefault(),e.stopPropagation(),!i||27===e.which||32===e.which)return 27===e.which&&c.default(t.querySelector(Ze)).trigger("focus"),void c.default(this).trigger("click");i=[].slice.call(t.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(e){return c.default(e).is(":visible")});0!==i.length&&(t=i.indexOf(e.target),38===e.which&&0<t&&t--,40===e.which&&t<i.length-1&&t++,i[t=t<0?0:t].focus())}}},r(d,null,[{key:"VERSION",get:function(){return"4.6.0"}},{key:"Default",get:function(){return et}},{key:"DefaultType",get:function(){return tt}}]),d}();c.default(document).on(qe,Ze,it._dataApiKeydownHandler).on(qe,Je,it._dataApiKeydownHandler).on(p+" keyup.bs.dropdown.data-api",it._clearMenus).on(p,Ze,function(e){e.preventDefault(),e.stopPropagation(),it._jQueryInterface.call(c.default(this),"toggle")}).on(p,".dropdown form",function(e){e.stopPropagation()}),c.default.fn[Fe]=it._jQueryInterface,c.default.fn[Fe].Constructor=it,c.default.fn[Fe].noConflict=function(){return c.default.fn[Fe]=Ye,it._jQueryInterface};var nt="modal",st="bs.modal",at="."+st,rt=c.default.fn[nt],ot={backdrop:!0,keyboard:!0,focus:!0,show:!0},lt={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},dt="hidden"+at,ct="show"+at,ut="focusin"+at,pt="resize"+at,ht="click.dismiss"+at,ft="keydown.dismiss"+at,mt="mousedown.dismiss"+at,gt="modal-open",vt="fade",yt="show",bt="modal-static",wt=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",St=".sticky-top",Tt=function(){function s(e,t){this._config=this._getConfig(t),this._element=e,this._dialog=e.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}var e=s.prototype;return e.toggle=function(e){return this._isShown?this.hide():this.show(e)},e.show=function(e){var t,i=this;this._isShown||this._isTransitioning||(c.default(this._element).hasClass(vt)&&(this._isTransitioning=!0),t=c.default.Event(ct,{relatedTarget:e}),c.default(this._element).trigger(t),this._isShown||t.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),c.default(this._element).on(ht,'[data-dismiss="modal"]',function(e){return i.hide(e)}),c.default(this._dialog).on(mt,function(){c.default(i._element).one("mouseup.dismiss.bs.modal",function(e){c.default(e.target).is(i._element)&&(i._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return i._showElement(e)})))},e.hide=function(e){var t=this;e&&e.preventDefault(),this._isShown&&!this._isTransitioning&&(e=c.default.Event("hide.bs.modal"),c.default(this._element).trigger(e),this._isShown&&!e.isDefaultPrevented()&&(this._isShown=!1,(e=c.default(this._element).hasClass(vt))&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),c.default(document).off(ut),c.default(this._element).removeClass(yt),c.default(this._element).off(ht),c.default(this._dialog).off(mt),e?(e=u.getTransitionDurationFromElement(this._element),c.default(this._element).one(u.TRANSITION_END,function(e){return t._hideModal(e)}).emulateTransitionEnd(e)):this._hideModal()))},e.dispose=function(){[window,this._element,this._dialog].forEach(function(e){return c.default(e).off(at)}),c.default(document).off(ut),c.default.removeData(this._element,st),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},e.handleUpdate=function(){this._adjustDialog()},e._getConfig=function(e){return e=o({},ot,e),u.typeCheckConfig(nt,e,lt),e},e._triggerBackdropTransition=function(){var e,t,i=this,n=c.default.Event("hidePrevented.bs.modal");c.default(this._element).trigger(n),n.isDefaultPrevented()||((e=this._element.scrollHeight>document.documentElement.clientHeight)||(this._element.style.overflowY="hidden"),this._element.classList.add(bt),t=u.getTransitionDurationFromElement(this._dialog),c.default(this._element).off(u.TRANSITION_END),c.default(this._element).one(u.TRANSITION_END,function(){i._element.classList.remove(bt),e||c.default(i._element).one(u.TRANSITION_END,function(){i._element.style.overflowY=""}).emulateTransitionEnd(i._element,t)}).emulateTransitionEnd(t),this._element.focus())},e._showElement=function(e){var t=this,i=c.default(this._element).hasClass(vt),n=this._dialog?this._dialog.querySelector(".modal-body"):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),c.default(this._dialog).hasClass("modal-dialog-scrollable")&&n?n.scrollTop=0:this._element.scrollTop=0,i&&u.reflow(this._element),c.default(this._element).addClass(yt),this._config.focus&&this._enforceFocus();var s=c.default.Event("shown.bs.modal",{relatedTarget:e}),e=function(){t._config.focus&&t._element.focus(),t._isTransitioning=!1,c.default(t._element).trigger(s)};i?(i=u.getTransitionDurationFromElement(this._dialog),c.default(this._dialog).one(u.TRANSITION_END,e).emulateTransitionEnd(i)):e()},e._enforceFocus=function(){var t=this;c.default(document).off(ut).on(ut,function(e){document!==e.target&&t._element!==e.target&&0===c.default(t._element).has(e.target).length&&t._element.focus()})},e._setEscapeEvent=function(){var t=this;this._isShown?c.default(this._element).on(ft,function(e){t._config.keyboard&&27===e.which?(e.preventDefault(),t.hide()):t._config.keyboard||27!==e.which||t._triggerBackdropTransition()}):this._isShown||c.default(this._element).off(ft)},e._setResizeEvent=function(){var t=this;this._isShown?c.default(window).on(pt,function(e){return t.handleUpdate(e)}):c.default(window).off(pt)},e._hideModal=function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop(function(){c.default(document.body).removeClass(gt),e._resetAdjustments(),e._resetScrollbar(),c.default(e._element).trigger(dt)})},e._removeBackdrop=function(){this._backdrop&&(c.default(this._backdrop).remove(),this._backdrop=null)},e._showBackdrop=function(e){var t,i=this,n=c.default(this._element).hasClass(vt)?vt:"";this._isShown&&this._config.backdrop?(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",n&&this._backdrop.classList.add(n),c.default(this._backdrop).appendTo(document.body),c.default(this._element).on(ht,function(e){i._ignoreBackdropClick?i._ignoreBackdropClick=!1:e.target===e.currentTarget&&("static"===i._config.backdrop?i._triggerBackdropTransition():i.hide())}),n&&u.reflow(this._backdrop),c.default(this._backdrop).addClass(yt),e&&(n?(t=u.getTransitionDurationFromElement(this._backdrop),c.default(this._backdrop).one(u.TRANSITION_END,e).emulateTransitionEnd(t)):e())):!this._isShown&&this._backdrop?(c.default(this._backdrop).removeClass(yt),n=function(){i._removeBackdrop(),e&&e()},c.default(this._element).hasClass(vt)?(t=u.getTransitionDurationFromElement(this._backdrop),c.default(this._backdrop).one(u.TRANSITION_END,n).emulateTransitionEnd(t)):n()):e&&e()},e._adjustDialog=function(){var e=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&e&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!e&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},e._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},e._checkScrollbar=function(){var e=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(e.left+e.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},e._setScrollbar=function(){var e,t,s=this;this._isBodyOverflowing&&(e=[].slice.call(document.querySelectorAll(wt)),t=[].slice.call(document.querySelectorAll(St)),c.default(e).each(function(e,t){var i=t.style.paddingRight,n=c.default(t).css("padding-right");c.default(t).data("padding-right",i).css("padding-right",parseFloat(n)+s._scrollbarWidth+"px")}),c.default(t).each(function(e,t){var i=t.style.marginRight,n=c.default(t).css("margin-right");c.default(t).data("margin-right",i).css("margin-right",parseFloat(n)-s._scrollbarWidth+"px")}),e=document.body.style.paddingRight,t=c.default(document.body).css("padding-right"),c.default(document.body).data("padding-right",e).css("padding-right",parseFloat(t)+this._scrollbarWidth+"px")),c.default(document.body).addClass(gt)},e._resetScrollbar=function(){var e=[].slice.call(document.querySelectorAll(wt));c.default(e).each(function(e,t){var i=c.default(t).data("padding-right");c.default(t).removeData("padding-right"),t.style.paddingRight=i||""});e=[].slice.call(document.querySelectorAll(St));c.default(e).each(function(e,t){var i=c.default(t).data("margin-right");void 0!==i&&c.default(t).css("margin-right",i).removeData("margin-right")});e=c.default(document.body).data("padding-right");c.default(document.body).removeData("padding-right"),document.body.style.paddingRight=e||""},e._getScrollbarWidth=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t},s._jQueryInterface=function(i,n){return this.each(function(){var e=c.default(this).data(st),t=o({},ot,c.default(this).data(),"object"===_typeof(i)&&i?i:{});if(e||(e=new s(this,t),c.default(this).data(st,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i](n)}else t.show&&e.show(n)})},r(s,null,[{key:"VERSION",get:function(){return"4.6.0"}},{key:"Default",get:function(){return ot}}]),s}();c.default(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(e){var t,i=this,n=u.getSelectorFromElement(this);n&&(t=document.querySelector(n));n=c.default(t).data(st)?"toggle":o({},c.default(t).data(),c.default(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault();var s=c.default(t).one(ct,function(e){e.isDefaultPrevented()||s.one(dt,function(){c.default(i).is(":visible")&&i.focus()})});Tt._jQueryInterface.call(c.default(t),n,this)}),c.default.fn[nt]=Tt._jQueryInterface,c.default.fn[nt].Constructor=Tt,c.default.fn[nt].noConflict=function(){return c.default.fn[nt]=rt,Tt._jQueryInterface};var xt=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],p={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Et=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,Ct=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function _t(e,a,t){if(0===e.length)return e;if(t&&"function"==typeof t)return t(e);for(var e=(new window.DOMParser).parseFromString(e,"text/html"),r=Object.keys(a),o=[].slice.call(e.body.querySelectorAll("*")),i=function(e,t){var i=o[e],n=i.nodeName.toLowerCase();if(-1===r.indexOf(i.nodeName.toLowerCase()))return i.parentNode.removeChild(i),"continue";var e=[].slice.call(i.attributes),s=[].concat(a["*"]||[],a[n]||[]);e.forEach(function(e){!function(e,t){var i=e.nodeName.toLowerCase();if(-1!==t.indexOf(i))return-1===xt.indexOf(i)||Boolean(e.nodeValue.match(Et)||e.nodeValue.match(Ct));for(var n=t.filter(function(e){return e instanceof RegExp}),s=0,a=n.length;s<a;s++)if(i.match(n[s]))return 1}(e,s)&&i.removeAttribute(e.nodeName)})},n=0,s=o.length;n<s;n++)i(n);return e.body.innerHTML}var kt="tooltip",Mt="bs.tooltip",At="."+Mt,Pt=c.default.fn[kt],Lt="bs-tooltip",Ot=new RegExp("(^|\\s)"+Lt+"\\S+","g"),It=["sanitize","whiteList","sanitizeFn"],Dt={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",customClass:"(string|function)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},$t={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},zt={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",customClass:"",sanitize:!0,sanitizeFn:null,whiteList:p,popperConfig:null},Nt="show",Ht={HIDE:"hide"+At,HIDDEN:"hidden"+At,SHOW:"show"+At,SHOWN:"shown"+At,INSERTED:"inserted"+At,CLICK:"click"+At,FOCUSIN:"focusin"+At,FOCUSOUT:"focusout"+At,MOUSEENTER:"mouseenter"+At,MOUSELEAVE:"mouseleave"+At},qt="fade",jt="show",Bt="hover",Ft=function(){function s(e,t){if(void 0===je)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=e,this.config=this._getConfig(t),this.tip=null,this._setListeners()}var e=s.prototype;return e.enable=function(){this._isEnabled=!0},e.disable=function(){this._isEnabled=!1},e.toggleEnabled=function(){this._isEnabled=!this._isEnabled},e.toggle=function(e){var t,i;this._isEnabled&&(e?(t=this.constructor.DATA_KEY,(i=c.default(e.currentTarget).data(t))||(i=new this.constructor(e.currentTarget,this._getDelegateConfig()),c.default(e.currentTarget).data(t,i)),i._activeTrigger.click=!i._activeTrigger.click,i._isWithActiveTrigger()?i._enter(null,i):i._leave(null,i)):c.default(this.getTipElement()).hasClass(jt)?this._leave(null,this):this._enter(null,this))},e.dispose=function(){clearTimeout(this._timeout),c.default.removeData(this.element,this.constructor.DATA_KEY),c.default(this.element).off(this.constructor.EVENT_KEY),c.default(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&c.default(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},e.show=function(){var t=this;if("none"===c.default(this.element).css("display"))throw new Error("Please use show on visible elements");var e,i,n=c.default.Event(this.constructor.Event.SHOW);this.isWithContent()&&this._isEnabled&&(c.default(this.element).trigger(n),i=u.findShadowRoot(this.element),e=c.default.contains(null!==i?i:this.element.ownerDocument.documentElement,this.element),!n.isDefaultPrevented()&&e&&(i=this.getTipElement(),n=u.getUID(this.constructor.NAME),i.setAttribute("id",n),this.element.setAttribute("aria-describedby",n),this.setContent(),this.config.animation&&c.default(i).addClass(qt),e="function"==typeof this.config.placement?this.config.placement.call(this,i,this.element):this.config.placement,n=this._getAttachment(e),this.addAttachmentClass(n),e=this._getContainer(),c.default(i).data(this.constructor.DATA_KEY,this),c.default.contains(this.element.ownerDocument.documentElement,this.tip)||c.default(i).appendTo(e),c.default(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new je(this.element,i,this._getPopperConfig(n)),c.default(i).addClass(jt),c.default(i).addClass(this.config.customClass),"ontouchstart"in document.documentElement&&c.default(document.body).children().on("mouseover",null,c.default.noop),n=function(){t.config.animation&&t._fixTransition();var e=t._hoverState;t._hoverState=null,c.default(t.element).trigger(t.constructor.Event.SHOWN),"out"===e&&t._leave(null,t)},c.default(this.tip).hasClass(qt)?(i=u.getTransitionDurationFromElement(this.tip),c.default(this.tip).one(u.TRANSITION_END,n).emulateTransitionEnd(i)):n()))},e.hide=function(e){function t(){i._hoverState!==Nt&&n.parentNode&&n.parentNode.removeChild(n),i._cleanTipClass(),i.element.removeAttribute("aria-describedby"),c.default(i.element).trigger(i.constructor.Event.HIDDEN),null!==i._popper&&i._popper.destroy(),e&&e()}var i=this,n=this.getTipElement(),s=c.default.Event(this.constructor.Event.HIDE);c.default(this.element).trigger(s),s.isDefaultPrevented()||(c.default(n).removeClass(jt),"ontouchstart"in document.documentElement&&c.default(document.body).children().off("mouseover",null,c.default.noop),this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger[Bt]=!1,c.default(this.tip).hasClass(qt)?(s=u.getTransitionDurationFromElement(n),c.default(n).one(u.TRANSITION_END,t).emulateTransitionEnd(s)):t(),this._hoverState="")},e.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},e.isWithContent=function(){return Boolean(this.getTitle())},e.addAttachmentClass=function(e){c.default(this.getTipElement()).addClass(Lt+"-"+e)},e.getTipElement=function(){return this.tip=this.tip||c.default(this.config.template)[0],this.tip},e.setContent=function(){var e=this.getTipElement();this.setElementContent(c.default(e.querySelectorAll(".tooltip-inner")),this.getTitle()),c.default(e).removeClass("fade show")},e.setElementContent=function(e,t){"object"!==_typeof(t)||!t.nodeType&&!t.jquery?this.config.html?(this.config.sanitize&&(t=_t(t,this.config.whiteList,this.config.sanitizeFn)),e.html(t)):e.text(t):this.config.html?c.default(t).parent().is(e)||e.empty().append(t):e.text(c.default(t).text())},e.getTitle=function(){return this.element.getAttribute("data-original-title")||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},e._getPopperConfig=function(e){var t=this;return o({},{placement:e,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(e){e.originalPlacement!==e.placement&&t._handlePopperPlacementChange(e)},onUpdate:function(e){return t._handlePopperPlacementChange(e)}},this.config.popperConfig)},e._getOffset=function(){var t=this,e={};return"function"==typeof this.config.offset?e.fn=function(e){return e.offsets=o({},e.offsets,t.config.offset(e.offsets,t.element)||{}),e}:e.offset=this.config.offset,e},e._getContainer=function(){return!1===this.config.container?document.body:u.isElement(this.config.container)?c.default(this.config.container):c.default(document).find(this.config.container)},e._getAttachment=function(e){return $t[e.toUpperCase()]},e._setListeners=function(){var i=this;this.config.trigger.split(" ").forEach(function(e){var t;"click"===e?c.default(i.element).on(i.constructor.Event.CLICK,i.config.selector,function(e){return i.toggle(e)}):"manual"!==e&&(t=e===Bt?i.constructor.Event.MOUSEENTER:i.constructor.Event.FOCUSIN,e=e===Bt?i.constructor.Event.MOUSELEAVE:i.constructor.Event.FOCUSOUT,c.default(i.element).on(t,i.config.selector,function(e){return i._enter(e)}).on(e,i.config.selector,function(e){return i._leave(e)}))}),this._hideModalHandler=function(){i.element&&i.hide()},c.default(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=o({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},e._fixTitle=function(){var e=_typeof(this.element.getAttribute("data-original-title"));!this.element.getAttribute("title")&&"string"===e||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},e._enter=function(e,t){var i=this.constructor.DATA_KEY;(t=t||c.default(e.currentTarget).data(i))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),c.default(e.currentTarget).data(i,t)),e&&(t._activeTrigger["focusin"===e.type?"focus":Bt]=!0),c.default(t.getTipElement()).hasClass(jt)||t._hoverState===Nt?t._hoverState=Nt:(clearTimeout(t._timeout),t._hoverState=Nt,t.config.delay&&t.config.delay.show?t._timeout=setTimeout(function(){t._hoverState===Nt&&t.show()},t.config.delay.show):t.show())},e._leave=function(e,t){var i=this.constructor.DATA_KEY;(t=t||c.default(e.currentTarget).data(i))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),c.default(e.currentTarget).data(i,t)),e&&(t._activeTrigger["focusout"===e.type?"focus":Bt]=!1),t._isWithActiveTrigger()||(clearTimeout(t._timeout),t._hoverState="out",t.config.delay&&t.config.delay.hide?t._timeout=setTimeout(function(){"out"===t._hoverState&&t.hide()},t.config.delay.hide):t.hide())},e._isWithActiveTrigger=function(){for(var e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1},e._getConfig=function(e){var t=c.default(this.element).data();return Object.keys(t).forEach(function(e){-1!==It.indexOf(e)&&delete t[e]}),"number"==typeof(e=o({},this.constructor.Default,t,"object"===_typeof(e)&&e?e:{})).delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),u.typeCheckConfig(kt,e,this.constructor.DefaultType),e.sanitize&&(e.template=_t(e.template,e.whiteList,e.sanitizeFn)),e},e._getDelegateConfig=function(){var e={};if(this.config)for(var t in this.config)this.constructor.Default[t]!==this.config[t]&&(e[t]=this.config[t]);return e},e._cleanTipClass=function(){var e=c.default(this.getTipElement()),t=e.attr("class").match(Ot);null!==t&&t.length&&e.removeClass(t.join(""))},e._handlePopperPlacementChange=function(e){this.tip=e.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(e.placement))},e._fixTransition=function(){var e=this.getTipElement(),t=this.config.animation;null===e.getAttribute("x-placement")&&(c.default(e).removeClass(qt),this.config.animation=!1,this.hide(),this.show(),this.config.animation=t)},s._jQueryInterface=function(n){return this.each(function(){var e=c.default(this),t=e.data(Mt),i="object"===_typeof(n)&&n;if((t||!/dispose|hide/.test(n))&&(t||(t=new s(this,i),e.data(Mt,t)),"string"==typeof n)){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},r(s,null,[{key:"VERSION",get:function(){return"4.6.0"}},{key:"Default",get:function(){return zt}},{key:"NAME",get:function(){return kt}},{key:"DATA_KEY",get:function(){return Mt}},{key:"Event",get:function(){return Ht}},{key:"EVENT_KEY",get:function(){return At}},{key:"DefaultType",get:function(){return Dt}}]),s}();c.default.fn[kt]=Ft._jQueryInterface,c.default.fn[kt].Constructor=Ft,c.default.fn[kt].noConflict=function(){return c.default.fn[kt]=Pt,Ft._jQueryInterface};var Rt="popover",Xt="bs.popover",Yt="."+Xt,Wt=c.default.fn[Rt],Vt="bs-popover",Gt=new RegExp("(^|\\s)"+Vt+"\\S+","g"),Ut=o({},Ft.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),Qt=o({},Ft.DefaultType,{content:"(string|element|function)"}),Kt={HIDE:"hide"+Yt,HIDDEN:"hidden"+Yt,SHOW:"show"+Yt,SHOWN:"shown"+Yt,INSERTED:"inserted"+Yt,CLICK:"click"+Yt,FOCUSIN:"focusin"+Yt,FOCUSOUT:"focusout"+Yt,MOUSEENTER:"mouseenter"+Yt,MOUSELEAVE:"mouseleave"+Yt},Zt=function(e){var t;function n(){return e.apply(this,arguments)||this}i=e,(t=n).prototype=Object.create(i.prototype),(t.prototype.constructor=t).__proto__=i;var i=n.prototype;return i.isWithContent=function(){return this.getTitle()||this._getContent()},i.addAttachmentClass=function(e){c.default(this.getTipElement()).addClass(Vt+"-"+e)},i.getTipElement=function(){return this.tip=this.tip||c.default(this.config.template)[0],this.tip},i.setContent=function(){var e=c.default(this.getTipElement());this.setElementContent(e.find(".popover-header"),this.getTitle());var t=this._getContent();"function"==typeof t&&(t=t.call(this.element)),this.setElementContent(e.find(".popover-body"),t),e.removeClass("fade show")},i._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},i._cleanTipClass=function(){var e=c.default(this.getTipElement()),t=e.attr("class").match(Gt);null!==t&&0<t.length&&e.removeClass(t.join(""))},n._jQueryInterface=function(i){return this.each(function(){var e=c.default(this).data(Xt),t="object"===_typeof(i)?i:null;if((e||!/dispose|hide/.test(i))&&(e||(e=new n(this,t),c.default(this).data(Xt,e)),"string"==typeof i)){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}})},r(n,null,[{key:"VERSION",get:function(){return"4.6.0"}},{key:"Default",get:function(){return Ut}},{key:"NAME",get:function(){return Rt}},{key:"DATA_KEY",get:function(){return Xt}},{key:"Event",get:function(){return Kt}},{key:"EVENT_KEY",get:function(){return Yt}},{key:"DefaultType",get:function(){return Qt}}]),n}(Ft);c.default.fn[Rt]=Zt._jQueryInterface,c.default.fn[Rt].Constructor=Zt,c.default.fn[Rt].noConflict=function(){return c.default.fn[Rt]=Wt,Zt._jQueryInterface};var Jt="scrollspy",ei="bs.scrollspy",ti="."+ei,ii=c.default.fn[Jt],ni={offset:10,method:"auto",target:""},si={offset:"number",method:"string",target:"(string|element)"},ai="active",ri=".nav, .list-group",oi=".nav-link",li=".list-group-item",di=function(){function n(e,t){var i=this;this._element=e,this._scrollElement="BODY"===e.tagName?window:e,this._config=this._getConfig(t),this._selector=this._config.target+" "+oi+","+this._config.target+" "+li+","+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,c.default(this._scrollElement).on("scroll.bs.scrollspy",function(e){return i._process(e)}),this.refresh(),this._process()}var e=n.prototype;return e.refresh=function(){var t=this,e=this._scrollElement===this._scrollElement.window?"offset":"position",n="auto"===this._config.method?e:this._config.method,s="position"===n?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(e){var t,i=u.getSelectorFromElement(e);if(t=i?document.querySelector(i):t){e=t.getBoundingClientRect();if(e.width||e.height)return[c.default(t)[n]().top+s,i]}return null}).filter(function(e){return e}).sort(function(e,t){return e[0]-t[0]}).forEach(function(e){t._offsets.push(e[0]),t._targets.push(e[1])})},e.dispose=function(){c.default.removeData(this._element,ei),c.default(this._scrollElement).off(ti),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},e._getConfig=function(e){var t;return"string"!=typeof(e=o({},ni,"object"===_typeof(e)&&e?e:{})).target&&u.isElement(e.target)&&((t=c.default(e.target).attr("id"))||(t=u.getUID(Jt),c.default(e.target).attr("id",t)),e.target="#"+t),u.typeCheckConfig(Jt,e,si),e},e._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},e._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},e._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},e._process=function(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),i=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),i<=e){i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&e<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var n=this._offsets.length;n--;)this._activeTarget!==this._targets[n]&&e>=this._offsets[n]&&(void 0===this._offsets[n+1]||e<this._offsets[n+1])&&this._activate(this._targets[n])}},e._activate=function(t){this._activeTarget=t,this._clear();var e=this._selector.split(",").map(function(e){return e+'[data-target="'+t+'"],'+e+'[href="'+t+'"]'}),e=c.default([].slice.call(document.querySelectorAll(e.join(","))));e.hasClass("dropdown-item")?(e.closest(".dropdown").find(".dropdown-toggle").addClass(ai),e.addClass(ai)):(e.addClass(ai),e.parents(ri).prev(oi+", "+li).addClass(ai),e.parents(ri).prev(".nav-item").children(oi).addClass(ai)),c.default(this._scrollElement).trigger("activate.bs.scrollspy",{relatedTarget:t})},e._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(e){return e.classList.contains(ai)}).forEach(function(e){return e.classList.remove(ai)})},n._jQueryInterface=function(i){return this.each(function(){var e=c.default(this).data(ei),t="object"===_typeof(i)&&i;if(e||(e=new n(this,t),c.default(this).data(ei,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}})},r(n,null,[{key:"VERSION",get:function(){return"4.6.0"}},{key:"Default",get:function(){return ni}}]),n}();c.default(window).on("load.bs.scrollspy.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),t=e.length;t--;){var i=c.default(e[t]);di._jQueryInterface.call(i,i.data())}}),c.default.fn[Jt]=di._jQueryInterface,c.default.fn[Jt].Constructor=di,c.default.fn[Jt].noConflict=function(){return c.default.fn[Jt]=ii,di._jQueryInterface};var ci="bs.tab",p="."+ci,ui=c.default.fn.tab,pi="active",hi="> li > .active",fi=function(){function n(e){this._element=e}var e=n.prototype;return e.show=function(){var e,t,i,n,s,a,r=this;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&c.default(this._element).hasClass(pi)||c.default(this._element).hasClass("disabled")||(a=c.default(this._element).closest(".nav, .list-group")[0],t=u.getSelectorFromElement(this._element),a&&(s="UL"===a.nodeName||"OL"===a.nodeName?hi:".active",i=(i=c.default.makeArray(c.default(a).find(s)))[i.length-1]),n=c.default.Event("hide.bs.tab",{relatedTarget:this._element}),s=c.default.Event("show.bs.tab",{relatedTarget:i}),i&&c.default(i).trigger(n),c.default(this._element).trigger(s),s.isDefaultPrevented()||n.isDefaultPrevented()||(t&&(e=document.querySelector(t)),this._activate(this._element,a),a=function(){var e=c.default.Event("hidden.bs.tab",{relatedTarget:r._element}),t=c.default.Event("shown.bs.tab",{relatedTarget:i});c.default(i).trigger(e),c.default(r._element).trigger(t)},e?this._activate(e,e.parentNode,a):a()))},e.dispose=function(){c.default.removeData(this._element,ci),this._element=null},e._activate=function(e,t,i){var n=this,s=(!t||"UL"!==t.nodeName&&"OL"!==t.nodeName?c.default(t).children(".active"):c.default(t).find(hi))[0],a=i&&s&&c.default(s).hasClass("fade"),t=function(){return n._transitionComplete(e,s,i)};s&&a?(a=u.getTransitionDurationFromElement(s),c.default(s).removeClass("show").one(u.TRANSITION_END,t).emulateTransitionEnd(a)):t()},e._transitionComplete=function(e,t,i){var n;t&&(c.default(t).removeClass(pi),(n=c.default(t.parentNode).find("> .dropdown-menu .active")[0])&&c.default(n).removeClass(pi),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!1)),c.default(e).addClass(pi),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),u.reflow(e),e.classList.contains("fade")&&e.classList.add("show"),e.parentNode&&c.default(e.parentNode).hasClass("dropdown-menu")&&((t=c.default(e).closest(".dropdown")[0])&&(t=[].slice.call(t.querySelectorAll(".dropdown-toggle")),c.default(t).addClass(pi)),e.setAttribute("aria-expanded",!0)),i&&i()},n._jQueryInterface=function(i){return this.each(function(){var e=c.default(this),t=e.data(ci);if(t||(t=new n(this),e.data(ci,t)),"string"==typeof i){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}})},r(n,null,[{key:"VERSION",get:function(){return"4.6.0"}}]),n}();c.default(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(e){e.preventDefault(),fi._jQueryInterface.call(c.default(this),"show")}),c.default.fn.tab=fi._jQueryInterface,c.default.fn.tab.Constructor=fi,c.default.fn.tab.noConflict=function(){return c.default.fn.tab=ui,fi._jQueryInterface};var mi="toast",gi="bs.toast",p="."+gi,vi=c.default.fn[mi],yi="click.dismiss"+p,bi="show",wi={animation:"boolean",autohide:"boolean",delay:"number"},Si={animation:!0,autohide:!0,delay:500},Ti=function(){function s(e,t){this._element=e,this._config=this._getConfig(t),this._timeout=null,this._setListeners()}var e=s.prototype;return e.show=function(){var e,t=this,i=c.default.Event("show.bs.toast");c.default(this._element).trigger(i),i.isDefaultPrevented()||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),e=function(){t._element.classList.remove("showing"),t._element.classList.add(bi),c.default(t._element).trigger("shown.bs.toast"),t._config.autohide&&(t._timeout=setTimeout(function(){t.hide()},t._config.delay))},this._element.classList.remove("hide"),u.reflow(this._element),this._element.classList.add("showing"),this._config.animation?(i=u.getTransitionDurationFromElement(this._element),c.default(this._element).one(u.TRANSITION_END,e).emulateTransitionEnd(i)):e())},e.hide=function(){var e;this._element.classList.contains(bi)&&(e=c.default.Event("hide.bs.toast"),c.default(this._element).trigger(e),e.isDefaultPrevented()||this._close())},e.dispose=function(){this._clearTimeout(),this._element.classList.contains(bi)&&this._element.classList.remove(bi),c.default(this._element).off(yi),c.default.removeData(this._element,gi),this._element=null,this._config=null},e._getConfig=function(e){return e=o({},Si,c.default(this._element).data(),"object"===_typeof(e)&&e?e:{}),u.typeCheckConfig(mi,e,this.constructor.DefaultType),e},e._setListeners=function(){var e=this;c.default(this._element).on(yi,'[data-dismiss="toast"]',function(){return e.hide()})},e._close=function(){function e(){i._element.classList.add("hide"),c.default(i._element).trigger("hidden.bs.toast")}var t,i=this;this._element.classList.remove(bi),this._config.animation?(t=u.getTransitionDurationFromElement(this._element),c.default(this._element).one(u.TRANSITION_END,e).emulateTransitionEnd(t)):e()},e._clearTimeout=function(){clearTimeout(this._timeout),this._timeout=null},s._jQueryInterface=function(n){return this.each(function(){var e=c.default(this),t=e.data(gi),i="object"===_typeof(n)&&n;if(t||(t=new s(this,i),e.data(gi,t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n](this)}})},r(s,null,[{key:"VERSION",get:function(){return"4.6.0"}},{key:"DefaultType",get:function(){return wi}},{key:"Default",get:function(){return Si}}]),s}();c.default.fn[mi]=Ti._jQueryInterface,c.default.fn[mi].Constructor=Ti,c.default.fn[mi].noConflict=function(){return c.default.fn[mi]=vi,Ti._jQueryInterface},e.Alert=f,e.Button=w,e.Carousel=I,e.Collapse=X,e.Dropdown=it,e.Modal=Tt,e.Popover=Zt,e.Scrollspy=di,e.Tab=fi,e.Toast=Ti,e.Tooltip=Ft,e.Util=u,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=scripts.min.js.map
