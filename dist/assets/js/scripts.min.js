function _typeof2(e){return(_typeof2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function setupResponsiveMenu(){$(".navbar-nav .nav-pills .nav-link").off("click.bs.dropdown"),$(".navbar-nav > .dropdown").off("mouseenter mouseleave"),$(".navbar-nav > .dropdown > .dropdown-menu .nav-pills .nav-item .nav-link").off("mouseenter"),$('.navbar-nav > .dropdown > .dropdown-menu .nav-pills .nav-item a[data-toggle="pill"]').off("shown.bs.tab"),$(".navbar-toggler, #navMobileOverlay").off("click"),$(".navbar-nav .tab-pane").off("click",'a[data-toggle="collapse"]'),window.matchMedia("(min-width: 992px)").matches?($(".navbar-nav .nav-pills .nav-link").on("click.bs.dropdown",function(e){$(this).tab("show"),e.stopPropagation()}),$(".navbar-nav > .dropdown").on({mouseenter:function(){var e=$(this);e.addClass("show"),e.find("> .dropdown-toggle").attr("aria-expanded","true"),e.find("> .dropdown-menu").addClass("show"),e.find("> .dropdown-menu .nav-pills .nav-item:first-child > .nav-link").tab("show")},mouseleave:function(){var e=$(this);e.removeClass("show"),e.find("> .dropdown-toggle").attr("aria-expanded","false"),e.find("> .dropdown-menu").removeClass("show")}}),$(".navbar-nav > .dropdown > .dropdown-menu .nav-pills .nav-item .nav-link").on("mouseenter",function(){clearTimeout(void 0),"pill"===$(this).attr("data-toggle")?$(this).tab("show"):($(this).closest(".nav-pills").find(".nav-link").removeClass("active").attr("aria-selected","false"),$(this).closest(".dropdown-menu").find(".tab-content .tab-pane").removeClass("show active"))}),$(".navbar-nav > .dropdown > .dropdown-menu .nav-pills").on("mouseleave",function(){}),$(".navbar-nav > .dropdown > .dropdown-menu .tab-content").on("mouseenter",function(){clearTimeout(void 0)}),$(".navbar-nav > .dropdown > .dropdown-menu .tab-content").on("mouseleave",function(){})):window.matchMedia("(max-width: 991.98px)").matches&&($(".navbar-toggler, #navMobileOverlay").on("click",function(){$(".navbar-toggler").toggleClass("is-active"),$("#navMobileOverlay").toggleClass("show"),$("#mainNavigation").toggleClass("slide"),$("body").toggleClass("modal-open"),$(this).attr("aria-expanded",function(e,t){return"true"==t?"false":"true"}),$(".navbar-toggler").hasClass("is-active")||setTimeout(function(){$(".navbar-nav .dropdown").removeClass("show"),$(".navbar-nav .dropdown-toggle, .navbar-nav .tab-content .tab-pane .card-header a").addClass("collapsed"),$(".navbar-nav .dropdown-toggle, .navbar-nav .tab-content .tab-pane .card-header a").attr("aria-expanded","false"),$(".navbar-nav .dropdown-menu").removeClass("show"),$(".navbar-nav .tab-content .tab-pane .card-header").removeClass("active"),$(".navbar-nav .tab-content .tab-pane .collapse").removeClass("show")},300)}),$(".navbar-nav .tab-pane").on("click",'a[data-toggle="collapse"]',function(e){$($(this).attr("data-target")).collapse("toggle"),e.stopPropagation()}))}(e=>{"object"===("undefined"==typeof exports?"undefined":_typeof2(exports))&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):self.GLightbox=e()})(function(){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function e(e,t,i){t&&n(e.prototype,t),i&&n(e,i)}var l=Date.now();function d(e){var t={},i=!0,n=0,o=arguments.length;for("[object Boolean]"===Object.prototype.toString.call(e)&&(i=e,n++);n<o;n++){s=r=void 0;var s,r=arguments[n];for(s in r)Object.prototype.hasOwnProperty.call(r,s)&&(i&&"[object Object]"===Object.prototype.toString.call(r[s])?t[s]=d(!0,t[s],r[s]):t[s]=r[s])}return t}function h(e,t){if(0!=A(e=$(e=!F(e)&&e!==window&&e!==document?e:[e])||c(e)?e:[e]))if($(e)&&!c(e))for(var i=e.length,n=0;n<i&&!1!==t.call(e[n],e[n],n,e);n++);else if(c(e))for(var o in e)if(w(e,o)&&!1===t.call(e[o],e[o],o,e))break}function k(e,t,i){var n=1<arguments.length&&void 0!==t?t:null,o=2<arguments.length&&void 0!==i?i:null,t=e[l]=e[l]||[],s={all:t,evt:null,found:null};return n&&o&&0<A(t)&&h(t,function(e,t){if(e.eventName==n&&e.fn.toString()==o.toString())return s.found=!0,s.evt=t,!1}),s}function D(i,e,t){var e=1<arguments.length&&void 0!==e?e:{},n=e.onElement,o=e.withCallback,s=e.avoidDuplicate,r=void 0===s||s,s=e.once,l=void 0!==s&&s,s=e.useCapture,a=void 0!==s&&s,d=2<arguments.length?t:void 0,c=n||[];function u(e){q(o)&&o.call(d,e,this),l&&u.destroy()}return b(c)&&(c=document.querySelectorAll(c)),u.destroy=function(){h(c,function(e){var t=k(e,i,u);t.found&&t.all.splice(t.evt,1),e.removeEventListener&&e.removeEventListener(i,u,a)})},h(c,function(e){var t=k(e,i,u);(e.addEventListener&&r&&!t.found||!r)&&(e.addEventListener(i,u,a),t.all.push({eventName:i,fn:u}))}),u}function P(t,e){h(e.split(" "),function(e){return t.classList.add(e)})}function L(t,e){h(e.split(" "),function(e){return t.classList.remove(e)})}function M(e,t){return e.classList.contains(t)}function H(e,t){for(;e!==document.body;){if(!(e=e.parentElement))return!1;if("function"==typeof e.matches?e.matches(t):e.msMatchesSelector(t))return e}}function j(t,e,i){var n,e=1<arguments.length&&void 0!==e?e:"",o=2<arguments.length&&void 0!==i&&i;t&&""!==e&&("none"===e?q(o)&&o():(i=(()=>{var e,t=document.createElement("fakeelement"),i={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"animationend",WebkitAnimation:"webkitAnimationEnd"};for(e in i)if(void 0!==t.style[e])return i[e]})(),h(n=e.split(" "),function(e){P(t,"g"+e)}),D(i,{onElement:t,avoidDuplicate:!1,once:!0,withCallback:function(e,t){h(n,function(e){L(t,"g"+e)}),q(o)&&o()}})))}function z(e,t){t=1<arguments.length&&void 0!==t?t:"";if(""===t)return e.style.webkitTransform="",e.style.MozTransform="",e.style.msTransform="",e.style.OTransform="",e.style.transform="",!1;e.style.webkitTransform=t,e.style.MozTransform=t,e.style.msTransform=t,e.style.OTransform=t,e.style.transform=t}function C(e){e.style.display="block"}function a(e){e.style.display="none"}function m(e){var t=document.createDocumentFragment(),i=document.createElement("div");for(i.innerHTML=e;i.firstChild;)t.appendChild(i.firstChild);return t}function X(){return{width:window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,height:window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight}}function v(e,t,i,n){var o,s;e()?t():(i=i||100,s=setInterval(function(){e()&&(clearInterval(s),o&&clearTimeout(o),t())},i),n&&(o=setTimeout(function(){clearInterval(s)},n)))}function x(e,t,i){var n,o,s;if(W(e))console.error("Inject assets error");else if(q(t)&&(i=t,t=!1),b(t)&&t in window)q(i)&&i();else if(-1!==e.indexOf(".css"))(n=document.querySelectorAll('link[href="'+e+'"]'))&&0<n.length||(r=(o=document.getElementsByTagName("head")[0]).querySelectorAll('link[rel="stylesheet"]'),(s=document.createElement("link")).rel="stylesheet",s.type="text/css",s.href=e,s.media="all",r?o.insertBefore(s,r[0]):o.appendChild(s)),q(i)&&i();else if((n=document.querySelectorAll('script[src="'+e+'"]'))&&0<n.length){if(q(i)){if(b(t))return void v(function(){return void 0!==window[t]},function(){i()});i()}}else{var r=document.createElement("script");r.type="text/javascript",r.src=e,r.onload=function(){if(q(i)){if(b(t))return v(function(){return void 0!==window[t]},function(){i()}),!1;i()}},document.body.appendChild(r)}}function y(){return"navigator"in window&&window.navigator.userAgent.match(/(iPad)|(iPhone)|(iPod)|(Android)|(PlayBook)|(BB10)|(BlackBerry)|(Opera Mini)|(IEMobile)|(webOS)|(MeeGo)/i)}function q(e){return"function"==typeof e}function b(e){return"string"==typeof e}function F(e){return e&&e.nodeType&&1==e.nodeType}function E(e){return Array.isArray(e)}function $(e){return e&&e.length&&isFinite(e.length)}function c(e){return"object"===t(e)&&null!=e&&!q(e)&&!E(e)}function W(e){return null==e}function w(e,t){return null!==e&&hasOwnProperty.call(e,t)}function A(e){if(c(e)){if(e.keys)return e.keys().length;var t,i=0;for(t in e)w(e,t)&&i++;return i}return e.length}function B(e){return!isNaN(parseFloat(e))&&isFinite(e)}function Y(e){var e=0<arguments.length&&void 0!==e?e:-1,t=document.querySelectorAll(".gbtn[data-taborder]:not(.disabled)");if(!t.length)return!1;if(1==t.length)return t[0];"string"==typeof e&&(e=parseInt(e));var i=[],t=(h(t,function(e){i.push(e.getAttribute("data-taborder"))}),Math.max.apply(Math,i.map(function(e){return parseInt(e)}))),n=e<0?1:e+1;t<n&&(n="1");e=i.filter(function(e){return e>=parseInt(n)}).sort()[0];return document.querySelector('.gbtn[data-taborder="'.concat(e,'"]'))}function u(e){return Math.sqrt(e.x*e.x+e.y*e.y)}function O(e,t){o=t;var i,n,o=0==(n=u(i=e)*u(o))?0:(1<(i=(i.x*o.x+i.y*o.y)/n)&&(i=1),Math.acos(i));return 0<e.x*t.y-t.x*e.y&&(o*=-1),180*o/Math.PI}e(i,[{key:"add",value:function(e){this.handlers.push(e)}},{key:"del",value:function(e){e||(this.handlers=[]);for(var t=this.handlers.length;0<=t;t--)this.handlers[t]===e&&this.handlers.splice(t,1)}},{key:"dispatch",value:function(){for(var e=0,t=this.handlers.length;e<t;e++){var i=this.handlers[e];"function"==typeof i&&i.apply(this.el,arguments)}}}]);var I=i;function i(e){r(this,i),this.handlers=[],this.el=e}function o(e,t){e=new I(e);return e.add(t),e}e(s,[{key:"start",value:function(e){var t,i;e.touches&&(e.target&&e.target.nodeName&&0<=["a","button","input"].indexOf(e.target.nodeName.toLowerCase())?console.log("ignore drag for this touched element",e.target.nodeName.toLowerCase()):(this.now=Date.now(),this.x1=e.touches[0].pageX,this.y1=e.touches[0].pageY,this.delta=this.now-(this.last||this.now),this.touchStart.dispatch(e,this.element),null!==this.preTapPosition.x&&(this.isDoubleTap=0<this.delta&&this.delta<=250&&Math.abs(this.preTapPosition.x-this.x1)<30&&Math.abs(this.preTapPosition.y-this.y1)<30,this.isDoubleTap)&&clearTimeout(this.singleTapTimeout),this.preTapPosition.x=this.x1,this.preTapPosition.y=this.y1,this.last=this.now,t=this.preV,1<e.touches.length&&(this._cancelLongTap(),this._cancelSingleTap(),i={x:e.touches[1].pageX-this.x1,y:e.touches[1].pageY-this.y1},t.x=i.x,t.y=i.y,this.pinchStartLen=u(t),this.multipointStart.dispatch(e,this.element)),this._preventTap=!1,this.longTapTimeout=setTimeout(function(){this.longTap.dispatch(e,this.element),this._preventTap=!0}.bind(this),750)))}},{key:"move",value:function(e){var t,i,n,o,s,r,l;e.touches&&(r=this.preV,t=e.touches.length,i=e.touches[0].pageX,n=e.touches[0].pageY,this.isDoubleTap=!1,1<t?(o=e.touches[1].pageX,s=e.touches[1].pageY,l={x:e.touches[1].pageX-i,y:e.touches[1].pageY-n},null!==r.x&&(0<this.pinchStartLen&&(e.zoom=u(l)/this.pinchStartLen,this.pinch.dispatch(e,this.element)),e.angle=O(l,r),this.rotate.dispatch(e,this.element)),r.x=l.x,r.y=l.y,null!==this.x2&&null!==this.sx2?(e.deltaX=(i-this.x2+o-this.sx2)/2,e.deltaY=(n-this.y2+s-this.sy2)/2):(e.deltaX=0,e.deltaY=0),this.twoFingerPressMove.dispatch(e,this.element),this.sx2=o,this.sy2=s):(null!==this.x2?(e.deltaX=i-this.x2,e.deltaY=n-this.y2,r=Math.abs(this.x1-this.x2),l=Math.abs(this.y1-this.y2),(10<r||10<l)&&(this._preventTap=!0)):(e.deltaX=0,e.deltaY=0),this.pressMove.dispatch(e,this.element)),this.touchMove.dispatch(e,this.element),this._cancelLongTap(),this.x2=i,this.y2=n,1<t)&&e.preventDefault()}},{key:"end",value:function(e){var t;e.changedTouches&&(this._cancelLongTap(),t=this,e.touches.length<2&&(this.multipointEnd.dispatch(e,this.element),this.sx2=this.sy2=null),this.x2&&30<Math.abs(this.x1-this.x2)||this.y2&&30<Math.abs(this.y1-this.y2)?(e.direction=this._swipeDirection(this.x1,this.x2,this.y1,this.y2),this.swipeTimeout=setTimeout(function(){t.swipe.dispatch(e,t.element)},0)):(this.tapTimeout=setTimeout(function(){t._preventTap||t.tap.dispatch(e,t.element),t.isDoubleTap&&(t.doubleTap.dispatch(e,t.element),t.isDoubleTap=!1)},0),t.isDoubleTap||(t.singleTapTimeout=setTimeout(function(){t.singleTap.dispatch(e,t.element)},250))),this.touchEnd.dispatch(e,this.element),this.preV.x=0,this.preV.y=0,this.zoom=1,this.pinchStartLen=null,this.x1=this.x2=this.y1=this.y2=null)}},{key:"cancelAll",value:function(){this._preventTap=!0,clearTimeout(this.singleTapTimeout),clearTimeout(this.tapTimeout),clearTimeout(this.longTapTimeout),clearTimeout(this.swipeTimeout)}},{key:"cancel",value:function(e){this.cancelAll(),this.touchCancel.dispatch(e,this.element)}},{key:"_cancelLongTap",value:function(){clearTimeout(this.longTapTimeout)}},{key:"_cancelSingleTap",value:function(){clearTimeout(this.singleTapTimeout)}},{key:"_swipeDirection",value:function(e,t,i,n){return Math.abs(e-t)>=Math.abs(i-n)?0<e-t?"Left":"Right":0<i-n?"Up":"Down"}},{key:"on",value:function(e,t){this[e]&&this[e].add(t)}},{key:"off",value:function(e,t){this[e]&&this[e].del(t)}},{key:"destroy",value:function(){return this.singleTapTimeout&&clearTimeout(this.singleTapTimeout),this.tapTimeout&&clearTimeout(this.tapTimeout),this.longTapTimeout&&clearTimeout(this.longTapTimeout),this.swipeTimeout&&clearTimeout(this.swipeTimeout),this.element.removeEventListener("touchstart",this.start),this.element.removeEventListener("touchmove",this.move),this.element.removeEventListener("touchend",this.end),this.element.removeEventListener("touchcancel",this.cancel),this.rotate.del(),this.touchStart.del(),this.multipointStart.del(),this.multipointEnd.del(),this.pinch.del(),this.swipe.del(),this.tap.del(),this.doubleTap.del(),this.longTap.del(),this.singleTap.del(),this.pressMove.del(),this.twoFingerPressMove.del(),this.touchMove.del(),this.touchEnd.del(),this.touchCancel.del(),this.preV=this.pinchStartLen=this.zoom=this.isDoubleTap=this.delta=this.last=this.now=this.tapTimeout=this.singleTapTimeout=this.longTapTimeout=this.swipeTimeout=this.x1=this.x2=this.y1=this.y2=this.preTapPosition=this.rotate=this.touchStart=this.multipointStart=this.multipointEnd=this.pinch=this.swipe=this.tap=this.doubleTap=this.longTap=this.singleTap=this.pressMove=this.touchMove=this.touchEnd=this.touchCancel=this.twoFingerPressMove=null,window.removeEventListener("scroll",this._cancelAllHandler),null}}]);var U=s;function s(e,t){r(this,s),this.element="string"==typeof e?document.querySelector(e):e,this.start=this.start.bind(this),this.move=this.move.bind(this),this.end=this.end.bind(this),this.cancel=this.cancel.bind(this),this.element.addEventListener("touchstart",this.start,!1),this.element.addEventListener("touchmove",this.move,!1),this.element.addEventListener("touchend",this.end,!1),this.element.addEventListener("touchcancel",this.cancel,!1),this.preV={x:null,y:null},this.pinchStartLen=null,this.zoom=1,this.isDoubleTap=!1;function i(){}this.rotate=o(this.element,t.rotate||i),this.touchStart=o(this.element,t.touchStart||i),this.multipointStart=o(this.element,t.multipointStart||i),this.multipointEnd=o(this.element,t.multipointEnd||i),this.pinch=o(this.element,t.pinch||i),this.swipe=o(this.element,t.swipe||i),this.tap=o(this.element,t.tap||i),this.doubleTap=o(this.element,t.doubleTap||i),this.longTap=o(this.element,t.longTap||i),this.singleTap=o(this.element,t.singleTap||i),this.pressMove=o(this.element,t.pressMove||i),this.twoFingerPressMove=o(this.element,t.twoFingerPressMove||i),this.touchMove=o(this.element,t.touchMove||i),this.touchEnd=o(this.element,t.touchEnd||i),this.touchCancel=o(this.element,t.touchCancel||i),this.translateContainer=this.element,this._cancelAllHandler=this.cancelAll.bind(this),window.addEventListener("scroll",this._cancelAllHandler),this.delta=null,this.last=null,this.now=null,this.tapTimeout=null,this.singleTapTimeout=null,this.longTapTimeout=null,this.swipeTimeout=null,this.x1=this.x2=this.y1=this.y2=null,this.preTapPosition={x:null,y:null}}function R(e){var t=(()=>{var e,t=document.createElement("fakeelement"),i={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"};for(e in i)if(void 0!==t.style[e])return i[e]})(),i=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,n=M(e,"gslide-media")?e:e.querySelector(".gslide-media"),o=H(n,".ginner-container"),e=e.querySelector(".gslide-description");P(n=769<i?o:n,"greset"),z(n,"translate3d(0, 0, 0)"),D(t,{onElement:n,once:!0,withCallback:function(e,t){L(n,"greset")}}),n.style.opacity="",e&&(e.style.opacity="")}e(p,[{key:"zoomIn",value:function(){var e,t=this.widowWidth();this.zoomedIn||t<=768||((e=this.img).setAttribute("data-style",e.getAttribute("style")),e.style.maxWidth=e.naturalWidth+"px",e.style.maxHeight=e.naturalHeight+"px",e.naturalWidth>t&&(t=t/2-e.naturalWidth/2,this.setTranslate(this.img.parentNode,t,0)),this.slide.classList.add("zoomed"),this.zoomedIn=!0)}},{key:"zoomOut",value:function(){this.img.parentNode.setAttribute("style",""),this.img.setAttribute("style",this.img.getAttribute("data-style")),this.slide.classList.remove("zoomed"),this.zoomedIn=!1,this.currentX=null,this.currentY=null,this.initialX=null,this.initialY=null,this.xOffset=0,this.yOffset=0,this.onclose&&"function"==typeof this.onclose&&this.onclose()}},{key:"dragStart",value:function(e){e.preventDefault(),this.zoomedIn?("touchstart"===e.type?(this.initialX=e.touches[0].clientX-this.xOffset,this.initialY=e.touches[0].clientY-this.yOffset):(this.initialX=e.clientX-this.xOffset,this.initialY=e.clientY-this.yOffset),e.target===this.img&&(this.active=!0,this.img.classList.add("dragging"))):this.active=!1}},{key:"dragEnd",value:function(e){var t=this;e.preventDefault(),this.initialX=this.currentX,this.initialY=this.currentY,this.active=!1,setTimeout(function(){t.dragging=!1,t.img.isDragging=!1,t.img.classList.remove("dragging")},100)}},{key:"drag",value:function(e){this.active&&(e.preventDefault(),"touchmove"===e.type?(this.currentX=e.touches[0].clientX-this.initialX,this.currentY=e.touches[0].clientY-this.initialY):(this.currentX=e.clientX-this.initialX,this.currentY=e.clientY-this.initialY),this.xOffset=this.currentX,this.yOffset=this.currentY,this.img.isDragging=!0,this.dragging=!0,this.setTranslate(this.img,this.currentX,this.currentY))}},{key:"onMove",value:function(e){var t;this.zoomedIn&&(t=e.clientX-this.img.naturalWidth/2,e=e.clientY-this.img.naturalHeight/2,this.setTranslate(this.img,t,e))}},{key:"setTranslate",value:function(e,t,i){e.style.transform="translate3d("+t+"px, "+i+"px, 0)"}},{key:"widowWidth",value:function(){return window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth}}]);var N=p;function p(e,t){var i=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(r(this,p),this.img=e,this.slide=t,this.onclose=n,this.img.setZoomEvents)return!1;this.active=!1,this.zoomedIn=!1,this.dragging=!1,this.currentX=null,this.currentY=null,this.initialX=null,this.initialY=null,this.xOffset=0,this.yOffset=0,this.img.addEventListener("mousedown",function(e){return i.dragStart(e)},!1),this.img.addEventListener("mouseup",function(e){return i.dragEnd(e)},!1),this.img.addEventListener("mousemove",function(e){return i.drag(e)},!1),this.img.addEventListener("click",function(e){return i.slide.classList.contains("dragging-nav")?(i.zoomOut(),!1):i.zoomedIn?void(i.zoomedIn&&!i.dragging&&i.zoomOut()):i.zoomIn()},!1),this.img.setZoomEvents=!0}e(f,[{key:"dragStart",value:function(e){var t;this.slide.classList.contains("zoomed")||("touchstart"===e.type?(this.initialX=e.touches[0].clientX-this.xOffset,this.initialY=e.touches[0].clientY-this.yOffset):(this.initialX=e.clientX-this.xOffset,this.initialY=e.clientY-this.yOffset),t=e.target.nodeName.toLowerCase(),e.target.classList.contains("nodrag"))||H(e.target,".nodrag")||-1!==["input","select","textarea","button","a"].indexOf(t)?this.active=!1:(e.preventDefault(),(e.target===this.el||"img"!==t&&H(e.target,".gslide-inline"))&&(this.active=!0,this.el.classList.add("dragging"),this.dragContainer=H(e.target,".ginner-container")))}},{key:"dragEnd",value:function(e){var t=this;e&&e.preventDefault(),this.initialX=0,this.initialY=0,this.currentX=null,this.currentY=null,this.initialX=null,this.initialY=null,this.xOffset=0,this.yOffset=0,this.active=!1,this.doSlideChange&&(this.instance.preventOutsideClick=!0,"right"==this.doSlideChange&&this.instance.prevSlide(),"left"==this.doSlideChange)&&this.instance.nextSlide(),this.doSlideClose&&this.instance.close(),this.toleranceReached||this.setTranslate(this.dragContainer,0,0,!0),setTimeout(function(){t.instance.preventOutsideClick=!1,t.toleranceReached=!1,t.lastDirection=null,t.dragging=!1,t.el.isDragging=!1,t.el.classList.remove("dragging"),t.slide.classList.remove("dragging-nav"),t.dragContainer.style.transform="",t.dragContainer.style.transition=""},100)}},{key:"drag",value:function(e){if(this.active){e.preventDefault(),this.slide.classList.add("dragging-nav"),"touchmove"===e.type?(this.currentX=e.touches[0].clientX-this.initialX,this.currentY=e.touches[0].clientY-this.initialY):(this.currentX=e.clientX-this.initialX,this.currentY=e.clientY-this.initialY),this.xOffset=this.currentX,this.yOffset=this.currentY,this.el.isDragging=!0,this.dragging=!0,this.doSlideChange=!1,this.doSlideClose=!1;var e=Math.abs(this.currentX),t=Math.abs(this.currentY);if(0<e&&e>=Math.abs(this.currentY)&&(!this.lastDirection||"x"==this.lastDirection)){this.yOffset=0,this.lastDirection="x",this.setTranslate(this.dragContainer,this.currentX,0);var i=this.shouldChange();if(!this.instance.settings.dragAutoSnap&&i&&(this.doSlideChange=i),this.instance.settings.dragAutoSnap&&i)return this.instance.preventOutsideClick=!0,this.toleranceReached=!0,this.active=!1,this.instance.preventOutsideClick=!0,this.dragEnd(null),"right"==i&&this.instance.prevSlide(),void("left"==i&&this.instance.nextSlide())}0<this.toleranceY&&0<t&&e<=t&&(!this.lastDirection||"y"==this.lastDirection)&&(this.xOffset=0,this.lastDirection="y",this.setTranslate(this.dragContainer,0,this.currentY),i=this.shouldClose(),!this.instance.settings.dragAutoSnap&&i&&(this.doSlideClose=!0),this.instance.settings.dragAutoSnap)&&i&&this.instance.close()}}},{key:"shouldChange",value:function(){var e,t=!1;return t=Math.abs(this.currentX)>=this.toleranceX&&("left"==(e=0<this.currentX?"right":"left")&&this.slide!==this.slide.parentNode.lastChild||"right"==e&&this.slide!==this.slide.parentNode.firstChild)?e:t}},{key:"shouldClose",value:function(){var e=!1;return e=Math.abs(this.currentY)>=this.toleranceY?!0:e}},{key:"setTranslate",value:function(e,t,i){e.style.transition=3<arguments.length&&void 0!==arguments[3]&&arguments[3]?"all .2s ease":"",e.style.transform="translate3d(".concat(t,"px, ").concat(i,"px, 0)")}}]);var V=f;function f(){var t=this,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},i=(r(this,f),e.dragEl),n=e.toleranceX,n=void 0===n?40:n,o=e.toleranceY,o=void 0===o?65:o,s=e.slide,s=void 0===s?null:s,e=e.instance,e=void 0===e?null:e;this.el=i,this.active=!1,this.dragging=!1,this.currentX=null,this.currentY=null,this.initialX=null,this.initialY=null,this.xOffset=0,this.yOffset=0,this.direction=null,this.lastDirection=null,this.toleranceX=n,this.toleranceY=o,this.toleranceReached=!1,this.dragContainer=this.el,this.slide=s,this.instance=e,this.el.addEventListener("mousedown",function(e){return t.dragStart(e)},!1),this.el.addEventListener("mouseup",function(e){return t.dragEnd(e)},!1),this.el.addEventListener("mousemove",function(e){return t.drag(e)},!1)}function Q(e){var t=H(e.target,".gslide-media");"enterfullscreen"===e.type&&P(t,"fullscreen"),"exitfullscreen"===e.type&&L(t,"fullscreen")}function G(e,t,i,n){var o,s,r,e=e.querySelector(".gslide-media"),l=(n={url:t.href,callback:n},l=n.url,o=n.allow,s=n.callback,n=n.appendTo,(r=document.createElement("iframe")).className="vimeo-video gvideo",r.src=l,r.style.width="100%",r.style.height="100%",o&&r.setAttribute("allow",o),r.onload=function(){r.onload=null,P(r,"node-ready"),q(s)&&s()},n&&n.appendChild(r),r);e.parentNode.style.maxWidth=t.width,e.parentNode.style.height=t.height,e.appendChild(l)}e(g,[{key:"sourceType",value:function(e){var t=e;if(null!==(e=e.toLowerCase()).match(/\.(jpeg|jpg|jpe|gif|png|apn|webp|avif|svg)/))return"image";if(e.match(/(youtube\.com|youtube-nocookie\.com)\/watch\?v=([a-zA-Z0-9\-_]+)/)||e.match(/youtu\.be\/([a-zA-Z0-9\-_]+)/)||e.match(/(youtube\.com|youtube-nocookie\.com)\/embed\/([a-zA-Z0-9\-_]+)/))return"video";if(e.match(/vimeo\.com\/([0-9]*)/))return"video";if(null!==e.match(/\.(mp4|ogg|webm|mov)/))return"video";if(null!==e.match(/\.(mp3|wav|wma|aac|ogg)/))return"audio";if(-1<e.indexOf("#")&&""!==t.split("#").pop().trim())return"inline";return-1<e.indexOf("goajax=true")?"ajax":"external"}},{key:"parseConfig",value:function(n,o){var s=this,r=d({descPosition:o.descPosition},this.defaults);if(c(n)&&!F(n))return w(n,"type")||(w(n,"content")&&n.content?n.type="inline":w(n,"href")&&(n.type=this.sourceType(n.href))),t=d(r,n),this.setSize(t,o),t;var l,e,t="",a=n.getAttribute("data-glightbox"),i=n.nodeName.toLowerCase();if("a"===i&&(t=n.href),"img"===i&&(t=n.src,r.alt=n.alt),r.href=t,h(r,function(e,t){w(o,t)&&"width"!==t&&(r[t]=o[t]);var i=n.dataset[t];W(i)||(r[t]=s.sanitizeValue(i))}),r.content&&(r.type="inline"),!r.type&&t&&(r.type=this.sourceType(t)),W(a)?(r.title||"a"!=i||W(t=n.title)||""===t||(r.title=t),r.title||"img"!=i||W(t=n.alt)||""===t||(r.title=t)):(l=[],h(r,function(e,t){l.push(";\\s?"+t)}),l=l.join("\\s?:|"),""!==a.trim()&&h(r,function(e,t){var i=a,n=new RegExp("s?"+t+"s?:s?(.*?)("+l+"s?:|$)"),i=i.match(n);i&&i.length&&i[1]&&(n=i[1].trim().replace(/;\s*$/,""),r[t]=s.sanitizeValue(n))})),r.description&&"."===r.description.substring(0,1)){try{e=document.querySelector(r.description).innerHTML}catch(e){if(!(e instanceof DOMException))throw e}e&&(r.description=e)}return r.description||(i=n.querySelector(".glightbox-desc"))&&(r.description=i.innerHTML),this.setSize(r,o,n),this.slideConfig=r}},{key:"setSize",value:function(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,n="video"==e.type?this.checkSize(t.videosWidth):this.checkSize(t.width),t=this.checkSize(t.height);return e.width=w(e,"width")&&""!==e.width?this.checkSize(e.width):n,e.height=w(e,"height")&&""!==e.height?this.checkSize(e.height):t,i&&"image"==e.type&&(e._hasCustomWidth=!!i.dataset.width,e._hasCustomHeight=!!i.dataset.height),e}},{key:"checkSize",value:function(e){return B(e)?"".concat(e,"px"):e}},{key:"sanitizeValue",value:function(e){return"true"!==e&&"false"!==e?e:"true"===e}}]);var K=g;function g(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};r(this,g),this.defaults={href:"",sizes:"",srcset:"",title:"",type:"",videoProvider:"",description:"",alt:"",descPosition:"bottom",effect:"",width:"",height:"",content:!1,zoomable:!0,draggable:!0},c(e)&&(this.defaults=d(this.defaults,e))}e(T,[{key:"setContent",value:function(){var t=this,i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,e=1<arguments.length&&void 0!==arguments[1]&&arguments[1];if(M(i,"loaded"))return!1;var n,o=this.instance.settings,s=this.slideConfig,r=y(),l=(q(o.beforeSlideLoad)&&o.beforeSlideLoad({index:this.index,slide:i,player:!1}),s.type),a=s.descPosition,d=i.querySelector(".gslide-media"),c=i.querySelector(".gslide-title"),u=i.querySelector(".gslide-desc"),h=i.querySelector(".gdesc-inner"),p=e,f="gSlideTitle_"+this.index,g="gSlideDesc_"+this.index;q(o.afterSlideLoad)&&(p=function(){q(e)&&e(),o.afterSlideLoad({index:t.index,slide:i,player:t.instance.getSlidePlayerInstance(t.index)})}),""==s.title&&""==s.description?h&&h.parentNode.parentNode.removeChild(h.parentNode):(c&&""!==s.title?(c.id=f,c.innerHTML=s.title):c.parentNode.removeChild(c),u&&""!==s.description?(u.id=g,r&&0<o.moreLength?(s.smallDescription=this.slideShortDesc(s.description,o.moreLength,o.moreText),u.innerHTML=s.smallDescription,this.descriptionEvents(u,s)):u.innerHTML=s.description):u.parentNode.removeChild(u),P(d.parentNode,"desc-".concat(a)),P(h.parentNode,"description-".concat(a))),P(d,"gslide-".concat(l)),P(i,"loaded"),"video"===l?function(t,i,n,o){var s=this,e=t.querySelector(".ginner-container"),r="gvideo"+n,l=t.querySelector(".gslide-media"),a=this.getAllPlayers(),d=(P(e,"gvideo-container"),l.insertBefore(m('<div class="gvideo-wrapper"></div>'),l.firstChild),t.querySelector(".gvideo-wrapper")),c=(x(this.settings.plyr.css,"Plyr"),i.href),u=null==i?void 0:i.videoProvider,h=!1;l.style.maxWidth=i.width,x(this.settings.plyr.js,"Plyr",function(){"local"!==(u=!(u=!u&&c.match(/vimeo\.com\/([0-9]*)/)?"vimeo":u)&&(c.match(/(youtube\.com|youtube-nocookie\.com)\/watch\?v=([a-zA-Z0-9\-_]+)/)||c.match(/youtu\.be\/([a-zA-Z0-9\-_]+)/)||c.match(/(youtube\.com|youtube-nocookie\.com)\/embed\/([a-zA-Z0-9\-_]+)/))?"youtube":u)&&u||(u="local",e=(e=(e='<video id="'+r+'" ')+'style="background:#000; max-width: '.concat(i.width,';" ')+'preload="metadata" x-webkit-airplay="allow" playsinline controls class="gvideo-local">')+'<source src="'.concat(c,'">'),h=m(e+="</video>"));var e=h||m('<div id="'.concat(r,'" data-plyr-provider="').concat(u,'" data-plyr-embed-id="').concat(c,'"></div>')),e=(P(d,"".concat(u,"-video gvideo")),d.appendChild(e),d.setAttribute("data-id",r),d.setAttribute("data-index",n),w(s.settings.plyr,"config")?s.settings.plyr.config:{}),e=new Plyr("#"+r,e);e.on("ready",function(e){a[r]=e.detail.plyr,q(o)&&o()}),v(function(){return t.querySelector("iframe")&&"true"==t.querySelector("iframe").dataset.ready},function(){s.resize(t)}),e.on("enterfullscreen",Q),e.on("exitfullscreen",Q)})}.apply(this.instance,[i,s,this.index,p]):"external"===l?G.apply(this,[i,s,this.index,p]):"inline"===l?(function(e,t,i,n){var o,s=this,e=e.querySelector(".gslide-media"),r=!(!w(t,"href")||!t.href)&&t.href.split("#").pop().trim(),l=!(!w(t,"content")||!t.content)&&t.content;if(l&&(b(l)&&(o=m('<div class="ginlined-content">'.concat(l,"</div>"))),F(l))&&("none"==l.style.display&&(l.style.display="block"),(a=document.createElement("div")).className="ginlined-content",a.appendChild(l),o=a),r){l=document.getElementById(r);if(!l)return!1;var a=l.cloneNode(!0);a.style.height=t.height,a.style.maxWidth=t.width,P(a,"ginlined-content"),o=a}if(!o)return console.error("Unable to append inline slide content",t),!1;e.style.height=t.height,e.style.width=t.width,e.appendChild(o),this.events["inlineclose"+r]=D("click",{onElement:e.querySelectorAll(".gtrigger-close"),withCallback:function(e){e.preventDefault(),s.close()}}),q(n)&&n()}.apply(this.instance,[i,s,this.index,p]),s.draggable&&new V({dragEl:i.querySelector(".gslide-inline"),toleranceX:o.dragToleranceX,toleranceY:o.dragToleranceY,slide:i,instance:this.instance})):"image"===l?(f=i,c=s,g=this.index,n=function(){var e=i.querySelector("img");s.draggable&&new V({dragEl:e,toleranceX:o.dragToleranceX,toleranceY:o.dragToleranceY,slide:i,instance:t.instance}),s.zoomable&&e.naturalWidth>e.offsetWidth&&(P(e,"zoomable"),new N(e,i,function(){t.instance.resize()})),q(p)&&p()},f=f.querySelector(".gslide-media"),r=new Image,u="gSlideTitle_"+g,g="gSlideDesc_"+g,r.addEventListener("load",function(){q(n)&&n()},!1),r.src=c.href,""!=c.sizes&&""!=c.srcset&&(r.sizes=c.sizes,r.srcset=c.srcset),r.alt="",W(c.alt)||""===c.alt||(r.alt=c.alt),""!==c.title&&r.setAttribute("aria-labelledby",u),""!==c.description&&r.setAttribute("aria-describedby",g),c.hasOwnProperty("_hasCustomWidth")&&c._hasCustomWidth&&(r.style.width=c.width),c.hasOwnProperty("_hasCustomHeight")&&c._hasCustomHeight&&(r.style.height=c.height),f.insertBefore(r,f.firstChild)):q(p)&&p()}},{key:"slideShortDesc",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:50,i=2<arguments.length&&void 0!==arguments[2]&&arguments[2],n=document.createElement("div");n.innerHTML=e;var o=i;return!((e=n.innerText.trim()).length<=t)&&(e=e.substr(0,t-1),o)?(n=null,e+'... <a href="#" class="desc-more">'+i+"</a>"):e}},{key:"descriptionEvents",value:function(e,s){var r=this,e=e.querySelector(".desc-more");if(!e)return!1;D("click",{onElement:e,withCallback:function(e,t){e.preventDefault();var i=document.body,n=H(t,".gslide-desc");if(!n)return!1;n.innerHTML=s.description,P(i,"gdesc-open");var o=D("click",{onElement:[i,H(n,".gslide-description")],withCallback:function(e,t){"a"!==e.target.nodeName.toLowerCase()&&(L(i,"gdesc-open"),P(i,"gdesc-closed"),n.innerHTML=s.smallDescription,r.descriptionEvents(n,s),setTimeout(function(){L(i,"gdesc-closed")},400),o.destroy())}})}})}},{key:"create",value:function(){return m(this.instance.settings.slideHTML)}},{key:"getConfig",value:function(){F(this.element)||this.element.hasOwnProperty("draggable")||(this.element.draggable=this.instance.settings.draggable);var e=new K(this.instance.settings.slideExtraAttributes);return this.slideConfig=e.parseConfig(this.element,this.instance.settings),this.slideConfig}}]);var _=T;function T(e,t,i){r(this,T),this.element=e,this.instance=t,this.index=i}var Z=y(),J=null!==y()||void 0!==document.createTouch||"ontouchstart"in window||"onmsgesturechange"in window||navigator.msMaxTouchPoints,ee=document.getElementsByTagName("html")[0],te={selector:".glightbox",elements:null,skin:"clean",theme:"clean",closeButton:!0,startAt:null,autoplayVideos:!0,autofocusVideos:!0,descPosition:"bottom",width:"900px",height:"506px",videosWidth:"960px",beforeSlideChange:null,afterSlideChange:null,beforeSlideLoad:null,afterSlideLoad:null,slideInserted:null,slideRemoved:null,slideExtraAttributes:null,onOpen:null,onClose:null,loop:!1,zoomable:!0,draggable:!0,dragAutoSnap:!1,dragToleranceX:40,dragToleranceY:65,preload:!0,oneSlidePerOpen:!1,touchNavigation:!0,touchFollowAxis:!0,keyboardNavigation:!0,closeOnOutsideClick:!0,plugins:!1,plyr:{css:"https://cdn.plyr.io/3.6.12/plyr.css",js:"https://cdn.plyr.io/3.6.12/plyr.js",config:{ratio:"16:9",fullscreen:{enabled:!0,iosNative:!0},youtube:{noCookie:!0,rel:0,showinfo:0,iv_load_policy:3},vimeo:{byline:!1,portrait:!1,title:!1,transparent:!1}}},openEffect:"zoom",closeEffect:"zoom",slideEffect:"slide",moreText:"See more",moreLength:60,cssEfects:{fade:{in:"fadeIn",out:"fadeOut"},zoom:{in:"zoomIn",out:"zoomOut"},slide:{in:"slideInRight",out:"slideOutLeft"},slideBack:{in:"slideInLeft",out:"slideOutRight"},none:{in:"none",out:"none"}},svg:{close:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" xml:space="preserve"><g><g><path d="M505.943,6.058c-8.077-8.077-21.172-8.077-29.249,0L6.058,476.693c-8.077,8.077-8.077,21.172,0,29.249C10.096,509.982,15.39,512,20.683,512c5.293,0,10.586-2.019,14.625-6.059L505.943,35.306C514.019,27.23,514.019,14.135,505.943,6.058z"/></g></g><g><g><path d="M505.942,476.694L35.306,6.059c-8.076-8.077-21.172-8.077-29.248,0c-8.077,8.076-8.077,21.171,0,29.248l470.636,470.636c4.038,4.039,9.332,6.058,14.625,6.058c5.293,0,10.587-2.019,14.624-6.057C514.018,497.866,514.018,484.771,505.942,476.694z"/></g></g></svg>',next:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 477.175 477.175" xml:space="preserve"> <g><path d="M360.731,229.075l-225.1-225.1c-5.3-5.3-13.8-5.3-19.1,0s-5.3,13.8,0,19.1l215.5,215.5l-215.5,215.5c-5.3,5.3-5.3,13.8,0,19.1c2.6,2.6,6.1,4,9.5,4c3.4,0,6.9-1.3,9.5-4l225.1-225.1C365.931,242.875,365.931,234.275,360.731,229.075z"/></g></svg>',prev:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 477.175 477.175" xml:space="preserve"><g><path d="M145.188,238.575l215.5-215.5c5.3-5.3,5.3-13.8,0-19.1s-13.8-5.3-19.1,0l-225.1,225.1c-5.3,5.3-5.3,13.8,0,19.1l225.1,225c2.6,2.6,6.1,4,9.5,4s6.9-1.3,9.5-4c5.3-5.3,5.3-13.8,0-19.1L145.188,238.575z"/></g></svg>'},slideHTML:'<div class="gslide">\n    <div class="gslide-inner-content">\n        <div class="ginner-container">\n            <div class="gslide-media">\n            </div>\n            <div class="gslide-description">\n                <div class="gdesc-inner">\n                    <h4 class="gslide-title"></h4>\n                    <div class="gslide-desc"></div>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>',lightboxHTML:'<div id="glightbox-body" class="glightbox-container" tabindex="-1" role="dialog" aria-hidden="false">\n    <div class="gloader visible"></div>\n    <div class="goverlay"></div>\n    <div class="gcontainer">\n    <div id="glightbox-slider" class="gslider"></div>\n    <button class="gclose gbtn" aria-label="Close" data-taborder="3">{closeSVG}</button>\n    <button class="gprev gbtn" aria-label="Previous" data-taborder="2">{prevSVG}</button>\n    <button class="gnext gbtn" aria-label="Next" data-taborder="1">{nextSVG}</button>\n</div>\n</div>'},ie=(e(S,[{key:"init",value:function(){var i=this,e=this.getSelector();e&&(this.baseEvents=D("click",{onElement:e,withCallback:function(e,t){e.preventDefault(),i.open(t)}})),this.elements=this.getElements()}},{key:"open",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(0===this.elements.length)return!1;this.activeSlide=null,this.prevActiveSlideIndex=null,this.prevActiveSlide=null;var i,n,o,s,r,l,a,d,c,u,h,p,f,g,m,v,y,b,w,_,T,S,k,C,x,E,$,A,O,I,t=B(t)?t:this.settings.startAt,N=(B(t=F(e)&&((N=e.getAttribute("data-gallery"))&&(this.fullElementsList=this.elements,this.elements=this.getGalleryElements(this.elements,N)),W(t))&&(t=this.getElementIndex(e))<0?0:t)||(t=0),this.build(),j(this.overlay,"none"===this.settings.openEffect?"none":this.settings.cssEfects.fade.in),document.body),e=window.innerWidth-document.documentElement.clientWidth;0<e&&((i=document.createElement("style")).type="text/css",i.className="gcss-styles",i.innerText=".gscrollbar-fixer {margin-right: ".concat(e,"px}"),document.head.appendChild(i),P(N,"gscrollbar-fixer")),P(N,"glightbox-open"),P(ee,"glightbox-open"),Z&&(P(document.body,"glightbox-mobile"),this.settings.slideEffect="slide"),this.showSlide(t,!0),(1===this.elements.length?(P(this.prevButton,"glightbox-button-hidden"),P):(L(this.prevButton,"glightbox-button-hidden"),L))(this.nextButton,"glightbox-button-hidden"),this.lightboxOpen=!0,this.trigger("open"),q(this.settings.onOpen)&&this.settings.onOpen(),J&&this.settings.touchNavigation&&((n=this).events.hasOwnProperty("touch")||(e=X(),o=e.width,s=e.height,c=r=!1,y=v=m=g=d=a=l=null,k=S=f=p=!(h=u=1),C={},x={},$=E=T=_=0,e=document.getElementById("glightbox-slider"),O=document.querySelector(".goverlay"),e=new U(e,{touchStart:function(e){r=!0,(M(e.targetTouches[0].target,"ginner-container")||H(e.targetTouches[0].target,".gslide-desc")||"a"==e.targetTouches[0].target.nodeName.toLowerCase())&&(r=!1),(r=H(e.targetTouches[0].target,".gslide-inline")&&!M(e.targetTouches[0].target.parentNode,"gslide-inline")?!1:r)&&(x=e.targetTouches[0],C.pageX=e.targetTouches[0].pageX,C.pageY=e.targetTouches[0].pageY,E=e.targetTouches[0].clientX,$=e.targetTouches[0].clientY,l=n.activeSlide,a=l.querySelector(".gslide-media"),A=l.querySelector(".gslide-inline"),d=null,M(a,"gslide-image")&&(d=a.querySelector("img")),769<(window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth)&&(a=l.querySelector(".ginner-container")),L(O,"greset"),20<e.pageX&&e.pageX<window.innerWidth-20||e.preventDefault())},touchMove:function(e){if(r&&(x=e.targetTouches[0],!p)&&!f){if(A&&A.offsetHeight>s){var t=C.pageX-x.pageX;if(Math.abs(t)<=13)return!1}c=!0;var i,t=e.targetTouches[0].clientX,e=e.targetTouches[0].clientY,t=E-t,e=$-e;if(Math.abs(t)>Math.abs(e)?k=!(S=!1):S=!(k=!1),b=x.pageX-C.pageX,_=100*b/o,w=x.pageY-C.pageY,T=100*w/s,S&&d&&(i=1-Math.abs(w)/s,O.style.opacity=i,n.settings.touchFollowAxis)&&(_=0),k&&(i=1-Math.abs(b)/o,a.style.opacity=i,n.settings.touchFollowAxis)&&(T=0),!d)return z(a,"translate3d(".concat(_,"%, 0, 0)"));z(a,"translate3d(".concat(_,"%, ").concat(T,"%, 0)"))}},touchEnd:function(){if(r)if(c=!1,f||p)v=g,y=m;else{var e=Math.abs(parseInt(T)),t=Math.abs(parseInt(_));if(!(29<e&&d))return e<29&&t<25?(P(O,"greset"),O.style.opacity=1,R(a)):void 0;n.close()}},multipointEnd:function(){setTimeout(function(){p=!1},50)},multipointStart:function(){p=!0,u=h||1},pinch:function(e){if(!d||c)return!1;p=!0,d.scaleX=d.scaleY=u*e.zoom;e=u*e.zoom;f=!0,e<=1?(f=!1,e=1,m=g=v=y=null,d.setAttribute("style","")):(d.style.transform="scale3d(".concat(e=4.5<e?4.5:e,", ").concat(e,", 1)"),h=e)},pressMove:function(e){var t,i;f&&!p&&(i=x.pageX-C.pageX,t=x.pageY-C.pageY,v&&(i+=v),y&&(t+=y),g=i,m=t,i="translate3d(".concat(i,"px, ").concat(t,"px, 0)"),h&&(i+=" scale3d(".concat(h,", ").concat(h,", 1)")),z(d,i))},swipe:function(e){if(!f)if(p)p=!1;else{if("Left"==e.direction){if(n.index==n.elements.length-1)return R(a);n.nextSlide()}if("Right"==e.direction){if(0==n.index)return R(a);n.prevSlide()}}}}),n.events.touch=e)),this.settings.keyboardNavigation&&!(I=this).events.hasOwnProperty("keyboard")&&(I.events.keyboard=D("keydown",{onElement:window,withCallback:function(e,t){var i=(e=e||window.event).keyCode;if(9==i){var n=document.querySelector(".gbtn.focused");if(!n){var o=!(!document.activeElement||!document.activeElement.nodeName)&&document.activeElement.nodeName.toLocaleLowerCase();if("input"==o||"textarea"==o||"button"==o)return}e.preventDefault();o=document.querySelectorAll(".gbtn[data-taborder]");if(!o||o.length<=0)return;if(!n)return void((e=Y())&&(e.focus(),P(e,"focused")));o=Y(n.getAttribute("data-taborder"));L(n,"focused"),o&&(o.focus(),P(o,"focused"))}39==i&&I.nextSlide(),37==i&&I.prevSlide(),27==i&&I.close()}}))}},{key:"openAt",value:function(){this.open(null,0<arguments.length&&void 0!==arguments[0]?arguments[0]:0)}},{key:"showSlide",value:function(){var e,t=this,i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,n=1<arguments.length&&void 0!==arguments[1]&&arguments[1],o=(C(this.loader),this.index=parseInt(i),this.slidesContainer.querySelector(".current")),s=(o&&L(o,"current"),this.slideAnimateOut(),this.slidesContainer.querySelectorAll(".gslide")[i]);M(s,"loaded")?(this.slideAnimateIn(s,n),a(this.loader)):(C(this.loader),o=this.elements[i],e={index:this.index,slide:s,slideNode:s,slideConfig:o.slideConfig,slideIndex:this.index,trigger:o.node,player:null},this.trigger("slide_before_load",e),o.instance.setContent(s,function(){a(t.loader),t.resize(),t.slideAnimateIn(s,n),t.trigger("slide_after_load",e)})),this.slideDescription=s.querySelector(".gslide-description"),this.slideDescriptionContained=this.slideDescription&&M(this.slideDescription.parentNode,"gslide-media"),this.settings.preload&&(this.preloadSlide(i+1),this.preloadSlide(i-1)),this.updateNavigationClasses(),this.activeSlide=s}},{key:"preloadSlide",value:function(e){var t,i,n,o,s=this;return!(e<0||e>this.elements.length-1||W(this.elements[e])||M(t=this.slidesContainer.querySelectorAll(".gslide")[e],"loaded"))&&(n=(i=this.elements[e]).type,o={index:e,slide:t,slideNode:t,slideConfig:i.slideConfig,slideIndex:e,trigger:i.node,player:null},this.trigger("slide_before_load",o),void("video"===n||"external"===n?setTimeout(function(){i.instance.setContent(t,function(){s.trigger("slide_after_load",o)})},200):i.instance.setContent(t,function(){s.trigger("slide_after_load",o)})))}},{key:"prevSlide",value:function(){this.goToSlide(this.index-1)}},{key:"nextSlide",value:function(){this.goToSlide(this.index+1)}},{key:"goToSlide",value:function(){var e=0<arguments.length&&void 0!==arguments[0]&&arguments[0];if(this.prevActiveSlide=this.activeSlide,this.prevActiveSlideIndex=this.index,!this.loop()&&(e<0||e>this.elements.length-1))return!1;e<0?e=this.elements.length-1:e>=this.elements.length&&(e=0),this.showSlide(e)}},{key:"insertSlide",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:-1,e=(t<0&&(t=this.elements.length),new _(e,this,t)),i=e.getConfig(),n=d({},i),o=e.create(),s=this.elements.length-1,e=(n.index=t,n.node=!1,n.instance=e,n.slideConfig=i,this.elements.splice(t,0,n),null),r=null;this.slidesContainer&&(s<t?this.slidesContainer.appendChild(o):(s=this.slidesContainer.querySelectorAll(".gslide")[t],this.slidesContainer.insertBefore(o,s)),(this.settings.preload&&0==this.index&&0==t||this.index-1==t||this.index+1==t)&&this.preloadSlide(t),0===this.index&&0===t&&(this.index=1),this.updateNavigationClasses(),e=this.slidesContainer.querySelectorAll(".gslide")[t],r=this.getSlidePlayerInstance(t),n.slideNode=e),this.trigger("slide_inserted",{index:t,slide:e,slideNode:e,slideConfig:i,slideIndex:t,trigger:null,player:r}),q(this.settings.slideInserted)&&this.settings.slideInserted({index:t,slide:e,player:r})}},{key:"removeSlide",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:-1;if(e<0||e>this.elements.length-1)return!1;var t=this.slidesContainer&&this.slidesContainer.querySelectorAll(".gslide")[e];t&&(this.getActiveSlideIndex()==e&&(e==this.elements.length-1?this.prevSlide():this.nextSlide()),t.parentNode.removeChild(t)),this.elements.splice(e,1),this.trigger("slide_removed",e),q(this.settings.slideRemoved)&&this.settings.slideRemoved(e)}},{key:"slideAnimateIn",value:function(e,t){var i=this,n=e.querySelector(".gslide-media"),o=e.querySelector(".gslide-description"),s={index:this.prevActiveSlideIndex,slide:this.prevActiveSlide,slideNode:this.prevActiveSlide,slideIndex:this.prevActiveSlide,slideConfig:W(this.prevActiveSlideIndex)?null:this.elements[this.prevActiveSlideIndex].slideConfig,trigger:W(this.prevActiveSlideIndex)?null:this.elements[this.prevActiveSlideIndex].node,player:this.getSlidePlayerInstance(this.prevActiveSlideIndex)},r={index:this.index,slide:this.activeSlide,slideNode:this.activeSlide,slideConfig:this.elements[this.index].slideConfig,slideIndex:this.index,trigger:this.elements[this.index].node,player:this.getSlidePlayerInstance(this.index)};0<n.offsetWidth&&o&&(a(o),o.style.display=""),L(e,this.effectsClasses),t?j(e,this.settings.cssEfects[this.settings.openEffect].in,function(){i.settings.autoplayVideos&&i.slidePlayerPlay(e),i.trigger("slide_changed",{prev:s,current:r}),q(i.settings.afterSlideChange)&&i.settings.afterSlideChange.apply(i,[s,r])}):(o="none"!==(n=this.settings.slideEffect)?this.settings.cssEfects[n].in:n,this.prevActiveSlideIndex>this.index&&"slide"==this.settings.slideEffect&&(o=this.settings.cssEfects.slideBack.in),j(e,o,function(){i.settings.autoplayVideos&&i.slidePlayerPlay(e),i.trigger("slide_changed",{prev:s,current:r}),q(i.settings.afterSlideChange)&&i.settings.afterSlideChange.apply(i,[s,r])})),setTimeout(function(){i.resize(e)},100),P(e,"current")}},{key:"slideAnimateOut",value:function(){if(!this.prevActiveSlide)return!1;var n=this.prevActiveSlide,e=(L(n,this.effectsClasses),P(n,"prev"),this.settings.slideEffect),e="none"!==e?this.settings.cssEfects[e].out:e;this.slidePlayerPause(n),this.trigger("slide_before_change",{prev:{index:this.prevActiveSlideIndex,slide:this.prevActiveSlide,slideNode:this.prevActiveSlide,slideIndex:this.prevActiveSlideIndex,slideConfig:W(this.prevActiveSlideIndex)?null:this.elements[this.prevActiveSlideIndex].slideConfig,trigger:W(this.prevActiveSlideIndex)?null:this.elements[this.prevActiveSlideIndex].node,player:this.getSlidePlayerInstance(this.prevActiveSlideIndex)},current:{index:this.index,slide:this.activeSlide,slideNode:this.activeSlide,slideIndex:this.index,slideConfig:this.elements[this.index].slideConfig,trigger:this.elements[this.index].node,player:this.getSlidePlayerInstance(this.index)}}),q(this.settings.beforeSlideChange)&&this.settings.beforeSlideChange.apply(this,[{index:this.prevActiveSlideIndex,slide:this.prevActiveSlide,player:this.getSlidePlayerInstance(this.prevActiveSlideIndex)},{index:this.index,slide:this.activeSlide,player:this.getSlidePlayerInstance(this.index)}]),this.prevActiveSlideIndex>this.index&&"slide"==this.settings.slideEffect&&(e=this.settings.cssEfects.slideBack.out),j(n,e,function(){var e=n.querySelector(".ginner-container"),t=n.querySelector(".gslide-media"),i=n.querySelector(".gslide-description");e.style.transform="",t.style.transform="",L(t,"greset"),t.style.opacity="",i&&(i.style.opacity=""),L(n,"prev")})}},{key:"getAllPlayers",value:function(){return this.videoPlayers}},{key:"getSlidePlayerInstance",value:function(e){var e="gvideo"+e,t=this.getAllPlayers();return!(!w(t,e)||!t[e])&&t[e]}},{key:"stopSlideVideo",value:function(e){F(e)&&(t=e.querySelector(".gvideo-wrapper"))&&(e=t.getAttribute("data-index")),console.log("stopSlideVideo is deprecated, use slidePlayerPause");var t=this.getSlidePlayerInstance(e);t&&t.playing&&t.pause()}},{key:"slidePlayerPause",value:function(e){F(e)&&(t=e.querySelector(".gvideo-wrapper"))&&(e=t.getAttribute("data-index"));var t=this.getSlidePlayerInstance(e);t&&t.playing&&t.pause()}},{key:"playSlideVideo",value:function(e){F(e)&&(t=e.querySelector(".gvideo-wrapper"))&&(e=t.getAttribute("data-index")),console.log("playSlideVideo is deprecated, use slidePlayerPlay");var t=this.getSlidePlayerInstance(e);t&&!t.playing&&t.play()}},{key:"slidePlayerPlay",value:function(e){var t;(!Z||null!=(t=this.settings.plyr.config)&&t.muted)&&(F(e)&&(t=e.querySelector(".gvideo-wrapper"))&&(e=t.getAttribute("data-index")),t=this.getSlidePlayerInstance(e))&&!t.playing&&(t.play(),this.settings.autofocusVideos)&&t.elements.container.focus()}},{key:"setElements",value:function(e){var o=this,s=(this.settings.elements=!1,[]);e&&e.length&&h(e,function(e,t){var e=new _(e,o,t),i=e.getConfig(),n=d({},i);n.slideConfig=i,n.instance=e,n.index=t,s.push(n)}),this.elements=s,this.lightboxOpen&&(this.slidesContainer.innerHTML="",this.elements.length)&&(h(this.elements,function(){var e=m(o.settings.slideHTML);o.slidesContainer.appendChild(e)}),this.showSlide(0,!0))}},{key:"getElementIndex",value:function(i){var n=!1;return h(this.elements,function(e,t){if(w(e,"node")&&e.node==i)return n=t,!0}),n}},{key:"getElements",value:function(){var s=this,r=[],e=(this.elements=this.elements||[],!W(this.settings.elements)&&E(this.settings.elements)&&this.settings.elements.length&&h(this.settings.elements,function(e,t){var e=new _(e,s,t),i=e.getConfig(),n=d({},i);n.node=!1,n.index=t,n.instance=e,n.slideConfig=i,r.push(n)}),!1);return(e=this.getSelector()?document.querySelectorAll(this.getSelector()):e)&&h(e,function(e,t){var i=new _(e,s,t),n=i.getConfig(),o=d({},n);o.node=e,o.index=t,o.instance=i,o.slideConfig=n,o.gallery=e.getAttribute("data-gallery"),r.push(o)}),r}},{key:"getGalleryElements",value:function(e,t){return e.filter(function(e){return e.gallery==t})}},{key:"getSelector",value:function(){return!this.settings.elements&&(this.settings.selector&&"data-"==this.settings.selector.substring(0,5)?"*[".concat(this.settings.selector,"]"):this.settings.selector)}},{key:"getActiveSlide",value:function(){return this.slidesContainer.querySelectorAll(".gslide")[this.index]}},{key:"getActiveSlideIndex",value:function(){return this.index}},{key:"getAnimationClasses",value:function(){var e,t,i=[];for(e in this.settings.cssEfects)this.settings.cssEfects.hasOwnProperty(e)&&(t=this.settings.cssEfects[e],i.push("g".concat(t.in)),i.push("g".concat(t.out)));return i.join(" ")}},{key:"build",value:function(){var i=this;if(this.built)return!1;var e=document.body.childNodes,t=[],e=(h(e,function(e){e.parentNode==document.body&&"#"!==e.nodeName.charAt(0)&&e.hasAttribute&&!e.hasAttribute("aria-hidden")&&(t.push(e),e.setAttribute("aria-hidden","true"))}),w(this.settings.svg,"next")?this.settings.svg.next:""),n=w(this.settings.svg,"prev")?this.settings.svg.prev:"",o=w(this.settings.svg,"close")?this.settings.svg.close:"",s=this.settings.lightboxHTML,e=(s=m(s=(s=(s=s.replace(/{nextSVG}/g,e)).replace(/{prevSVG}/g,n)).replace(/{closeSVG}/g,o)),document.body.appendChild(s),document.getElementById("glightbox-body")),n=(this.modal=e).querySelector(".gclose");this.prevButton=e.querySelector(".gprev"),this.nextButton=e.querySelector(".gnext"),this.overlay=e.querySelector(".goverlay"),this.loader=e.querySelector(".gloader"),this.slidesContainer=document.getElementById("glightbox-slider"),this.bodyHiddenChildElms=t,this.events={},P(this.modal,"glightbox-"+this.settings.skin),this.settings.closeButton&&n&&(this.events.close=D("click",{onElement:n,withCallback:function(e,t){e.preventDefault(),i.close()}})),n&&!this.settings.closeButton&&n.parentNode.removeChild(n),this.nextButton&&(this.events.next=D("click",{onElement:this.nextButton,withCallback:function(e,t){e.preventDefault(),i.nextSlide()}})),this.prevButton&&(this.events.prev=D("click",{onElement:this.prevButton,withCallback:function(e,t){e.preventDefault(),i.prevSlide()}})),this.settings.closeOnOutsideClick&&(this.events.outClose=D("click",{onElement:e,withCallback:function(e,t){i.preventOutsideClick||M(document.body,"glightbox-mobile")||H(e.target,".ginner-container")||H(e.target,".gbtn")||M(e.target,"gnext")||M(e.target,"gprev")||i.close()}})),h(this.elements,function(e,t){i.slidesContainer.appendChild(e.instance.create()),e.slideNode=i.slidesContainer.querySelectorAll(".gslide")[t]}),J&&P(document.body,"glightbox-touch"),this.events.resize=D("resize",{onElement:window,withCallback:function(){i.resize()}}),this.built=!0}},{key:"resize",value:function(){var e,t,i,n,o,s,r,l=(l=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null)||this.activeSlide;l&&!M(l,"zoomed")&&(i=X(),e=l.querySelector(".gvideo-wrapper"),l=l.querySelector(".gslide-image"),t=this.slideDescription,s=i.width,i=i.height,(s<=768?P:L)(document.body,"glightbox-mobile"),e||l)&&(n=!1,t&&(M(t,"description-bottom")||M(t,"description-top"))&&!M(t,"gabsolute")&&(n=!0),l&&(s<=768?l.querySelector("img"):n&&(o=t.offsetHeight,(l=l.querySelector("img")).setAttribute("style","max-height: calc(100vh - ".concat(o,"px)")),t.setAttribute("style","max-width: ".concat(l.offsetWidth,"px;")))),e)&&((o=w(this.settings.plyr.config,"ratio")?this.settings.plyr.config.ratio:"")||(l=e.clientWidth,r=e.clientHeight,o="".concat(l/(l=l/r),":").concat(r/l)),r=o.split(":"),l=this.settings.videosWidth,o=this.settings.videosWidth,r=(o=B(l)||-1!==l.indexOf("px")?parseInt(l):-1!==l.indexOf("vw")?s*parseInt(l)/100:-1!==l.indexOf("vh")?i*parseInt(l)/100:-1!==l.indexOf("%")?s*parseInt(l)/100:parseInt(e.clientWidth))/(parseInt(r[0])/parseInt(r[1])),r=Math.floor(r),n&&(i-=t.offsetHeight),s<o||i<r||i<r&&o<s?(r=e.offsetWidth,o=e.offsetHeight,e.parentNode.setAttribute("style","max-width: ".concat((r={width:r*(s=i/o),height:o*s}).width,"px")),n&&t.setAttribute("style","max-width: ".concat(r.width,"px;"))):(e.parentNode.style.maxWidth="".concat(l),n&&t.setAttribute("style","max-width: ".concat(l,";"))))}},{key:"reload",value:function(){this.init()}},{key:"updateNavigationClasses",value:function(){var e=this.loop();L(this.nextButton,"disabled"),L(this.prevButton,"disabled"),0==this.index&&this.elements.length-1==0?(P(this.prevButton,"disabled"),P(this.nextButton,"disabled")):0!==this.index||e?this.index!==this.elements.length-1||e||P(this.nextButton,"disabled"):P(this.prevButton,"disabled")}},{key:"loop",value:function(){var e=w(this.settings,"loopAtEnd")?this.settings.loopAtEnd:null;return e=w(this.settings,"loop")?this.settings.loop:e}},{key:"close",value:function(){var i=this;if(!this.lightboxOpen){if(this.events){for(var e in this.events)this.events.hasOwnProperty(e)&&this.events[e].destroy();this.events=null}return!1}if(this.closing)return!1;this.closing=!0,this.slidePlayerPause(this.activeSlide),this.fullElementsList&&(this.elements=this.fullElementsList),this.bodyHiddenChildElms.length&&h(this.bodyHiddenChildElms,function(e){e.removeAttribute("aria-hidden")}),P(this.modal,"glightbox-closing"),j(this.overlay,"none"==this.settings.openEffect?"none":this.settings.cssEfects.fade.out),j(this.activeSlide,this.settings.cssEfects[this.settings.closeEffect].out,function(){if(i.activeSlide=null,i.prevActiveSlideIndex=null,i.prevActiveSlide=null,i.built=!1,i.events){for(var e in i.events)i.events.hasOwnProperty(e)&&i.events[e].destroy();i.events=null}var t=document.body,t=(L(ee,"glightbox-open"),L(t,"glightbox-open touching gdesc-open glightbox-touch glightbox-mobile gscrollbar-fixer"),i.modal.parentNode.removeChild(i.modal),i.trigger("close"),q(i.settings.onClose)&&i.settings.onClose(),document.querySelector(".gcss-styles"));t&&t.parentNode.removeChild(t),i.lightboxOpen=!1,i.closing=null})}},{key:"destroy",value:function(){this.close(),this.clearAllEvents(),this.baseEvents&&this.baseEvents.destroy()}},{key:"on",value:function(e,t){var i=2<arguments.length&&void 0!==arguments[2]&&arguments[2];if(!e||!q(t))throw new TypeError("Event name and callback must be defined");this.apiEvents.push({evt:e,once:i,callback:t})}},{key:"once",value:function(e,t){this.on(e,t,!0)}},{key:"trigger",value:function(o){var t=this,s=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,r=[];h(this.apiEvents,function(e,t){var i=e.evt,n=e.once;i==o&&((0,e.callback)(s),n)&&r.push(t)}),r.length&&h(r,function(e){return t.apiEvents.splice(e,1)})}},{key:"clearAllEvents",value:function(){this.apiEvents.splice(0,this.apiEvents.length)}},{key:"version",value:function(){return"3.1.0"}}]),S);function S(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};r(this,S),this.customOptions=e,this.settings=d(te,e),this.effectsClasses=this.getAnimationClasses(),this.videoPlayers={},this.apiEvents=[],this.fullElementsList=!1}return function(){var e=new ie(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{});return e.init(),e}}),$(document).ready(function(){var e;setupResponsiveMenu(),$(window).on("resize",function(){clearTimeout(e),e=setTimeout(function(){setupResponsiveMenu()},250)})});var $scrollHeight=$("#siteHeader").outerHeight();function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}$(window).on("load resize",function(){window.matchMedia("(min-width: 992px)").matches?($("body").css("padding-top","0"),$("#mainNavigation").css("top","0"),$("#navMobileOverlay").css("top","0"),$(window).scroll(function(){$(this).scrollTop()>$scrollHeight+60?($("#mainNavigation").addClass("fixed-top shrink"),$("body").css("padding-top",$("#mainNavigation").outerHeight()-8+"px")):($("#mainNavigation").removeClass("fixed-top shrink"),$("body").css("padding-top","0"))})):($("body").css("padding-top",$("#siteHeader").outerHeight()+"px"),$("#mainNavigation").css("top",$("#siteHeader").outerHeight()+"px"),$("#navMobileOverlay").css("top",$("#siteHeader").outerHeight()+"px"),$("#mainNavigation").removeClass("fixed-top shrink"))}),$(window).on("load resize",function(){window.matchMedia("(max-width: 991.98px)").matches&&($("body").css("padding-top",$("#siteHeader").outerHeight()+"px"),$("#mainNavigation").css("top",$("#siteHeader").outerHeight()+"px"),$("#navMobileOverlay").css("top",$("#siteHeader").outerHeight()+"px"),$("#mainNavigation").removeClass("fixed-top shrink"),$(window).scroll(function(){$("body").css("padding-top",$("#siteHeader").outerHeight()+"px"),$("#mainNavigation").css("top",$("#siteHeader").outerHeight()+"px"),$("#navMobileOverlay").css("top",$("#siteHeader").outerHeight()+"px"),$("#mainNavigation").removeClass("fixed-top shrink")}))}),$(window).on("load",function(){0!=$(".content-box").length&&($("body").addClass("position-relative"),$("body").attr("data-spy","scroll"),$("body").attr("data-target","#contents"),$("body").attr("data-offset","62"))}),$(document).ready(function(){var e=$(".meganav.navbar").outerHeight();function t(){$(window).off("scroll.contentBox"),window.matchMedia("(min-width: 992px)").matches?0<$('body[data-spy="scroll"]').length&&($("body").scrollspy("refresh"),$("body").scrollspy({offset:e})):window.matchMedia("(max-width: 991.98px)").matches&&0<$('body[data-spy="scroll"]').length&&0<$(".content-box").length&&($("body").scrollspy("refresh"),$("body").scrollspy({offset:$(".content-box").outerHeight()})),window.matchMedia("(min-width: 992px)").matches?($("#contentsHeader").removeAttr("href data-toggle aria-expanded aria-controls"),$("#contents").addClass("show"),$("#contents").removeClass("collapse"),$(".content-box").removeClass("scroll-fix"),$("#contentArea").css("padding-top","0"),$(".content-box").css({top:"calc(42px + 1.5rem)"}),$("#contentsHeader").removeAttr("aria-expanded"),$(".content-box .card-header > a").removeClass("collapsed"),$(".content-box a.nav-link").off("click.contentBox")):window.matchMedia("(max-width: 991.98px)").matches&&($("#contentsHeader").attr("href","#contents"),$("#contentsHeader").attr("data-toggle","collapse"),$("#contentsHeader").attr("aria-expanded","false"),$("#contentsHeader").attr("aria-controls","contents"),$("#contents").removeClass("show"),$("#contents").addClass("collapse"),$("#contentsHeader").attr("aria-expanded","false"),$(".content-box .card-header > a").addClass("collapsed"),$(".content-box a.nav-link").off("click.contentBox").on("click.contentBox",function(){$("#contentsHeader").attr("aria-expanded","false"),$("#contents").removeClass("show"),$(".content-box .card-header > a").addClass("collapsed")}),$(window).on("scroll.contentBox",function(){var e,t=$(window).scrollTop(),i=$(".scroll-fix-anchor"),n=$("footer");0===i.length||0===n.length?console.warn("Required elements not found: scroll-fix-anchor or footer"):(i=i.offset().top,(n=n.offset().top)-16<t+(e=$(".content-box .card-header").outerHeight())+32?$(".content-box").css({top:-1*(t+e-n+32)}):i-16<t?($("#contentArea").css("padding-top",e+16),$(".content-box").addClass("scroll-fix"),$(".content-box").css({top:"1rem"})):($(".content-box").removeClass("scroll-fix"),$("#contentArea").css("padding-top","0")))}))}$(window).on("load resize",t),0<$(".content-box").length&&t()}),$(window).on("load resize",function(){$(window).width()<973?($("#categoryFilterHeading, #attributeFilterHeading").attr("aria-expanded","false"),$("#categoryFilter, #attributeFilter").removeClass("show")):($("#categoryFilterHeading, #attributeFilterHeading").attr("aria-expanded","true"),$("#categoryFilter, #attributeFilter").addClass("show"))}),$(".filter-toggle").click(function(){$("#sidebar").addClass("slide")}),$(".sidebar-close").click(function(){$("#sidebar").removeClass("slide")}),$(".shop-view-grid").click(function(){$("#shopProductGrid").addClass("grid-view"),$(".shop-view-grid").addClass("active"),$("#shopProductGrid").removeClass("list-view"),$(".shop-view-list").removeClass("active"),$(".product-grid-column").addClass("col-6 col-sm-4"),$(".product-grid-column").removeClass("col-12")}),$(".shop-view-list").click(function(){$("#shopProductGrid").addClass("list-view"),$(".shop-view-list").addClass("active"),$("#shopProductGrid").removeClass("grid-view"),$(".shop-view-grid").removeClass("active"),$(".product-grid-column").removeClass("col-6 col-sm-4"),$(".product-grid-column").addClass("col-12")}),$(window).on("load",function(){$(".accordion .collapse, .tab-content .collapse").on("shown.bs.collapse",function(){$(this).prev().addClass("active")}),$(".accordion .collapse, .tab-content .collapse").on("hidden.bs.collapse",function(){$(this).prev().removeClass("active")})}),(e=>{"function"==typeof define&&define.amd?define(["jquery"],e):"undefined"!=typeof exports?module.exports=e(require("jquery")):e(jQuery)})(function(d){var n,r=window.Slick||{};n=0,(r=function(e,t){var i=this;i.defaults={accessibility:!0,adaptiveHeight:!1,appendArrows:d(e),appendDots:d(e),arrows:!0,asNavFor:null,prevArrow:'<button class="slick-prev" aria-label="Previous" type="button">Previous</button>',nextArrow:'<button class="slick-next" aria-label="Next" type="button">Next</button>',autoplay:!1,autoplaySpeed:3e3,centerMode:!1,centerPadding:"50px",cssEase:"ease",customPaging:function(e,t){return d('<button type="button" />').text(t+1)},dots:!1,dotsClass:"slick-dots",draggable:!0,easing:"linear",edgeFriction:.35,fade:!1,focusOnSelect:!1,focusOnChange:!1,infinite:!0,initialSlide:0,lazyLoad:"ondemand",mobileFirst:!1,pauseOnHover:!0,pauseOnFocus:!0,pauseOnDotsHover:!1,respondTo:"window",responsive:null,rows:1,rtl:!1,slide:"",slidesPerRow:1,slidesToShow:1,slidesToScroll:1,speed:500,swipe:!0,swipeToSlide:!1,touchMove:!0,touchThreshold:5,useCSS:!0,useTransform:!0,variableWidth:!1,vertical:!1,verticalSwiping:!1,waitForAnimate:!0,zIndex:1e3},i.initials={animating:!1,dragging:!1,autoPlayTimer:null,currentDirection:0,currentLeft:null,currentSlide:0,direction:1,$dots:null,listWidth:null,listHeight:null,loadIndex:0,$nextArrow:null,$prevArrow:null,scrolling:!1,slideCount:null,slideWidth:null,$slideTrack:null,$slides:null,sliding:!1,slideOffset:0,swipeLeft:null,swiping:!1,$list:null,touchObject:{},transformsEnabled:!1,unslicked:!1},d.extend(i,i.initials),i.activeBreakpoint=null,i.animType=null,i.animProp=null,i.breakpoints=[],i.breakpointSettings=[],i.cssTransitions=!1,i.focussed=!1,i.interrupted=!1,i.hidden="hidden",i.paused=!0,i.positionProp=null,i.respondTo=null,i.rowCount=1,i.shouldClick=!0,i.$slider=d(e),i.$slidesCache=null,i.transformType=null,i.transitionType=null,i.visibilityChange="visibilitychange",i.windowWidth=0,i.windowTimer=null,e=d(e).data("slick")||{},i.options=d.extend({},i.defaults,t,e),i.currentSlide=i.options.initialSlide,i.originalSettings=i.options,void 0!==document.mozHidden?(i.hidden="mozHidden",i.visibilityChange="mozvisibilitychange"):void 0!==document.webkitHidden&&(i.hidden="webkitHidden",i.visibilityChange="webkitvisibilitychange"),i.autoPlay=d.proxy(i.autoPlay,i),i.autoPlayClear=d.proxy(i.autoPlayClear,i),i.autoPlayIterator=d.proxy(i.autoPlayIterator,i),i.changeSlide=d.proxy(i.changeSlide,i),i.clickHandler=d.proxy(i.clickHandler,i),i.selectHandler=d.proxy(i.selectHandler,i),i.setPosition=d.proxy(i.setPosition,i),i.swipeHandler=d.proxy(i.swipeHandler,i),i.dragHandler=d.proxy(i.dragHandler,i),i.keyHandler=d.proxy(i.keyHandler,i),i.instanceUid=n++,i.htmlExpr=/^(?:\s*(<[\w\W]+>)[^>]*)$/,i.registerBreakpoints(),i.init(!0)}).prototype.activateADA=function(){this.$slideTrack.find(".slick-active").attr({"aria-hidden":"false"}).find("a, input, button, select").attr({tabindex:"0"})},r.prototype.addSlide=r.prototype.slickAdd=function(e,t,i){var n=this;if("boolean"==typeof t)i=t,t=null;else if(t<0||t>=n.slideCount)return!1;n.unload(),"number"==typeof t?0===t&&0===n.$slides.length?d(e).appendTo(n.$slideTrack):i?d(e).insertBefore(n.$slides.eq(t)):d(e).insertAfter(n.$slides.eq(t)):!0===i?d(e).prependTo(n.$slideTrack):d(e).appendTo(n.$slideTrack),n.$slides=n.$slideTrack.children(this.options.slide),n.$slideTrack.children(this.options.slide).detach(),n.$slideTrack.append(n.$slides),n.$slides.each(function(e,t){d(t).attr("data-slick-index",e)}),n.$slidesCache=n.$slides,n.reinit()},r.prototype.animateHeight=function(){var e,t=this;1===t.options.slidesToShow&&!0===t.options.adaptiveHeight&&!1===t.options.vertical&&(e=t.$slides.eq(t.currentSlide).outerHeight(!0),t.$list.animate({height:e},t.options.speed))},r.prototype.animateSlide=function(e,t){var i={},n=this;n.animateHeight(),!0===n.options.rtl&&!1===n.options.vertical&&(e=-e),!1===n.transformsEnabled?!1===n.options.vertical?n.$slideTrack.animate({left:e},n.options.speed,n.options.easing,t):n.$slideTrack.animate({top:e},n.options.speed,n.options.easing,t):!1===n.cssTransitions?(!0===n.options.rtl&&(n.currentLeft=-n.currentLeft),d({animStart:n.currentLeft}).animate({animStart:e},{duration:n.options.speed,easing:n.options.easing,step:function(e){e=Math.ceil(e),!1===n.options.vertical?i[n.animType]="translate("+e+"px, 0px)":i[n.animType]="translate(0px,"+e+"px)",n.$slideTrack.css(i)},complete:function(){t&&t.call()}})):(n.applyTransition(),e=Math.ceil(e),!1===n.options.vertical?i[n.animType]="translate3d("+e+"px, 0px, 0px)":i[n.animType]="translate3d(0px,"+e+"px, 0px)",n.$slideTrack.css(i),t&&setTimeout(function(){n.disableTransition(),t.call()},n.options.speed))},r.prototype.getNavTarget=function(){var e=this.options.asNavFor;return e=e&&null!==e?d(e).not(this.$slider):e},r.prototype.asNavFor=function(t){var e=this.getNavTarget();null!==e&&"object"===_typeof(e)&&e.each(function(){var e=d(this).slick("getSlick");e.unslicked||e.slideHandler(t,!0)})},r.prototype.applyTransition=function(e){var t=this,i={};!1===t.options.fade?i[t.transitionType]=t.transformType+" "+t.options.speed+"ms "+t.options.cssEase:i[t.transitionType]="opacity "+t.options.speed+"ms "+t.options.cssEase,(!1===t.options.fade?t.$slideTrack:t.$slides.eq(e)).css(i)},r.prototype.autoPlay=function(){var e=this;e.autoPlayClear(),e.slideCount>e.options.slidesToShow&&(e.autoPlayTimer=setInterval(e.autoPlayIterator,e.options.autoplaySpeed))},r.prototype.autoPlayClear=function(){this.autoPlayTimer&&clearInterval(this.autoPlayTimer)},r.prototype.autoPlayIterator=function(){var e=this,t=e.currentSlide+e.options.slidesToScroll;e.paused||e.interrupted||e.focussed||(!1===e.options.infinite&&(1===e.direction&&e.currentSlide+1===e.slideCount-1?e.direction=0:0===e.direction&&(t=e.currentSlide-e.options.slidesToScroll,e.currentSlide-1==0)&&(e.direction=1)),e.slideHandler(t))},r.prototype.buildArrows=function(){var e=this;!0===e.options.arrows&&(e.$prevArrow=d(e.options.prevArrow).addClass("slick-arrow"),e.$nextArrow=d(e.options.nextArrow).addClass("slick-arrow"),e.slideCount>e.options.slidesToShow?(e.$prevArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"),e.$nextArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"),e.htmlExpr.test(e.options.prevArrow)&&e.$prevArrow.prependTo(e.options.appendArrows),e.htmlExpr.test(e.options.nextArrow)&&e.$nextArrow.appendTo(e.options.appendArrows),!0!==e.options.infinite&&e.$prevArrow.addClass("slick-disabled").attr("aria-disabled","true")):e.$prevArrow.add(e.$nextArrow).addClass("slick-hidden").attr({"aria-disabled":"true",tabindex:"-1"}))},r.prototype.buildDots=function(){var e,t,i=this;if(!0===i.options.dots&&i.slideCount>i.options.slidesToShow){for(i.$slider.addClass("slick-dotted"),t=d("<ul />").addClass(i.options.dotsClass),e=0;e<=i.getDotCount();e+=1)t.append(d("<li />").append(i.options.customPaging.call(this,i,e)));i.$dots=t.appendTo(i.options.appendDots),i.$dots.find("li").first().addClass("slick-active")}},r.prototype.buildOut=function(){var e=this;e.$slides=e.$slider.children(e.options.slide+":not(.slick-cloned)").addClass("slick-slide"),e.slideCount=e.$slides.length,e.$slides.each(function(e,t){d(t).attr("data-slick-index",e).data("originalStyling",d(t).attr("style")||"")}),e.$slider.addClass("slick-slider"),e.$slideTrack=0===e.slideCount?d('<div class="slick-track"/>').appendTo(e.$slider):e.$slides.wrapAll('<div class="slick-track"/>').parent(),e.$list=e.$slideTrack.wrap('<div class="slick-list"/>').parent(),e.$slideTrack.css("opacity",0),!0!==e.options.centerMode&&!0!==e.options.swipeToSlide||(e.options.slidesToScroll=1),d("img[data-lazy]",e.$slider).not("[src]").addClass("slick-loading"),e.setupInfinite(),e.buildArrows(),e.buildDots(),e.updateDots(),e.setSlideClasses("number"==typeof e.currentSlide?e.currentSlide:0),!0===e.options.draggable&&e.$list.addClass("draggable")},r.prototype.buildRows=function(){var e,t,i,n=this,o=document.createDocumentFragment(),s=n.$slider.children();if(0<n.options.rows){for(i=n.options.slidesPerRow*n.options.rows,t=Math.ceil(s.length/i),e=0;e<t;e++){for(var r=document.createElement("div"),l=0;l<n.options.rows;l++){for(var a=document.createElement("div"),d=0;d<n.options.slidesPerRow;d++){var c=e*i+(l*n.options.slidesPerRow+d);s.get(c)&&a.appendChild(s.get(c))}r.appendChild(a)}o.appendChild(r)}n.$slider.empty().append(o),n.$slider.children().children().children().css({width:100/n.options.slidesPerRow+"%",display:"inline-block"})}},r.prototype.checkResponsive=function(e,t){var i,n,o,s=this,r=!1,l=s.$slider.width(),a=window.innerWidth||d(window).width();if("window"===s.respondTo?o=a:"slider"===s.respondTo?o=l:"min"===s.respondTo&&(o=Math.min(a,l)),s.options.responsive&&s.options.responsive.length&&null!==s.options.responsive){for(i in n=null,s.breakpoints)s.breakpoints.hasOwnProperty(i)&&(!1===s.originalSettings.mobileFirst?o<s.breakpoints[i]&&(n=s.breakpoints[i]):o>s.breakpoints[i]&&(n=s.breakpoints[i]));null!==n?null!==s.activeBreakpoint&&n===s.activeBreakpoint&&!t||(s.activeBreakpoint=n,"unslick"===s.breakpointSettings[n]?s.unslick(n):(s.options=d.extend({},s.originalSettings,s.breakpointSettings[n]),!0===e&&(s.currentSlide=s.options.initialSlide),s.refresh(e)),r=n):null!==s.activeBreakpoint&&(s.activeBreakpoint=null,s.options=s.originalSettings,!0===e&&(s.currentSlide=s.options.initialSlide),s.refresh(e),r=n),e||!1===r||s.$slider.trigger("breakpoint",[s,r])}},r.prototype.changeSlide=function(e,t){var i,n=this,o=d(e.currentTarget);switch(o.is("a")&&e.preventDefault(),o.is("li")||(o=o.closest("li")),i=n.slideCount%n.options.slidesToScroll!=0?0:(n.slideCount-n.currentSlide)%n.options.slidesToScroll,e.data.message){case"previous":s=0==i?n.options.slidesToScroll:n.options.slidesToShow-i,n.slideCount>n.options.slidesToShow&&n.slideHandler(n.currentSlide-s,!1,t);break;case"next":s=0==i?n.options.slidesToScroll:i,n.slideCount>n.options.slidesToShow&&n.slideHandler(n.currentSlide+s,!1,t);break;case"index":var s=0===e.data.index?0:e.data.index||o.index()*n.options.slidesToScroll;n.slideHandler(n.checkNavigable(s),!1,t),o.children().trigger("focus");break;default:return}},r.prototype.checkNavigable=function(e){var t=this.getNavigableIndexes(),i=0;if(e>t[t.length-1])e=t[t.length-1];else for(var n in t){if(e<t[n]){e=i;break}i=t[n]}return e},r.prototype.cleanUpEvents=function(){var e=this;e.options.dots&&null!==e.$dots&&(d("li",e.$dots).off("click.slick",e.changeSlide).off("mouseenter.slick",d.proxy(e.interrupt,e,!0)).off("mouseleave.slick",d.proxy(e.interrupt,e,!1)),!0===e.options.accessibility)&&e.$dots.off("keydown.slick",e.keyHandler),e.$slider.off("focus.slick blur.slick"),!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow&&e.$prevArrow.off("click.slick",e.changeSlide),e.$nextArrow&&e.$nextArrow.off("click.slick",e.changeSlide),!0===e.options.accessibility)&&(e.$prevArrow&&e.$prevArrow.off("keydown.slick",e.keyHandler),e.$nextArrow)&&e.$nextArrow.off("keydown.slick",e.keyHandler),e.$list.off("touchstart.slick mousedown.slick",e.swipeHandler),e.$list.off("touchmove.slick mousemove.slick",e.swipeHandler),e.$list.off("touchend.slick mouseup.slick",e.swipeHandler),e.$list.off("touchcancel.slick mouseleave.slick",e.swipeHandler),e.$list.off("click.slick",e.clickHandler),d(document).off(e.visibilityChange,e.visibility),e.cleanUpSlideEvents(),!0===e.options.accessibility&&e.$list.off("keydown.slick",e.keyHandler),!0===e.options.focusOnSelect&&d(e.$slideTrack).children().off("click.slick",e.selectHandler),d(window).off("orientationchange.slick.slick-"+e.instanceUid,e.orientationChange),d(window).off("resize.slick.slick-"+e.instanceUid,e.resize),d("[draggable!=true]",e.$slideTrack).off("dragstart",e.preventDefault),d(window).off("load.slick.slick-"+e.instanceUid,e.setPosition)},r.prototype.cleanUpSlideEvents=function(){var e=this;e.$list.off("mouseenter.slick",d.proxy(e.interrupt,e,!0)),e.$list.off("mouseleave.slick",d.proxy(e.interrupt,e,!1))},r.prototype.cleanUpRows=function(){var e;0<this.options.rows&&((e=this.$slides.children().children()).removeAttr("style"),this.$slider.empty().append(e))},r.prototype.clickHandler=function(e){!1===this.shouldClick&&(e.stopImmediatePropagation(),e.stopPropagation(),e.preventDefault())},r.prototype.destroy=function(e){var t=this;t.autoPlayClear(),t.touchObject={},t.cleanUpEvents(),d(".slick-cloned",t.$slider).detach(),t.$dots&&t.$dots.remove(),t.$prevArrow&&t.$prevArrow.length&&(t.$prevArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display",""),t.htmlExpr.test(t.options.prevArrow))&&t.$prevArrow.remove(),t.$nextArrow&&t.$nextArrow.length&&(t.$nextArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display",""),t.htmlExpr.test(t.options.nextArrow))&&t.$nextArrow.remove(),t.$slides&&(t.$slides.removeClass("slick-slide slick-active slick-center slick-visible slick-current").removeAttr("aria-hidden").removeAttr("data-slick-index").each(function(){d(this).attr("style",d(this).data("originalStyling"))}),t.$slideTrack.children(this.options.slide).detach(),t.$slideTrack.detach(),t.$list.detach(),t.$slider.append(t.$slides)),t.cleanUpRows(),t.$slider.removeClass("slick-slider"),t.$slider.removeClass("slick-initialized"),t.$slider.removeClass("slick-dotted"),t.unslicked=!0,e||t.$slider.trigger("destroy",[t])},r.prototype.disableTransition=function(e){var t={};t[this.transitionType]="",(!1===this.options.fade?this.$slideTrack:this.$slides.eq(e)).css(t)},r.prototype.fadeSlide=function(e,t){var i=this;!1===i.cssTransitions?(i.$slides.eq(e).css({zIndex:i.options.zIndex}),i.$slides.eq(e).animate({opacity:1},i.options.speed,i.options.easing,t)):(i.applyTransition(e),i.$slides.eq(e).css({opacity:1,zIndex:i.options.zIndex}),t&&setTimeout(function(){i.disableTransition(e),t.call()},i.options.speed))},r.prototype.fadeSlideOut=function(e){var t=this;!1===t.cssTransitions?t.$slides.eq(e).animate({opacity:0,zIndex:t.options.zIndex-2},t.options.speed,t.options.easing):(t.applyTransition(e),t.$slides.eq(e).css({opacity:0,zIndex:t.options.zIndex-2}))},r.prototype.filterSlides=r.prototype.slickFilter=function(e){var t=this;null!==e&&(t.$slidesCache=t.$slides,t.unload(),t.$slideTrack.children(this.options.slide).detach(),t.$slidesCache.filter(e).appendTo(t.$slideTrack),t.reinit())},r.prototype.focusHandler=function(){var i=this;i.$slider.off("focus.slick blur.slick").on("focus.slick","*",function(e){var t=d(this);setTimeout(function(){i.options.pauseOnFocus&&t.is(":focus")&&(i.focussed=!0,i.autoPlay())},0)}).on("blur.slick","*",function(e){d(this);i.options.pauseOnFocus&&(i.focussed=!1,i.autoPlay())})},r.prototype.getCurrent=r.prototype.slickCurrentSlide=function(){return this.currentSlide},r.prototype.getDotCount=function(){var e=this,t=0,i=0,n=0;if(!0===e.options.infinite)if(e.slideCount<=e.options.slidesToShow)++n;else for(;t<e.slideCount;)++n,t=i+e.options.slidesToScroll,i+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;else if(!0===e.options.centerMode)n=e.slideCount;else if(e.options.asNavFor)for(;t<e.slideCount;)++n,t=i+e.options.slidesToScroll,i+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;else n=1+Math.ceil((e.slideCount-e.options.slidesToShow)/e.options.slidesToScroll);return n-1},r.prototype.getLeft=function(e){var t,i,n=this,o=0;return n.slideOffset=0,t=n.$slides.first().outerHeight(!0),!0===n.options.infinite?(n.slideCount>n.options.slidesToShow&&(n.slideOffset=n.slideWidth*n.options.slidesToShow*-1,i=-1,!0===n.options.vertical&&!0===n.options.centerMode&&(2===n.options.slidesToShow?i=-1.5:1===n.options.slidesToShow&&(i=-2)),o=t*n.options.slidesToShow*i),n.slideCount%n.options.slidesToScroll!=0&&e+n.options.slidesToScroll>n.slideCount&&n.slideCount>n.options.slidesToShow&&(o=e>n.slideCount?(n.slideOffset=(n.options.slidesToShow-(e-n.slideCount))*n.slideWidth*-1,(n.options.slidesToShow-(e-n.slideCount))*t*-1):(n.slideOffset=n.slideCount%n.options.slidesToScroll*n.slideWidth*-1,n.slideCount%n.options.slidesToScroll*t*-1))):e+n.options.slidesToShow>n.slideCount&&(n.slideOffset=(e+n.options.slidesToShow-n.slideCount)*n.slideWidth,o=(e+n.options.slidesToShow-n.slideCount)*t),n.slideCount<=n.options.slidesToShow&&(o=n.slideOffset=0),!0===n.options.centerMode&&n.slideCount<=n.options.slidesToShow?n.slideOffset=n.slideWidth*Math.floor(n.options.slidesToShow)/2-n.slideWidth*n.slideCount/2:!0===n.options.centerMode&&!0===n.options.infinite?n.slideOffset+=n.slideWidth*Math.floor(n.options.slidesToShow/2)-n.slideWidth:!0===n.options.centerMode&&(n.slideOffset=0,n.slideOffset+=n.slideWidth*Math.floor(n.options.slidesToShow/2)),i=!1===n.options.vertical?e*n.slideWidth*-1+n.slideOffset:e*t*-1+o,!0===n.options.variableWidth&&(t=n.slideCount<=n.options.slidesToShow||!1===n.options.infinite?n.$slideTrack.children(".slick-slide").eq(e):n.$slideTrack.children(".slick-slide").eq(e+n.options.slidesToShow),i=!0===n.options.rtl?t[0]?-1*(n.$slideTrack.width()-t[0].offsetLeft-t.width()):0:t[0]?-1*t[0].offsetLeft:0,!0===n.options.centerMode)&&(t=n.slideCount<=n.options.slidesToShow||!1===n.options.infinite?n.$slideTrack.children(".slick-slide").eq(e):n.$slideTrack.children(".slick-slide").eq(e+n.options.slidesToShow+1),i=!0===n.options.rtl?t[0]?-1*(n.$slideTrack.width()-t[0].offsetLeft-t.width()):0:t[0]?-1*t[0].offsetLeft:0,i+=(n.$list.width()-t.outerWidth())/2),i},r.prototype.getOption=r.prototype.slickGetOption=function(e){return this.options[e]},r.prototype.getNavigableIndexes=function(){for(var e=this,t=0,i=0,n=[],o=!1===e.options.infinite?e.slideCount:(t=-1*e.options.slidesToScroll,i=-1*e.options.slidesToScroll,2*e.slideCount);t<o;)n.push(t),t=i+e.options.slidesToScroll,i+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;return n},r.prototype.getSlick=function(){return this},r.prototype.getSlideCount=function(){var o,s=this,e=!0===s.options.centerMode?Math.floor(s.$list.width()/2):0,r=-1*s.swipeLeft+e;return!0===s.options.swipeToSlide?(s.$slideTrack.find(".slick-slide").each(function(e,t){var i=d(t).outerWidth(),n=t.offsetLeft;if(!0!==s.options.centerMode&&(n+=i/2),r<n+i)return o=t,!1}),Math.abs(d(o).attr("data-slick-index")-s.currentSlide)||1):s.options.slidesToScroll},r.prototype.goTo=r.prototype.slickGoTo=function(e,t){this.changeSlide({data:{message:"index",index:parseInt(e)}},t)},r.prototype.init=function(e){var t=this;d(t.$slider).hasClass("slick-initialized")||(d(t.$slider).addClass("slick-initialized"),t.buildRows(),t.buildOut(),t.setProps(),t.startLoad(),t.loadSlider(),t.initializeEvents(),t.updateArrows(),t.updateDots(),t.checkResponsive(!0),t.focusHandler()),e&&t.$slider.trigger("init",[t]),!0===t.options.accessibility&&t.initADA(),t.options.autoplay&&(t.paused=!1,t.autoPlay())},r.prototype.initADA=function(){var i=this,n=Math.ceil(i.slideCount/i.options.slidesToShow),o=i.getNavigableIndexes().filter(function(e){return 0<=e&&e<i.slideCount});i.$slides.add(i.$slideTrack.find(".slick-cloned")).attr({"aria-hidden":"true",tabindex:"-1"}).find("a, input, button, select").attr({tabindex:"-1"}),null!==i.$dots&&(i.$slides.not(i.$slideTrack.find(".slick-cloned")).each(function(e){var t=o.indexOf(e);d(this).attr({role:"tabpanel",id:"slick-slide"+i.instanceUid+e,tabindex:-1}),-1!==t&&(e="slick-slide-control"+i.instanceUid+t,d("#"+e).length)&&d(this).attr({"aria-describedby":e})}),i.$dots.attr("role","tablist").find("li").each(function(e){var t=o[e];d(this).attr({role:"presentation"}),d(this).find("button").first().attr({role:"tab",id:"slick-slide-control"+i.instanceUid+e,"aria-controls":"slick-slide"+i.instanceUid+t,"aria-label":e+1+" of "+n,"aria-selected":null,tabindex:"-1"})}).eq(i.currentSlide).find("button").attr({"aria-selected":"true",tabindex:"0"}).end());for(var e=i.currentSlide,t=e+i.options.slidesToShow;e<t;e++)i.options.focusOnChange?i.$slides.eq(e).attr({tabindex:"0"}):i.$slides.eq(e).removeAttr("tabindex");i.activateADA()},r.prototype.initArrowEvents=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.off("click.slick").on("click.slick",{message:"previous"},e.changeSlide),e.$nextArrow.off("click.slick").on("click.slick",{message:"next"},e.changeSlide),!0===e.options.accessibility)&&(e.$prevArrow.on("keydown.slick",e.keyHandler),e.$nextArrow.on("keydown.slick",e.keyHandler))},r.prototype.initDotEvents=function(){var e=this;!0===e.options.dots&&e.slideCount>e.options.slidesToShow&&(d("li",e.$dots).on("click.slick",{message:"index"},e.changeSlide),!0===e.options.accessibility)&&e.$dots.on("keydown.slick",e.keyHandler),!0===e.options.dots&&!0===e.options.pauseOnDotsHover&&e.slideCount>e.options.slidesToShow&&d("li",e.$dots).on("mouseenter.slick",d.proxy(e.interrupt,e,!0)).on("mouseleave.slick",d.proxy(e.interrupt,e,!1))},r.prototype.initSlideEvents=function(){var e=this;e.options.pauseOnHover&&(e.$list.on("mouseenter.slick",d.proxy(e.interrupt,e,!0)),e.$list.on("mouseleave.slick",d.proxy(e.interrupt,e,!1)))},r.prototype.initializeEvents=function(){var e=this;e.initArrowEvents(),e.initDotEvents(),e.initSlideEvents(),e.$list.on("touchstart.slick mousedown.slick",{action:"start"},e.swipeHandler),e.$list.on("touchmove.slick mousemove.slick",{action:"move"},e.swipeHandler),e.$list.on("touchend.slick mouseup.slick",{action:"end"},e.swipeHandler),e.$list.on("touchcancel.slick mouseleave.slick",{action:"end"},e.swipeHandler),e.$list.on("click.slick",e.clickHandler),d(document).on(e.visibilityChange,d.proxy(e.visibility,e)),!0===e.options.accessibility&&e.$list.on("keydown.slick",e.keyHandler),!0===e.options.focusOnSelect&&d(e.$slideTrack).children().on("click.slick",e.selectHandler),d(window).on("orientationchange.slick.slick-"+e.instanceUid,d.proxy(e.orientationChange,e)),d(window).on("resize.slick.slick-"+e.instanceUid,d.proxy(e.resize,e)),d("[draggable!=true]",e.$slideTrack).on("dragstart",e.preventDefault),d(window).on("load.slick.slick-"+e.instanceUid,e.setPosition),d(e.setPosition)},r.prototype.initUI=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.show(),e.$nextArrow.show()),!0===e.options.dots&&e.slideCount>e.options.slidesToShow&&e.$dots.show()},r.prototype.keyHandler=function(e){var t=this;e.target.tagName.match("TEXTAREA|INPUT|SELECT")||(37===e.keyCode&&!0===t.options.accessibility?t.changeSlide({data:{message:!0===t.options.rtl?"next":"previous"}}):39===e.keyCode&&!0===t.options.accessibility&&t.changeSlide({data:{message:!0===t.options.rtl?"previous":"next"}}))},r.prototype.lazyLoad=function(){var e,t,i,s=this;function n(e){d("img[data-lazy]",e).each(function(){var e=d(this),t=d(this).attr("data-lazy"),i=d(this).attr("data-srcset"),n=d(this).attr("data-sizes")||s.$slider.attr("data-sizes"),o=document.createElement("img");o.onload=function(){e.animate({opacity:0},100,function(){i&&(e.attr("srcset",i),n)&&e.attr("sizes",n),e.attr("src",t).animate({opacity:1},200,function(){e.removeAttr("data-lazy data-srcset data-sizes").removeClass("slick-loading")}),s.$slider.trigger("lazyLoaded",[s,e,t])})},o.onerror=function(){e.removeAttr("data-lazy").removeClass("slick-loading").addClass("slick-lazyload-error"),s.$slider.trigger("lazyLoadError",[s,e,t])},o.src=t})}if(!0===s.options.centerMode?i=!0===s.options.infinite?(t=s.currentSlide+(s.options.slidesToShow/2+1))+s.options.slidesToShow+2:(t=Math.max(0,s.currentSlide-(s.options.slidesToShow/2+1)),s.options.slidesToShow/2+1+2+s.currentSlide):(t=s.options.infinite?s.options.slidesToShow+s.currentSlide:s.currentSlide,i=Math.ceil(t+s.options.slidesToShow),!0===s.options.fade&&(0<t&&t--,i<=s.slideCount)&&i++),e=s.$slider.find(".slick-slide").slice(t,i),"anticipated"===s.options.lazyLoad)for(var o=t-1,r=i,l=s.$slider.find(".slick-slide"),a=0;a<s.options.slidesToScroll;a++)o<0&&(o=s.slideCount-1),e=(e=e.add(l.eq(o))).add(l.eq(r)),o--,r++;n(e),s.slideCount<=s.options.slidesToShow?n(s.$slider.find(".slick-slide")):s.currentSlide>=s.slideCount-s.options.slidesToShow?n(s.$slider.find(".slick-cloned").slice(0,s.options.slidesToShow)):0===s.currentSlide&&n(s.$slider.find(".slick-cloned").slice(-1*s.options.slidesToShow))},r.prototype.loadSlider=function(){var e=this;e.setPosition(),e.$slideTrack.css({opacity:1}),e.$slider.removeClass("slick-loading"),e.initUI(),"progressive"===e.options.lazyLoad&&e.progressiveLazyLoad()},r.prototype.next=r.prototype.slickNext=function(){this.changeSlide({data:{message:"next"}})},r.prototype.orientationChange=function(){this.checkResponsive(),this.setPosition()},r.prototype.pause=r.prototype.slickPause=function(){this.autoPlayClear(),this.paused=!0},r.prototype.play=r.prototype.slickPlay=function(){var e=this;e.autoPlay(),e.options.autoplay=!0,e.paused=!1,e.focussed=!1,e.interrupted=!1},r.prototype.postSlide=function(e){var t=this;t.unslicked||(t.$slider.trigger("afterChange",[t,e]),t.animating=!1,t.slideCount>t.options.slidesToShow&&t.setPosition(),t.swipeLeft=null,t.options.autoplay&&t.autoPlay(),!0===t.options.accessibility&&(t.initADA(),t.options.focusOnChange)&&d(t.$slides.get(t.currentSlide)).attr("tabindex",0).focus())},r.prototype.prev=r.prototype.slickPrev=function(){this.changeSlide({data:{message:"previous"}})},r.prototype.preventDefault=function(e){e.preventDefault()},r.prototype.progressiveLazyLoad=function(e){e=e||1;var t,i,n,o,s=this,r=d("img[data-lazy]",s.$slider);r.length?(t=r.first(),i=t.attr("data-lazy"),n=t.attr("data-srcset"),o=t.attr("data-sizes")||s.$slider.attr("data-sizes"),(r=document.createElement("img")).onload=function(){n&&(t.attr("srcset",n),o)&&t.attr("sizes",o),t.attr("src",i).removeAttr("data-lazy data-srcset data-sizes").removeClass("slick-loading"),!0===s.options.adaptiveHeight&&s.setPosition(),s.$slider.trigger("lazyLoaded",[s,t,i]),s.progressiveLazyLoad()},r.onerror=function(){e<3?setTimeout(function(){s.progressiveLazyLoad(e+1)},500):(t.removeAttr("data-lazy").removeClass("slick-loading").addClass("slick-lazyload-error"),s.$slider.trigger("lazyLoadError",[s,t,i]),s.progressiveLazyLoad())},r.src=i):s.$slider.trigger("allImagesLoaded",[s])},r.prototype.refresh=function(e){var t=this,i=t.slideCount-t.options.slidesToShow;!t.options.infinite&&t.currentSlide>i&&(t.currentSlide=i),t.slideCount<=t.options.slidesToShow&&(t.currentSlide=0),i=t.currentSlide,t.destroy(!0),d.extend(t,t.initials,{currentSlide:i}),t.init(),e||t.changeSlide({data:{message:"index",index:i}},!1)},r.prototype.registerBreakpoints=function(){var e,t,i,n=this,o=n.options.responsive||null;if("array"===d.type(o)&&o.length){for(e in n.respondTo=n.options.respondTo||"window",o)if(i=n.breakpoints.length-1,o.hasOwnProperty(e)){for(t=o[e].breakpoint;0<=i;)n.breakpoints[i]&&n.breakpoints[i]===t&&n.breakpoints.splice(i,1),i--;n.breakpoints.push(t),n.breakpointSettings[t]=o[e].settings}n.breakpoints.sort(function(e,t){return n.options.mobileFirst?e-t:t-e})}},r.prototype.reinit=function(){var e=this;e.$slides=e.$slideTrack.children(e.options.slide).addClass("slick-slide"),e.slideCount=e.$slides.length,e.currentSlide>=e.slideCount&&0!==e.currentSlide&&(e.currentSlide=e.currentSlide-e.options.slidesToScroll),e.slideCount<=e.options.slidesToShow&&(e.currentSlide=0),e.registerBreakpoints(),e.setProps(),e.setupInfinite(),e.buildArrows(),e.updateArrows(),e.initArrowEvents(),e.buildDots(),e.updateDots(),e.initDotEvents(),e.cleanUpSlideEvents(),e.initSlideEvents(),e.checkResponsive(!1,!0),!0===e.options.focusOnSelect&&d(e.$slideTrack).children().on("click.slick",e.selectHandler),e.setSlideClasses("number"==typeof e.currentSlide?e.currentSlide:0),e.setPosition(),e.focusHandler(),e.paused=!e.options.autoplay,e.autoPlay(),e.$slider.trigger("reInit",[e])},r.prototype.resize=function(){var e=this;d(window).width()!==e.windowWidth&&(clearTimeout(e.windowDelay),e.windowDelay=window.setTimeout(function(){e.windowWidth=d(window).width(),e.checkResponsive(),e.unslicked||e.setPosition()},50))},r.prototype.removeSlide=r.prototype.slickRemove=function(e,t,i){var n=this;if(e="boolean"==typeof e?!0===(t=e)?0:n.slideCount-1:!0===t?--e:e,n.slideCount<1||e<0||e>n.slideCount-1)return!1;n.unload(),(!0===i?n.$slideTrack.children():n.$slideTrack.children(this.options.slide).eq(e)).remove(),n.$slides=n.$slideTrack.children(this.options.slide),n.$slideTrack.children(this.options.slide).detach(),n.$slideTrack.append(n.$slides),n.$slidesCache=n.$slides,n.reinit()},r.prototype.setCSS=function(e){var t,i,n=this,o={};!0===n.options.rtl&&(e=-e),t="left"==n.positionProp?Math.ceil(e)+"px":"0px",i="top"==n.positionProp?Math.ceil(e)+"px":"0px",o[n.positionProp]=e,!1!==n.transformsEnabled&&(!(o={})===n.cssTransitions?o[n.animType]="translate("+t+", "+i+")":o[n.animType]="translate3d("+t+", "+i+", 0px)"),n.$slideTrack.css(o)},r.prototype.setDimensions=function(){var e=this,t=(!1===e.options.vertical?!0===e.options.centerMode&&e.$list.css({padding:"0px "+e.options.centerPadding}):(e.$list.height(e.$slides.first().outerHeight(!0)*e.options.slidesToShow),!0===e.options.centerMode&&e.$list.css({padding:e.options.centerPadding+" 0px"})),e.listWidth=e.$list.width(),e.listHeight=e.$list.height(),!1===e.options.vertical&&!1===e.options.variableWidth?(e.slideWidth=Math.ceil(e.listWidth/e.options.slidesToShow),e.$slideTrack.width(Math.ceil(e.slideWidth*e.$slideTrack.children(".slick-slide").length))):!0===e.options.variableWidth?e.$slideTrack.width(5e3*e.slideCount):(e.slideWidth=Math.ceil(e.listWidth),e.$slideTrack.height(Math.ceil(e.$slides.first().outerHeight(!0)*e.$slideTrack.children(".slick-slide").length))),e.$slides.first().outerWidth(!0)-e.$slides.first().width());!1===e.options.variableWidth&&e.$slideTrack.children(".slick-slide").width(e.slideWidth-t)},r.prototype.setFade=function(){var i,n=this;n.$slides.each(function(e,t){i=n.slideWidth*e*-1,!0===n.options.rtl?d(t).css({position:"relative",right:i,top:0,zIndex:n.options.zIndex-2,opacity:0}):d(t).css({position:"relative",left:i,top:0,zIndex:n.options.zIndex-2,opacity:0})}),n.$slides.eq(n.currentSlide).css({zIndex:n.options.zIndex-1,opacity:1})},r.prototype.setHeight=function(){var e,t=this;1===t.options.slidesToShow&&!0===t.options.adaptiveHeight&&!1===t.options.vertical&&(e=t.$slides.eq(t.currentSlide).outerHeight(!0),t.$list.css("height",e))},r.prototype.setOption=r.prototype.slickSetOption=function(){var e,t,i,n,o,s=this,r=!1;if("object"===d.type(arguments[0])?(i=arguments[0],r=arguments[1],o="multiple"):"string"===d.type(arguments[0])&&(i=arguments[0],n=arguments[1],r=arguments[2],"responsive"===arguments[0]&&"array"===d.type(arguments[1])?o="responsive":void 0!==arguments[1]&&(o="single")),"single"===o)s.options[i]=n;else if("multiple"===o)d.each(i,function(e,t){s.options[e]=t});else if("responsive"===o)for(t in n)if("array"!==d.type(s.options.responsive))s.options.responsive=[n[t]];else{for(e=s.options.responsive.length-1;0<=e;)s.options.responsive[e].breakpoint===n[t].breakpoint&&s.options.responsive.splice(e,1),e--;s.options.responsive.push(n[t])}r&&(s.unload(),s.reinit())},r.prototype.setPosition=function(){var e=this;e.setDimensions(),e.setHeight(),!1===e.options.fade?e.setCSS(e.getLeft(e.currentSlide)):e.setFade(),e.$slider.trigger("setPosition",[e])},r.prototype.setProps=function(){var e=this,t=document.body.style;e.positionProp=!0===e.options.vertical?"top":"left","top"===e.positionProp?e.$slider.addClass("slick-vertical"):e.$slider.removeClass("slick-vertical"),void 0===t.WebkitTransition&&void 0===t.MozTransition&&void 0===t.msTransition||!0===e.options.useCSS&&(e.cssTransitions=!0),e.options.fade&&("number"==typeof e.options.zIndex?e.options.zIndex<3&&(e.options.zIndex=3):e.options.zIndex=e.defaults.zIndex),void 0!==t.OTransform&&(e.animType="OTransform",e.transformType="-o-transform",e.transitionType="OTransition",void 0===t.perspectiveProperty)&&void 0===t.webkitPerspective&&(e.animType=!1),void 0!==t.MozTransform&&(e.animType="MozTransform",e.transformType="-moz-transform",e.transitionType="MozTransition",void 0===t.perspectiveProperty)&&void 0===t.MozPerspective&&(e.animType=!1),void 0!==t.webkitTransform&&(e.animType="webkitTransform",e.transformType="-webkit-transform",e.transitionType="webkitTransition",void 0===t.perspectiveProperty)&&void 0===t.webkitPerspective&&(e.animType=!1),void 0!==t.msTransform&&(e.animType="msTransform",e.transformType="-ms-transform",e.transitionType="msTransition",void 0===t.msTransform)&&(e.animType=!1),void 0!==t.transform&&!1!==e.animType&&(e.animType="transform",e.transformType="transform",e.transitionType="transition"),e.transformsEnabled=e.options.useTransform&&null!==e.animType&&!1!==e.animType},r.prototype.setSlideClasses=function(e){var t,i,n,o=this,s=o.$slider.find(".slick-slide").removeClass("slick-active slick-center slick-current").attr("aria-hidden","true");o.$slides.eq(e).addClass("slick-current"),!0===o.options.centerMode?(i=o.options.slidesToShow%2==0?1:0,n=Math.floor(o.options.slidesToShow/2),!0===o.options.infinite&&((n<=e&&e<=o.slideCount-1-n?o.$slides.slice(e-n+i,e+n+1):(t=o.options.slidesToShow+e,s.slice(t-n+1+i,t+n+2))).addClass("slick-active").attr("aria-hidden","false"),0===e?s.eq(o.options.slidesToShow+o.slideCount+1).addClass("slick-center"):e===o.slideCount-1&&s.eq(o.options.slidesToShow).addClass("slick-center")),o.$slides.eq(e).addClass("slick-center")):(0<=e&&e<=o.slideCount-o.options.slidesToShow?o.$slides.slice(e,e+o.options.slidesToShow):s.length<=o.options.slidesToShow?s:(i=o.slideCount%o.options.slidesToShow,t=!0===o.options.infinite?o.options.slidesToShow+e:e,o.options.slidesToShow==o.options.slidesToScroll&&o.slideCount-e<o.options.slidesToShow?s.slice(t-(o.options.slidesToShow-i),t+i):s.slice(t,t+o.options.slidesToShow))).addClass("slick-active").attr("aria-hidden","false"),"ondemand"!==o.options.lazyLoad&&"anticipated"!==o.options.lazyLoad||o.lazyLoad()},r.prototype.setupInfinite=function(){var e,t,i,n=this;if(!0===n.options.fade&&(n.options.centerMode=!1),!0===n.options.infinite&&!1===n.options.fade&&(t=null,n.slideCount>n.options.slidesToShow)){for(i=!0===n.options.centerMode?n.options.slidesToShow+1:n.options.slidesToShow,e=n.slideCount;e>n.slideCount-i;--e)d(n.$slides[t=e-1]).clone(!0).attr("id","").attr("data-slick-index",t-n.slideCount).prependTo(n.$slideTrack).addClass("slick-cloned");for(e=0;e<i+n.slideCount;e+=1)t=e,d(n.$slides[t]).clone(!0).attr("id","").attr("data-slick-index",t+n.slideCount).appendTo(n.$slideTrack).addClass("slick-cloned");n.$slideTrack.find(".slick-cloned").find("[id]").each(function(){d(this).attr("id","")})}},r.prototype.interrupt=function(e){e||this.autoPlay(),this.interrupted=e},r.prototype.selectHandler=function(e){e=d(e.target).is(".slick-slide")?d(e.target):d(e.target).parents(".slick-slide"),e=(e=parseInt(e.attr("data-slick-index")))||0;this.slideCount<=this.options.slidesToShow?this.slideHandler(e,!1,!0):this.slideHandler(e)},r.prototype.slideHandler=function(e,t,i){var n,o,s,r=this;t=t||!1,!0===r.animating&&!0===r.options.waitForAnimate||!0===r.options.fade&&r.currentSlide===e||(!1===t&&r.asNavFor(e),n=e,t=r.getLeft(n),s=r.getLeft(r.currentSlide),r.currentLeft=null===r.swipeLeft?s:r.swipeLeft,!1===r.options.infinite&&!1===r.options.centerMode&&(e<0||e>r.getDotCount()*r.options.slidesToScroll)||!1===r.options.infinite&&!0===r.options.centerMode&&(e<0||e>r.slideCount-r.options.slidesToScroll)?!1===r.options.fade&&(n=r.currentSlide,!0!==i&&r.slideCount>r.options.slidesToShow?r.animateSlide(s,function(){r.postSlide(n)}):r.postSlide(n)):(r.options.autoplay&&clearInterval(r.autoPlayTimer),o=n<0?r.slideCount%r.options.slidesToScroll!=0?r.slideCount-r.slideCount%r.options.slidesToScroll:r.slideCount+n:n>=r.slideCount?r.slideCount%r.options.slidesToScroll!=0?0:n-r.slideCount:n,r.animating=!0,r.$slider.trigger("beforeChange",[r,r.currentSlide,o]),e=r.currentSlide,r.currentSlide=o,r.setSlideClasses(r.currentSlide),r.options.asNavFor&&(s=(s=r.getNavTarget()).slick("getSlick")).slideCount<=s.options.slidesToShow&&s.setSlideClasses(r.currentSlide),r.updateDots(),r.updateArrows(),!0===r.options.fade?(!0!==i?(r.fadeSlideOut(e),r.fadeSlide(o,function(){r.postSlide(o)})):r.postSlide(o),r.animateHeight()):!0!==i&&r.slideCount>r.options.slidesToShow?r.animateSlide(t,function(){r.postSlide(o)}):r.postSlide(o)))},r.prototype.startLoad=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.hide(),e.$nextArrow.hide()),!0===e.options.dots&&e.slideCount>e.options.slidesToShow&&e.$dots.hide(),e.$slider.addClass("slick-loading")},r.prototype.swipeDirection=function(){var e=this,t=e.touchObject.startX-e.touchObject.curX,i=e.touchObject.startY-e.touchObject.curY,i=Math.atan2(i,t),t=Math.round(180*i/Math.PI);return(t=t<0?360-Math.abs(t):t)<=45&&0<=t||t<=360&&315<=t?!1===e.options.rtl?"left":"right":135<=t&&t<=225?!1===e.options.rtl?"right":"left":!0===e.options.verticalSwiping?35<=t&&t<=135?"down":"up":"vertical"},r.prototype.swipeEnd=function(e){var t,i,n=this;if(n.dragging=!1,n.swiping=!1,n.scrolling)return n.scrolling=!1;if(n.interrupted=!1,n.shouldClick=!(10<n.touchObject.swipeLength),void 0===n.touchObject.curX)return!1;if(!0===n.touchObject.edgeHit&&n.$slider.trigger("edge",[n,n.swipeDirection()]),n.touchObject.swipeLength>=n.touchObject.minSwipe){switch(i=n.swipeDirection()){case"left":case"down":t=n.options.swipeToSlide?n.checkNavigable(n.currentSlide+n.getSlideCount()):n.currentSlide+n.getSlideCount(),n.currentDirection=0;break;case"right":case"up":t=n.options.swipeToSlide?n.checkNavigable(n.currentSlide-n.getSlideCount()):n.currentSlide-n.getSlideCount(),n.currentDirection=1}"vertical"!=i&&(n.slideHandler(t),n.touchObject={},n.$slider.trigger("swipe",[n,i]))}else n.touchObject.startX!==n.touchObject.curX&&(n.slideHandler(n.currentSlide),n.touchObject={})},r.prototype.swipeHandler=function(e){var t=this;if(!(!1===t.options.swipe||"ontouchend"in document&&!1===t.options.swipe||!1===t.options.draggable&&-1!==e.type.indexOf("mouse")))switch(t.touchObject.fingerCount=e.originalEvent&&void 0!==e.originalEvent.touches?e.originalEvent.touches.length:1,t.touchObject.minSwipe=t.listWidth/t.options.touchThreshold,!0===t.options.verticalSwiping&&(t.touchObject.minSwipe=t.listHeight/t.options.touchThreshold),e.data.action){case"start":t.swipeStart(e);break;case"move":t.swipeMove(e);break;case"end":t.swipeEnd(e)}},r.prototype.swipeMove=function(e){var t,i,n=this,o=void 0!==e.originalEvent?e.originalEvent.touches:null;return!(!n.dragging||n.scrolling||o&&1!==o.length)&&(t=n.getLeft(n.currentSlide),n.touchObject.curX=void 0!==o?o[0].pageX:e.clientX,n.touchObject.curY=void 0!==o?o[0].pageY:e.clientY,n.touchObject.swipeLength=Math.round(Math.sqrt(Math.pow(n.touchObject.curX-n.touchObject.startX,2))),o=Math.round(Math.sqrt(Math.pow(n.touchObject.curY-n.touchObject.startY,2))),!n.options.verticalSwiping&&!n.swiping&&4<o?!(n.scrolling=!0):(!0===n.options.verticalSwiping&&(n.touchObject.swipeLength=o),o=n.swipeDirection(),void 0!==e.originalEvent&&4<n.touchObject.swipeLength&&(n.swiping=!0,e.preventDefault()),e=(!1===n.options.rtl?1:-1)*(n.touchObject.curX>n.touchObject.startX?1:-1),!0===n.options.verticalSwiping&&(e=n.touchObject.curY>n.touchObject.startY?1:-1),i=n.touchObject.swipeLength,(n.touchObject.edgeHit=!1)===n.options.infinite&&(0===n.currentSlide&&"right"===o||n.currentSlide>=n.getDotCount()&&"left"===o)&&(i=n.touchObject.swipeLength*n.options.edgeFriction,n.touchObject.edgeHit=!0),!1===n.options.vertical?n.swipeLeft=t+i*e:n.swipeLeft=t+i*(n.$list.height()/n.listWidth)*e,!0===n.options.verticalSwiping&&(n.swipeLeft=t+i*e),!0!==n.options.fade&&!1!==n.options.touchMove&&(!0===n.animating?(n.swipeLeft=null,!1):void n.setCSS(n.swipeLeft))))},r.prototype.swipeStart=function(e){var t,i=this;if(i.interrupted=!0,1!==i.touchObject.fingerCount||i.slideCount<=i.options.slidesToShow)return!(i.touchObject={});void 0!==e.originalEvent&&void 0!==e.originalEvent.touches&&(t=e.originalEvent.touches[0]),i.touchObject.startX=i.touchObject.curX=void 0!==t?t.pageX:e.clientX,i.touchObject.startY=i.touchObject.curY=void 0!==t?t.pageY:e.clientY,i.dragging=!0},r.prototype.unfilterSlides=r.prototype.slickUnfilter=function(){var e=this;null!==e.$slidesCache&&(e.unload(),e.$slideTrack.children(this.options.slide).detach(),e.$slidesCache.appendTo(e.$slideTrack),e.reinit())},r.prototype.unload=function(){var e=this;d(".slick-cloned",e.$slider).remove(),e.$dots&&e.$dots.remove(),e.$prevArrow&&e.htmlExpr.test(e.options.prevArrow)&&e.$prevArrow.remove(),e.$nextArrow&&e.htmlExpr.test(e.options.nextArrow)&&e.$nextArrow.remove(),e.$slides.removeClass("slick-slide slick-active slick-visible slick-current").attr("aria-hidden","true").css("width","")},r.prototype.unslick=function(e){this.$slider.trigger("unslick",[this,e]),this.destroy()},r.prototype.updateArrows=function(){var e=this;Math.floor(e.options.slidesToShow/2);!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&!e.options.infinite&&(e.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false"),e.$nextArrow.removeClass("slick-disabled").attr("aria-disabled","false"),0===e.currentSlide?(e.$prevArrow.addClass("slick-disabled").attr("aria-disabled","true"),e.$nextArrow.removeClass("slick-disabled").attr("aria-disabled","false")):(e.currentSlide>=e.slideCount-e.options.slidesToShow&&!1===e.options.centerMode||e.currentSlide>=e.slideCount-1&&!0===e.options.centerMode)&&(e.$nextArrow.addClass("slick-disabled").attr("aria-disabled","true"),e.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false")))},r.prototype.updateDots=function(){var e=this;null!==e.$dots&&(e.$dots.find("li").removeClass("slick-active").end(),e.$dots.find("li").eq(Math.floor(e.currentSlide/e.options.slidesToScroll)).addClass("slick-active"))},r.prototype.visibility=function(){this.options.autoplay&&(document[this.hidden]?this.interrupted=!0:this.interrupted=!1)},d.fn.slick=function(){for(var e,t=this,i=arguments[0],n=Array.prototype.slice.call(arguments,1),o=t.length,s=0;s<o;s++)if("object"==_typeof(i)||void 0===i?t[s].slick=new r(t[s],i):e=t[s].slick[i].apply(t[s].slick,n),void 0!==e)return e;return t}}),$(document).ready(function(){$(".product-img-lg").slick({arrows:!1,slidesToShow:1,slidesToScroll:1,fade:!0,asNavFor:".product-img-nav"}),$(".product-img-nav").slick({slidesToShow:4,slidesToScroll:1,infinate:!1,focusOnSelect:!0,initialSlide:0,asNavFor:".product-img-lg",responsive:[{breakpoint:480,settings:{slidesToShow:3,slidesToScroll:1}}]}),$("#prodCarRelProd .product-row").slick({slidesToShow:3,slidesToScroll:1,centerMode:!1,infinite:!0,appendArrows:"#prodCarRelProd",responsive:[{breakpoint:600,settings:{slidesToShow:1,slidesToScroll:1,centerMode:!0}},{breakpoint:992,settings:{slidesToShow:2,slidesToScroll:1,centerMode:!1}}]}),$("#prodCarAlsoPurchased .product-row").slick({slidesToShow:3,slidesToScroll:1,centerMode:!1,infinite:!0,appendArrows:"#prodCarAlsoPurchased",responsive:[{breakpoint:600,settings:{slidesToShow:1,slidesToScroll:1,centerMode:!0}},{breakpoint:992,settings:{slidesToShow:2,slidesToScroll:1,centerMode:!1}}]}),$("#prodCarCartRecs .product-row").slick({slidesToShow:3,slidesToScroll:1,centerMode:!1,infinite:!0,appendArrows:"#prodCarCartRecs",responsive:[{breakpoint:600,settings:{slidesToShow:1,slidesToScroll:1,centerMode:!0}},{breakpoint:992,settings:{slidesToShow:2,slidesToScroll:1,centerMode:!1}}]}),$("#prodCarOrderConfRecs .product-row").slick({slidesToShow:3,slidesToScroll:1,centerMode:!1,infinite:!0,appendArrows:"#prodCarOrderConfRecs",responsive:[{breakpoint:600,settings:{slidesToShow:1,slidesToScroll:1,centerMode:!0}},{breakpoint:992,settings:{slidesToShow:2,slidesToScroll:1,centerMode:!1}}]}),$("#prodCarHomeRecs").slick({slidesToShow:3,slidesToScroll:1,centerMode:!1,infinite:!0,appendArrows:"#prodCarHomeRecs",responsive:[{breakpoint:600,settings:{slidesToShow:1,slidesToScroll:1,centerMode:!0}},{breakpoint:992,settings:{slidesToShow:2,slidesToScroll:1,centerMode:!1}}]}),$("#artCarNewArt .ii-article-row").slick({slidesToShow:3,slidesToScroll:1,centerMode:!1,infinite:!1,appendArrows:"#artCarNewArt",responsive:[{breakpoint:768,settings:{slidesToShow:1,slidesToScroll:1,centerMode:!0,centerPadding:"0",arrows:!1}},{breakpoint:992,settings:{slidesToShow:2,slidesToScroll:1,centerMode:!1,centerPadding:"50px"}}]}),$("#quoteCarousel").slick({dots:!0,centerMode:!0,slidesToShow:1,centerPadding:"24px",mobileFirst:!0,appendArrows:"#quoteCarousel",responsive:[{breakpoint:768,settings:{centerPadding:"32px"}},{breakpoint:992,settings:{centerPadding:"48px"}},{breakpoint:1232,settings:{centerPadding:"60px"}},{breakpoint:1472,settings:{centerPadding:"90px"}}]})}),((e,t)=>{"object"===("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?t(exports,require("jquery")):"function"==typeof define&&define.amd?define(["exports","jquery"],t):t((e="undefined"!=typeof globalThis?globalThis:self).bootstrap={},e.jQuery)})(void 0,function(e,t){function M(e){return e&&"object"===_typeof(e)&&"default"in e?e:{default:e}}var c=M(t);function H(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function r(e,t,i){t&&H(e.prototype,t),i&&H(e,i),Object.defineProperty(e,"prototype",{writable:!1})}function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i,n=arguments[t];for(i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}function j(e,t){return(j=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var z="transitionend";function q(e){var t=this,i=!1;return c.default(this).one(u.TRANSITION_END,function(){i=!0}),setTimeout(function(){i||u.triggerTransitionEnd(t)},e),this}var u={TRANSITION_END:"bsTransitionEnd",getUID:function(e){for(;e+=~~(1e6*Math.random()),document.getElementById(e););return e},getSelectorFromElement:function(e){var t=e.getAttribute("data-target");t&&"#"!==t||(t=(e=e.getAttribute("href"))&&"#"!==e?e.trim():"");try{return document.querySelector(t)?t:null}catch(e){return null}},getTransitionDurationFromElement:function(e){var t,i,n;return e&&(t=c.default(e).css("transition-duration"),e=c.default(e).css("transition-delay"),i=parseFloat(t),n=parseFloat(e),i||n)?(t=t.split(",")[0],e=e.split(",")[0],1e3*(parseFloat(t)+parseFloat(e))):0},reflow:function(e){return e.offsetHeight},triggerTransitionEnd:function(e){c.default(e).trigger(z)},supportsTransitionEnd:function(){return Boolean(z)},isElement:function(e){return(e[0]||e).nodeType},typeCheckConfig:function(e,t,i){for(var n in i)if(Object.prototype.hasOwnProperty.call(i,n)){var o=i[n],s=t[n],s=s&&u.isElement(s)?"element":null==(s=s)?""+s:{}.toString.call(s).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(o).test(s))throw new Error(e.toUpperCase()+': Option "'+n+'" provided type "'+s+'" but expected type "'+o+'".')}},findShadowRoot:function(e){var t;return document.documentElement.attachShadow?"function"==typeof e.getRootNode?(t=e.getRootNode())instanceof ShadowRoot?t:null:e instanceof ShadowRoot?e:e.parentNode?u.findShadowRoot(e.parentNode):null:null},jQueryDetection:function(){if(void 0===c.default)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var e=c.default.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||4<=e[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}},F=(u.jQueryDetection(),c.default.fn.emulateTransitionEnd=q,c.default.event.special[u.TRANSITION_END]={bindType:z,delegateType:z,handle:function(e){if(c.default(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}},"alert"),W="bs.alert",B=c.default.fn[F],i=(()=>{function n(e){this._element=e}var e=n.prototype;return e.close=function(e){var t=this._element;e&&(t=this._getRootElement(e)),this._triggerCloseEvent(t).isDefaultPrevented()||this._removeElement(t)},e.dispose=function(){c.default.removeData(this._element,W),this._element=null},e._getRootElement=function(e){var t=u.getSelectorFromElement(e),i=!1;return i=(i=t?document.querySelector(t):i)||c.default(e).closest(".alert")[0]},e._triggerCloseEvent=function(e){var t=c.default.Event("close.bs.alert");return c.default(e).trigger(t),t},e._removeElement=function(t){var e,i=this;c.default(t).removeClass("show"),c.default(t).hasClass("fade")?(e=u.getTransitionDurationFromElement(t),c.default(t).one(u.TRANSITION_END,function(e){return i._destroyElement(t,e)}).emulateTransitionEnd(e)):this._destroyElement(t)},e._destroyElement=function(e){c.default(e).detach().trigger("closed.bs.alert").remove()},n._jQueryInterface=function(i){return this.each(function(){var e=c.default(this),t=e.data(W);t||(t=new n(this),e.data(W,t)),"close"===i&&t[i](this)})},n._handleDismiss=function(t){return function(e){e&&e.preventDefault(),t.close(this)}},r(n,null,[{key:"VERSION",get:function(){return"4.6.2"}}]),n})(),R=(c.default(document).on("click.bs.alert.data-api",'[data-dismiss="alert"]',i._handleDismiss(new i)),c.default.fn[F]=i._jQueryInterface,c.default.fn[F].Constructor=i,c.default.fn[F].noConflict=function(){return c.default.fn[F]=B,i._jQueryInterface},"button"),X="bs.button",Y=c.default.fn[R],a="active",t='[data-toggle^="button"]',U='input:not([type="hidden"])',V=(()=>{function o(e){this._element=e,this.shouldAvoidTriggerChange=!1}var e=o.prototype;return e.toggle=function(){var e,t=!0,i=!0,n=c.default(this._element).closest('[data-toggle="buttons"]')[0];n&&(e=this._element.querySelector(U))&&("radio"===e.type&&(e.checked&&this._element.classList.contains(a)?t=!1:(n=n.querySelector(".active"))&&c.default(n).removeClass(a)),t&&("checkbox"!==e.type&&"radio"!==e.type||(e.checked=!this._element.classList.contains(a)),this.shouldAvoidTriggerChange||c.default(e).trigger("change")),e.focus(),i=!1),this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(i&&this._element.setAttribute("aria-pressed",!this._element.classList.contains(a)),t&&c.default(this._element).toggleClass(a))},e.dispose=function(){c.default.removeData(this._element,X),this._element=null},o._jQueryInterface=function(i,n){return this.each(function(){var e=c.default(this),t=e.data(X);t||(t=new o(this),e.data(X,t)),t.shouldAvoidTriggerChange=n,"toggle"===i&&t[i]()})},r(o,null,[{key:"VERSION",get:function(){return"4.6.2"}}]),o})(),n=(c.default(document).on("click.bs.button.data-api",t,function(e){var t,i=e.target,n=i;!(i=c.default(i).hasClass("btn")?i:c.default(i).closest(".btn")[0])||i.hasAttribute("disabled")||i.classList.contains("disabled")||(t=i.querySelector(U))&&(t.hasAttribute("disabled")||t.classList.contains("disabled"))?e.preventDefault():"INPUT"!==n.tagName&&"LABEL"===i.tagName||V._jQueryInterface.call(c.default(i),"toggle","INPUT"===n.tagName)}).on("focus.bs.button.data-api blur.bs.button.data-api",t,function(e){var t=c.default(e.target).closest(".btn")[0];c.default(t).toggleClass("focus",/^focus(in)?$/.test(e.type))}),c.default(window).on("load.bs.button.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),t=0,i=e.length;t<i;t++){var n=e[t],o=n.querySelector(U);o.checked||o.hasAttribute("checked")?n.classList.add(a):n.classList.remove(a)}for(var s=0,r=(e=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;s<r;s++){var l=e[s];"true"===l.getAttribute("aria-pressed")?l.classList.add(a):l.classList.remove(a)}}),c.default.fn[R]=V._jQueryInterface,c.default.fn[R].Constructor=V,c.default.fn[R].noConflict=function(){return c.default.fn[R]=Y,V._jQueryInterface},"carousel"),Q="bs.carousel",G="."+Q,K=c.default.fn[n],h="active",Z="next",J="prev",ee="slid"+G,te=".active.carousel-item",ie={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},ne={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},oe={TOUCH:"touch",PEN:"pen"},o=(()=>{function o(e,t){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(t),this._element=e,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}var e=o.prototype;return e.next=function(){this._isSliding||this._slide(Z)},e.nextWhenVisible=function(){var e=c.default(this._element);!document.hidden&&e.is(":visible")&&"hidden"!==e.css("visibility")&&this.next()},e.prev=function(){this._isSliding||this._slide(J)},e.pause=function(e){e||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(u.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},e.cycle=function(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._updateInterval(),this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},e.to=function(e){var t=this,i=(this._activeElement=this._element.querySelector(te),this._getItemIndex(this._activeElement));e>this._items.length-1||e<0||(this._isSliding?c.default(this._element).one(ee,function(){return t.to(e)}):i===e?(this.pause(),this.cycle()):this._slide(i<e?Z:J,this._items[e]))},e.dispose=function(){c.default(this._element).off(G),c.default.removeData(this._element,Q),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},e._getConfig=function(e){return e=l({},ie,e),u.typeCheckConfig(n,e,ne),e},e._handleSwipe=function(){var e=Math.abs(this.touchDeltaX);e<=40||(e=e/this.touchDeltaX,(this.touchDeltaX=0)<e&&this.prev(),e<0&&this.next())},e._addEventListeners=function(){var t=this;this._config.keyboard&&c.default(this._element).on("keydown.bs.carousel",function(e){return t._keydown(e)}),"hover"===this._config.pause&&c.default(this._element).on("mouseenter.bs.carousel",function(e){return t.pause(e)}).on("mouseleave.bs.carousel",function(e){return t.cycle(e)}),this._config.touch&&this._addTouchEventListeners()},e._addTouchEventListeners=function(){var e,t,i=this;this._touchSupported&&(e=function(e){i._pointerEvent&&oe[e.originalEvent.pointerType.toUpperCase()]?i.touchStartX=e.originalEvent.clientX:i._pointerEvent||(i.touchStartX=e.originalEvent.touches[0].clientX)},t=function(e){i._pointerEvent&&oe[e.originalEvent.pointerType.toUpperCase()]&&(i.touchDeltaX=e.originalEvent.clientX-i.touchStartX),i._handleSwipe(),"hover"===i._config.pause&&(i.pause(),i.touchTimeout&&clearTimeout(i.touchTimeout),i.touchTimeout=setTimeout(function(e){return i.cycle(e)},500+i._config.interval))},c.default(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel",function(e){return e.preventDefault()}),this._pointerEvent?(c.default(this._element).on("pointerdown.bs.carousel",e),c.default(this._element).on("pointerup.bs.carousel",t),this._element.classList.add("pointer-event")):(c.default(this._element).on("touchstart.bs.carousel",e),c.default(this._element).on("touchmove.bs.carousel",function(e){e=e,i.touchDeltaX=e.originalEvent.touches&&1<e.originalEvent.touches.length?0:e.originalEvent.touches[0].clientX-i.touchStartX}),c.default(this._element).on("touchend.bs.carousel",t)))},e._keydown=function(e){if(!/input|textarea/i.test(e.target.tagName))switch(e.which){case 37:e.preventDefault(),this.prev();break;case 39:e.preventDefault(),this.next()}},e._getItemIndex=function(e){return this._items=e&&e.parentNode?[].slice.call(e.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(e)},e._getItemByDirection=function(e,t){var i=e===Z,n=e===J,o=this._getItemIndex(t),s=this._items.length-1;return(n&&0===o||i&&o===s)&&!this._config.wrap?t:-1==(n=(o+(e===J?-1:1))%this._items.length)?this._items[this._items.length-1]:this._items[n]},e._triggerSlideEvent=function(e,t){var i=this._getItemIndex(e),n=this._getItemIndex(this._element.querySelector(te)),e=c.default.Event("slide.bs.carousel",{relatedTarget:e,direction:t,from:n,to:i});return c.default(this._element).trigger(e),e},e._setActiveIndicatorElement=function(e){var t;this._indicatorsElement&&(t=[].slice.call(this._indicatorsElement.querySelectorAll(".active")),c.default(t).removeClass(h),t=this._indicatorsElement.children[this._getItemIndex(e)])&&c.default(t).addClass(h)},e._updateInterval=function(){var e=this._activeElement||this._element.querySelector(te);e&&((e=parseInt(e.getAttribute("data-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=e):this._config.interval=this._config.defaultInterval||this._config.interval)},e._slide=function(e,t){var i,n,o,s=this,r=this._element.querySelector(te),l=this._getItemIndex(r),a=t||r&&this._getItemByDirection(e,r),t=this._getItemIndex(a),d=Boolean(this._interval),e=e===Z?(i="carousel-item-left",n="carousel-item-next","left"):(i="carousel-item-right",n="carousel-item-prev","right");a&&c.default(a).hasClass(h)?this._isSliding=!1:this._triggerSlideEvent(a,e).isDefaultPrevented()||r&&a&&(this._isSliding=!0,d&&this.pause(),this._setActiveIndicatorElement(a),this._activeElement=a,o=c.default.Event(ee,{relatedTarget:a,direction:e,from:l,to:t}),c.default(this._element).hasClass("slide")?(c.default(a).addClass(n),u.reflow(a),c.default(r).addClass(i),c.default(a).addClass(i),e=u.getTransitionDurationFromElement(r),c.default(r).one(u.TRANSITION_END,function(){c.default(a).removeClass(i+" "+n).addClass(h),c.default(r).removeClass(h+" "+n+" "+i),s._isSliding=!1,setTimeout(function(){return c.default(s._element).trigger(o)},0)}).emulateTransitionEnd(e)):(c.default(r).removeClass(h),c.default(a).addClass(h),this._isSliding=!1,c.default(this._element).trigger(o)),d)&&this.cycle()},o._jQueryInterface=function(n){return this.each(function(){var e=c.default(this).data(Q),t=l({},ie,c.default(this).data()),i=("object"===_typeof(n)&&(t=l({},t,n)),"string"==typeof n?n:t.slide);if(e||(e=new o(this,t),c.default(this).data(Q,e)),"number"==typeof n)e.to(n);else if("string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}else t.interval&&t.ride&&(e.pause(),e.cycle())})},o._dataApiClickHandler=function(e){var t,i,n=u.getSelectorFromElement(this);n&&(n=c.default(n)[0])&&c.default(n).hasClass("carousel")&&(t=l({},c.default(n).data(),c.default(this).data()),(i=this.getAttribute("data-slide-to"))&&(t.interval=!1),o._jQueryInterface.call(c.default(n),t),i&&c.default(n).data(Q).to(i),e.preventDefault())},r(o,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return ie}}]),o})(),d=(c.default(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",o._dataApiClickHandler),c.default(window).on("load.bs.carousel.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),t=0,i=e.length;t<i;t++){var n=c.default(e[t]);o._jQueryInterface.call(n,n.data())}}),c.default.fn[n]=o._jQueryInterface,c.default.fn[n].Constructor=o,c.default.fn[n].noConflict=function(){return c.default.fn[n]=K,o._jQueryInterface},"collapse"),p="bs.collapse",se=c.default.fn[d],f="show",re="collapse",le="collapsing",ae="collapsed",de='[data-toggle="collapse"]',ce={toggle:!0,parent:""},ue={toggle:"boolean",parent:"(string|element)"},he=(()=>{function s(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+t.id+'"],[data-toggle="collapse"][data-target="#'+t.id+'"]'));for(var i=[].slice.call(document.querySelectorAll(de)),n=0,o=i.length;n<o;n++){var s=i[n],r=u.getSelectorFromElement(s),l=[].slice.call(document.querySelectorAll(r)).filter(function(e){return e===t});null!==r&&0<l.length&&(this._selector=r,this._triggerArray.push(s))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var e=s.prototype;return e.toggle=function(){c.default(this._element).hasClass(f)?this.hide():this.show()},e.show=function(){var e,t,i,n,o=this;this._isTransitioning||c.default(this._element).hasClass(f)||(e=this._parent&&0===(e=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(e){return"string"==typeof o._config.parent?e.getAttribute("data-parent")===o._config.parent:e.classList.contains(re)})).length?null:e)&&(n=c.default(e).not(this._selector).data(p))&&n._isTransitioning||(i=c.default.Event("show.bs.collapse"),c.default(this._element).trigger(i),i.isDefaultPrevented())||(e&&(s._jQueryInterface.call(c.default(e).not(this._selector),"hide"),n||c.default(e).data(p,null)),t=this._getDimension(),c.default(this._element).removeClass(re).addClass(le),this._element.style[t]=0,this._triggerArray.length&&c.default(this._triggerArray).removeClass(ae).attr("aria-expanded",!0),this.setTransitioning(!0),i="scroll"+(t[0].toUpperCase()+t.slice(1)),n=u.getTransitionDurationFromElement(this._element),c.default(this._element).one(u.TRANSITION_END,function(){c.default(o._element).removeClass(le).addClass(re+" "+f),o._element.style[t]="",o.setTransitioning(!1),c.default(o._element).trigger("shown.bs.collapse")}).emulateTransitionEnd(n),this._element.style[t]=this._element[i]+"px")},e.hide=function(){var e=this;if(!this._isTransitioning&&c.default(this._element).hasClass(f)){var t=c.default.Event("hide.bs.collapse");if(c.default(this._element).trigger(t),!t.isDefaultPrevented()){var t=this._getDimension(),i=(this._element.style[t]=this._element.getBoundingClientRect()[t]+"px",u.reflow(this._element),c.default(this._element).addClass(le).removeClass(re+" "+f),this._triggerArray.length);if(0<i)for(var n=0;n<i;n++){var o=this._triggerArray[n],s=u.getSelectorFromElement(o);null===s||c.default([].slice.call(document.querySelectorAll(s))).hasClass(f)||c.default(o).addClass(ae).attr("aria-expanded",!1)}this.setTransitioning(!0);this._element.style[t]="";t=u.getTransitionDurationFromElement(this._element);c.default(this._element).one(u.TRANSITION_END,function(){e.setTransitioning(!1),c.default(e._element).removeClass(le).addClass(re).trigger("hidden.bs.collapse")}).emulateTransitionEnd(t)}}},e.setTransitioning=function(e){this._isTransitioning=e},e.dispose=function(){c.default.removeData(this._element,p),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},e._getConfig=function(e){return(e=l({},ce,e)).toggle=Boolean(e.toggle),u.typeCheckConfig(d,e,ue),e},e._getDimension=function(){return c.default(this._element).hasClass("width")?"width":"height"},e._getParent=function(){var e,i=this,t=(u.isElement(this._config.parent)?(e=this._config.parent,void 0!==this._config.parent.jquery&&(e=this._config.parent[0])):e=document.querySelector(this._config.parent),'[data-toggle="collapse"][data-parent="'+this._config.parent+'"]'),t=[].slice.call(e.querySelectorAll(t));return c.default(t).each(function(e,t){i._addAriaAndCollapsedClass(s._getTargetFromElement(t),[t])}),e},e._addAriaAndCollapsedClass=function(e,t){e=c.default(e).hasClass(f);t.length&&c.default(t).toggleClass(ae,!e).attr("aria-expanded",e)},s._getTargetFromElement=function(e){e=u.getSelectorFromElement(e);return e?document.querySelector(e):null},s._jQueryInterface=function(n){return this.each(function(){var e=c.default(this),t=e.data(p),i=l({},ce,e.data(),"object"===_typeof(n)&&n?n:{});if(!t&&i.toggle&&"string"==typeof n&&/show|hide/.test(n)&&(i.toggle=!1),t||(t=new s(this,i),e.data(p,t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},r(s,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return ce}}]),s})(),pe=(c.default(document).on("click.bs.collapse.data-api",de,function(e){"A"===e.currentTarget.tagName&&e.preventDefault();var i=c.default(this),e=u.getSelectorFromElement(this),e=[].slice.call(document.querySelectorAll(e));c.default(e).each(function(){var e=c.default(this),t=e.data(p)?"toggle":i.data();he._jQueryInterface.call(e,t)})}),c.default.fn[d]=he._jQueryInterface,c.default.fn[d].Constructor=he,c.default.fn[d].noConflict=function(){return c.default.fn[d]=se,he._jQueryInterface},"undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator),fe=(()=>{for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(pe&&0<=navigator.userAgent.indexOf(e[t]))return 1;return 0})();var ge=pe&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},fe))}};function me(e){return e&&"[object Function]"==={}.toString.call(e)}function g(e,t){return 1!==e.nodeType?[]:(e=e.ownerDocument.defaultView.getComputedStyle(e,null),t?e[t]:e)}function ve(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function ye(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=g(e),i=t.overflow;return/(auto|scroll|overlay)/.test(i+t.overflowY+t.overflowX)?e:ye(ve(e))}function be(e){return e&&e.referenceNode?e.referenceNode:e}var we=pe&&!(!window.MSInputMethodContext||!document.documentMode),_e=pe&&/MSIE 10/.test(navigator.userAgent);function m(e){return 11===e?we:10!==e&&we||_e}function v(e){if(!e)return document.documentElement;for(var t=m(10)?document.body:null,i=e.offsetParent||null;i===t&&e.nextElementSibling;)i=(e=e.nextElementSibling).offsetParent;var n=i&&i.nodeName;return n&&"BODY"!==n&&"HTML"!==n?-1!==["TH","TD","TABLE"].indexOf(i.nodeName)&&"static"===g(i,"position")?v(i):i:(e?e.ownerDocument:document).documentElement}function Te(e){return null!==e.parentNode?Te(e.parentNode):e}function Se(e,t){var i,n,o;return e&&e.nodeType&&t&&t.nodeType?(n=(o=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING)?e:t,o=o?t:e,(i=document.createRange()).setStart(n,0),i.setEnd(o,0),e!==(i=i.commonAncestorContainer)&&t!==i||n.contains(o)?"BODY"===(o=(n=i).nodeName)||"HTML"!==o&&v(n.firstElementChild)!==n?v(i):i:(o=Te(e)).host?Se(o.host,t):Se(e,Te(t).host)):document.documentElement}function y(e,t){var t="top"===(1<arguments.length&&void 0!==t?t:"top")?"scrollTop":"scrollLeft",i=e.nodeName;return("BODY"===i||"HTML"===i?(i=e.ownerDocument.documentElement,e.ownerDocument.scrollingElement||i):e)[t]}function ke(e,t){var t="x"===t?"Left":"Top",i="Left"==t?"Right":"Bottom";return parseFloat(e["border"+t+"Width"])+parseFloat(e["border"+i+"Width"])}function Ce(e,t,i,n){return Math.max(t["offset"+e],t["scroll"+e],i["client"+e],i["offset"+e],i["scroll"+e],m(10)?parseInt(i["offset"+e])+parseInt(n["margin"+("Height"===e?"Top":"Left")])+parseInt(n["margin"+("Height"===e?"Bottom":"Right")]):0)}function xe(e){var t=e.body,e=e.documentElement,i=m(10)&&getComputedStyle(e);return{height:Ce("Height",t,e,i),width:Ce("Width",t,e,i)}}t=function(e,t,i){return t&&Ee(e.prototype,t),i&&Ee(e,i),e};function Ee(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function b(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}var w=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i,n=arguments[t];for(i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e};function _(e){return w({},e,{right:e.left+e.width,bottom:e.top+e.height})}function $e(e){var t={};try{m(10)?(t=e.getBoundingClientRect(),i=y(e,"top"),n=y(e,"left"),t.top+=i,t.left+=n,t.bottom+=i,t.right+=n):t=e.getBoundingClientRect()}catch(e){}var i={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},n="HTML"===e.nodeName?xe(e.ownerDocument):{},t=n.width||e.clientWidth||i.width,n=n.height||e.clientHeight||i.height,t=e.offsetWidth-t,n=e.offsetHeight-n;return(t||n)&&(t-=ke(e=g(e),"x"),n-=ke(e,"y"),i.width-=t,i.height-=n),_(i)}function Ae(e,t,i){var i=2<arguments.length&&void 0!==i&&i,n=m(10),o="HTML"===t.nodeName,s=$e(e),r=$e(t),e=ye(e),l=g(t),a=parseFloat(l.borderTopWidth),d=parseFloat(l.borderLeftWidth),r=(i&&o&&(r.top=Math.max(r.top,0),r.left=Math.max(r.left,0)),_({top:s.top-r.top-a,left:s.left-r.left-d,width:s.width,height:s.height}));return r.marginTop=0,r.marginLeft=0,!n&&o&&(s=parseFloat(l.marginTop),o=parseFloat(l.marginLeft),r.top-=a-s,r.bottom-=a-s,r.left-=d-o,r.right-=d-o,r.marginTop=s,r.marginLeft=o),r=(n&&!i?t.contains(e):t===e&&"BODY"!==e.nodeName)?function(e,t,i){var i=2<arguments.length&&void 0!==i&&i,n=y(t,"top"),t=y(t,"left"),i=i?-1:1;return e.top+=n*i,e.bottom+=n*i,e.left+=t*i,e.right+=t*i,e}(r,t):r}function Oe(e){if(!e||!e.parentElement||m())return document.documentElement;for(var t=e.parentElement;t&&"none"===g(t,"transform");)t=t.parentElement;return t||document.documentElement}function Ie(e,t,i,n,o){var s,o=4<arguments.length&&void 0!==o&&o,r={top:0,left:0},l=o?Oe(e):Se(e,be(t)),l=("viewport"===n?r=function(e,t){var t=1<arguments.length&&void 0!==t&&t,i=e.ownerDocument.documentElement,e=Ae(e,i),n=Math.max(i.clientWidth,window.innerWidth||0),o=Math.max(i.clientHeight,window.innerHeight||0),s=t?0:y(i),t=t?0:y(i,"left");return _({top:s-e.top+e.marginTop,left:t-e.left+e.marginLeft,width:n,height:o})}(l,o):(s=void 0,"scrollParent"===n?"BODY"===(s=ye(ve(t))).nodeName&&(s=e.ownerDocument.documentElement):s="window"===n?e.ownerDocument.documentElement:n,t=Ae(s,l,o),"HTML"!==s.nodeName||function e(t){var i=t.nodeName;return"BODY"!==i&&"HTML"!==i&&("fixed"===g(t,"position")||!!(i=ve(t))&&e(i))}(l)?r=t:(o=(n=xe(e.ownerDocument)).height,s=n.width,r.top+=t.top-t.marginTop,r.bottom=o+t.top,r.left+=t.left-t.marginLeft,r.right=s+t.left)),"number"==typeof(i=i||0));return r.left+=l?i:i.left||0,r.top+=l?i:i.top||0,r.right-=l?i:i.right||0,r.bottom-=l?i:i.bottom||0,r}function Ne(e,t,i,n,o,s){var r,s=5<arguments.length&&void 0!==s?s:0;return-1===e.indexOf("auto")?e:(n=Ie(i,n,s,o),r={top:{width:n.width,height:t.top-n.top},right:{width:n.right-t.right,height:n.height},bottom:{width:n.width,height:n.bottom-t.bottom},left:{width:t.left-n.left,height:n.height}},(0<(o=(s=Object.keys(r).map(function(e){return w({key:e},r[e],{area:(e=r[e]).width*e.height})}).sort(function(e,t){return t.area-e.area})).filter(function(e){return e.width>=i.clientWidth&&e.height>=i.clientHeight})).length?o:s)[0].key+((t=e.split("-")[1])?"-"+t:""))}function De(e,t,i,n){n=3<arguments.length&&void 0!==n?n:null;return Ae(i,n?Oe(t):Se(t,be(i)),n)}function Pe(e){var t=e.ownerDocument.defaultView.getComputedStyle(e),i=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),t=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:e.offsetWidth+t,height:e.offsetHeight+i}}function Le(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function Me(e,t,i){i=i.split("-")[0];var e=Pe(e),n={width:e.width,height:e.height},o=-1!==["right","left"].indexOf(i),s=o?"top":"left",r=o?"left":"top",l=o?"height":"width",o=o?"width":"height";return n[s]=t[s]+t[l]/2-e[l]/2,n[r]=i===r?t[r]-e[o]:t[Le(r)],n}function He(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function je(e,i,t){var n,o;return(void 0===t?e:e.slice(0,(e=e,n="name",o=t,Array.prototype.findIndex?e.findIndex(function(e){return e[n]===o}):(t=He(e,function(e){return e[n]===o}),e.indexOf(t))))).forEach(function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var t=e.function||e.fn;e.enabled&&me(t)&&(i.offsets.popper=_(i.offsets.popper),i.offsets.reference=_(i.offsets.reference),i=t(i,e))}),i}function ze(e,i){return e.some(function(e){var t=e.name;return e.enabled&&t===i})}function qe(e){for(var t=[!1,"ms","Webkit","Moz","O"],i=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<t.length;n++){var o=t[n],o=o?""+o+i:e;if(void 0!==document.body.style[o])return o}return null}function Fe(e){e=e.ownerDocument;return e?e.defaultView:window}function We(e,t,i,n){i.updateBound=n,Fe(e).addEventListener("resize",i.updateBound,{passive:!0});n=ye(e);return function e(t,i,n,o){var s="BODY"===t.nodeName,t=s?t.ownerDocument.defaultView:t;t.addEventListener(i,n,{passive:!0}),s||e(ye(t.parentNode),i,n,o),o.push(t)}(n,"scroll",i.updateBound,i.scrollParents),i.scrollElement=n,i.eventsEnabled=!0,i}function Be(){var e,t;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(e=this.reference,t=this.state,Fe(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t))}function Re(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function Xe(i,n){Object.keys(n).forEach(function(e){var t="";-1!==["width","height","top","right","bottom","left"].indexOf(e)&&Re(n[e])&&(t="px"),i.style[e]=n[e]+t})}function Ye(e,t){function i(e){return e}var n=e.offsets,o=n.popper,s=Math.round,r=Math.floor,n=s(n.reference.width),l=s(o.width),a=-1!==["left","right"].indexOf(e.placement),e=-1!==e.placement.indexOf("-"),a=t?a||e||n%2==l%2?s:r:i,r=t?s:i;return{left:a(n%2==1&&l%2==1&&!e&&t?o.left-1:o.left),top:r(o.top),bottom:r(o.bottom),right:a(o.right)}}var Ue=pe&&/Firefox/i.test(navigator.userAgent);function Ve(e,t,i){var n,o=He(e,function(e){return e.name===t}),e=!!o&&e.some(function(e){return e.name===i&&e.enabled&&e.order<o.order});return e||(n="`"+t+"`",console.warn("`"+i+"`"+" modifier is required by "+n+" modifier in order to work, be sure to include it before "+n+"!")),e}var Qe=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Ge=Qe.slice(3);function Ke(e,t){t=1<arguments.length&&void 0!==t&&t,e=Ge.indexOf(e),e=Ge.slice(e+1).concat(Ge.slice(0,e));return t?e.reverse():e}var Ze="flip",Je="clockwise",et="counterclockwise";function tt(e,l,a,t){var o=[0,0],n=-1!==["right","left"].indexOf(t),t=e.split(/(\+|\-)/).map(function(e){return e.trim()}),e=t.indexOf(He(t,function(e){return-1!==e.search(/,|\s/)})),i=(t[e]&&-1===t[e].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead."),/\s*,\s*|\s+/);return(-1!==e?[t.slice(0,e).concat([t[e].split(i)[0]]),[t[e].split(i)[1]].concat(t.slice(e+1))]:[t]).map(function(e,t){var r=(1===t?!n:n)?"height":"width",i=!1;return e.reduce(function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,i=!0,e):i?(e[e.length-1]+=t,i=!1,e):e.concat(t)},[]).map(function(e){return t=r,i=l,n=a,o=+(s=(e=e).match(/((?:\-|\+)?\d*\.?\d*)(.*)/))[1],s=s[2],o?0===s.indexOf("%")?_("%p"===s?i:n)[t]/100*o:"vh"===s||"vw"===s?("vh"===s?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*o:o:e;var t,i,n,o,s})}).forEach(function(i,n){i.forEach(function(e,t){Re(e)&&(o[n]+=e*("-"===i[t-1]?-1:1))})}),o}var it={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t,i,n,o=e.placement,s=o.split("-")[0];return(o=o.split("-")[1])&&(t=(i=e.offsets).reference,i=i.popper,n=(s=-1!==["bottom","top"].indexOf(s))?"width":"height",s={start:b({},s=s?"left":"top",t[s]),end:b({},s,t[s]+t[n]-i[n])},e.offsets.popper=w({},i,s[o])),e}},offset:{order:200,enabled:!0,fn:function(e,t){var t=t.offset,i=e.placement,n=(o=e.offsets).popper,o=o.reference,i=i.split("-")[0],s=void 0,s=Re(+t)?[+t,0]:tt(t,n,o,i);return"left"===i?(n.top+=s[0],n.left-=s[1]):"right"===i?(n.top+=s[0],n.left+=s[1]):"top"===i?(n.left+=s[0],n.top-=s[1]):"bottom"===i&&(n.left+=s[0],n.top+=s[1]),e.popper=n,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,n){var t=n.boundariesElement||v(e.instance.popper),i=(e.instance.reference===t&&(t=v(t)),qe("transform")),o=e.instance.popper.style,s=o.top,r=o.left,l=o[i],a=(o.top="",o.left="",o[i]="",Ie(e.instance.popper,e.instance.reference,n.padding,t,e.positionFixed)),t=(o.top=s,o.left=r,o[i]=l,n.boundaries=a,n.priority),d=e.offsets.popper,c={primary:function(e){var t=d[e];return d[e]<a[e]&&!n.escapeWithReference&&(t=Math.max(d[e],a[e])),b({},e,t)},secondary:function(e){var t="right"===e?"left":"top",i=d[t];return d[e]>a[e]&&!n.escapeWithReference&&(i=Math.min(d[t],a[e]-("right"===e?d.width:d.height))),b({},t,i)}};return t.forEach(function(e){var t=-1!==["left","top"].indexOf(e)?"primary":"secondary";d=w({},d,c[t](e))}),e.offsets.popper=d,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=(i=e.offsets).popper,i=i.reference,n=e.placement.split("-")[0],o=Math.floor,s=(n=-1!==["top","bottom"].indexOf(n))?"right":"bottom",r=n?"left":"top",n=n?"width":"height";return t[s]<o(i[r])&&(e.offsets.popper[r]=o(i[r])-t[n]),t[r]>o(i[s])&&(e.offsets.popper[r]=o(i[s])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){if(Ve(e.instance.modifiers,"arrow","keepTogether")){t=t.element;if("string"==typeof t){if(!(t=e.instance.popper.querySelector(t)))return e}else if(!e.instance.popper.contains(t))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var i=e.placement.split("-")[0],n=e.offsets,o=n.popper,n=n.reference,i=-1!==["left","right"].indexOf(i),s=i?"height":"width",r=i?"Top":"Left",l=r.toLowerCase(),a=i?"left":"top",i=i?"bottom":"right",d=Pe(t)[s],i=(n[i]-d<o[l]&&(e.offsets.popper[l]-=o[l]-(n[i]-d)),n[l]+d>o[i]&&(e.offsets.popper[l]+=n[l]+d-o[i]),e.offsets.popper=_(e.offsets.popper),n[l]+n[s]/2-d/2),n=g(e.instance.popper),c=parseFloat(n["margin"+r]),n=parseFloat(n["border"+r+"Width"]),r=i-e.offsets.popper[l]-c-n,r=Math.max(Math.min(o[s]-d,r),0);e.arrowElement=t,e.offsets.arrow=(b(i={},l,Math.round(r)),b(i,a,""),i)}return e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(a,d){if(!(ze(a.instance.modifiers,"inner")||a.flipped&&a.placement===a.originalPlacement)){var c=Ie(a.instance.popper,a.instance.reference,d.padding,d.boundariesElement,a.positionFixed),u=a.placement.split("-")[0],h=Le(u),p=a.placement.split("-")[1]||"",f=[];switch(d.behavior){case Ze:f=[u,h];break;case Je:f=Ke(u);break;case et:f=Ke(u,!0);break;default:f=d.behavior}f.forEach(function(e,t){if(u!==e||f.length===t+1)return a;u=a.placement.split("-")[0],h=Le(u);var e=a.offsets.popper,i=a.offsets.reference,n=Math.floor,i="left"===u&&n(e.right)>n(i.left)||"right"===u&&n(e.left)<n(i.right)||"top"===u&&n(e.bottom)>n(i.top)||"bottom"===u&&n(e.top)<n(i.bottom),o=n(e.left)<n(c.left),s=n(e.right)>n(c.right),r=n(e.top)<n(c.top),e=n(e.bottom)>n(c.bottom),n="left"===u&&o||"right"===u&&s||"top"===u&&r||"bottom"===u&&e,l=-1!==["top","bottom"].indexOf(u),s=!!d.flipVariations&&(l&&"start"===p&&o||l&&"end"===p&&s||!l&&"start"===p&&r||!l&&"end"===p&&e)||!!d.flipVariationsByContent&&(l&&"start"===p&&s||l&&"end"===p&&o||!l&&"start"===p&&e||!l&&"end"===p&&r);(i||n||s)&&(a.flipped=!0,(i||n)&&(u=f[t+1]),s&&(p="end"===(o=p)?"start":"start"===o?"end":o),a.placement=u+(p?"-"+p:""),a.offsets.popper=w({},a.offsets.popper,Me(a.instance.popper,a.offsets.reference,a.placement)),a=je(a.instance.modifiers,a,"flip"))})}return a},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,i=t.split("-")[0],n=(o=e.offsets).popper,o=o.reference,s=-1!==["left","right"].indexOf(i),r=-1===["top","left"].indexOf(i);return n[s?"left":"top"]=o[i]-(r?n[s?"width":"height"]:0),e.placement=Le(t),e.offsets.popper=_(n),e}},hide:{order:800,enabled:!0,fn:function(e){if(Ve(e.instance.modifiers,"hide","preventOverflow")){var t=e.offsets.reference,i=He(e.instance.modifiers,function(e){return"preventOverflow"===e.name}).boundaries;if(t.bottom<i.top||t.left>i.right||t.top>i.bottom||t.right<i.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var i=t.x,n=t.y,o=e.offsets.popper,s=(void 0!==(s=He(e.instance.modifiers,function(e){return"applyStyle"===e.name}).gpuAcceleration)&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!"),void 0!==s?s:t.gpuAcceleration),r=$e(t=v(e.instance.popper)),o={position:o.position},l=Ye(e,window.devicePixelRatio<2||!Ue),i="bottom"===i?"top":"bottom",n="right"===n?"left":"right",a=qe("transform"),d=void 0,c=void 0,c="bottom"==i?"HTML"===t.nodeName?-t.clientHeight+l.bottom:-r.height+l.bottom:l.top,d="right"==n?"HTML"===t.nodeName?-t.clientWidth+l.right:-r.width+l.right:l.left;return s&&a?(o[a]="translate3d("+d+"px, "+c+"px, 0)",o[i]=0,o[n]=0,o.willChange="transform"):(t="right"==n?-1:1,o[i]=c*("bottom"==i?-1:1),o[n]=d*t,o.willChange=i+", "+n),e.attributes=w({},{"x-placement":e.placement},e.attributes),e.styles=w({},o,e.styles),e.arrowStyles=w({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){var t,i;return Xe(e.instance.popper,e.styles),t=e.instance.popper,i=e.attributes,Object.keys(i).forEach(function(e){!1!==i[e]?t.setAttribute(e,i[e]):t.removeAttribute(e)}),e.arrowElement&&Object.keys(e.arrowStyles).length&&Xe(e.arrowElement,e.arrowStyles),e},onLoad:function(e,t,i,n,o){return o=De(o,t,e,i.positionFixed),o=Ne(i.placement,o,t,e,i.modifiers.flip.boundariesElement,i.modifiers.flip.padding),t.setAttribute("x-placement",o),Xe(t,{position:i.positionFixed?"fixed":"absolute"}),i},gpuAcceleration:void 0}}},t=(t(T,[{key:"update",value:function(){return function(){var e;this.state.isDestroyed||((e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}}).offsets.reference=De(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=Ne(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=Me(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=je(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e)))}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,ze(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[qe("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=We(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return Be.call(this)}}]),T);function T(e,t){var i=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},o=this,s=T;if(!(o instanceof s))throw new TypeError("Cannot call a class as a function");this.scheduleUpdate=function(){return requestAnimationFrame(i.update)},this.update=ge(this.update.bind(this)),this.options=w({},T.Defaults,n),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e&&e.jquery?e[0]:e,this.popper=t&&t.jquery?t[0]:t,this.options.modifiers={},Object.keys(w({},T.Defaults.modifiers,n.modifiers)).forEach(function(e){i.options.modifiers[e]=w({},T.Defaults.modifiers[e]||{},n.modifiers?n.modifiers[e]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return w({name:e},i.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&me(e.onLoad)&&e.onLoad(i.reference,i.popper,i.options,e,i.state)}),this.update();o=this.options.eventsEnabled;o&&this.enableEventListeners(),this.state.eventsEnabled=o}t.Utils=("undefined"!=typeof window?window:global).PopperUtils,t.placements=Qe,t.Defaults=it;var nt=t,s="dropdown",ot="bs.dropdown",st="."+ot,Qe=".data-api",rt=c.default.fn[s],lt=new RegExp("38|40|27"),at="disabled",S="show",dt="dropdown-menu-right",ct="hide"+st,ut="hidden"+st,it="click"+st+Qe,t="keydown"+st+Qe,ht='[data-toggle="dropdown"]',pt=".dropdown-menu",ft={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},gt={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},k=(()=>{function d(e,t){this._element=e,this._popper=null,this._config=this._getConfig(t),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var e=d.prototype;return e.toggle=function(){var e;this._element.disabled||c.default(this._element).hasClass(at)||(e=c.default(this._menu).hasClass(S),d._clearMenus(),e)||this.show(!0)},e.show=function(e){if(void 0===e&&(e=!1),!(this._element.disabled||c.default(this._element).hasClass(at)||c.default(this._menu).hasClass(S))){var t={relatedTarget:this._element},i=c.default.Event("show.bs.dropdown",t),n=d._getParentFromElement(this._element);if(c.default(n).trigger(i),!i.isDefaultPrevented()){if(!this._inNavbar&&e){if(void 0===nt)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");i=this._element;"parent"===this._config.reference?i=n:u.isElement(this._config.reference)&&(i=this._config.reference,void 0!==this._config.reference.jquery)&&(i=this._config.reference[0]),"scrollParent"!==this._config.boundary&&c.default(n).addClass("position-static"),this._popper=new nt(i,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===c.default(n).closest(".navbar-nav").length&&c.default(document.body).children().on("mouseover",null,c.default.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),c.default(this._menu).toggleClass(S),c.default(n).toggleClass(S).trigger(c.default.Event("shown.bs.dropdown",t))}}},e.hide=function(){var e,t,i;this._element.disabled||c.default(this._element).hasClass(at)||!c.default(this._menu).hasClass(S)||(e={relatedTarget:this._element},t=c.default.Event(ct,e),i=d._getParentFromElement(this._element),c.default(i).trigger(t),t.isDefaultPrevented())||(this._popper&&this._popper.destroy(),c.default(this._menu).toggleClass(S),c.default(i).toggleClass(S).trigger(c.default.Event(ut,e)))},e.dispose=function(){c.default.removeData(this._element,ot),c.default(this._element).off(st),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},e.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},e._addEventListeners=function(){var t=this;c.default(this._element).on("click.bs.dropdown",function(e){e.preventDefault(),e.stopPropagation(),t.toggle()})},e._getConfig=function(e){return e=l({},this.constructor.Default,c.default(this._element).data(),e),u.typeCheckConfig(s,e,this.constructor.DefaultType),e},e._getMenuElement=function(){var e;return this._menu||(e=d._getParentFromElement(this._element))&&(this._menu=e.querySelector(pt)),this._menu},e._getPlacement=function(){var e=c.default(this._element.parentNode),t="bottom-start";return e.hasClass("dropup")?t=c.default(this._menu).hasClass(dt)?"top-end":"top-start":e.hasClass("dropright")?t="right-start":e.hasClass("dropleft")?t="left-start":c.default(this._menu).hasClass(dt)&&(t="bottom-end"),t},e._detectNavbar=function(){return 0<c.default(this._element).closest(".navbar").length},e._getOffset=function(){var t=this,e={};return"function"==typeof this._config.offset?e.fn=function(e){return e.offsets=l({},e.offsets,t._config.offset(e.offsets,t._element)),e}:e.offset=this._config.offset,e},e._getPopperConfig=function(){var e={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(e.modifiers.applyStyle={enabled:!1}),l({},e,this._config.popperConfig)},d._jQueryInterface=function(i){return this.each(function(){var e=c.default(this).data(ot),t="object"===_typeof(i)?i:null;if(e||(e=new d(this,t),c.default(this).data(ot,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}})},d._clearMenus=function(e){if(!e||3!==e.which&&("keyup"!==e.type||9===e.which))for(var t=[].slice.call(document.querySelectorAll(ht)),i=0,n=t.length;i<n;i++){var o,s,r=d._getParentFromElement(t[i]),l=c.default(t[i]).data(ot),a={relatedTarget:t[i]};e&&"click"===e.type&&(a.clickEvent=e),l&&(o=l._menu,!c.default(r).hasClass(S)||e&&("click"===e.type&&/input|textarea/i.test(e.target.tagName)||"keyup"===e.type&&9===e.which)&&c.default.contains(r,e.target)||(s=c.default.Event(ct,a),c.default(r).trigger(s),s.isDefaultPrevented())||("ontouchstart"in document.documentElement&&c.default(document.body).children().off("mouseover",null,c.default.noop),t[i].setAttribute("aria-expanded","false"),l._popper&&l._popper.destroy(),c.default(o).removeClass(S),c.default(r).removeClass(S).trigger(c.default.Event(ut,a))))}},d._getParentFromElement=function(e){var t,i=u.getSelectorFromElement(e);return(t=i?document.querySelector(i):t)||e.parentNode},d._dataApiKeydownHandler=function(e){var t,i,n;(/input|textarea/i.test(e.target.tagName)?32===e.which||27!==e.which&&(40!==e.which&&38!==e.which||c.default(e.target).closest(pt).length):!lt.test(e.which))||this.disabled||c.default(this).hasClass(at)||(t=d._getParentFromElement(this),!(i=c.default(t).hasClass(S))&&27===e.which)||(e.preventDefault(),e.stopPropagation(),i&&27!==e.which&&32!==e.which?0!==(i=[].slice.call(t.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(e){return c.default(e).is(":visible")})).length&&(n=i.indexOf(e.target),38===e.which&&0<n&&n--,40===e.which&&n<i.length-1&&n++,i[n=n<0?0:n].focus()):(27===e.which&&c.default(t.querySelector(ht)).trigger("focus"),c.default(this).trigger("click")))},r(d,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return ft}},{key:"DefaultType",get:function(){return gt}}]),d})(),C=(c.default(document).on(t,ht,k._dataApiKeydownHandler).on(t,pt,k._dataApiKeydownHandler).on(it+" keyup.bs.dropdown.data-api",k._clearMenus).on(it,ht,function(e){e.preventDefault(),e.stopPropagation(),k._jQueryInterface.call(c.default(this),"toggle")}).on(it,".dropdown form",function(e){e.stopPropagation()}),c.default.fn[s]=k._jQueryInterface,c.default.fn[s].Constructor=k,c.default.fn[s].noConflict=function(){return c.default.fn[s]=rt,k._jQueryInterface},"modal"),mt="bs.modal",x="."+mt,vt=c.default.fn[C],yt="modal-open",E="fade",bt="show",wt="modal-static",_t="hidden"+x,Tt="show"+x,St="focusin"+x,kt="resize"+x,Ct="click.dismiss"+x,xt="keydown.dismiss"+x,Et="mousedown.dismiss"+x,$t=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",At=".sticky-top",Ot={backdrop:!0,keyboard:!0,focus:!0,show:!0},It={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},Nt=(()=>{function o(e,t){this._config=this._getConfig(t),this._element=e,this._dialog=e.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}var e=o.prototype;return e.toggle=function(e){return this._isShown?this.hide():this.show(e)},e.show=function(e){var t,i=this;this._isShown||this._isTransitioning||(t=c.default.Event(Tt,{relatedTarget:e}),c.default(this._element).trigger(t),t.isDefaultPrevented())||(this._isShown=!0,c.default(this._element).hasClass(E)&&(this._isTransitioning=!0),this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),c.default(this._element).on(Ct,'[data-dismiss="modal"]',function(e){return i.hide(e)}),c.default(this._dialog).on(Et,function(){c.default(i._element).one("mouseup.dismiss.bs.modal",function(e){c.default(e.target).is(i._element)&&(i._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return i._showElement(e)}))},e.hide=function(e){var t=this;e&&e.preventDefault(),this._isShown&&!this._isTransitioning&&(e=c.default.Event("hide.bs.modal"),c.default(this._element).trigger(e),this._isShown)&&!e.isDefaultPrevented()&&(this._isShown=!1,(e=c.default(this._element).hasClass(E))&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),c.default(document).off(St),c.default(this._element).removeClass(bt),c.default(this._element).off(Ct),c.default(this._dialog).off(Et),e?(e=u.getTransitionDurationFromElement(this._element),c.default(this._element).one(u.TRANSITION_END,function(e){return t._hideModal(e)}).emulateTransitionEnd(e)):this._hideModal())},e.dispose=function(){[window,this._element,this._dialog].forEach(function(e){return c.default(e).off(x)}),c.default(document).off(St),c.default.removeData(this._element,mt),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},e.handleUpdate=function(){this._adjustDialog()},e._getConfig=function(e){return e=l({},Ot,e),u.typeCheckConfig(C,e,It),e},e._triggerBackdropTransition=function(){var e,t,i=this,n=c.default.Event("hidePrevented.bs.modal");c.default(this._element).trigger(n),n.isDefaultPrevented()||((e=this._element.scrollHeight>document.documentElement.clientHeight)||(this._element.style.overflowY="hidden"),this._element.classList.add(wt),t=u.getTransitionDurationFromElement(this._dialog),c.default(this._element).off(u.TRANSITION_END),c.default(this._element).one(u.TRANSITION_END,function(){i._element.classList.remove(wt),e||c.default(i._element).one(u.TRANSITION_END,function(){i._element.style.overflowY=""}).emulateTransitionEnd(i._element,t)}).emulateTransitionEnd(t),this._element.focus())},e._showElement=function(e){function t(){i._config.focus&&i._element.focus(),i._isTransitioning=!1,c.default(i._element).trigger(s)}var i=this,n=c.default(this._element).hasClass(E),o=this._dialog?this._dialog.querySelector(".modal-body"):null,s=(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),c.default(this._dialog).hasClass("modal-dialog-scrollable")&&o?o.scrollTop=0:this._element.scrollTop=0,n&&u.reflow(this._element),c.default(this._element).addClass(bt),this._config.focus&&this._enforceFocus(),c.default.Event("shown.bs.modal",{relatedTarget:e}));n?(o=u.getTransitionDurationFromElement(this._dialog),c.default(this._dialog).one(u.TRANSITION_END,t).emulateTransitionEnd(o)):t()},e._enforceFocus=function(){var t=this;c.default(document).off(St).on(St,function(e){document!==e.target&&t._element!==e.target&&0===c.default(t._element).has(e.target).length&&t._element.focus()})},e._setEscapeEvent=function(){var t=this;this._isShown?c.default(this._element).on(xt,function(e){t._config.keyboard&&27===e.which?(e.preventDefault(),t.hide()):t._config.keyboard||27!==e.which||t._triggerBackdropTransition()}):(this._isShown,c.default(this._element).off(xt))},e._setResizeEvent=function(){var t=this;this._isShown?c.default(window).on(kt,function(e){return t.handleUpdate(e)}):c.default(window).off(kt)},e._hideModal=function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop(function(){c.default(document.body).removeClass(yt),e._resetAdjustments(),e._resetScrollbar(),c.default(e._element).trigger(_t)})},e._removeBackdrop=function(){this._backdrop&&(c.default(this._backdrop).remove(),this._backdrop=null)},e._showBackdrop=function(e){var t,i=this,n=c.default(this._element).hasClass(E)?E:"";this._isShown&&this._config.backdrop?(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",n&&this._backdrop.classList.add(n),c.default(this._backdrop).appendTo(document.body),c.default(this._element).on(Ct,function(e){i._ignoreBackdropClick?i._ignoreBackdropClick=!1:e.target===e.currentTarget&&("static"===i._config.backdrop?i._triggerBackdropTransition():i.hide())}),n&&u.reflow(this._backdrop),c.default(this._backdrop).addClass(bt),e&&(n?(n=u.getTransitionDurationFromElement(this._backdrop),c.default(this._backdrop).one(u.TRANSITION_END,e).emulateTransitionEnd(n)):e())):!this._isShown&&this._backdrop?(c.default(this._backdrop).removeClass(bt),n=function(){i._removeBackdrop(),e&&e()},c.default(this._element).hasClass(E)?(t=u.getTransitionDurationFromElement(this._backdrop),c.default(this._backdrop).one(u.TRANSITION_END,n).emulateTransitionEnd(t)):n()):e&&e()},e._adjustDialog=function(){var e=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&e&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!e&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},e._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},e._checkScrollbar=function(){var e=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(e.left+e.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},e._setScrollbar=function(){var e,t,o=this;this._isBodyOverflowing&&(e=[].slice.call(document.querySelectorAll($t)),t=[].slice.call(document.querySelectorAll(At)),c.default(e).each(function(e,t){var i=t.style.paddingRight,n=c.default(t).css("padding-right");c.default(t).data("padding-right",i).css("padding-right",parseFloat(n)+o._scrollbarWidth+"px")}),c.default(t).each(function(e,t){var i=t.style.marginRight,n=c.default(t).css("margin-right");c.default(t).data("margin-right",i).css("margin-right",parseFloat(n)-o._scrollbarWidth+"px")}),e=document.body.style.paddingRight,t=c.default(document.body).css("padding-right"),c.default(document.body).data("padding-right",e).css("padding-right",parseFloat(t)+this._scrollbarWidth+"px")),c.default(document.body).addClass(yt)},e._resetScrollbar=function(){var e=[].slice.call(document.querySelectorAll($t)),e=(c.default(e).each(function(e,t){var i=c.default(t).data("padding-right");c.default(t).removeData("padding-right"),t.style.paddingRight=i||""}),[].slice.call(document.querySelectorAll(At))),e=(c.default(e).each(function(e,t){var i=c.default(t).data("margin-right");void 0!==i&&c.default(t).css("margin-right",i).removeData("margin-right")}),c.default(document.body).data("padding-right"));c.default(document.body).removeData("padding-right"),document.body.style.paddingRight=e||""},e._getScrollbarWidth=function(){var e=document.createElement("div"),t=(e.className="modal-scrollbar-measure",document.body.appendChild(e),e.getBoundingClientRect().width-e.clientWidth);return document.body.removeChild(e),t},o._jQueryInterface=function(i,n){return this.each(function(){var e=c.default(this).data(mt),t=l({},Ot,c.default(this).data(),"object"===_typeof(i)&&i?i:{});if(e||(e=new o(this,t),c.default(this).data(mt,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i](n)}else t.show&&e.show(n)})},r(o,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return Ot}}]),o})(),Dt=(c.default(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(e){var t,i=this,n=u.getSelectorFromElement(this),n=(n&&(t=document.querySelector(n)),c.default(t).data(mt)?"toggle":l({},c.default(t).data(),c.default(this).data())),o=("A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault(),c.default(t).one(Tt,function(e){e.isDefaultPrevented()||o.one(_t,function(){c.default(i).is(":visible")&&i.focus()})}));Nt._jQueryInterface.call(c.default(t),n,this)}),c.default.fn[C]=Nt._jQueryInterface,c.default.fn[C].Constructor=Nt,c.default.fn[C].noConflict=function(){return c.default.fn[C]=vt,Nt._jQueryInterface},["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Qe={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Pt=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i,Lt=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function Mt(e,s,t){if(0===e.length)return e;if(t&&"function"==typeof t)return t(e);for(var t=(new window.DOMParser).parseFromString(e,"text/html"),r=Object.keys(s),l=[].slice.call(t.body.querySelectorAll("*")),i=function(e,t){var i=l[e],e=i.nodeName.toLowerCase();if(-1===r.indexOf(i.nodeName.toLowerCase()))return i.parentNode.removeChild(i),"continue";var n=[].slice.call(i.attributes),o=[].concat(s["*"]||[],s[e]||[]);n.forEach(function(e){((e,t)=>{var i=e.nodeName.toLowerCase();if(-1!==t.indexOf(i))return-1===Dt.indexOf(i)||Boolean(Pt.test(e.nodeValue)||Lt.test(e.nodeValue));for(var n=t.filter(function(e){return e instanceof RegExp}),o=0,s=n.length;o<s;o++)if(n[o].test(i))return 1})(e,o)||i.removeAttribute(e.nodeName)})},n=0,o=l.length;n<o;n++)i(n);return t.body.innerHTML}var $="tooltip",Ht="bs.tooltip",A="."+Ht,jt=c.default.fn[$],zt="bs-tooltip",qt=new RegExp("(^|\\s)"+zt+"\\S+","g"),Ft=["sanitize","whiteList","sanitizeFn"],Wt="fade",Bt="show",Rt="show",Xt="hover",Yt="focus",Ut={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},Vt={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",customClass:"",sanitize:!0,sanitizeFn:null,whiteList:Qe,popperConfig:null},Qt={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",customClass:"(string|function)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},Gt={HIDE:"hide"+A,HIDDEN:"hidden"+A,SHOW:"show"+A,SHOWN:"shown"+A,INSERTED:"inserted"+A,CLICK:"click"+A,FOCUSIN:"focusin"+A,FOCUSOUT:"focusout"+A,MOUSEENTER:"mouseenter"+A,MOUSELEAVE:"mouseleave"+A},O=(()=>{function o(e,t){if(void 0===nt)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=e,this.config=this._getConfig(t),this.tip=null,this._setListeners()}var e=o.prototype;return e.enable=function(){this._isEnabled=!0},e.disable=function(){this._isEnabled=!1},e.toggleEnabled=function(){this._isEnabled=!this._isEnabled},e.toggle=function(e){var t,i;this._isEnabled&&(e?(t=this.constructor.DATA_KEY,(i=c.default(e.currentTarget).data(t))||(i=new this.constructor(e.currentTarget,this._getDelegateConfig()),c.default(e.currentTarget).data(t,i)),i._activeTrigger.click=!i._activeTrigger.click,i._isWithActiveTrigger()?i._enter(null,i):i._leave(null,i)):c.default(this.getTipElement()).hasClass(Bt)?this._leave(null,this):this._enter(null,this))},e.dispose=function(){clearTimeout(this._timeout),c.default.removeData(this.element,this.constructor.DATA_KEY),c.default(this.element).off(this.constructor.EVENT_KEY),c.default(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&c.default(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},e.show=function(){var t=this;if("none"===c.default(this.element).css("display"))throw new Error("Please use show on visible elements");var e,i,n=c.default.Event(this.constructor.Event.SHOW);this.isWithContent()&&this._isEnabled&&(c.default(this.element).trigger(n),i=u.findShadowRoot(this.element),i=c.default.contains(null!==i?i:this.element.ownerDocument.documentElement,this.element),!n.isDefaultPrevented())&&i&&(n=this.getTipElement(),i=u.getUID(this.constructor.NAME),n.setAttribute("id",i),this.element.setAttribute("aria-describedby",i),this.setContent(),this.config.animation&&c.default(n).addClass(Wt),i="function"==typeof this.config.placement?this.config.placement.call(this,n,this.element):this.config.placement,i=this._getAttachment(i),this.addAttachmentClass(i),e=this._getContainer(),c.default(n).data(this.constructor.DATA_KEY,this),c.default.contains(this.element.ownerDocument.documentElement,this.tip)||c.default(n).appendTo(e),c.default(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new nt(this.element,n,this._getPopperConfig(i)),c.default(n).addClass(Bt),c.default(n).addClass(this.config.customClass),"ontouchstart"in document.documentElement&&c.default(document.body).children().on("mouseover",null,c.default.noop),e=function(){t.config.animation&&t._fixTransition();var e=t._hoverState;t._hoverState=null,c.default(t.element).trigger(t.constructor.Event.SHOWN),"out"===e&&t._leave(null,t)},c.default(this.tip).hasClass(Wt)?(i=u.getTransitionDurationFromElement(this.tip),c.default(this.tip).one(u.TRANSITION_END,e).emulateTransitionEnd(i)):e())},e.hide=function(e){function t(){i._hoverState!==Rt&&n.parentNode&&n.parentNode.removeChild(n),i._cleanTipClass(),i.element.removeAttribute("aria-describedby"),c.default(i.element).trigger(i.constructor.Event.HIDDEN),null!==i._popper&&i._popper.destroy(),e&&e()}var i=this,n=this.getTipElement(),o=c.default.Event(this.constructor.Event.HIDE);c.default(this.element).trigger(o),o.isDefaultPrevented()||(c.default(n).removeClass(Bt),"ontouchstart"in document.documentElement&&c.default(document.body).children().off("mouseover",null,c.default.noop),this._activeTrigger.click=!1,this._activeTrigger[Yt]=!1,this._activeTrigger[Xt]=!1,c.default(this.tip).hasClass(Wt)?(o=u.getTransitionDurationFromElement(n),c.default(n).one(u.TRANSITION_END,t).emulateTransitionEnd(o)):t(),this._hoverState="")},e.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},e.isWithContent=function(){return Boolean(this.getTitle())},e.addAttachmentClass=function(e){c.default(this.getTipElement()).addClass(zt+"-"+e)},e.getTipElement=function(){return this.tip=this.tip||c.default(this.config.template)[0],this.tip},e.setContent=function(){var e=this.getTipElement();this.setElementContent(c.default(e.querySelectorAll(".tooltip-inner")),this.getTitle()),c.default(e).removeClass(Wt+" "+Bt)},e.setElementContent=function(e,t){"object"===_typeof(t)&&(t.nodeType||t.jquery)?this.config.html?c.default(t).parent().is(e)||e.empty().append(t):e.text(c.default(t).text()):this.config.html?(this.config.sanitize&&(t=Mt(t,this.config.whiteList,this.config.sanitizeFn)),e.html(t)):e.text(t)},e.getTitle=function(){return this.element.getAttribute("data-original-title")||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},e._getPopperConfig=function(e){var t=this;return l({},{placement:e,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(e){e.originalPlacement!==e.placement&&t._handlePopperPlacementChange(e)},onUpdate:function(e){return t._handlePopperPlacementChange(e)}},this.config.popperConfig)},e._getOffset=function(){var t=this,e={};return"function"==typeof this.config.offset?e.fn=function(e){return e.offsets=l({},e.offsets,t.config.offset(e.offsets,t.element)),e}:e.offset=this.config.offset,e},e._getContainer=function(){return!1===this.config.container?document.body:u.isElement(this.config.container)?c.default(this.config.container):c.default(document).find(this.config.container)},e._getAttachment=function(e){return Ut[e.toUpperCase()]},e._setListeners=function(){var i=this;this.config.trigger.split(" ").forEach(function(e){var t;"click"===e?c.default(i.element).on(i.constructor.Event.CLICK,i.config.selector,function(e){return i.toggle(e)}):"manual"!==e&&(t=e===Xt?i.constructor.Event.MOUSEENTER:i.constructor.Event.FOCUSIN,e=e===Xt?i.constructor.Event.MOUSELEAVE:i.constructor.Event.FOCUSOUT,c.default(i.element).on(t,i.config.selector,function(e){return i._enter(e)}).on(e,i.config.selector,function(e){return i._leave(e)}))}),this._hideModalHandler=function(){i.element&&i.hide()},c.default(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=l({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},e._fixTitle=function(){var e=_typeof(this.element.getAttribute("data-original-title"));!this.element.getAttribute("title")&&"string"===e||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},e._enter=function(e,t){var i=this.constructor.DATA_KEY;(t=t||c.default(e.currentTarget).data(i))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),c.default(e.currentTarget).data(i,t)),e&&(t._activeTrigger["focusin"===e.type?Yt:Xt]=!0),c.default(t.getTipElement()).hasClass(Bt)||t._hoverState===Rt?t._hoverState=Rt:(clearTimeout(t._timeout),t._hoverState=Rt,t.config.delay&&t.config.delay.show?t._timeout=setTimeout(function(){t._hoverState===Rt&&t.show()},t.config.delay.show):t.show())},e._leave=function(e,t){var i=this.constructor.DATA_KEY;(t=t||c.default(e.currentTarget).data(i))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),c.default(e.currentTarget).data(i,t)),e&&(t._activeTrigger["focusout"===e.type?Yt:Xt]=!1),t._isWithActiveTrigger()||(clearTimeout(t._timeout),t._hoverState="out",t.config.delay&&t.config.delay.hide?t._timeout=setTimeout(function(){"out"===t._hoverState&&t.hide()},t.config.delay.hide):t.hide())},e._isWithActiveTrigger=function(){for(var e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1},e._getConfig=function(e){var t=c.default(this.element).data();return Object.keys(t).forEach(function(e){-1!==Ft.indexOf(e)&&delete t[e]}),"number"==typeof(e=l({},this.constructor.Default,t,"object"===_typeof(e)&&e?e:{})).delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),u.typeCheckConfig($,e,this.constructor.DefaultType),e.sanitize&&(e.template=Mt(e.template,e.whiteList,e.sanitizeFn)),e},e._getDelegateConfig=function(){var e={};if(this.config)for(var t in this.config)this.constructor.Default[t]!==this.config[t]&&(e[t]=this.config[t]);return e},e._cleanTipClass=function(){var e=c.default(this.getTipElement()),t=e.attr("class").match(qt);null!==t&&t.length&&e.removeClass(t.join(""))},e._handlePopperPlacementChange=function(e){this.tip=e.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(e.placement))},e._fixTransition=function(){var e=this.getTipElement(),t=this.config.animation;null===e.getAttribute("x-placement")&&(c.default(e).removeClass(Wt),this.config.animation=!1,this.hide(),this.show(),this.config.animation=t)},o._jQueryInterface=function(n){return this.each(function(){var e=c.default(this),t=e.data(Ht),i="object"===_typeof(n)&&n;if((t||!/dispose|hide/.test(n))&&(t||(t=new o(this,i),e.data(Ht,t)),"string"==typeof n)){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},r(o,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return Vt}},{key:"NAME",get:function(){return $}},{key:"DATA_KEY",get:function(){return Ht}},{key:"Event",get:function(){return Gt}},{key:"EVENT_KEY",get:function(){return A}},{key:"DefaultType",get:function(){return Qt}}]),o})(),I=(c.default.fn[$]=O._jQueryInterface,c.default.fn[$].Constructor=O,c.default.fn[$].noConflict=function(){return c.default.fn[$]=jt,O._jQueryInterface},"popover"),Kt="bs.popover",N="."+Kt,Zt=c.default.fn[I],Jt="bs-popover",ei=new RegExp("(^|\\s)"+Jt+"\\S+","g"),ti=l({},O.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),ii=l({},O.DefaultType,{content:"(string|element|function)"}),ni={HIDE:"hide"+N,HIDDEN:"hidden"+N,SHOW:"show"+N,SHOWN:"shown"+N,INSERTED:"inserted"+N,CLICK:"click"+N,FOCUSIN:"focusin"+N,FOCUSOUT:"focusout"+N,MOUSEENTER:"mouseenter"+N,MOUSELEAVE:"mouseleave"+N},oi=(e=>{var t;function n(){return e.apply(this,arguments)||this}t=e,(i=n).prototype=Object.create(t.prototype),j(i.prototype.constructor=i,t);var i=n.prototype;return i.isWithContent=function(){return this.getTitle()||this._getContent()},i.addAttachmentClass=function(e){c.default(this.getTipElement()).addClass(Jt+"-"+e)},i.getTipElement=function(){return this.tip=this.tip||c.default(this.config.template)[0],this.tip},i.setContent=function(){var e=c.default(this.getTipElement()),t=(this.setElementContent(e.find(".popover-header"),this.getTitle()),this._getContent());"function"==typeof t&&(t=t.call(this.element)),this.setElementContent(e.find(".popover-body"),t),e.removeClass("fade show")},i._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},i._cleanTipClass=function(){var e=c.default(this.getTipElement()),t=e.attr("class").match(ei);null!==t&&0<t.length&&e.removeClass(t.join(""))},n._jQueryInterface=function(i){return this.each(function(){var e=c.default(this).data(Kt),t="object"===_typeof(i)?i:null;if((e||!/dispose|hide/.test(i))&&(e||(e=new n(this,t),c.default(this).data(Kt,e)),"string"==typeof i)){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}})},r(n,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return ti}},{key:"NAME",get:function(){return I}},{key:"DATA_KEY",get:function(){return Kt}},{key:"Event",get:function(){return ni}},{key:"EVENT_KEY",get:function(){return N}},{key:"DefaultType",get:function(){return ii}}]),n})(O),D=(c.default.fn[I]=oi._jQueryInterface,c.default.fn[I].Constructor=oi,c.default.fn[I].noConflict=function(){return c.default.fn[I]=Zt,oi._jQueryInterface},"scrollspy"),si="bs.scrollspy",ri="."+si,li=c.default.fn[D],P="active",ai="position",di=".nav, .list-group",ci=".nav-link",ui=".list-group-item",hi={offset:10,method:"auto",target:""},pi={offset:"number",method:"string",target:"(string|element)"},fi=(()=>{function n(e,t){var i=this;this._element=e,this._scrollElement="BODY"===e.tagName?window:e,this._config=this._getConfig(t),this._selector=this._config.target+" "+ci+","+this._config.target+" "+ui+","+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,c.default(this._scrollElement).on("scroll.bs.scrollspy",function(e){return i._process(e)}),this.refresh(),this._process()}var e=n.prototype;return e.refresh=function(){var t=this,e=this._scrollElement===this._scrollElement.window?"offset":ai,n="auto"===this._config.method?e:this._config.method,o=n===ai?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(e){var t,e=u.getSelectorFromElement(e);if(t=e?document.querySelector(e):t){var i=t.getBoundingClientRect();if(i.width||i.height)return[c.default(t)[n]().top+o,e]}return null}).filter(Boolean).sort(function(e,t){return e[0]-t[0]}).forEach(function(e){t._offsets.push(e[0]),t._targets.push(e[1])})},e.dispose=function(){c.default.removeData(this._element,si),c.default(this._scrollElement).off(ri),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},e._getConfig=function(e){var t;return"string"!=typeof(e=l({},hi,"object"===_typeof(e)&&e?e:{})).target&&u.isElement(e.target)&&((t=c.default(e.target).attr("id"))||(t=u.getUID(D),c.default(e.target).attr("id",t)),e.target="#"+t),u.typeCheckConfig(D,e,pi),e},e._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},e._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},e._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},e._process=function(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),i=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),i<=e)t=this._targets[this._targets.length-1],this._activeTarget!==t&&this._activate(t);else if(this._activeTarget&&e<this._offsets[0]&&0<this._offsets[0])this._activeTarget=null,this._clear();else for(var n=this._offsets.length;n--;)this._activeTarget!==this._targets[n]&&e>=this._offsets[n]&&(void 0===this._offsets[n+1]||e<this._offsets[n+1])&&this._activate(this._targets[n])},e._activate=function(t){this._activeTarget=t,this._clear();var e=this._selector.split(",").map(function(e){return e+'[data-target="'+t+'"],'+e+'[href="'+t+'"]'}),e=c.default([].slice.call(document.querySelectorAll(e.join(","))));(e.hasClass("dropdown-item")?(e.closest(".dropdown").find(".dropdown-toggle").addClass(P),e):(e.addClass(P),e.parents(di).prev(ci+", "+ui).addClass(P),e.parents(di).prev(".nav-item").children(ci))).addClass(P),c.default(this._scrollElement).trigger("activate.bs.scrollspy",{relatedTarget:t})},e._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(e){return e.classList.contains(P)}).forEach(function(e){return e.classList.remove(P)})},n._jQueryInterface=function(i){return this.each(function(){var e=c.default(this).data(si),t="object"===_typeof(i)&&i;if(e||(e=new n(this,t),c.default(this).data(si,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}})},r(n,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return hi}}]),n})(),gi=(c.default(window).on("load.bs.scrollspy.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),t=e.length;t--;){var i=c.default(e[t]);fi._jQueryInterface.call(i,i.data())}}),c.default.fn[D]=fi._jQueryInterface,c.default.fn[D].Constructor=fi,c.default.fn[D].noConflict=function(){return c.default.fn[D]=li,fi._jQueryInterface},"bs.tab"),mi=c.default.fn.tab,vi="active",yi=".active",bi="> li > .active",wi=(()=>{function n(e){this._element=e}var e=n.prototype;return e.show=function(){var e,t,i,n,o,s,r=this;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&c.default(this._element).hasClass(vi)||c.default(this._element).hasClass("disabled")||this._element.hasAttribute("disabled")||(t=c.default(this._element).closest(".nav, .list-group")[0],i=u.getSelectorFromElement(this._element),t&&(o="UL"===t.nodeName||"OL"===t.nodeName?bi:yi,n=(n=c.default.makeArray(c.default(t).find(o)))[n.length-1]),o=c.default.Event("hide.bs.tab",{relatedTarget:this._element}),s=c.default.Event("show.bs.tab",{relatedTarget:n}),n&&c.default(n).trigger(o),c.default(this._element).trigger(s),s.isDefaultPrevented())||o.isDefaultPrevented()||(i&&(e=document.querySelector(i)),this._activate(this._element,t),s=function(){var e=c.default.Event("hidden.bs.tab",{relatedTarget:r._element}),t=c.default.Event("shown.bs.tab",{relatedTarget:n});c.default(n).trigger(e),c.default(r._element).trigger(t)},e?this._activate(e,e.parentNode,s):s())},e.dispose=function(){c.default.removeData(this._element,gi),this._element=null},e._activate=function(e,t,i){function n(){return o._transitionComplete(e,s,i)}var o=this,s=(!t||"UL"!==t.nodeName&&"OL"!==t.nodeName?c.default(t).children(yi):c.default(t).find(bi))[0],t=i&&s&&c.default(s).hasClass("fade");s&&t?(t=u.getTransitionDurationFromElement(s),c.default(s).removeClass("show").one(u.TRANSITION_END,n).emulateTransitionEnd(t)):n()},e._transitionComplete=function(e,t,i){t&&(c.default(t).removeClass(vi),(n=c.default(t.parentNode).find("> .dropdown-menu .active")[0])&&c.default(n).removeClass(vi),"tab"===t.getAttribute("role"))&&t.setAttribute("aria-selected",!1),c.default(e).addClass(vi),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),u.reflow(e),e.classList.contains("fade")&&e.classList.add("show");var n=e.parentNode;(n=n&&"LI"===n.nodeName?n.parentNode:n)&&c.default(n).hasClass("dropdown-menu")&&((t=c.default(e).closest(".dropdown")[0])&&(n=[].slice.call(t.querySelectorAll(".dropdown-toggle")),c.default(n).addClass(vi)),e.setAttribute("aria-expanded",!0)),i&&i()},n._jQueryInterface=function(i){return this.each(function(){var e=c.default(this),t=e.data(gi);if(t||(t=new n(this),e.data(gi,t)),"string"==typeof i){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}})},r(n,null,[{key:"VERSION",get:function(){return"4.6.2"}}]),n})(),L=(c.default(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(e){e.preventDefault(),wi._jQueryInterface.call(c.default(this),"show")}),c.default.fn.tab=wi._jQueryInterface,c.default.fn.tab.Constructor=wi,c.default.fn.tab.noConflict=function(){return c.default.fn.tab=mi,wi._jQueryInterface},"toast"),_i="bs.toast",t="."+_i,Ti=c.default.fn[L],Si="show",ki="showing",Ci="click.dismiss"+t,xi={animation:!0,autohide:!0,delay:500},Ei={animation:"boolean",autohide:"boolean",delay:"number"},$i=(()=>{function o(e,t){this._element=e,this._config=this._getConfig(t),this._timeout=null,this._setListeners()}var e=o.prototype;return e.show=function(){var e,t=this,i=c.default.Event("show.bs.toast");c.default(this._element).trigger(i),i.isDefaultPrevented()||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),i=function(){t._element.classList.remove(ki),t._element.classList.add(Si),c.default(t._element).trigger("shown.bs.toast"),t._config.autohide&&(t._timeout=setTimeout(function(){t.hide()},t._config.delay))},this._element.classList.remove("hide"),u.reflow(this._element),this._element.classList.add(ki),this._config.animation?(e=u.getTransitionDurationFromElement(this._element),c.default(this._element).one(u.TRANSITION_END,i).emulateTransitionEnd(e)):i())},e.hide=function(){var e;this._element.classList.contains(Si)&&(e=c.default.Event("hide.bs.toast"),c.default(this._element).trigger(e),e.isDefaultPrevented()||this._close())},e.dispose=function(){this._clearTimeout(),this._element.classList.contains(Si)&&this._element.classList.remove(Si),c.default(this._element).off(Ci),c.default.removeData(this._element,_i),this._element=null,this._config=null},e._getConfig=function(e){return e=l({},xi,c.default(this._element).data(),"object"===_typeof(e)&&e?e:{}),u.typeCheckConfig(L,e,this.constructor.DefaultType),e},e._setListeners=function(){var e=this;c.default(this._element).on(Ci,'[data-dismiss="toast"]',function(){return e.hide()})},e._close=function(){function e(){i._element.classList.add("hide"),c.default(i._element).trigger("hidden.bs.toast")}var t,i=this;this._element.classList.remove(Si),this._config.animation?(t=u.getTransitionDurationFromElement(this._element),c.default(this._element).one(u.TRANSITION_END,e).emulateTransitionEnd(t)):e()},e._clearTimeout=function(){clearTimeout(this._timeout),this._timeout=null},o._jQueryInterface=function(n){return this.each(function(){var e=c.default(this),t=e.data(_i),i="object"===_typeof(n)&&n;if(t||(t=new o(this,i),e.data(_i,t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n](this)}})},r(o,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"DefaultType",get:function(){return Ei}},{key:"Default",get:function(){return xi}}]),o})();c.default.fn[L]=$i._jQueryInterface,c.default.fn[L].Constructor=$i,c.default.fn[L].noConflict=function(){return c.default.fn[L]=Ti,$i._jQueryInterface},e.Alert=i,e.Button=V,e.Carousel=o,e.Collapse=he,e.Dropdown=k,e.Modal=Nt,e.Popover=oi,e.Scrollspy=fi,e.Tab=wi,e.Toast=$i,e.Tooltip=O,e.Util=u,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=scripts.min.js.map
