{"version": 3, "sources": ["glightbox.js"], "names": ["factory", "exports", "_typeof2", "module", "define", "amd", "GLightbox", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "descriptor", "i", "length", "enumerable", "configurable", "Object", "defineProperty", "key", "staticProps", "protoProps", "uid", "Date", "now", "extend", "arguments", "deep", "merge", "call", "each", "collection", "callback", "extended", "prop", "hasOwnProperty", "toString", "size", "isArrayLike", "isNode", "window", "document", "isObject", "l", "name", "cache", "node", "getNodeEvents", "found", "undefined", "fn", "data", "all", "evt", "_ref", "onElement", "<PERSON><PERSON><PERSON><PERSON>", "_ref$once", "_ref$avoidDuplicate", "avoidDuplicate", "_ref$useCapture", "element", "handler", "event", "thisArg", "once", "useCapture", "isFunction", "this", "el", "addEventListener", "isString", "splice", "querySelectorAll", "destroy", "events", "eventName", "addClass", "removeEventListener", "hasClass", "closest", "selector", "push", "parentElement", "elem", "matches", "split", "cl", "classList", "add", "animation", "remove", "contains", "body", "animationNames", "animationEnd", "msMatchesSelector", "animateElement", "translate", "webkitTransform", "MozTransform", "iframe", "className", "createElement", "width", "height", "MozAnimation", "setAttribute", "onload", "msTransform", "OTransform", "addEvent", "display", "style", "removeClass", "innerHTML", "frag", "cssTransform", "windowSize", "innerWidth", "whichAnimationEvent", "transform", "OAnimation", "WebkitAnimation", "t", "animations", "show", "hide", "whichTransitionEvent", "transitions", "createDocumentFragment", "transition", "temp", "OTransition", "htmlStr", "WebkitTransition", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "createIframe", "documentElement", "clientWidth", "config", "url", "clientHeight", "waitUntil", "check", "onComplete", "delay", "timeout", "timeoutPointer", "link", "setInterval", "type", "clearInterval", "insertBefore", "headStyles", "head", "clearTimeout", "injectAssets", "waitFor", "isNil", "console", "error", "script", "indexOf", "getElementsByTagName", "rel", "href", "s", "media", "Array", "ar", "o", "keys", "isNumber", "getNextFocusElement", "current", "btns", "parseInt", "btn", "getAttribute", "highestOrder", "order", "navigator", "userAgent", "match", "f", "orders", "nextOrders", "nextFocus", "keyboardNavigation", "nodeType", "isArray", "focusedButton", "activeElement", "has", "preventDefault", "k", "first", "n", "parseFloat", "isFinite", "getLen", "Math", "sqrt", "v1", "mr", "r", "max", "apply", "map", "newIndex", "acos", "getAngle", "filter", "v2", "sort", "angle", "querySelector", "concat", "swipe", "v", "tap", "wrapFunc", "x", "option", "getRotateAngle", "start", "_cancelAllHandler", "touchCancel", "singleTapTimeout", "pressMove", "y", "preTapPosition", "ignoreDragFor", "PI", "_createClass", "EventsHandlerAdmin", "value", "len", "_cancelSingleTap", "touches", "handlers", "y1", "preV", "longTapTimeout", "longTap", "dispatch", "_preventTap", "isDoubleTap", "currentY", "EventshandlerAdmin", "multipointEnd", "x2", "tapTimeout", "setTimeout", "nodeName", "toLowerCase", "self", "doubleTap", "x1", "pageX", "pageY", "delta", "last", "touchStart", "abs", "pinchStartLen", "cancelAll", "multipointStart", "currentX", "sCurrentX", "sCurrentY", "twoFingerPressMove", "on", "rotate", "swipeTimeout", "sx2", "y2", "move", "sy2", "pinch", "del", "singleTap", "touchEnd", "movedY", "deltaX", "deltaY", "windowWidth", "container", "changedTouches", "desc", "winHeight", "winSize", "process", "_swipeDirection", "currentSlide", "mediaImage", "initScale", "currentScale", "zoomedPosX", "zoomedPosY", "lastZoomedPosX", "hDistance", "hDistancePercent", "vDistancePercent", "hSwipe", "yDown", "isInlined", "sliderWrapper", "overlay", "touchInstance", "TouchEvents", "e", "targetTouches", "xDown", "touchMove", "endCoords", "startCoords", "yUp", "doingMove", "xDiff", "touchFollowAxis", "end", "cancel", "opacity", "settings", "zoom", "bind", "noop", "movedX", "translate<PERSON><PERSON><PERSON>", "resetSlideMove", "transitionEnd", "appendTo", "MozTransition", "close", "slide", "doingZoom", "lastDirection", "_this2", "img", "dragContainer", "max<PERSON><PERSON><PERSON>", "naturalWidth", "active", "naturalHeight", "winWidth", "setTranslate", "parentNode", "centerX", "initialX", "clientX", "zoomedIn", "dragging", "doSlideChange", "doSlideClose", "currentXInt", "currentYInt", "initialY", "xOffset", "yOffset", "doChange", "onclose", "preventOutsideClick", "dragEnd", "dragAutoSnap", "isDragging", "doClose", "clientY", "animated", "slideImage", "slideMedia", "Image", "sizes", "xPos", "yPos", "srcset", "ZoomImages", "_this", "exludeClicks", "dragStart", "drag", "zoomOut", "toleranceReached", "zoomIn", "videoWrapper", "clicked", "playerConfig", "handleMediaFullScreen", "slideInline", "index", "content", "innerContent", "createHTML", "hash", "getElementById", "div", "prevSlide", "nextSlide", "cloned", "slideIframe", "<PERSON><PERSON><PERSON><PERSON>", "SlideConfigParser", "defaults", "title", "description", "descPosition", "effect", "<PERSON><PERSON><PERSON><PERSON>", "zoomable", "sourceType", "origin", "shouldClose", "dragDir", "toleranceX", "parseConfig", "<PERSON><PERSON><PERSON><PERSON>", "toleranceY", "nodeData", "DragSlides", "dragEl", "_config$toleranceX", "slideContainer", "videoID", "_config$toleranceY", "videoPlayers", "getAllPlayers", "_config$slide", "_config$instance", "provider", "customPlaceholder", "direction", "html", "slideConfig", "beforeSlideLoad", "player", "intervalPointer", "allow", "draggable", "useWordBoundary", "wordBoundary", "string", "subString", "descriptionEvents", "pop", "objectData", "setSize", "parser", "dragToleranceY", "Slide", "fade", "_version", "isMobile$1", "isMobile", "src", "elements", "alt", "closeButton", "startAt", "val", "autoplayVideos", "autofocusVideos", "dataset", "beforeSlideChange", "afterSlideChange", "sanitizeValue", "afterSlideLoad", "slideRemoved", "slideExtraAttributes", "dragToleranceX", "showinfo", "iv_load_policy", "byline", "closeEffect", "preload", "oneSlidePerOpen", "touchNavigation", "cleanKeys", "join", "closeOnOutsideClick", "str", "regex", "RegExp", "trim", "replace", "iosNative", "youtube", "substring", "DOMException", "slideBack", "out", "nodeDesc", "prev", "GlightboxInit", "options", "checkSize", "videosWidth", "customOptions", "defaultWith", "effectsClasses", "getAnimationClasses", "defaultHeight", "apiEvents", "fullElementsList", "_hasCustomHeight", "videoProvider", "finalCallback", "scrollBar", "styleSheet", "slideText", "slideEffect", "slideDesc", "titleID", "nextButton", "onOpen", "isTouch$1", "<PERSON><PERSON><PERSON><PERSON>", "slideTitle", "id", "<PERSON><PERSON><PERSON><PERSON>", "smallDescription", "moreText", "slideAnimateOut", "slideAnimateIn", "slideNode", "position", "slideIndex", "plyr", "css", "js", "_hasCustomWidth", "placeholder", "detail", "ready", "resize", "<PERSON><PERSON><PERSON><PERSON>", "loader", "isMobileDevice", "slideVideo", "slideDescriptionContained", "preloadSlide", "updateNavigationClasses", "activeSlide", "slidesContainer", "slideData", "offsetWidth", "trigger", "textID", "innerText", "prevActiveSlide", "substr", "moreLink", "insertSlide", "newSlide", "create", "totalSlides", "slideInfo", "addedSlidePlayer", "existingSlide", "addedSlideNode", "slideHTML", "prevActiveSlideIndex", "gallery", "getActiveSlideIndex", "createTouch", "msMaxTouchPoints", "skin", "theme", "slideInserted", "onClose", "loop", "nextData", "getSlidePlayerInstance", "plugins", "ratio", "fullscreen", "enabled", "_this4", "slidePlayerPlay", "<PERSON><PERSON><PERSON><PERSON>", "prevData", "vimeo", "portrait", "effectName", "transparent", "openEffect", "cssEfects", "in", "svg", "next", "animOut", "lastZoomedPosY", "vDistance", "vSwipe", "getGalleryElements", "getElementIndex", "stopSlideVideo", "play", "prevButton", "lightboxOpen", "imageZoomed", "setZoomEvents", "offsetHeight", "moved", "xUp", "yDiff", "h", "scaleY", "scale", "mvDistance", "mhDistance", "keyCode", "toLocaleLowerCase", "focus", "newElements", "getElements", "_this6", "list", "slideDescription", "nodes", "getSelector", "_this3", "getActiveSlide", "goToSlide", "_this7", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prevSVG", "lightboxHTML", "closeSVG", "modal", "descriptionResize", "image", "descHeight", "_imgNode", "containerWidth", "containerHeight", "videoRatio", "videoWidth", "maxHeight", "floor", "vwidth", "video", "vheight", "animIn", "_ratio", "reload", "init", "_this8", "slidePlayerPause", "closing", "bodyHidden<PERSON>hild<PERSON><PERSON>s", "built", "styles", "clearAllEvents", "baseEvents", "_this9", "playing", "pause", "onceTriggered", "version", "log", "glightbox", "_this$settings$plyr$c", "muted", "_this5", "getConfig", "showSlide", "elData", "effects", "childNodes", "nextSVG", "char<PERSON>t", "hasAttribute", "divisor", "vsize", "loopAtEnd", "removeAttribute", "_key"], "mappings": "oPAAmBA,IACE,YAAL,aAAd,OAAOC,QAAO,YAAAC,SAAPD,OAAO,IAAmC,aAAlB,OAAOE,OAAyBA,OAAOF,QAAUD,EAAQ,EACtE,YAAlB,OAAOI,QAAyBA,OAAOC,IAAMD,OAAOJ,CAAO,EAF1CA,KAASM,UAAAN,EAAA,CAI3B,GAHC,WAGqB,SAAAO,EAAYC,GAa/B,OAPED,EAHF,YAAA,OAAAE,QAAyB,UAAA,OAAAA,OAAAC,SAGb,SAAUF,GADlB,OAAOC,OAAMD,CACfD,EAEC,SAAAC,GACF,OAAMA,GAAA,YAAA,OAAAC,QAAAD,EAAAG,cAAAF,QAAAD,IAAAC,OAAAG,UAAA,SAAA,OAAAJ,CACLD,GAGFC,CAAA,CAGF,CAEA,SAASK,EAAgBC,EAAUC,GACjC,GAAI,EAAED,aAAoBC,GACxB,MAAM,IAAIC,UAAU,mCAAmC,CAE3D,CAEA,SACOC,EAAoBC,EAAOC,GAAhC,IACE,IAAIC,EAAAA,EAAUC,EAAGF,EAAOG,OAACD,CAAA,GAAA,CACzBD,IAAAA,EAAWG,EAAUF,GACrBD,EAAWI,WAAYJ,EAAOG,YAAA,CAAA,EAC9BH,EAAWI,aAAc,CAAA,EACzBC,UAAOC,IAAeR,EAAQE,SAAgBA,CAAAA,GAChDK,OAAAC,eAAAR,EAAAE,EAAAO,IAAAP,CAAA,CACF,CAEA,CAAA,SAEMQ,EAAaX,EAAAA,EAAkBF,GACnCc,GAAOd,EAAWA,EAAAH,UAAAiB,CAAA,EACpBD,GAAAX,EAAAF,EAAAa,CAAA,CAGA,CADA,IAGEE,EAAQC,KAAGC,IAAI,EAFjB,SAGOC,EAHP,GAIE,IAAIX,EAASY,GAETT,EAAOb,CAAAA,EACTuB,EAAI,EACHb,EAAEY,UAAAZ,OAsBP,IAnBkC,qBAA5Bc,OAAKxB,UAAYwB,SAASC,KAXhC,CAWkC,IAC9BF,EAZJ,EAQId,CAAC,IAsBIiB,EAAIA,EAACC,CAAAA,GAAYC,CAbhBC,EADKjC,EAAAA,KAAAA,EAeb,IAdQiC,EADKjC,EAeF+B,UAAWlB,GAnBpB,IAKMoB,KAASC,EACXjB,OAAAb,UAAA+B,eAAAN,KAAA7B,EAAAkC,CAAA,IACFP,GAAA,oBAAAV,OAAAb,UAAAgC,SAAAP,KAAA7B,EAAAkC,EAAA,EACFD,EAAAC,GAAAT,EAAA,CAAA,EAAAQ,EAAAC,GAAAlC,EAAAkC,EAAA,EAGMD,EAASC,GAAOlC,EAAAkC,GAUxB,CALA,OAQEH,CAPJ,CACA,SASMM,EAAKN,EAAeC,GAAxB,GAWI,GAAAK,EAHFN,EAFKO,EAJPP,EADEQ,CAAAA,EAAAR,CAAA,GAAAA,IAAAS,QAAAT,IAAAU,SAKSV,EAJX,CAAAA,EAIW,GAAAW,EAAAX,CAAA,EAKPA,EAHU,CAAElB,EAGZ,EAPJ,GAWIyB,EAAQP,CAAYZ,GAAM,CAAAuB,EAAAX,CAAA,EAP5B,IAHA,IAWIY,EAAIX,EAASH,OAVbhB,EAWE,EAEJA,EAAA8B,GACF,CAAA,IAAAX,EAAAH,KAAAE,EAAAlB,GAAAkB,EAAAlB,GAAAA,EAAAkB,CAAA,EADElB,CAAA,SAKA+B,GAAOlB,EAASK,CAAU,EAC9B,IAAM,IAAGL,KAASK,EACdc,GAAAA,EAAQC,EAAYA,CAAAA,GACb,CAAA,IAAHd,EAAGH,KAAAE,EAAAZ,GAAAY,EAAAZ,GAAAA,EAAAY,CAAA,EACJc,KANT,CACA,SAYME,EAAgBD,EAZtB,EAAA,GACE,IAAIF,EAYmB,EAAZI,UAAQlC,QAAImC,KAAAA,IAbzB,EAAA,EAayB,KAXnBC,EAYc,EAAZC,UAAYrC,QAAAmC,KAAAA,IAdpB,EAAA,EAcoB,KAXdJ,EAYEC,EAAOxB,GAAKwB,EAAAxB,IAAA,GACd6B,EAAA,CACFC,IAAEP,EACJQ,IAAA,KAEAL,MAAOG,IACT,EAaE,OAXIG,GAAO5B,GAAoB,EAApBA,EAAUZ,CAAM,GACvByC,EAAAA,EAAYD,SAAKC,EAAS1C,GAC1B2C,GAAAA,EAAAA,WAAoBA,GAAAA,EAAYN,GAAAd,SAAA,GAAAc,EAAAd,SAAA,EAGhCqB,OAFAC,EAAAA,MAAAA,CAAAA,EACAC,EAAAA,IAAc9C,EACL,CAAA,CAET+C,CAAAA,EAIAC,CAbN,CACA,SAeIA,EAAUpB,EAfd,EAAA,GAgBE,IAAAa,EAAA,EAAA5B,UAAAZ,QAAAmC,KAAAA,IAhBF,EAAA,EAgBE,GAEAM,EAAgBO,EAACC,UACfP,EAAeA,EAAAA,aACbA,EAAkBQ,EAASD,eAC7BJ,EAAA,KAAA,IAAAD,GAAAA,EAEAD,EAAUH,EAAAW,KACRH,EAAiB,KAAA,IAAVL,GAAUA,EACnBG,EAAAN,EAAAY,WACFA,EAAA,KAAA,IAAAN,GAAAA,EAGE9B,EAA4B,EAAhBJ,UAAEZ,OA7BlB,EA6BgCmC,KAAAA,EAC1BY,EAAUN,GAAGR,GAZjB,SAoBIe,EAAAC,GACAI,EAAAX,CAAA,GACHA,EAAA3B,KAAAmC,EAAAD,EAAAK,IAAA,EAKKC,GACFA,EAAGC,QAAAA,CApBP,CA2BA,OAvCIC,EAmBSnB,CAAIoB,IAlBfX,EAmBEpB,SAAAgC,iBAAAZ,CAAA,GANJC,EAsBMY,QAAA,WACJ5C,EAAA+B,EAAA,SAAAQ,GACA,IAAAM,EAAA5B,EAAAsB,EAAAO,EAAAd,CAAA,EAEJa,EAAA3B,OACA2B,EAASE,IAAS/B,OAAMF,EAAMS,IAAA,CAAA,EAG1BgB,EAAAS,qBACJT,EAAAS,oBAAAF,EAAAd,EAAAI,CAAA,CAEEpC,CAAAA,CArBA,EAwBFA,EAAA+B,EAAA,SAAAQ,GACA,IAAAM,EAAiBI,EAAaV,EAAAO,EAAAd,CAAA,GAE9BO,EAAAC,kBAAAX,GAAA,CAAAgB,EAAA3B,OAAA,CAAAW,KACAU,EAASW,iBAAcC,EAAUnB,EAAAI,CAAA,EAC/BS,EAAWvB,IAAA8B,KAAKzC,CACVmC,UAAQO,EAERjC,GAACkC,CACH,CAAA,EArBJ,CAAC,EA0BKC,CAxBR,CACA,SAyBIR,EAAA/B,EAAAF,GACFd,EAAAc,EAAA0C,MAAA,GAAA,EAAA,SAAAC,GACF,OAAAzC,EAAA0C,UAAAC,IAAAF,CAAA,CACA,CAAA,CAxBA,CACA,SAyBMvD,EAAWN,EAAUZ,GAEzBgB,EAAK+B,EAAAA,MAAW6B,GAAAA,EAAAA,SAAgBH,GAC9B,OAAOzC,EAAK0C,UAAAG,OAAAJ,CAAA,CACd,CAAA,CAzBF,CACA,SA2BQpB,EAAUrB,EAACd,GA1BjB,OA2BIA,EAAQwD,UAAEI,SAAAhD,CAAA,CA1BhB,CACA,SA4BIoC,EAAYI,EAAAH,GACd,KAAAG,IAAA3C,SAAAoD,MAAA,CAIA/D,GAAKgE,EAFLV,EAAIW,EAAAA,eAGFlB,MAAShB,CAAAA,EAKTI,GAFkB,YAAPJ,OAAOuB,EAAAC,QAAAD,EAAAC,QAAAJ,CAAA,EAAAG,EAAAY,kBAAAf,CAAA,EAGlBzB,OAAAA,CA3BF,CACF,CACA,SA8BMyC,EAAejE,EA9BrB,EAAA,GACE,IAgDI8D,EAhDAJ,EA8BY,EAAAhE,UAAAZ,QAAAmC,KAAAA,IA/BlB,EAAA,EA+BkB,GACZjB,EAAA,EAAAN,UAAAZ,QAAAmC,KAAAA,IAhCN,GAAA,EAkCIY,GAAA,KAAA6B,IAKgB,SAAdQ,EACE/B,EAAOgC,CAAAA,GACXrD,EAAWsD,GAOTL,GA6BN,KAqCE,IAAIM,EACJA,EAAOC,SAASC,cAAG,aAAoB,EACvCF,EAAgB,CAChBA,UAAaG,eACbH,WAAaI,gBAEbC,aAAW,eACTL,gBAAOM,oBACT,EAnCA,IAsCEN,KAAOO,EACP/B,GAAiB5B,KAAAA,IAAjB4B,EAAAA,MAASwB,GAET,OAAIlC,EAAWnC,EApCnB,GA5CwC,EAEtCc,EADIgD,EAAOM,EAAeF,MAAS,GAAA,EACxBW,SAAcX,GACzBpD,EAAWgE,EAAAA,IAAaZ,CAAAA,CACxBpD,CAAAA,EACFiE,EAAAhB,EAAA,CACAxC,UAAcM,EACZA,eAAcmD,CAAAA,EAChB/C,KAAA,CAAA,EACAT,aAAcK,SAASE,EAAArD,GACrBmD,EAAQoD,EAAgB,SAAMrE,GAChCsE,EAAAxG,EAAA,IAAAkC,CAAA,CACA,CAAA,EAEUuB,EAAYoC,CAAAA,GACfY,EAAS,CAGZC,CACF,CAAA,GAhCF,CAmCA,SAAAC,EAAAvE,EAAA,GACAoD,EAAsB,EAAboB,UAAaxG,QAAAmC,KAAAA,IADtB,EAAA,EACsB,GAhCpB,GAkCgBsE,KAAdf,EAMEnC,OALFoC,EAAAA,MAAQjE,gBAAkB,GAC3BM,EAAAmE,MAAAb,aAAA,GACHtD,EAAAmE,MAAAJ,YAAA,GACA/D,EAAS0E,MAAAA,WAAmBA,GAC1B1E,EAAKmE,MAAAQ,UAAA,GACIhF,CAAAA,EA/BTK,EAkCE4E,MAAAA,gBAA2BxB,EAjC7BpD,EAkCE4D,MAAAA,aAAcR,EAjChBpD,EAkCE6E,MAAAA,YAAiBzB,EACnBpD,EAACmE,MAAAH,WAAAZ,EAEDpD,EAAK8E,MAAKC,UAAY3B,CAlCxB,CACA,SAmCM4B,EAAOD,GAlCXhE,EAmCEoD,MAAAD,QAAA,OAlCJ,CAoCA,SAAAe,EAAAlE,GACAA,EAASmE,MAAAA,QAAAA,MAlCT,CACA,SAmCWvF,EAAS8D,GAClB,IAAI0B,EAAAA,SAAcC,uBAAA,EAChBC,EAAAA,SAAY5B,cAAe,KAAA,EAhC7B,IAFA6B,EAmCEC,UAAaC,EAEbC,EAAAA,YACDnB,EAAAoB,YAAAJ,EAAAK,UAAA,EAhCD,OAoCIrB,CAnCN,CACA,SAoCEE,IACF,MAAA,CACAd,MAASkC,OAAAA,YAAqBjG,SAAAkG,gBAAAC,aAAAnG,SAAAoD,KAAA+C,YAC5BnC,OAAUoC,OAAOC,aAAGrG,SAAAkG,gBAAAI,cAAAtG,SAAAoD,KAAAkD,YAnCpB,CACF,CA+DA,SAoDSC,EAASC,EAAOC,EAASC,EAAAC,GAnDhC,IAgEEC,EACAC,EAbAtG,EAAQP,EAERyG,EAAS,GAKPC,EAAAA,GACF,IAKAG,EAAWC,YAAY,WAClBC,EAAI,IAnDTC,cAwDOC,CAAmBC,EAExBC,GACFC,aAAAR,CAAA,EAIAH,EAAA,EAxDF,EA0DEC,CAAA,EAGFnG,IAEAqG,EAAmBvI,WAAU,WAC3B2I,cAAezH,CAAW,CA3D1B,EA4DEoH,CAAI7E,GA1DV,CACA,SA4DSuF,EAAEhB,EAAYiB,EAAA/H,GAzCrB,IAAIgB,EA+EN4G,EAEEN,EAnGA,GAAIU,EA4DIhI,CAAAA,EA3DNiI,QA4DMC,MAAA,qBAAA,OAYRC,GAPEhG,EAAA4F,CAAA,IAEA/H,EAAA+H,EACFA,EAAA,CAAA,GAIAI,EAAarB,CAAG,GAAAiB,KAAAvH,OAEV2B,EAAUnC,CAAA,GACdA,EAAImC,OAxDN,GAgEI,CAAA,IAAA2E,EAAAsB,QAAA,MAAA,GA/DFpH,EAiEEhB,SAAUyC,iBAAA,cAAAqE,EAAA,IAAA,IAEb,EAAA9F,EAAAlC,SAUH6I,GADAC,EAAAnH,SAAA4H,qBAAA,MAAA,EAAA,IACuB5F,iBAAA,wBAAA,GACrB6E,EAAO7G,SAAa8D,cAAU,MAAA,GAChC+D,IAAA,aACAhB,EAAS/E,KAAAA,WACP+E,EAAOiB,KAAOC,EAChBlB,EAAAmB,MAAA,MAEEd,EACFC,EAAAF,aAAAJ,EAAAK,EAAA,EAAA,EAEEC,EAAOc,YAAcC,CAAG,GAGxBxG,EAAgBrD,CAAM,GACxBkB,EAAA,OASA,IAFEgB,EAAQP,SAAQgC,iBAAA,eAAAqE,EAAA,IAAA,IAEK,EAAV9I,EAAQc,QACnB,GAAAqD,EAAenC,CAAQG,EAAAA,CACzB,GAAAoC,EAAAwF,CAAA,EAOQpH,OANRqG,KAAAA,EAAiB,WACXtG,OAAa,KAAA,IAAAF,OAAAuH,EACXa,EAAEC,WACJ7I,EAAa,CACf,CAAA,EA/DEA,EAqEK,CApEP,CAAA,KAuDJ,CAoBE,IAAAmI,EAAA1H,SAAA8D,cAAA,QAAA,EACF4D,EAAAX,KAAA,kBACAW,EAASW,IAAAA,EAETX,EAAAvD,OAAA,WAEA,GAAAzC,EAAS4G,CAAAA,EAAmB,CACtBC,GAAAA,EAAUtJ,CAAUZ,EAQtB,OAPEmK,EAAOxI,WAENwI,OAAa,KAAA,IAAAzI,OAAAuH,EAChB,EAAO,WACT/H,EAAA,CAEIiJ,CAAAA,EACS,CAAA,EAIXD,EAAUE,CACZ,CAEA,EAzEAzI,SA2ESyC,KAAKiG,YAAIC,CAAa,CA1B/B,CAhDF,CACA,SA2EMC,IA1EJ,MA2EE,cAAgBC,QAAM9I,OAAA+I,UAAAC,UAAAC,MAAA,0GAAA,CA1E1B,CAIA,SA4EEtH,EAAAuH,GAEA,MAAiBC,YAAbC,OAAUF,CA5EhB,CACA,SA6EInH,EAAAiG,GACF,MAAgBoB,UAAZC,OAASrB,CA5Ef,CA8EA,SAAAjI,EAAA8B,GAEA,OAASyH,GAAAA,EAAAA,UAA6B,GAAVxL,EAAQyL,QA7EpC,CACA,SA8EIC,EAAYrB,GACd,OAAAD,MAAAsB,QAAArB,CAAA,CA7EF,CACA,SA+EIpH,EAAiBoH,GA9EnB,OA+EEnH,GAAAA,EAAc1C,QAAS0C,SAAAA,EAAaO,MAAK,CA9E7C,CACA,SA+EMrB,EAAUqB,GA5Ed,MAiFWkI,WAHAlM,EAAO6K,CAAA,GAGQ,MAAAA,GAAA,CAAAzG,EAAAyG,CAAA,GAAA,CAAAoB,EAAApB,CAAA,CAhF5B,CACA,SAASZ,EAkFKkC,GAjFZ,OAkFU,MAlFHtB,CACT,CACA,SAkFQuB,EAAAnM,EAAAmB,GAjFN,OAmFYiL,OAANrI,GAAMqI,eAAgBvK,KAAA7B,EAAAmB,CAAA,CAlF9B,CACA,SAoFQkB,EAAK4I,GAnFX,GAAIvI,EAoFIkI,CAAA,EAAA,CAnFN,GAoFIA,EAAAC,KAnFF,OAqFOoB,EAAAA,KAAAA,EAAAA,OAlFT,IAwFMI,EAxFF1J,EAsFI2J,EApFR,IAsFMD,KAAAzB,EAEAuB,EAAAvB,EAAAyB,CAAA,GACF1J,CAAA,GAnFJ,OAyFIA,CAxFN,CACE,OAyFMkC,EAAAA,MAvFV,CACA,SA0FMiG,EAAWyB,GAzFf,MA0FMjM,CAAAA,MAAQkM,WAAWD,CAAC,CAAA,GAAAE,SAAAF,CAAA,CAzF5B,CAEA,SA2FQjM,EA3FR,GACE,IA2FI0K,EAAA,EAAAtJ,UAAAZ,QAAAmC,KAAAA,IA5FN,EAAA,EA4FM,CAAA,EAEAgI,EAAOxI,SAAQgC,iBAAA,qCAAA,EA1FnB,GA4FI,CAAAwG,EAAAnK,OACF,MAAA,CAAA,EAIJ,GAAmB,GAAnBmK,EAASyB,OACP,OAAOC,EAAKC,GAISC,UAArB,OAAW7B,IACbA,EAAAE,SAAAF,CAAA,GAKE,IAAI8B,EAAQ,GAMRC,GArGJjL,EAgGEmJ,EAAQ,SAAAE,GACVQ,EAAAzG,KAAAiG,EAAAC,aAAA,eAAA,CAAA,CAEA,CAAA,EAEWuB,KAAAK,IAAAC,MAAAN,KAAAhB,EAAAuB,IAAA,SAAA5B,GACTyB,OAAK7B,SAAAI,CAAA,CACP,CAAA,CAAA,GAEA6B,EAAYC,EAAO,EAAA,EAAApC,EAAA,EAGJK,EAAjB8B,IACEA,EAAW,KAQXtB,EAJYwB,EAAWC,OAAK,SAAAjJ,GAE5B,OAASA,GAAKkJ,SAASJ,CAAA,CArGvB,CAAC,EAuGDK,KAAA,EAAA,GAEA,OAAOC,SAAQC,cAAa,wBAAAC,OAAA9B,EAAA,IAAA,CAAA,CAC9B,CApCA,SAkHS+B,EAAKC,GAjHZ,OAkHOC,KAAGlB,KAAGmB,EAAAA,EAASF,EAAAG,EAAKnK,EAAAA,EAAOgK,EAAEI,CAAAA,CAjHtC,CA0BA,SAmHSC,EAAWC,EAAM9K,GArBN0K,EAsBIR,EAnHtB,IA6FeV,EACbC,EAqBEW,EAnBGW,IAFLtB,EAAKuB,EADQxB,EAsBKA,CArBF,EAAGkB,EAAQR,CAAC,GAGrBjJ,GAKFgK,GAFLvB,GAAQF,EAVH0B,EAUMhB,EAVGS,EAUNnB,EAVSkB,EAUNR,EAVeiB,GAUX1B,KAGfC,EAAI,GAGC0B,KAAAA,KAAAA,CAAAA,GApGP,OA+GI,EAAA5B,EAPJmB,EAOIT,EAPJiB,EAOIjB,EAPJS,EAOInB,EAPJ2B,IA3GEf,GAoHMiB,CAAAA,GAIF,IAAAjB,EAAAd,KAAAgC,EApHR,CAUEC,EAwHMC,EAAA,CAAA,CAvHJ1N,IAwHE,MAvHF2N,MAyHOL,SAAAA,GACLrK,KAAKqK,SAAAA,KAAc3K,CAAK,CAxH1B,CACF,EAAG,CACD3C,IAyHM4N,MAxHND,MA0HMC,SAASjL,GACXA,IAEAM,KAAK4K,SAAAA,IAxHP,IA4HK,IAAE3L,EAAI4L,KAAOC,SAASpO,OAAQqO,GAAAA,EAAAA,CAAAA,GAChC/K,KAAA8K,SAAArO,KAAAiD,GACDsL,KAAMF,SAAM1K,OAAA3D,EAAA,CAAA,CAzHhB,CACF,EAAG,CACDM,IA6HE,WA5HF2N,MA6HOO,WA5HL,IA6HE,IAAKC,EAAAA,EAAQC,EAAAA,KAAYL,SAAOrL,OAAQhD,EAAAkO,EAAAlO,CAAA,GAAA,CACxC,IAAIiD,EAAC0L,KAAcN,SAAIrO,GAE3B,YAAA,OAAAiD,GACCA,EAAAmJ,MAAA7I,KAAAC,GAAA3C,SAAA,CAEDoN,CA5HA,CACF,EAAE,EArCJ,IAAID,EAqKEA,EApKJ,SAsHSM,EAAmB9K,GArH1BhE,EAsHe+D,KAAK5C,CAAoB,EApHxC4C,KAuHE8K,SAAST,GAtHXrK,KAuHIC,GAAKoL,CAtHX,CAoCF,SA8HUC,EAAQrL,EAAMP,GAClB6L,EAAmB,IAAKd,EAAAxK,CAAA,EA5H5B,OADAsL,EA+HiBlK,IAAA3B,CAAA,EACX6L,CA9HR,CAyDEf,EA6IWgB,EAAcL,CAAAA,CA5IvBpO,IA6II,QA5IJ2N,MA6IE,SAAAzL,GAEA,IA+BD+L,EAQDvB,EAvCMxK,EAAKwM,UAMPxM,EAAKyM,QAAUzM,EAAG0M,OAAAA,UAAuB,GAFpC,CAAA,IAAA,SAAA,SAEoC3F,QAAA/G,EAAA3C,OAAAsP,SAAAC,YAAA,CAAA,EA7IzChG,QA8IOiG,IAAKV,uCAAanM,EAAA3C,OAAAsP,SAAAC,YAAA,CAAA,GA1I3B7L,KAAK5C,IA+IM2O,KAAAA,IAAS,EA9IpB/L,KAAKgM,GA+IK/M,EAACoM,QAAW,GAAGY,MA9IzBjM,KA+II+K,GAAA9L,EAAA4L,QAAA,GAAAqB,MA9IJlM,KA+IKmM,MAAEnM,KAAA5C,KAAA4C,KAAAoM,MAAApM,KAAA5C,KA9IP4C,KAgJEqM,WAAUhB,SAAapM,EAAAe,KAAAP,OAAA,EAEKR,OAhJ1Be,KAgJE8L,eAAeX,IA/InBnL,KAgJKqL,YAAI,EAAArL,KAAAmM,OAAAnM,KAAAmM,OAAA,KAAA5D,KAAA+D,IAAAtM,KAAAqK,eAAAT,EAAA5J,KAAAgM,EAAA,EAAA,IAAAzD,KAAA+D,IAAAtM,KAAAqK,eAAAD,EAAApK,KAAA+K,EAAA,EAAA,GAEX/K,KAAAqL,cAEI5F,aAAU0F,KAAQjB,gBAAWzK,EAIjCO,KAAKuM,eAAa3C,EAAG5J,KAAIgM,GACzBhM,KAAKgM,eAAe5B,EAAIpK,KAAG+K,GAC7B/K,KAAAoM,KAAApM,KAAA5C,IACC4N,EAAAhL,KAAAgL,KAGMI,EAFFnM,EAAW4L,QAAAnO,SAGd+I,KAAAA,eAAkByE,EAElBzE,KAAAA,iBAAkBwF,EAEpBxB,EAAA,CACCG,EAAA3K,EAAA4L,QAAA,GAAAoB,MAAAjM,KAAAgM,GACI5B,EAAAnL,EAAQ4L,QAAA,GAAAqB,MAAAlM,KAAA+K,EACbL,EACEM,EAAKwB,EAAAA,EAAAA,EACLxB,EAAKf,EAAAA,EAAAA,EACPjK,KAAAuM,cAAAjE,EAAA0C,CAAA,EACChL,KAAAyM,gBAAAtB,SAAAlM,EAAAe,KAAAP,OAAA,GAGCgG,KAAAA,YAAkBwF,CAAAA,EACpBjL,KAAAiL,eAAAU,WAAA,WACC3L,KAAAkL,QAAAC,SAAAlM,EAAAe,KAAAP,OAAA,EACEO,KAAEoL,YAAkB,CAAA,CACvBV,EAAAA,KAAO1K,IAAA,EAAS4K,GAAAA,GAhJhB,CACF,EAiJE,CACD7N,IAAE,OACDA,MAAK,SAAAkC,GACLyL,IAKKC,EACC+B,EACFpB,EAICqB,EACEC,EAsCAzC,EACA0C,EAnDA5N,EAAA4L,UAIFG,EAAIhL,KAAAgL,KACJL,EAAE1L,EAAS6N,QAAQpN,OAClBgN,EAAWzN,EAAA4L,QAAA,GAAAoB,MACbX,EAAarM,EAACS,QAAQ,GAAAwM,MACxBlM,KAAAqL,YAAA,CAAA,EAED,EAAAV,GACIgC,EAAK1N,EAAA4L,QAAA,GAAAoB,MACHW,EAAa3N,EAAG4L,QAAS,GAAEqB,MAC5BzC,EAAKxK,CACP2K,EAAK3K,EAAI4L,QAAKnL,GAAAA,MAAQgN,EACxBtC,EAAAnL,EAAA4L,QAAA,GAAAqB,MAAAZ,CACF,EAEc,OAATN,EAAApB,IACqB,EAAnB5J,KAAAuM,gBACDtN,EAAKiL,KAAAA,EAAAA,CAAgB,EAAElK,KAAAuM,cACzB9G,KAAAA,MAAa0F,SAAKjB,EAAAA,KAAgBzK,OAAC,GAInCgG,EAAAA,MAAaqE,EAAgBL,EAAAuB,CAAA,EAC/BhL,KAAA+M,OAAA5B,SAAAlM,EAAAe,KAAAP,OAAA,GAIAuL,EAAApB,EAAAH,EAAAG,EAEAoB,EAAIZ,EAAK4C,EAAAA,EAET,OAAAhN,KAAAyL,IAAA,OAAAzL,KAAAiN,KAEIhO,EAACQ,QAAQiB,EAAAA,KAAoB+K,GAAAkB,EAAkB3M,KAAC+J,KAAM,EACtD9K,EAACQ,QAAQiB,EAAAA,KAAoBwM,GAAAN,EAAkBO,KAAKC,KAAA,IAEpDnO,EAACQ,OAAQiB,EACTzB,EAAC8N,OAAY,GAGjB/M,KAAKwL,mBAAmBL,SAAAlM,EAAAe,KAAAP,OAAA,EACxBO,KAAKqN,IAAMC,EACXtN,KAAKwJ,IAAM8D,IAEO,OAAbvB,KAAAA,IACD9M,EAACiM,OAAYwB,EAAC1M,KAAAyL,GACdxM,EAACsO,OAAUD,EAAKtN,KAAAkN,GACf/C,EAAUmD,KAAKhB,IAAAtM,KAAAgM,GAAAhM,KAAAyL,EAAA,EACfoB,EAAAA,KAAAA,IAAmBS,KAAKvC,GAAA/K,KAAAkN,EAAA,GAEZ,GAAZM,GAAc,GAAAC,KACdxD,KAAAA,YAAiB,CAAA,KAGtBhL,EAAOyO,OAAI,EACbzO,EAAA0O,OAAA,GAID3N,KAAAmK,UAAAgB,SAAAlM,EAAAe,KAAAP,OAAA,GAIGmO,KAAAA,UAAcxP,SAAO+E,EAAAA,KAAU1D,OAAIpB,EAEnCwP,KAAAA,eAAoBxH,EAGpBuH,KAAAA,GAAAA,EACFvH,KAAK6G,GAAGW,EAGI,EAANlD,IACR1H,EAAAA,eAAoB,CAElB9D,CAxJF,EAyJEU,CACAT,IAAAA,MAxJAsL,MAyJE5H,SAAiB7D,GACnB,IAOJ6M,EAPI7M,EAAA6O,iBAKAC,KAAKlL,eAAgB,EAEzBiJ,EAAA9L,KAGM9D,EAAQ2O,QAAQ9M,OAAAA,IAClBiC,KAAOwL,cAAKL,SAAAlM,EAAAe,KAAAP,OAAA,EACdO,KAAAiN,IAAAjN,KAAAoN,IAAA,MAIIY,KAASvC,IAAiB,GAAdwC,KAAQ5L,IAAAA,KAAM2J,GAAAhM,KAAAyL,EAAA,GAAAzL,KAAAkN,IAAA,GAAA3E,KAAA+D,IAAAtM,KAAA+K,GAAA/K,KAAAkN,EAAA,GAC1BgB,EAAAA,UAAelO,KAAAmO,gBAAAnO,KAAAgM,GAAAhM,KAAAyL,GAAAzL,KAAA+K,GAAA/K,KAAAkN,EAAA,EACfkB,KAAAA,aAAmBzC,WAAA,WACnBtF,EAAQmD,MAAI2B,SAAAlM,EAAA6M,EAAArM,OAAA,CACZ4O,EAAAA,CAAAA,IAEAC,KAAAA,WAAa3C,WAAA,WACLG,EAAMV,aACdmD,EAAAA,IAAepD,SAAClM,EAAA6M,EAAArM,OAAA,EAGhB+O,EAAanD,cACboD,EAAU1C,UAAOZ,SAAAlM,EAAA6M,EAAArM,OAAA,EACjBiP,EAAAA,YAAqB,CAAA,EAErBC,EAAAA,CAAAA,EAEAC,EAAAA,cACAC,EAAAA,iBAAoBlD,WAAA,WACdG,EAAGyB,UAAKpC,SAAAlM,EAAA6M,EAAArM,OAAA,CACdqP,EAAM,GAAG,IAITC,KAAKvB,SAAIrC,SAAAlM,EAAAe,KAAAP,OAAA,EACTuP,KAAAA,KAASpF,EAAA,EACTqF,KAAAA,KAAAA,EAAa,EACbC,KAAAA,KAAU7Q,EACV8Q,KAAAA,cAAoBC,KACtB/C,KAAAA,GAAYrM,KAAAyL,GAASY,KAAAA,GAAWgD,KAAGnC,GAAA,KA3JnC,CACF,EAAG,CACDnQ,IA6JImR,YA5JJxD,MA6JE,WAEA1K,KAAIY,YAAU0O,CAAAA,EA7Jd7J,aA8JYzF,KAAKkK,gBAAA,EACjBzE,aAAAzF,KAAA0L,UAAA,EAEAjG,aAAazF,KAAAiL,cAAA,EA9JbxF,aA+JWzF,KAAKsP,YAAc,CA9JhC,CACF,EAAG,CACDvS,IA+JIwS,SA9JJ7E,MA+JIqE,SAAUO,GA9JZtP,KA+JEoO,UAAY,EA9JdpO,KA+JEqG,YAAQ+H,SAAa9E,EAAAA,KAAa7J,OAAC,CA9JvC,CACF,EAAG,CACD1C,IAgKI,iBA/JJ2N,MAgKM2D,WA/JJ5I,aAgKEzF,KAAAiL,cAAA,CA/JJ,CACF,EAAG,CACDlO,IAAK,mBACL2N,MAkKI,WAjKFjF,aAmKazF,KAACkP,gBAAkB,CAlKlC,CACF,EAAG,CACDnS,IAoKI,kBAnKJ2N,MAqKM1C,SAAgBgE,EAAAP,EAAAV,EAAAmC,GACpB,OAAA3E,KAAA+D,IAAAN,EAAAP,CAAA,GAAAlD,KAAA+D,IAAAvB,EAAAmC,CAAA,EAAA,EAAAlB,EAAAP,EAAA,OAAA,QAAA,EAAAV,EAAAmC,EAAA,KAAA,MACF,CApKF,EAqKEsC,CApKAzS,IAqKE,KApKF2N,MAqKI,SAAAzL,EAAAS,GACFM,KAAAf,IAEAwQ,KAAAA,GAAYJ,IAAEC,CAAAA,CApKhB,CACF,EAAG,CACDvS,IAwKE,MAvKF2N,MAwKI,SAAYgF,EAAAA,GAEZ1P,KAAIuI,IAxKJvI,KAyKEf,GAAOqO,IAAK5N,CAAA,CAvKlB,CACF,EAAG,CACD3C,IA0KE,UAzKF2N,MA0KMiF,WA+CJC,OA9CIC,KAAK3F,kBACTzE,aAAYsJ,KAAQY,gBAAG,EAIrBb,KAAMpD,YACPjG,aAAMzF,KAAA0L,UAAA,EAGP1L,KAAAiL,gBAEA0D,aAAYc,KAAAA,cAAkBC,EAG9Bb,KAAAA,cACApJ,aAAWzF,KAAAgN,YAAA,EAzKXhN,KA6KEkP,QAAQrM,oBAAuB,aAAA7C,KAAA+J,KAAA,EA5KjC/J,KA8KEP,QAAIvD,oBAAkB4T,YAAiB9P,KAAAmN,IAAA,EA7KzCnN,KA8KI4O,QAAAA,oBAAoB,WAAA5O,KAAA+P,GAAA,EA7KxB/P,KA8KEP,QAAAiB,oBAAA,cAAAV,KAAAgQ,MAAA,EACFhQ,KAAA+M,OAAAO,IAAA,EAEAtN,KAAI8O,WAAQxB,IAAA,EA9KZtN,KA+KEiQ,gBAAmB3D,IAAIqC,EA9KzB3O,KA+KEqG,cAAY4J,IAAO,EA9KrBjQ,KAgLEqN,MAAInR,IAASgU,EA/KflQ,KAgLI6O,MAAAA,IAAAA,EA/KJ7O,KAgLE0J,IAAA4D,IAAA,EACFtN,KAAA+L,UAAAuB,IAAA,EAEAtN,KAAKqO,QAAAA,IAAY,EAhLjBrO,KAiLEuN,UAAOtK,IAAAA,EACTjD,KAAAmK,UAAAmD,IAAA,EAEArK,KAAAA,mBAAoBqK,IAAA,EACrBtN,KAAAwP,UAAAlC,IAAA,EACDE,KAAAA,SAAUF,IAASE,EACjBxN,KAAKkO,YAASZ,IAAA,EAjLdtN,KAkLEgL,KAAAhL,KAAAuM,cAAAvM,KAAAmQ,KAAAnQ,KAAAqL,YAAArL,KAAAmM,MAAAnM,KAAAoM,KAAApM,KAAA5C,IAAA4C,KAAA0L,WAAA1L,KAAAkK,iBAAAlK,KAAAiL,eAAAjL,KAAAgN,aAAAhN,KAAAgM,GAAAhM,KAAAyL,GAAAzL,KAAA+K,GAAA/K,KAAAkN,GAAAlN,KAAAqK,eAAArK,KAAA+M,OAAA/M,KAAAqM,WAAArM,KAAAyM,gBAAAzM,KAAAwL,cAAAxL,KAAAqN,MAAArN,KAAAwJ,MAAAxJ,KAAA0J,IAAA1J,KAAA+L,UAAA/L,KAAAkL,QAAAlL,KAAAuN,UAAAvN,KAAAmK,UAAAnK,KAAAwP,UAAAxP,KAAAwN,SAAAxN,KAAAiK,YAAAjK,KAAA6M,mBAAA,KACFzO,OAAAsC,oBAAA,SAAAV,KAAAgK,iBAAA,EAEA4F,IAlLF,CACF,EAAE,EA7TJ,IAAIR,EAmfIA,EAlfN,SA+HSA,EAAMvE,EAAUhB,GA9HvB5N,EA+HK+D,KAAAoP,CAAA,EA7HLpP,KAAKP,QAgIuB,UAAb8M,OAAAA,EAAmBlO,SAAAiL,cAAArJ,CAAA,EAAAA,EA/HlCD,KAAK+J,MAgIOoG,KAAIpG,MAAGzB,KAAStI,IAAG,EA/H/BA,KAAKmN,KAgIOnN,KAACqN,KAAMlC,KAAQnL,IAAI,EA/H/BA,KAAK+P,IAgIC/P,KAAA+P,IAAAK,KAAApQ,IAAA,EA/HNA,KAAKgQ,OAiIK3G,KAAQS,OAAAA,KAAAA,IAAgB,EAhIlC9J,KAAKP,QAiIMsN,iBAAmB,aAAc/M,KAAC+J,MAAA,CAAA,CAAA,EAhI7C/J,KAiIIP,QAAAS,iBAAA,YAAAF,KAAAmN,KAAA,CAAA,CAAA,EAhIJnN,KAkIIgL,QAASvB,iBAAG,WAAAzJ,KAAA+P,IAAA,CAAA,CAAA,EAjIhB/P,KAkIIgL,QAASvB,iBAAG,cAAAzJ,KAAAgQ,OAAA,CAAA,CAAA,EAjIhBhQ,KAmIIgL,KAAQ,CAlIVpB,EAAG,KACHQ,EAAG,IACL,EACApK,KAAKuM,cAmIe,KAlIpBvM,KAAKmQ,KAmIKxC,EAlIV3N,KAmIIqL,YAAA,CAAA,EAGK4B,SAALoD,KAlIJrQ,KAoIG+M,OAAMpD,EAAA3J,KAAAP,QAAAoK,EAAAkD,QAAAsD,CAAA,EAnITrQ,KAoIIqM,WAAW1C,EAAW3J,KAAAP,QAAAoK,EAAAwC,YAAAgE,CAAA,EAnI1BrQ,KAAKyM,gBAoIcC,EAAW1M,KAAOP,QAAAoK,EAAA4C,iBAAA4D,CAAA,EAnIrCrQ,KAAKwL,cAoIcF,EAAWtL,KAAKkN,QAAErD,EAAA2B,eAAA6E,CAAA,EAnIrCrQ,KAAKqN,MAoIKiD,EAAS/H,KAAQ9I,QAAQoK,EAAQ4B,OAAG4E,CAAA,EAnI9CrQ,KAAKwJ,MAoIKiE,EAAcnB,KAAI7M,QAAUoK,EAAOL,OAAC6G,CAAA,EAnI9CrQ,KAAK0J,IAqIK4G,EAAStQ,KAAMyN,QAAS5D,EAAIH,KAAA2G,CAAA,EApItCrQ,KAAK+L,UAqIQX,EAAWpL,KAAOP,QAAAoK,EAAAkC,WAAAsE,CAAA,EApI/BrQ,KAAKkL,QAqICvB,EAAA3J,KAAAP,QAAAoK,EAAAqB,SAAAmF,CAAA,EApINrQ,KAqIKuN,UAAM5D,EAAA3J,KAAAP,QAAAoK,EAAA0D,WAAA8C,CAAA,EApIXrQ,KAAKmK,UAqIWR,EAAI3J,KAAAP,QAAAoK,EAAAM,WAAAkG,CAAA,EApIpBrQ,KAAK6M,mBAqIelD,EAAA3J,KAAAP,QAAAoK,EAAAgD,oBAAAwD,CAAA,EApIpBrQ,KAqIIwP,UAAA7F,EAAA3J,KAAAP,QAAAoK,EAAA2F,WAAAa,CAAA,EApIJrQ,KAsIIwN,SAAKrD,EAAUgB,KAASlM,QAAUQ,EAAQ+N,UAAA6C,CAAA,EArI9CrQ,KAsIEiK,YAAAN,EAAA3J,KAAAP,QAAAoK,EAAAI,aAAAoG,CAAA,EArIFrQ,KAuIEuQ,mBAAwBtR,KAAKQ,QAtI/BO,KAwIEgK,kBAAqBhK,KAAAwM,UAAA4D,KAAApQ,IAAA,EAvIvB5B,OAyIOqN,iBAAa,SAAAzL,KAAAgK,iBAAA,EAxIpBhK,KAyIEmM,MAAUb,KAxIZtL,KA0IEoM,KAAU,KAzIZpM,KA0IIf,IAAI+I,KAzIRhI,KA0IE0L,WAAA,KACF1L,KAAAkK,iBAAA,KACDlK,KAAEiL,eAAA,KACDlO,KAAKiQ,aAAK,KACVtC,KAAKsB,GAAEhM,KAAAyL,GAAaxM,KAAK8L,GAAA/K,KAAAkN,GAAA,KAzIzBlN,KA0IEqK,eAASyD,CAzITlE,EA0IE,KACFQ,EAAA,IAzIF,CACF,CA6QF,SAqLMoG,EAAiB1J,GApLrB,IAsLI2J,GAr5BN,KACE,IAwCEC,EACFzQ,EAAA5B,SAAA8D,cAAA,aAAA,EAEA0B,EAAa,CACfE,WAAA,gBACAE,YAASW,iBACP+L,cAAa,gBACX7L,iBAAY,qBAxCd,EA4CA,IAAKC,KAAKlB,EACRkB,GAAWlG,KAAAA,IAAXkG,EAAKlC,MAAMW,GACb,OAAAK,EAAAL,EAvCF,GAs4BgC,EArL1BoK,EAsLWgD,OAAOzN,YAAA9E,SAAAkG,gBAAAC,aAAAnG,SAAAoD,KAAA+C,YArLlB6B,EAsLE1F,EAAAkQ,EAAA,cAAA,EAAAA,EAAAA,EAAAvH,cAAA,eAAA,EACFuE,EAAAjN,EAAAyF,EAAA,mBAAA,EAEA0H,EAAK8C,EAAKvH,cAAY,qBAAA,EAjL1B7I,EAHE4F,EAsLYxD,IAvLV+K,EAwLEC,EAGJrC,EAAa,QAAE,EAtLjBvI,EAuLI0I,EAAW,sBAAY,EAtL3BhJ,EAuLMmO,EAAiB,CAtLrB3R,UAuLQkH,EACRxG,KAAC,CAAA,EACD4M,aAAAA,SAA0BA,EAAenQ,GACvCwU,EAAYzK,EAAI,QAAA,CAtLlB,CACF,CAAC,EACDA,EAuLEgH,MAAO4C,QAAS5C,GArLdU,IACFA,EAuLElL,MAAAoN,QAAA,GArLN,CAwTEzF,EA4OauG,EAAAA,CAAa,CA3OxBhU,IA4OIiU,SA3OJtG,MA4OIsG,WA3OF,IAqPDC,EARGD,EAAU5P,KAAAA,WAAiB,EAI3B4P,KAAOE,UAAAA,GAAoB7N,OAI9B4N,EAAAjR,KAAAiR,KACI1O,aAAM,aAAA0O,EAAAjK,aAAA,OAAA,CAAA,EACX0D,EAAK7H,MAAEsO,SAAeF,EAAEG,aAAA,KACtBH,EAAIpO,MAAKwO,UAAQJ,EAAAK,cAAA,KAEfL,EAAKJ,aAAgBxP,IAEjBgO,EAAMkC,EAAK,EAAaN,EAAAG,aAAA,EA/O5BpR,KAgPEwR,aAAgBnC,KAAExE,IAAO4G,WAAWC,EAAQC,CAAAA,GA7OhD3R,KAgPI6Q,MAAKnE,UAAYrL,IAACuQ,QAAU,EA/OhC5R,KAgPI6R,SAAKvG,CAAAA,EA/OX,CACF,EAAG,CACDvO,IAiPI,UAhPJ2N,MAiPI,WAhPF1K,KAiPEiR,IAAKa,WAAWvP,aAAI,QAAA,EAAA,EAhPtBvC,KAiPEiR,IAAKc,aAAa,QAAQ/R,KAAAiR,IAAAjK,aAAA,YAAA,CAAA,EAhP5BhH,KAiPE6Q,MAAKmB,UAAYzQ,OAAQ,QAAA,EAhP3BvB,KAiPE6R,SAAII,CAAAA,EAhPNjS,KAiPE0M,SAAIwF,KAhPNlS,KAkPEsL,SAAI2G,KAjPNjS,KAkPI2R,SAAY,KAjPhB3R,KAkPImS,SAAKpB,KAjPT/Q,KAkPIoS,QAAKZ,EAjPTxR,KAkPIqS,QAAIC,EAhPJtS,KAmPEuS,SAA6B,YAAxBR,OAAgBO,KAAAA,SAlPzBtS,KAmPEuS,QAAA,CAjPN,CACF,EAAG,CACDxV,IAAK,YACL2N,MAAO,SAoPe8H,GAnPpBnD,EAAErH,eAoPSyK,EAlPNzS,KAoPCsS,UAMGF,eAAL/C,EAAAjK,MApPFpF,KAqPE2R,SAAKZ,EAAAA,QAAmB,GAAAa,QAAA5R,KAAAoS,QApP1BpS,KAqPEmS,SAAKX,EAAAA,QAAkBN,GAAAA,QAAalR,KAAKqS,UAnP3CrS,KAsPE2R,SAAUzV,EAAAA,QAASgU,KAASwC,QArP9B1S,KAsPImS,SAAKH,EAAAA,QAAmBhS,KAAAqS,SAnP1BhD,EAuPE/S,SAAKJ,KAAS0U,MAtPlB5Q,KAuPEqR,OAAA,CAAA,EAtPFrR,KAwPEiR,IAAA7P,UAAAC,IAAA,UAAA,IAtQFrB,KAoPIqR,OAAA,CAAA,CApOR,CACF,EAwPE,CACDtU,IAAE,UACDA,MAAK,SAAcsS,GACnB3E,IAAKsG,EAAEhR,KAELqP,EAAArH,eAAe,EAEfhI,KAAIiS,SAAWjS,KAAI0M,SAxPnB1M,KAyPEmS,SAAcnS,KAAK0M,SAxPrB1M,KA0PEqR,OAAW,CAAA,EAzPb1F,WA0PI2G,WACFtB,EAAAc,SAAA,CAAA,EACFd,EAAAC,IAAA0B,WAAA,CAAA,EAGF3B,EAAAC,IAAA7P,UAAAG,OAAA,UAAA,CACC,EAAA,GAAA,CACDxE,CA1PF,EA2PE2N,CA1PA3N,IA2PE,OA1PF2N,MA2PMwH,SAAc3J,GAEd2J,KAAAA,SACFU,EAAAA,eAAc,EAGF,cAAdvD,EAAOuD,MACT5S,KAAA0M,SAAA2C,EAAAxE,QAAA,GAAA+G,QAAA5R,KAAA2R,SACC3R,KAAAsL,SAAA+D,EAAAxE,QAAA,GAAAgI,QAAA7S,KAAAmS,WAEInS,KAAE0M,SAAS8E,EAAAA,QAAiBxR,KAAM2R,SACjCmB,KAAAA,SAAWxV,EAAAA,QAAgB0C,KAAImS,UAIlCnS,KAAMoS,QAAApS,KAAA0M,SACLhO,KAAKmE,QAAMkB,KAAAA,SACb/D,KAAAiR,IAAA0B,WAAA,CAAA,EAEAjU,KAAKmE,SAAMQ,CAAAA,EACbrD,KAAAwR,aAAAxR,KAAAiR,IAAAjR,KAAA0M,SAAA1M,KAAAsL,QAAA,EAGF,CACA,EAAC,CAEHvO,IAASgW,SACPrI,MAAIsI,SAAmB1J,GACnB2H,IAIElR,EAJCC,KAAOiT,WAIRlT,EAAWnC,EAAAA,QAAWoC,KAAAiR,IAAAG,aAAA,EACxBxT,EAAUyR,EAAAwD,QAAA7S,KAAAiR,IAAAK,cAAA,EACZtR,KAAAwR,aAAAxR,KAAAiR,IAAAmB,EAAAC,CAAA,EACD,CACDpB,EAAG,CAEHlU,IAAIgC,eACFkS,MAAIiC,SAAkBxU,EAAAyU,EAAAC,GACtBnC,EAAIoC,MAAStU,UAAW,eAAAoU,EAAA,OAAAC,EAAA,QAC1B,CAEAnC,EAAG,CAEHlU,IAAK6I,aACHqL,MAAO,WACT,OAAA7S,OAAA+E,YAAA9E,SAAAkG,gBAAAC,aAAAnG,SAAAoD,KAAA+C,WAEA,CApQA,EAqQEyM,EAlbJ,IAAIqC,EAqbEvU,EApbJ,SAkOMuU,EAAcrT,EAAA4Q,GAjOlB,IAkOI0C,EAAAvT,KAGFuS,EAA6B,EAAnBjV,UAAKZ,QAAcmC,KAAAA,IAAAvB,UAAA,GAAAA,UAAA,GAAA,KA3N/B,GANArB,EAmOSkW,KAAatH,CAAU,EAjOhC7K,KAmOIiR,IAAKU,EAlOT3R,KAmOI6Q,MAAKsB,EAlOTnS,KAmOEuS,QAAAA,EAGAvS,KAAIwT,IAAAA,cAEJ,MAAMlX,CAAAA,EAlOR0D,KAqOEqR,OAAA,CAAA,EApOFrR,KAsOIgI,SAAAA,CAAAA,EArOJhI,KAuOE8R,SAAY,CAAA,EAtOd9R,KAuOI0M,SAAW,KAtOf1M,KAuOIsL,SAAQlK,KAtOZpB,KAuOI2R,SAAKT,KAtOTlR,KAuOEmS,SAAA,KACFnS,KAAAoS,QAAA,EACDpS,KAAEqS,QAAA,EACDtV,KAAKkU,IAAA/Q,iBAAS,YAAA,SAAAmP,GACd3E,OAAO6I,EAAAE,UAAkBpE,CAAA,CAtOzB,EAuOE,CAAA,CAAI2B,EAtONhR,KAwOGiR,IAAMjJ,iBAAgB,UAAA,SAAAqH,GACvB,OAAKsC,EAAQc,QAAIpD,CAAA,CAvOnB,EAwOE,CAAA,CAAK8C,EAvOPnS,KAwOEiR,IAAKvE,iBAAe,YAAA,SAAA2C,GACpB,OAAK/D,EAAQoI,KAAGrE,CAAA,CAvOlB,EAwOE,CAAA,CAAKsC,EAvOP3R,KAwOEiR,IAAKkB,iBAAe,QAAA,SAAA9C,GACpB,OAAIkE,EAACnB,MAAWhR,UAAAI,SAAA,cAAA,GAChB+R,EAAKlB,QAAW,EAGPN,CAAAA,GAGPwB,EAAKxB,SAKP,KAAAwB,EAAA1B,UAAA,CAAA0B,EAAAzB,UAEAyB,EAAKI,QAAKC,GANVL,EAAAM,OAAA,CAnOF,EA2OE,CAAA,CAAA,EA1OF7T,KA4OE2L,IAAAA,cAAuB,CAAA,CA3O3B,CA6KAnB,EA8QEsJ,EAAa1P,CAAW,CACxB0P,IAAAA,YACAA,MAAAA,SAA0BzE,GAC1B,IAcM0E,EAdFC,KAAAA,MAAY5S,UAAOmS,SAAMrD,QAAe,IAM9B,eAAVtS,EAAAA,MACFoC,KAAA2R,SAAAtC,EAAAxE,QAAA,GAAA+G,QAAA5R,KAAAoS,QACApS,KAAAmS,SAAA9C,EAAAxE,QAAA,GAAAgI,QAAA7S,KAAAqS,UAEArS,KAAO6Q,SAAMvH,EAAAA,QAActJ,KAAQoS,QAClCpS,KAAAmS,SAAY9C,EAAAwD,QAAA7S,KAAAqS,SAGT0B,EAAI1E,EAAA/S,OAAAsP,SAAmBqI,YAAAA,EAGjC5E,EAAA/S,OAAA8E,UAAAI,SAAA,QAAA,IAAAZ,EAAAyO,EAAA/S,OAAA,SAAA,GAAA,CAAA,IAFc,CAAA,QAAkB2X,SAAAA,WAAsB,SAAA,KAEtDjO,QAAA+N,CAAA,EAEA/T,KAASiU,OAAAA,CAAAA,GAKP5E,EAAArH,eAAA,GAGElF,EAAAA,SAAmB9C,KAAAC,IAAa,QAAb8T,GAAanT,EAAAyO,EAAA/S,OAAA,gBAAA,KAClC0D,KAAAqR,OAAA,CAAA,EACFrR,KAAAC,GAAAmB,UAAAC,IAAA,UAAA,EAEArB,KAASkU,cAAmBnV,EAAMoV,EAAK7X,OAAEsB,mBAAU,GAGjD,CACA,EAAA,CACAb,IAAIqX,UACJ1J,MAAI2J,SAAYhF,GAEZ+E,IAAAA,EAASpU,KAETqU,GAAAA,EAAAA,eAAeC,EACjBtU,KAAA2R,SAAA,EAEA3R,KAAI7B,SAAc,EAChB6B,KAAIoU,SAAQvR,KArRZ7C,KAsREoU,SAAcxR,KAChB5C,KAAA2R,SAAA,KAEA3R,KAAI6N,SAAYxP,KAChBwP,KAAAA,QAAU3L,EACV2L,KAAAA,QAAUzJ,EACViQ,KAAAA,OAAY,CAAA,EAEhBrU,KAAA+R,gBAEIwC,KAAMrY,SAAAsW,oBAAA,CAAA,EACWgC,SAAfC,KAAG1C,eAA2BwC,KAAKrY,SAAAwY,UAAA,EAE7B,QAAN1U,KAAI+R,gBAAE/R,KAAA9D,SAAAyY,UAAA,EAINC,KAAM5C,cACV4C,KAAO/R,SAAMR,MAAStD,EAGtBsV,KAAYT,kBACd5T,KAAAwR,aAAAxR,KAAAkR,cAAA,EAAA,EAAA,CAAA,CAAA,EAIEvF,WAAY,WACdqF,EAAA9U,SAAAsW,oBAAA,CAAA,EAEAQ,EAAWnQ,iBAAoBR,CAAAA,EAC/B2Q,EAAWnQ,cAAkB,KAC7BmQ,EAAW5O,SAAYiQ,CAAAA,EAClB9T,EAAON,GAAA0S,WAAgB4B,CAAAA,EAE1BnV,EAAAA,GAAcgC,UAAShC,OAAAA,UAAgB,EAGrCmU,EAAM3C,MAAOxP,UAAAG,OAAA,cAAA,EAEfyP,EAAAE,cAAArO,MAAAQ,UAAA,GAEEtD,EAAAA,cAAsB8C,MAAAkB,WAAA,EACxBnG,EAAAA,GAAQ,CACV,CAEA,EAAA,CACFb,IAAA,OAEA2N,MAASmK,SAAYhE,GACfmC,GAAAA,KAAAA,OAAanC,CACb5O,EAAAA,eAASqC,EACRtE,KAAM6Q,MAAKzP,UAAAC,IAAA,cAAA,EAEd,cAAAgO,EAAAjK,MACF4N,KAAWvB,SAAW5O,EAAAA,QAAMsO,GAAQS,QAAQxP,KAAKuP,SACjDqB,KAAWvB,SAAW5O,EAAAA,QAAY,GAAG9D,QAAKsD,KAAM8P,WAEhDnS,KAAA0M,SAAA2C,EAAAuC,QAAA5R,KAAA2R,SACF3R,KAAAsL,SAAA+D,EAAAwD,QAAA7S,KAAAmS,UAIQ2C,KAAAA,QAAexX,KAAAA,SAEnBrB,KAAAA,QAAgB+D,KAAM+U,SAElB/U,KAACgV,GAAAA,WAAW,CAAA,EACd7O,KAAM2L,SAAE,CAAA,EACRoB,KAAOnB,cAAE,CAAA,EACTsB,KAAMrB,aAAI,CAAA,EACViD,IAAKhD,EAAI1J,KAAA+D,IAAAtM,KAAA0M,QAAA,EACHwF,EAAE3J,KAAA+D,IAAAtM,KAAAsL,QAAA,EAER4J,GAAe,EAAfA,GAAejD,GAAA1J,KAAA+D,IAAAtM,KAAAsL,QAAA,IAAA,CAAAtL,KAAA+Q,eAAA,KAAA/Q,KAAA+Q,eAAA,CACZ/Q,KAAIqS,QAAA,EACP8C,KAAAA,cAAsB,IACtBC,KAAQ5D,aAAExR,KAAAkR,cAAAlR,KAAA0M,SAAA,CAAA,EACVtK,IAAOkQ,EAAEtS,KAAAqV,aAAA,EAOP/W,GALK,CAAE0B,KAAK9D,SAAAgU,SAAAwC,cAAAJ,IACdgD,KAAUvD,cAAIO,GAIZhU,KAASwW,SAAAA,SAAepC,cAAAJ,EASpB5N,OARDsQ,KAAAA,SAAW3X,oBAAsByX,CAAAA,EACxC9U,KAAA4T,iBAAA,CAAA,EACF5T,KAAAqR,OAAA,CAAA,EAEA7G,KAAauK,SAAAA,oBAAoB,CAAA,EAC1B/U,KAAAyS,QAAY,IAAA,EACD8C,SAATjD,GAAuBtS,KAAE9D,SAAAwY,UAAA,EANzBM,KAOW,QAAZQ,GAAYxV,KAAA9D,SAAAyY,UAAA,EAId,CAGY,EAAVjQ,KAAI2C,YAAM,EAAA6K,GAAAD,GAAAC,IAAA,CAAkElS,KAAK0E,eAAU,KAAA1E,KAAA+Q,iBAC7F/Q,KAAOoS,QAAO,EAChBpS,KAAA+Q,cAAA,IAEIrM,KAAI2C,aAAMrH,KAAAkR,cAAyB,EAAAlR,KAAAsL,QAAA,EACrCsH,EAAc5S,KAAAyV,YAAA,EAGRpO,CAAAA,KAAMnL,SAAAgU,SAAAwC,cAAkCE,IAC9C5S,KAAOgS,aAAO,CAAA,GAIdhS,KAAO9D,SAAOgU,SAAAwC,eAAAE,GAChB5S,KAAA9D,SAAA0U,MAAA,CArSA,CACF,CACF,EAAG,CACD7T,IA6SI,eA5SJ2N,MA6SE,WAEA,IAIKgL,EAJLpD,EAAO,CAAA,EAnSP,OA2SE6C,EAPJ5M,KAAA+D,IAAAtM,KAAA0M,QAAA,GAEkB1M,KAAA2V,aAIC,SAHZD,EAAqBjW,EAAZmW,KAAAA,SAAqB1F,QAAU,SAG3BlQ,KAAA6Q,QAAA7Q,KAAA6Q,MAAAY,WAAAoE,WAAA,SAAAH,GAAA1V,KAAA6Q,QAAA7Q,KAAA6Q,MAAAY,WAAApN,YACJqR,EAKVpD,CA/SN,CACF,EAAG,CACDvV,IAAK,cACL2N,MAgTM,WA/SJ,IAgTEkI,EAAA,CAAA,EASF,OAJAA,EAHgBrK,KAAGlL,IAAO0B,KAAMU,QAAQ,GAErBO,KAAA8V,WACnB,CAAA,EAIInO,CAjTN,CACF,EAAG,CACD5K,IAmTE,eAlTF2N,MAoTM/C,SAAoBjJ,EAAAyU,EAAAC,GAKxBrU,EAAKoH,MAAOzB,WAJO,EAAJpH,UAAIZ,QAAAmC,KAAAA,IAAAvB,UAAA,IAAAA,UAAA,GAIJ,eAES,GAlTxBoB,EAsTEmE,MAAIkT,UAAWtW,eAAoB8J,OAAA4J,EAAA,MAAA,EAAA5J,OAAA6J,EAAA,QAAA,CArTvC,CACF,EAAE,EAnOJ,IAuQE4C,EAqRMA,EA3hBN,SAuQMnT,IACN,IAAA0Q,EAAAvT,KAGM6C,EAA0B,EAApBR,UAAa3F,QAAOmC,KAAAA,IAAAvB,UAAA,GAAAA,UAAA,GAAA,GAKlC2Y,GAFEjD,EAAW1N,KAAa2L,CAAK+B,EAE/BvO,EAAAwR,QAEAC,EAA2BnX,EAAMoV,WAC3BZ,EAAY,KAAA,IAAA2C,EAAA,GAAAA,EAEZC,EAAuB7M,EAAAA,WACvB8M,EAA0B,KAAA,IAAhBC,EAAgB,GAAAA,EAC1BrD,EAAkBvO,EAAC6E,MACnBgN,EAAoBC,KAAAA,IAApBD,EAAmC,KAAAE,EACvC/V,EAAyBgE,EAAAvI,SACzB8W,EAAmC,KAAA,IAAxB1N,EAAwB,KAAAmR,EACnCzW,KAAI8T,GAAAA,EACJpO,KAAAA,OAAa,CAAA,EACb1F,KAAO8R,SAAQ3L,CAAAA,EACfnG,KAAI0W,SAAW3X,KACfiB,KAAI2W,SAAAA,KACJ3D,KAAAA,SAAiB7B,KACjBzL,KAAAA,SAAkBwK,KAChBlQ,KAAK0W,QAAQ,EA1Qb1W,KA2QE0W,QAAW,EACb1W,KAAA4W,UAAA,KAEA5W,KAAK0W,cAAgB,KA3QrB1W,KA4QE0W,WAAWf,EACb3V,KAAA8V,WAAAA,EAEA9V,KAAI0W,iBAAoB,CAAA,EA5QxB1W,KA6QE0W,cAAkB1W,KAAAC,GA5QpBD,KA6QE6Q,MAAWA,EA5Qb7Q,KA6QE6W,SAAQ3a,EA5QV8D,KA6QE6W,GAAI3W,iBAAI,YAAqB,SAAAmP,GAC7BwH,OAAItD,EAAIE,UAAApE,CAAA,CA5QV,EA6QEwH,CAAAA,CAAI,EA5QN7W,KA6QE6W,GAAI3W,iBAAe,UAAA,SAAAmP,GACnBwH,OAAItD,EAAId,QAAApD,CAAA,CA5QV,EA6QEwH,CAAAA,CAAI,EA5QN7W,KA6QE6W,GAAI3W,iBAAc,YAAA,SAAAmP,GAClBsH,OAAAA,EAAAA,KAAiBtH,CAAA,CACnB,EAAA,CAAA,CAAA,CA5QF,CAsSF,SA+UM4E,EAAoB/X,GA9UxB,IA+UImK,EAAIyQ,EAAWnX,EAAOrD,OAACwa,eAAW,EAGnB5G,oBAAfvQ,EAAII,MA/UNU,EAgVIyP,EAAS6G,YAAe,EAGd,mBAhVZpX,EAgVIqX,MA/UNlU,EAgVMuD,EAAA,YAAA,CA9UV,CAiEA,SAASwO,EA4VWhE,EAAO3U,EAAAA,EAAAA,GA3VzB,IA5gDEmJ,EAEAzH,EAEAqE,EAwgDE+Q,EA4VMnC,EAAAvH,cAAA,eAAA,EA3VNrH,GAhhDAwC,EA42DE,CA3VJC,IA6VI3F,EAAAoH,KA5VJvI,SA6VEA,CA5VJ,EAlhDE8G,EAAAD,EAAAC,IAEAW,EAAAA,EAAc4R,MAEdrZ,EAAIqH,EAAgBrH,SAClB6H,EAAAA,EAAaR,UACfhD,EAAA5D,SAAA8D,cAAA,QAAA,GAEA2C,UAAY,qBACd7C,EAAG8C,IAAML,EAETzC,EAAI+C,MAAS5C,MAAA,OA9CbH,EA+CEgD,MAAAA,OAAiB0G,OAEhBuL,GACHjV,EAAAM,aAAA,QAAA2U,CAAA,EAGAjV,EAAI2D,OAAY,WACdC,EAAOrD,OAAO,KACd/B,EAAAwB,EAAA,YAAA,EAGElC,EAAW4F,CAAU,GACvB/H,EAAW+H,CAEb,EAGE+K,GAhDAA,EAiDE9S,YAAUqE,CAAA,EAIdA,GAi/CA+Q,EA8VMD,WAAgBlQ,MAAEiU,SAAa/X,EAAKoV,MA7V1CnB,EA8VQvB,WAAUZ,MAAMvH,OAAavK,EAACsD,OA7VtC2Q,EA+VQ5O,YAAenC,CAACkV,CA7V1B,CA+BE3M,EAkWQ4M,EAAkBC,CAAAA,CAjWxBta,IAkWEua,aAjWF5M,MAmWM4M,SAAoB5S,GAlWxB,IAmWE8Q,EAAO8B,EAKT,GAAsB,QAJtB5S,EAAAA,EAAAmH,YAAA,GAIKuL,MAAAA,4CAAiB,EACpB,MAAOG,QAIT,GAAA7S,EAAO6S,MAAAA,kEAAwE,GAAA7S,EAAA2C,MAAA,8BAAA,GAAA3C,EAAA2C,MAAA,gEAAA,EACjF,MAAA,QAGAqD,GAAKhG,EAAE2C,MAASmQ,sBAAsB,EACpC,MAAIxG,QAnWJ,GAwWc,OAAZtM,EAAA2C,MAAY,sBAAA,EACd,MAAA,QArWA,GAyW6C/K,OAA3C8C,EAAAA,MAAY,yBAAwBO,EAxWpC,MAyWO,QAtWT,GA0We,CAAA,EAAX+E,EAAIsB,QAAO,GAAA,GAIMjH,KAHRyW,EAAKtU,MAAA,GAAA,EAAAuW,IAAA,EAGT1U,KAAAA,EACLtC,MAAAA,SAvWJ,MA2WiC,CAACoL,EA3W9BnH,EA2WIsB,QAAIrG,aAAaiM,EA1WhB,OAGF,UACT,CACF,EAAG,CACD7O,IAAK,cACL2N,MAAO,SA6WgBpK,EAAS4P,GA5W9B,IAAIqD,EA6WIvT,KAEJjB,EAAE1B,EAAA,CACJ8X,aAAAjF,EAAAiF,YACF,EAAEnV,KAAAgV,QAAA,EAEH,GAAA1W,EAAAmB,CAAA,GAAA,CAAAtB,EAAAsB,CAAA,EAYC,OAXGsI,EAAQtI,EAAA,MAAA,IACNsI,EAAAtI,EAAe,SAAG,GAAAA,EAAA2U,QACvB3U,EAAO6U,KAAW,SACpBvM,EAAAtI,EAAA,MAAA,IACCA,EAAA2F,KAAApF,KAAAuV,WAAA9V,EAAA0G,IAAA,IAIOuR,EAASP,EAASpY,EAAQ7C,CAAAA,EAChC8D,KAAA2X,QAAAD,EAAAxH,CAAA,EAEI0H,EAGN,IAwCFC,EAsCU3C,EA9ERxQ,EAAA,GACCD,EAAAhF,EAAAuH,aAAA,gBAAA,EAEI8Q,EAAKrY,EAAAmM,SAAAC,YAAA,EA0EVkM,GAvEkB,MAAlBC,IAEAC,EAAUxY,EAAGyY,MAKF,QAAXlD,IACFnU,EAAUpB,EAAA0Y,IACVC,EAAUC,IAAI5Y,EAAA4Y,KAGdC,EAAAA,KAAa5T,EACb6T,EAASxZ,EAAI,SAAAyZ,EAAAzb,GACb0b,EAAcvI,EAAMnT,CAAA,GAAA,UAAAA,IACpB2b,EAAAA,GAAiBxI,EAAInT,IAGf,IAAEgZ,EAAOtW,EAAAkZ,QAAA5b,GAEf6b,EAAiB7C,CAAM,IACvB8C,EAAAA,GAAkBtF,EAAIuF,cAAA/C,CAAA,EAEtBgD,CAAAA,EAEAC,EAAY5E,UACZ6E,EAAAA,KAAAA,UAGM,CAAAla,EAAKqG,MAAAV,IACX4Q,EAAUlQ,KAAIpF,KAAAuV,WAAA7Q,CAAA,GAGdwU,EAAczU,CAAI,GAqBZ0U,EAAUlE,OAAC,KAAAtN,GAGN/B,EAFLwT,EAAc3Z,EAAEwV,KAEX,GAAA,KAAAA,IACLoE,EAAQpE,MAAKA,GAIflW,EAAAkW,OAAA,OAAAtN,GAGQ/B,EAFVyS,EAAA5Y,EAAA4Y,GAEgB,GAAA,KAAAA,IAClBiB,EAAarE,MAAMoD,KAhCnBR,EAAkB,GAClB0B,EAASxa,EAAI,SAAA0K,EAAAxB,GACbuR,EAAiB1Y,KAAK,QAAAmH,CAAA,CACtBwR,CAAAA,EACA3J,EAAiB4J,EAAIC,KAAA,QAAA,EAEI,KAAzBC,EAAAA,KAAqB,GACdlc,EAAEqB,EAAK,SAAAyZ,EAAAzb,GACR,IAAA8c,EAAApV,EAEAqV,EAAA,IAAAC,OADC,KAAAhd,EAAA,cAAqC2c,EAAA,QACtC,EACIzY,EAAA4Y,EAAAxS,MAAAyS,CAAA,EAEI7Y,GAAEA,EAAAvE,QAAAuE,EAAA,KACDyJ,EAAIzJ,EAAA,GAAA+Y,KAAA,EAAAC,QAAA,QAAA,EAAA,EACbC,EAAWnd,GAAAwW,EAAAuF,cAAApO,CAAA,EAEbyP,CAAAA,GAoBIpb,EAAAmW,aAAA,MAAAnW,EAAAmW,YAAAkF,UAAA,EAAA,CAAA,EAAA,CAGL,IACKlF,EAAA7W,SAAAiL,cAAAvK,EAAAmW,WAAA,EAAAnS,SAKJ,CAJI,MAAE+C,GACH,GAAE,EAAAA,aAAAuU,cACN,MAAAvU,CAEC,CAEDoP,IACDoF,EAAWpF,YAAAA,EAETqF,CAYL,OAVSxb,EAAAmW,cACEsF,EAAM/a,EAAA6J,cAAA,iBAAA,KAGfvK,EAAAmW,YAAAsF,EAAAzX,WAIC0X,KAAM9C,QAAA5Y,EAAAmR,EAAAzQ,CAAA,EACRO,KAAA8W,YAAA/X,CAEFiW,CACAA,EAAAA,CAEI0F,IAAAA,UACFhQ,MAASgQ,SAAgB3b,EAAAmR,GACvB,IAAIyK,EAA8B,EAApBrd,UAAUZ,QAA+BmC,KAAAA,IAAjBvB,UAAY,GAAiBA,UAAY,GAAK,KAEpFrB,EAAsBye,SAAF3b,EAAE2b,KAAc1a,KAAA4a,UAAA1K,EAAA2K,WAAA,EAAA7a,KAAA4a,UAAA1K,EAAA9N,KAAA,EAE/B0Y,EAAgBH,KAAAA,UAAOzK,EAAA7N,MAAA,EAU5BqI,OATA3L,EAAKmR,MAAQnI,EAAG1K,EAAO2X,OAAQ,GAAU,KAAR2F,EAAQvY,MAAApC,KAAA4a,UAAA7b,EAAAqD,KAAA,EAAA2Y,EACzChc,EAAKic,OAAAA,EAAcjc,EAAO,QAACkc,GAAqB,KAArBA,EAAAA,OAAqBjb,KAAA4a,UAAA7b,EAAAsD,MAAA,EAAA6Y,EAE3CC,GAAc,SAAFpc,EAAEqG,OACfrG,EAACqc,gBAAmB3b,CAAAA,CAAAA,EAAKkZ,QAAAvW,MAC/BrD,EAAAsc,iBAAA5b,CAAAA,CAAAA,EAAAkZ,QAAAtW,QAIStD,CAtXP,CACF,EAAG,CACDhC,IAyXE,YAxXF2N,MAyXI,SAAkB/H,GAxXpB,OAyXIxD,EAAW0B,CAAAA,EAAQ,GAAA0I,OAAAtL,EAAA,IAAA,EAAAA,CAxXzB,CACF,EAAG,CACDlB,IAAK,gBACL2N,MA0XM,SAAA8N,GAzXJ,MA0XI,SAAAA,GAAA,UAAAA,EACJA,EAID,SAAAA,CACDzb,CA1XF,EA2XE2N,EAzlBJ,IAAIqK,EA2lBEA,EA1lBJ,SAASA,IACP,IAAID,EA+VsB5Y,EAARoB,UAAQpB,QAAAA,KAAAA,IAAAA,UAAAA,GAAAA,UAAAA,GAAAA,GA7V1BD,EA+VM+D,KAAA+U,CAAA,EA7VN/U,KAAKgV,SAgWW,CA/Vd7O,KAgWM,GA/VN+M,MAAO,GACPG,OAgWQ,GA/VR4B,MAgWI,GA/VJ7P,KAiWI,GAhWJkW,cAiWMC,GAhWNrG,YAiWI,GAhWJmD,IAiWI,GAhWJlD,aAiWE,SACFC,OAAA,GAEAhT,MAAIrC,GAjWJsC,OAkWEkZ,GACFnH,QAAA,CAAA,EACFkB,SAAA,CAAA,EACC6B,UAAA,CAAA,CACDpa,EAEEuB,EAAQhB,CAAUZ,IAClBsD,KAAIqX,SAAYha,EAAGC,KAAS0X,SAAUF,CAAKxX,EAhW/C,CAiNAkN,EA+Xa4N,EAAAA,CAAQ,CA9XnBrb,IA+XI,aA9XJ2N,MAgYI,WA/XF,IAgYIyJ,EAAQnU,KA9XR6Q,EAiYW,EAAJvT,UAAIZ,QAAAmC,KAAAA,IAAAvB,UAAA,GAAAA,UAAA,GAAA,KACXM,EAAA,EAAAN,UAAAZ,QAAAmC,KAAAA,IAAAvB,UAAA,IAAAA,UAAA,GAEJ,GAAAqD,EAAAkQ,EAAA,QAAA,EAEA,MAAKnK,CAAAA,EAIL,IAngBA9I,EAmgBIsS,EAAQlQ,KAAA9D,SAAAgU,SAEZrO,EAAe7B,KAAKkP,YAEhBzN,EAAgBA,EAAI,EAaxBhB,GAVI+a,EAAStL,EAAM6G,eAAA,GACjB7G,EAAIuL,gBAAqB,CACzBA,MAAAA,KAAWrW,MACXqW,MAAAA,EACAA,OAAU,CAAA,CACVpd,CAAAA,EAKOoD,EAAM2D,MAEf3E,EAAeqW,EAAA3B,aAEX8C,EAAYpH,EAAAvH,cAAA,eAAA,EACd7I,EAASpC,EAASoD,cAAM,eAAmB,EAE3Cia,EAAcC,EAAAA,cAAc,cAAO,EACrCC,EAAA/K,EAAAvH,cAAA,cAAA,EAEIiS,EAAgB3d,EAEhBie,EAAKzD,eAAqBpY,KAAEmU,MAC9B1T,EAAS,cAAiBT,KAAAmU,MAG3BpU,EAAMmQ,EAAA6I,cAAA,IACLjW,EAAgB,WAEhBA,EAAiBgZ,CAAAA,GACnBle,EAAA,EAKImC,EAAAA,eAAwB,CACtBoU,MAACjE,EAAS6L,MAChBlL,MAAAA,EAEImL,OAASzI,EAAIrX,SAAKgU,uBAA0BqD,EAAAY,KAAA,CAC9CsF,CAAAA,CACF,GAIA,IAAA3C,EAAA7B,OAAA,IAAA6B,EAAA5B,YACF0G,GACCA,EAAAnK,WAAAA,WAAAwK,YAAAL,EAAAnK,UAAA,GAGK0C,GAAgC7W,KAAxBA,EAAoB2X,OAC5BiH,EAAUC,GAAEhI,EAClB+H,EAAAnZ,UAAA+T,EAAA7B,OAEKiH,EAAWzK,WAAAwK,YAAAC,CAAA,EAIV/H,GAA2C,KAAnC7W,EAAmB4X,aAC3BhN,EAAQ5K,GAAAA,EAIP6W,GAAuB,EAANA,EAAMiI,YACxBxV,EAAcyV,iBAAiB/S,KAAAA,eAAcwN,EAAW5B,YAAAhF,EAAAkM,WAAAlM,EAAAoM,QAAA,EAExD1V,EAAS7D,UAAA+T,EAAAuF,iBACXvZ,KAAAA,kBAAqB4Y,EAAU5E,CAAA,GAG5ByF,EAAAA,UAAiBzF,EAAA5B,aAIpBwG,EAAKc,WAAeC,YAAWvU,CAAM,EAIrCxE,EAASsP,EAAQvB,WAAA,QAAAlI,OAAAmT,CAAA,CAAA,EAEjBjc,EAASmb,EAAQxD,WAAc,eAAC7O,OAAAmT,CAAA,CAAA,GAxZlCjc,EA2ZSuS,EAAW,UAAAzJ,OAAAnE,CAAA,CAAA,EA1ZpB3E,EA2ZIgc,EAAWA,QAAS,EAEpBE,UAAAA,EA72BV,SAiUU9L,EAAA9R,EAAAoV,EAAAvW,GAhUR,IAAI2V,EAiUEvT,KA/TFmW,EAkUSjB,EAAcA,cAAW,mBAAA,EAjUlCkB,EAkUE,SAAAjC,EACFnB,EAAAnC,EAAAvH,cAAA,eAAA,EAEAgN,EAAUpB,KAAAA,cAAa,EAhUvBpB,GAFJrT,EAmUM0V,EAAe1W,kBAAsB,EAlU3CuT,EAoUUwH,aAAUlG,EAAA,oCAAA,EAAAtB,EAAA3O,UAAA,EACP6Q,EAAW5L,cAAYvG,iBAAS,GAEzC2B,GArUJgB,EAoUM1F,KAAAkQ,SAAA0M,KAAAC,IAAA,MAAA,EACF9d,EAAAoH,MAEAuQ,EAAKiB,MAAAA,EAAgC,KAAA,EAAA5Y,EAAAuc,cACrC3E,EAAmB5X,CAAAA,EApUvBiU,EAqUWjU,MAAIoS,SAAApS,EAAAqD,MApUfsD,EAqUE1F,KAAAkQ,SAAA0M,KAAAE,GAAA,OAAA,WAUiB/d,WAJf2X,EADA,EAHF3Z,EADC,CAAA2Z,GAAAhS,EAAA2C,MAAA,sBAAA,EACI,QAGHqP,KAAehS,EAAG3F,MAAKqG,kEAA8DwV,GAAU1K,EAAAA,MAAS9N,8BAAM,GAAAsC,EAAA2C,MAAA,gEAAA,GAC1G6T,UAIJxE,IAAwBA,IArUxBA,EAsUOqG,QASPlG,GADFnM,GANEmM,EADOwE,cAAmB5b,EAAQkZ,MAClC,sCAAApP,OAAAxK,EAAAqD,MAAA,KAAA,EAMK,0FACEsE,gBAAiB6C,OAAGA,EAAOtL,IAAM,EAEzC0Y,EAAArC,EADDuC,GAAA,UACC,GAlVD,IAqVEmG,EAAYrG,GAA2BrC,EAAA,YAAA/K,OAAA6M,EAAA,wBAAA,EAAA7M,OAAAmN,EAAA,wBAAA,EAAAnN,OAAA7E,EAAA,UAAA,CAAA,EAMxCsP,GA5UDvT,EAuUIqT,EAAU,GAAAvK,OAAAmN,EAAA,eAAA,CAAA,EAtUd5C,EAuUE1P,YAAA4Y,CAAA,EAtUFlJ,EAwUYvR,aAAW,UAAA6T,CAAA,EACvBtC,EAAAvR,aAAA,aAAA4R,CAAA,EACCpM,EAAAwL,EAAArD,SAAA0M,KAAA,QAAA,EAAArJ,EAAArD,SAAA0M,KAAAnY,OAAA,IAEHuS,EAAOjC,IAAAA,KAAiB,IAAAqB,EAAApC,CAAA,EACvBgD,EAAAlK,GAAA,QAAA,SAAAnN,GAECmY,EAAQ1B,GAAYzW,EAAAsd,OAAAL,KAEpB3gB,EAAgB2B,CAAMka,GAElBla,EAAQ,CAEZ,CAAA,EACFgH,EAAA,WAEA4F,OAAAA,EAAasN,cAAQ,QAAA,GAAA,QAAAjH,EAAAvH,cAAA,QAAA,EAAAqP,QAAAuE,KACnBngB,EAAG,WACH2N,EAAOyS,OAAAtM,CAASuM,CA3UhB,CAAC,EACDpG,EA6UMnG,GAAAA,kBAAkBnU,CAA0B,EA5UlDsa,EA6UMpZ,GAAAA,iBAAqBlB,CAAcY,CA5U3C,CAAC,CACH,EAkzBmBuT,MAAMnS,KAAIxC,SAAA,CAAA2U,EAAAiG,EAAA9W,KAAAmU,MAAAoH,EAAA,EAIfrf,aAAN2U,EA1ZAgE,EA2ZO7D,MAAOqM,KAAO,CAAAxM,EAAAiG,EAAA9W,KAAAmU,MAAAoH,EAAA,EAOnB,WAAAnW,GAhzBV,SAiVoByL,EAAQ9R,EAACuK,EAAAA,GAhV3B,IAsVI+K,EALAd,EAAI2I,KAEJlJ,EAAanC,EAAGA,cAAMvH,eAAc,EACpCiL,EAAIgH,EAAAA,CAAAA,EAAAA,EAAa,MAAG3d,GAAQmB,CAAAA,EAAAoH,OAAApH,EAAAoH,KAAAjF,MAAA,GAAA,EAAAuW,IAAA,EAAAuC,KAAA,EAC5B5F,EAAIyH,EAAAA,CAAAA,EAAU9c,EAAA,SAAc,GAAGA,CAAAA,EAAKoV,UAAKpV,EAAAqV,QA7T7C,GAjBIA,IACEjU,EAkVMJ,CAAU,IAjVlBsU,EAkVeC,EAAC,iCAAA/K,OAAA6K,EAAA,QAAA,CAAA,GA/UdjW,EAmVIgW,CAAOZ,KACK,QAnVda,EAmVOvR,MAAOD,UAlVhBwR,EAmVI4C,MAAQzD,QAAMrX,UAGpB2R,EAAAxP,SAAA8D,cAAA,KAAA,GAEI2U,UAAiB,mBAnVrBjJ,EAoVM+N,YAAWxH,CAAA,EAnVjBC,EAoVc5C,GAhVd8C,EAoVI2H,CAnVFzH,EAoVEyH,SAAWnZ,eAAY+T,CAAAA,EAlV7B,GAAI,CAACrC,EACH,MAoVE,CAAA,EAjVJ,IAAIG,EAsVM0I,EAAAA,UAAc,CAAA,CAAIpN,EArV5B0E,EAAO/R,MAsVCiU,OAAYuF,EAAAA,OArVpBzH,EAAO/R,MAsVC6Y,SAAU3Y,EAASX,MArV3B3B,EAsVQmU,EAAK4C,kBAAkBkE,EArV/BrH,EAsVaO,CArVf,CAEA,GAAI,CAACP,EAEH,OADAxO,QAsVM6V,MAAAA,wCAA2C3c,CAAA,EAC7C,CAAA,EAnVNiU,EAuVInQ,MAAAR,OAAAtD,EAAAsD,OAtVJ2Q,EAwVIvS,MAASuS,MAAYjU,EAAAqD,MAvVzB4Q,EAwVIvS,YAAgB4T,CAAS,EAvV7BrU,KAyVIO,OAAQ,cAAcgU,GAAA5R,EAAA,QAAA,CAxVxBxD,UAyVIoe,EAAiBld,iBAAgBwQ,iBAAkB,EAxVvDzR,aAyVI,SAAAiQ,GACFA,EAAArH,eAAA,EAvVAuL,EA0VEsB,MAAAA,CAzVJ,CACF,CAAC,EAEG9U,EA2VEmU,CAAiB,GA1VrBtW,EA4VQkZ,CAxVZ,EAovBMjO,MAAA7I,KAAA9D,SAAA,CAAA2U,EAAAiG,EAAA9W,KAAAmU,MAAAoH,EAAA,EAGKiC,EAAAA,WAED,IAAIxH,EAAUuD,CACZtD,OAACwH,EAAatJ,cAAU,gBAAA,EACxBwB,WAAC8H,EAAkBvE,eACzBpD,WAAA5F,EAAA2H,eAEK6F,MAAAA,EACAC,SAAW3d,KAAGyc,QACrB,CAAA,GAOW,UAAPrX,GAjoBFyL,EAkoBAA,EAloBA9R,EAkoBA+X,EAloBA3C,EAkoBAnU,KAAAmU,MAloBAvW,EAkoBA,WAEIgI,IAAAA,EAAUiL,EAACuH,cAAkB,KAAA,EAEjCtB,EAAAK,WAEIsF,IAAAA,EAAiBmB,CAEjBjd,OAAS8b,EACX9G,WAAYzF,EAAAgJ,eACdpD,WAAA5F,EAAA2H,eAEIhH,MAAQA,EACJ3U,SAASkJ,EAAIlJ,QACjB2hB,CAAAA,EAGFpB,EAAWA,UAASxL,EAAAG,aAAAH,EAAA6M,cACpBhH,EAAajG,EAAK,UAACiG,EACnB6F,IAAAA,EAAiB1L,EAAAJ,EAAA,WACjBkN,EAASlN,SAAUsM,OAAA,CACnBnG,CAAAA,GAIMjX,EAAKwb,CAAe,GAC1B5P,EAAW,CAraX,EArPFqH,EAAU5N,EAAQV,cAAK,eAAA,EAxTvBuM,EAyTM,IAAC7L,MACPyW,EAAA,eAAA1H,EAEA6J,EAAKpY,cAAeuO,EAzTxBlD,EAAI/Q,iBA0Te,OAAK,WAClBxC,EAAWE,CAAA,GAzTbA,EA0TI8b,CAxTR,EAAG,CAAA,CA0TGA,EAzTNzI,EAAIkH,IA2TM1T,EAAAA,KAEO,IA3Tb1F,EAAKmU,OA2TiB,IAANzO,EAAM4O,SA1TxBpC,EAAIiC,MA2TQ7L,EAAK6L,MA1TjBjC,EAAIoC,OA2TQyG,EAAKzG,QAxTnBpC,EAAIoH,IAAM,GAELzS,EA4TK7G,EAAAsZ,GAAA,GAAA,KAAAtZ,EAAAsZ,MA3TRpH,EAAIoH,IA4TItZ,EAAAsZ,KAGMpD,KA5TZlW,EA4TEkW,OA3TJhE,EAAI1O,aA4TW,kBAAgBsZ,CAAA,EAIzB,KA7TJ9c,EA6TImW,aA5TNjE,EA6TI1O,aAAA,mBAAAyb,CAAA,EA1TFjf,EA+TIhB,eAAe,iBAAgB,GAAAgB,EAAAge,kBA9TrC9L,EAAIpO,MA+TI9D,MAAUA,EAAGsZ,OAGnBtZ,EAAAhB,eAAA,kBAAA,GAAAgB,EAAAsc,mBA9TFpK,EAgUEpO,MAAQR,OAAC6S,EAAW7S,QA7TxB2Q,EAiUQkC,aAAc7W,EAAAA,EAASiL,UAAmB4L,GAknB5CrE,EAAM3U,CAAoBugB,GAta1BlB,EAuaSwC,CArab,CACF,EAuaE,CACDhhB,IAAE,iBACDA,MAAK,SAAWua,GAChB5M,IAAKvC,EAAuB,EAArB7K,UAASoX,QAAY7V,KAAAA,IAAAvB,UAAA,GAAAA,UAAA,GAAA,GACtB+Z,EAA0B,EAAX/Z,UAAUZ,QAACmC,KAAAA,IAAAvB,UAAA,IAAAA,UAAA,GAChCmX,EAAApW,SAAA8D,cAAA,KAAA,EACCsS,EAAA1R,UAAAuU,EAHD5M,IAKK0M,EAAWzC,EAGf,MAAA2C,GAFCA,EAFc7C,EAAAwJ,UAEM9J,KAAQ,GAE7BzX,QAAAyL,KAIKoP,EAAC2G,EAAkBC,OAAKR,EAAAA,EAAAA,CAAAA,EAGvBvG,IApaL3C,EAyaEN,KACKoD,EAAS,qCAA0BF,EAAA,QALjCE,CAnaX,CACF,EAAG,CACDxa,IA0aE,oBACF2N,MAAA,SAAAqD,EAAAhP,GACC,IAAAiS,EAAAhR,KAEIoe,EAAWC,EAAAA,cAAc,YAAA,EAE5B,GAAIlK,CAAAA,EAEJ,MAAS,CAAA,EAITxR,EAAS,QAAOmV,CAChB3Y,UAAW0R,EAEXzR,aAAgB/B,SAAgBsC,EAAArD,GAE5BgiB,EAAAA,eAAiBC,EACjBC,IAAAA,EAAWngB,SAAQ+Z,KACvBqG,EAAe7d,EAAQtE,EAAA,cAAA,EAEvBmiB,GAAAA,CAAS1Q,EACT0Q,MAAU3H,CAAAA,EAGN4H,EAAAA,UAAgB3f,EAAGmW,YAEnBzU,EAAKmd,EAAAA,YAAiB,EACxB,IAAIzJ,EAAQqK,EAAa,QAAA,CACvBrf,UAAKye,CAAAA,EAAehd,EAACwD,EAAYka,qBAAS,GAC3Clf,aAAM,SAAAO,EAAArD,GACoC+D,MAArCse,EAAAA,OAAgB/S,SAAKgS,YAAgBvd,IACpCud,EAAAA,EAAgBtY,YAAagZ,EACpC7d,EAAAgB,EAAA,cAAA,EAEQsM,EAACmC,UAAgBnR,EAAIsd,iBAE7BrL,EAAAwG,kBAAAzJ,EAAAhP,CAAA,EAGOoV,WAAS,WAChBrR,EAAArB,EAAA,cAAA,CAEKic,EAAAA,GAAAA,EACLkB,EAAiBte,QAAKsd,EAEtBa,CACF,CAAA,CAEA,CAlbA,CAAC,CACH,CACF,EAAG,CACD1hB,IAmbI+Z,SAlbJpM,MAmbIiS,WAlbF,OAmbEoB,EAAa/d,KAAA9D,SAAAgU,SAAA2O,SAAA,CAlbjB,CACF,EAAG,CACD9hB,IAobE,YAnbF2N,MAobI,WACEyJ,EAAOA,KAAK1U,OAAA,GAAAO,KAAAP,QAAA1B,eAAA,WAAA,IAnbdiC,KAobE6Q,QAAO+N,UAAc5e,KAAA9D,SAAAgU,SAAAiH,WAGzB,IAAAS,EAAA,IAAA7C,EAAA/U,KAAA9D,SAAAgU,SAAA+I,oBAAA,EAED,OADDjZ,KAAA8W,YAAAc,EAAAhC,YAAA5V,KAAAP,QAAAO,KAAA9D,SAAAgU,QAAA,EACClQ,KAAA8W,WACD/Z,CAnbF,EAobE2N,EA5pBJ,IAAIoN,EA+pBEA,EA9pBJ,SA6XS6F,EAAAA,EAAAA,EAAkBxJ,GA5XzBlY,EA6XO6iB,KAAAA,CAAoB,EA3X3B9e,KA6XEP,QAAYiH,EA5Xd1G,KA8XE9D,SAAWuD,EA7XbO,KA8XImU,MAAI4K,CA7XV,CAsOF,IAEI9G,EAuba+G,EAAAA,EArbbhD,EA/yDa/U,OAAX8B,EAAW9B,GAAcpI,KAAAA,IAAAR,SAAA4gB,aAAA,iBAAA7gB,QAAA,sBAAAA,QAAA+I,UAAA+X,iBAizD3BrI,GAAOxY,SAubMsW,qBAAW,MAAA,EAAA,GAtbxBK,GAubM,CAtbRnU,SAubM,aAtbNuX,SAwbMvH,KAvbNsO,KAwbI,QAvbJC,MAybI,QAxbJ9G,YAybSyF,CAAAA,EAxbTxF,QA0bQxY,KAzbR0Y,eA0bWvI,CAAAA,EAzbXwI,gBA0bI,CAAA,EAzbJvD,aA0bE,SACF/S,MAAG,QAzbHC,OA0bO,QAzbPwY,YA0bS,QAzbTjC,kBA0biB,KAzbjBC,iBA2bkB,KA1blB9B,gBA2biB,KA1bjBgC,eA2bgB,KA1bhBsG,cA2ba,KA1bbrG,aA2ba,KA1bbC,qBA2bsBiF,KA1btBnC,OA2bMY,KA1bN2C,QA2bMxI,KA1bNyI,KA2bMxB,CAAAA,EA1bNzI,SA2bM0B,CAAAA,EA1bNG,UA2bK,CAAA,EA1bLzE,aA2bQ8M,CAAAA,EA1bRtG,eA2ba,GA1bbrB,eA2ba,GA1bb0B,QA2bMkD,CAAAA,EA1bNjD,gBA2biB,CAAA,EA1bjBC,gBA2bgB,CAAA,EA1bhB3J,gBA2be,CAAA,EA1bfpI,mBA2bmB+X,CAAAA,EA1bnB7F,oBA2bK,CAAA,EA1bL8F,QA4bQ1M,CAAAA,EA3bR4J,KA4bMjZ,CA3bJkZ,IA6bIjB,sCA5bJkB,GA6bE,qCA5bFrY,OA8bE3B,CAEA6c,MAAIzX,OA9bJ0X,WA+bE/d,CA9bAge,QA+bMC,CAAAA,EA9bN5F,UA+bW6F,CAAAA,CA9bb,EACA5F,QAgcI2F,CA/bFE,SAgcUC,CAAAA,EA/bV/Z,IAgcIU,EA/bJuS,SAgcI,EA/bJC,eAicgB,CAhclB,EACA8G,MAicI,CACF7G,OAAE,CAAA,EACH8G,SAAM,CAAA,EACLlL,MAAImL,CAAAA,EACJC,YAAaD,CAAAA,CAhcf,CACF,CACF,EACAE,WAkcQ,OAjcRhH,YAkcM,OAjcNqC,YAmcM9Z,QAlcNya,SAmcQ,WAlcRF,WAmcU0D,GAlcVS,UAmcQ,CAlcNxI,KAocM+H,CAncJU,GAocM/F,SAncNF,IAAK,SACP,EACApK,KAqcM,CApcJqQ,GAqcMV,SApcNvF,IAqcI,SApcN,EACA1J,MAqcE,CAEAlF,GAAAA,eArcA4O,IAscEuF,cArcJ,EACAxF,UAucWzJ,CACX2P,GAAA,cACCjG,IAAA,eACDxd,EACA2N,KAAK,CACH8V,GAAK,OAtcLjG,IAucE,MAtcJ,CACF,EACAkG,IAycI3d,CAxcF8N,MA0cEnQ,ypBAzcFigB,KA2cMpf,iZA1cNmZ,KA2cMkG,iXA1cR,EAEF3L,UA2cc,qbA1cdA,aA2csB8J,miBA7ctB,EAIIpE,IAcFlQ,EA2cMkQ,EAAA,CAAA,CA1cJ3d,IA2cI,OA1cJ2N,MA4cM3K,WA3cJ,IA4cEwT,EAAKrD,KAEHW,EAAW7Q,KAACke,YAAe,EAE5Brd,IA3cDb,KA4cEmU,WAAYA,EAAK,QAAA,CACjBtD,UAAWhQ,EACXmW,aAAayI,SAAuBpQ,EAAA/S,GACnC+S,EAAArH,eAAA,EAGDuL,EAAKuL,KAAAA,CAAAA,CACP6B,CACF,CAAA,GA1cA3gB,KA8cEoY,SAAY1D,KAAAA,YAAUpL,CA7c1B,CACF,EAAG,CACDvM,IA8cIsJ,OA7cJqE,MA+cI5H,WA9cF,IAgdEuD,EAAwB,EAAZ4J,UAAUvT,QAAEmC,KAAAA,IAAAvB,UAAA,GAAAA,UAAA,GAAA,KAExBib,EAAU,EAAAjb,UAAAZ,QAAAmC,KAAAA,IAAAvB,UAAA,GAAAA,UAAA,GAAA,KA/cZ,GAidE,IAAA0C,KAAAoY,SAAA1b,OAEAoG,MAAAA,CAAAA,EAGH9C,KAAA2d,YAAA,KACD5gB,KAAK+hB,qBAAe,KACpBpU,KAAOwT,gBAAS3H,KACd,IAoCM7X,EA9vDYxC,EAlLlBqV,EACAvD,EACAE,EACAE,EA0LA/H,EAEAgI,EAzLAuB,EA2LAtB,EAGAC,EACDuC,EACD3G,EACEqE,EA1LAC,EACAC,EACAkS,EACAjS,EACAkS,EACAjS,EACAC,EACAiS,EACAhS,EACAY,EACAD,EACAF,EACAR,EACAC,EAkMAE,EA9mBKzE,EA8xEL0J,EAAYmC,EAAAA,CAAY,EAAAiC,EAAAvY,KAAAkQ,SAAAqI,QAgCzB9W,GAVGiF,EANGyN,EAdNhW,EAAAsB,CAAA,KACIsf,EAAAtf,EAAAuH,aAAwB,cAAA,KAGvBsP,KAAAA,iBAAoBC,KAAAA,SAEpBxO,KAAIuO,SAAYtW,KAAK+gB,mBAAmB/gB,KAAGoY,SAAA2G,CAAA,GAI/CnZ,EAAYuO,CAAA,KACdA,EAAAnU,KAAAghB,gBAAAvhB,CAAA,GAEK,EACWwhB,EAMZ9M,CAAA,IACFA,EAAA,GAKAnU,KAAIgX,MAAM,EAEVnV,EAAA7B,KAAAkP,QAAA,SAAAlP,KAAAkQ,SAAAoQ,WAAA,OAAAtgB,KAAAkQ,SAAAqQ,UAAAxI,KAAA,EAAA,EAED1Z,SAAAoD,MACI+Z,EAAApd,OAAkB+E,WAAA9E,SAAAkG,gBAAAC,YAEL,EAAZrG,KACEO,EAAa4K,SAAAA,cAAc,OAAA,GAErBlE,KAAA,WAvdVqW,EAwdU/c,UAAKsI,cACfyU,EAAAwC,UAAA,oCAAA1U,OAAAiS,EAAA,KAAA,EACFnd,SAAAmH,KAAApB,YAAAqX,CAAA,EAIAhb,EAAUgB,EAAIuV,kBAAgB,GAGhCvW,EAAAgB,EAAA,gBAAA,EAEA1E,EAAK8Z,GAAA,gBAAgB,EAEf1Y,IACFsC,EAAQpC,SAASiL,KAAAA,kBAAc,EAxd/BtJ,KA2dE6Q,SAAY8K,YAAc,SAI9B9V,KAAAA,UAAYsO,EAAA,CAAA,CAAA,GAGiB,IAAzB6C,KAAAA,SAAWA,QACbA,EAAOkK,KAAMC,WAAA,yBAAA,EAEjB1gB,IAEGqC,EAAE9C,KAAAmhB,WAAiB,yBAAA,EAEpBre,IAJF9C,KAAA8b,WAAA,yBAAA,EAQE9b,KAAAohB,aAAA,CAAA,EAEAphB,KAAI7B,QAAO0S,MAAQ,EAGjB9Q,EAAUC,KAAAkQ,SAAA6L,MAAA,GA/dV/b,KAgeE6Q,SAAYkL,OAAC/U,EAIbgQ,GAAahX,KAACyf,SAAAA,mBA9yDAvjB,EAgzDH8a,MA9yDNzW,OAAOxC,eAAA,OAAA,IArLhBkQ,EAyLES,EAAiB,EAxLnB6C,EAyLE/C,EAAiBpM,MAxLnB4L,EAyLYC,EAAO5L,OApLnBuN,EAJA1B,EAyLEG,CAAAA,EA5KFuS,EADAlS,EADAD,EA0LAD,EARAH,EAFAhI,EA1LA+H,EAyLE,KAvKFU,EADAgS,EAkLF3W,EADC2G,EAAA,EADCvC,EAHAD,EAAA,GA3KAoB,EA8LY,GA7LZD,EA8LW,GA5LXV,EADAQ,EALAV,EADAD,EA6LMgS,EApLN3R,EAgMeZ,SAAUmG,eAAQ,kBAAA,EACjCtF,EAAA7Q,SAAAiL,cAAA,WAAA,EACD6F,EAAA,IAAAC,EAAAH,EAAA,CACDzF,WAAO,SAAoB6F,GACzBnB,EAAImT,CAAAA,GAEJ1gB,EAAA0O,EAAAC,cAAA,GAAAhT,OAAA,kBAAA,GAAAsE,EAAAyO,EAAAC,cAAA,GAAAhT,OAAA,cAAA,GAAA,KAAA+S,EAAAC,cAAA,GAAAhT,OAAAsP,SAAAC,YAAA,KAEAqC,EAAI4C,CAAAA,IAKJ5C,EAFAtN,EAAAyO,EAAAC,cAAA,GAAAhT,OAAA,gBAAA,GAAA,CAAAqE,EAAA0O,EAAAC,cAAA,GAAAhT,OAAAmV,WAAA,eAAA,EAEQmF,CAAAA,EAGN1I,KAEAhS,EAASyY,EAAAA,cAAW,GACtBjF,EAAAzD,MAAAoD,EAAAC,cAAA,GAAArD,MAEAyD,EAAQkH,MAAavH,EAAAC,cAAS,GAAApD,MAC5BqD,EAAIrT,EAAAA,cAAmB,GAAE0V,QAnMzB7C,EAoMEM,EAAOmB,cAAenK,GAAAA,QACxB+H,EAAAlS,EAAAyhB,YAEAzhB,EAAQkS,EAAY9E,cAAA,eAAA,EACtB0F,EAAAZ,EAAA9E,cAAA,gBAAA,EACF+E,EAAA,KAEO9N,EAAO8F,EAAQ,cAAgB,IAC1CgI,EAAAhI,EAAAiD,cAAA,KAAA,GAQ0BgK,KAJNlV,OAAA+E,YAAA9E,SAAAkG,gBAAAC,aAAAnG,SAAAoD,KAAA+C,eAMXyM,EAAQ7C,EAAA9E,cAAA,mBAAA,GAITxG,EAASwe,EAAAA,QAAe,EAE5B,GAAAjS,EAAApD,OAAAoD,EAAApD,MAAA7N,OAAA+E,WAAA,IAKIkM,EAAC3C,eAAe,EAEpB,EACA8C,UAAK2C,SAAe9C,GACpB,GAAK+C,IAIJ3C,EAAQJ,EAAAC,cAAA,GAEPwB,CAAAA,IAAa2B,CAAAA,EAAb,CAID,GAAEzD,GAAMA,EAAAuS,aAAAvT,EAAA,CACL,IAAIwT,EAACthB,EAAiB+L,MAASwD,EAAWxD,MAE1CsH,GAAAA,KAAMI,IAAO6N,CAAE,GAAA,GAEf,MAAO,CAAA,CAGT,CAEA5R,EAAA,CAAA,EAEA,IAqBIqB,EArBAsC,EAAM1B,EAAAA,cAAa0B,GAAMzB,QAC3ByB,EAAMI,EAAAA,cAAS,GAAAd,QACjBhD,EAAAN,EAAAkS,EACCC,EAAM3S,EAAAY,EAwCP,GAtCJpH,KAAA+D,IAAAuD,CAAA,EAAAtH,KAAA+D,IAAAoV,CAAA,EAGK5S,EAAU,EADftE,EAAa8I,CAAAA,GAKTwN,EAASjP,EAFT/C,EAAIyC,CAAAA,GAMJ5C,EAAUc,EAAQxD,MAAAyD,EAAAzD,MAClBgF,EAA+BA,IAAdtC,EAAkB3H,EACnCiK,EAAUE,EAAWF,MAAIG,EAAelF,MACxC+E,EAA0BK,IAAPuP,EAAOvP,EAIxBwP,GAAKtP,IACPvB,EAAA,EAAA1H,KAAA+D,IAAAuU,CAAA,EAAA7S,EAEAkB,EAAUrM,MAACzB,QAAc6O,EAE3B/T,EAAAgU,SAAAJ,mBACClB,EAAA,GAIKE,IACJmB,EAAW7O,EAAAA,KAAUG,IAAAA,CAAO,EAASgQ,EACrClL,EAAKwL,MAAQ5B,QAAQA,EAEhB3E,EAAQ4E,SAAOJ,mBAChBjB,EAAgB,GAKhB,CAAAR,EACF,OAAKkE,EAASlM,EAAA,eAAAkD,OAAAqF,EAAA,UAAA,CAAA,EAGjB3L,EAAAoD,EAAA,eAAAkD,OAAAqF,EAAA,KAAA,EAAArF,OAAAsF,EAAA,OAAA,CAAA,CA7DD,CA8DA9R,EACA2N,SAAO,WACL2E,GAAErH,EA9MF,GAmNA4H,EAAA,CAAA,EAGEyR,GAAiBvQ,EACjBpC,EAAiBF,EAClBoS,EAAMnS,MAxNP,CA6NA,IAAIY,EAAE/S,KAAMgQ,IAAKxF,SAAQ+H,CAAE,CAAA,EACzB8S,EAAKtQ,KAAM/E,IAAGxF,SAAI8H,CAAA,CAAA,EAEpB,GAAAnF,EAAA,GAAAA,GAAA4E,GAKA,OAAI2C,EAAAA,IAAS2Q,EAAI,IAEf3Z,EAAAA,EAAgB,QAAA,EAClBkH,EAAKyC,MAAW1B,QAAKvD,EAChByF,EAAgB7G,CAAQ,GAJ7B,KAAA,EAJFpP,EAAA0U,MAAA,CA7NE,CAeF,EACApF,cAwNWsG,WAvNTnG,WAwNSsF,WAEPD,EAAW5P,CAAAA,CACb,EAAG,EAAA,CACL,EACDqL,gBAAE,WACD1P,EAAW,CAAA,EACX2N,EAAO6D,GAAiB,CAxNxB,EACAlB,MAyNMrF,SAAgB/I,GAxNpB,GA0NE,CAAAoP,GAAeuB,EAzNf,MA0NOlD,CAAAA,EAvNToE,EA0NSpE,CAAAA,EAzNT2B,EA0NS/C,OAAW+D,EAASuS,OAAQzP,EAAQlT,EAAAkR,KAC3C0R,EAAAvT,EAAArP,EAAAkR,KAzNFkR,EA2Nc,CAAA,EAEZQ,GAASlP,GACT0O,EAAa,CAAA,EACbQ,EAAKrQ,EAIN/C,EADFD,EADDE,EADEkS,EAAA,KAIFlW,EAAOnI,aAAmB,QAAA,EAAA,IASzB8L,EAAAxL,MAAAQ,UAAA,WAAAkG,OAHCsY,EAzNWhf,IAwNPuP,EAxNOvP,IA4NZgf,EAAA,IAAA,EAAAtY,OAAAsY,EAAA,MAAA,EACD9kB,EAAK8kB,EACLnX,EA3NAP,UA4NOtH,SAAkBwM,GACzB,IAEKyS,EAiBD7L,EAnBJoL,GAAA,CAAAvQ,IACCiR,EAAAtS,EAAAxD,MAAAyD,EAAAzD,MACI6V,EAAYrS,EAAAvD,MAAAwD,EAAAxD,MAEfwC,IACFqT,GAAArT,GAIDkS,IAEC5K,GAAyB4K,GAIrBnc,EAASnH,EAEbrB,EAAgB6lB,EAEZ7L,EAASxR,eAAa8E,OAAAwY,EAAA,MAAA,EAAAxY,OAAAuY,EAAA,QAAA,EAEtBnM,IACAU,GAAAA,YAAqB5R,OAAOqR,EAAU,IAAA,EAAAvM,OAAAgF,EAAA,MAAA,GAGtCsC,EAAQ2F,EAAkB3T,CAAM,EA/NpC,EAkOA2G,MAAO,SAASvK,GAChB,GAAKoS,CAAAA,EAIL,GAAKM,EACDb,EAAY,CAAA,MADhB,CAKA,GAAqB,QAAhBC,EAAAA,UAAoB,CACrB,GAAC4E,EAAUxB,OAAGwB,EAAUyC,SAAA1b,OAAA,EACvBoZ,OAAAA,EAAuBzP,CAAA,EAGxBnK,EAAS2U,UAAK,CAClB,CAEE,GAAa4C,SAAbxU,EAAOsU,UAAkB,CACxB,GAAM,GAADrX,EAACiY,MACJlU,OAAGC,EAAiBmG,CAAA,EAGrBnK,EAAIgE,UAAAA,CACN,CAhBF,CAiBA,CACF,CAAA,EAEAsK,EAAAA,OAAuB,MAAG2E,IA2gDlB6H,KAAAA,SAAOoB,oBAv3Ebnc,EADOwO,EAy3EHzK,MAx3EJ/D,OAAgB8B,eAAM0M,UAAmB,IAM3CD,EAAAA,OAAaC,SAAAA,EAAqB,UAAA,CAChC1N,UAAUqB,OACVsM,aAAO,SAAsB/K,EAAArD,GAE7B,IAAAS,GADE4C,EAAKmL,GAAShK,OAAKpB,OACrBsiB,QAEAjlB,GAAU,GAALA,EAAK,CACV2N,IAAO7C,EAAanI,SAAS4J,cAAA,eAAA,EAEzB,GAAI,CAACwB,EAAa,CACpB,IAAAhD,EAAAzJ,EAAAA,CAAAA,SAAAyJ,eAAAzJ,CAAAA,SAAAyJ,cAAA8D,WAAAvN,SAAAyJ,cAAA8D,SAAAqW,kBAAA,EAGE,GAAoB,SAAhBna,GAA8B,YAAAA,GAAA,UAAAA,EAChC,MAEJ,CAEDnI,EAAAqI,eAAA,EACInB,EAAUxI,SAAAgC,iBAAA,sBAAA,EAEb,GAAK,CAAAwG,GAASA,EAAKnK,QAAQoO,EACzB,OAIA,GAAA,CAAAjD,EAUN0D,OAPG,MAFCrD,EAAAvB,EAAA,KAIG8D,EAAAA,MAAAA,EACNhK,EAAAyH,EAAA,SAAA,IASQkH,EAAYnP,EADO4H,EAAAb,aAAA,eAAA,CACK,EAC/B/K,EAAgB4L,EAAMuH,SAAY,EAG7BrF,IACAoD,EAAW+U,MAAM9R,EACjBL,EAAUtI,EAAU,SAAK,EAE9B,CAEavH,IAART,GACDvD,EAASgE,UAAAA,EAGR,IAAAnD,GACJb,EAAAwY,UAAA,EAGIrJ,IAAAA,GAEDgF,EAAOO,MAAA,CAGX,CAjHF,CAAC,EAq6EC,CACF,EAAG,CACD7T,IAAK,SACL2N,MAAO,WAGL1K,KAAKkQ,KAAAA,KAFY,EAAP5S,UAAOZ,QAAAmC,KAAAA,IAAAvB,UAAA,GAAAA,UAAA,GAAA,CAEH8a,CAnehB,CACF,EAAG,CACDrb,IAqeIW,YApeJgN,MAqeM,WApeJ,IAsgBM3C,EAjCFiJ,EAAQhR,KAIRye,EAAwB1f,EAAxB0f,UAAU3H,QAAkBjY,KAAAA,IAAAvB,UAAA,GAAAA,UAAA,GAAA,EAC5BmhB,EAA0B,EAA1BA,UAAkB/hB,QAAQmC,KAAAA,IAAAvB,UAAA,IAAAA,UAAA,GAM1BsJ,GA3eJlD,EAueIye,KAAAA,MAAYrhB,EAEhBd,KAAAmU,MAAArN,SAAAqN,CAAA,EAEanU,KAAGmiB,gBAAW7Y,cAAA,UAAA,GAUvBmT,GAPF7V,GAEA9D,EAASsV,EAAS1b,SAAQ,EAve5BsD,KAAKuc,gBA2eQqB,EACP5d,KAAA4d,gBAAAvd,iBAAA,SAAA,EAAA8T,IAGJxT,EAAA8b,EAAA,QAAA,GACFzc,KAAAwc,eAAAC,EAAAvU,CAAA,EAEDvE,EAAA3D,KAAAqd,MAAA,IAED3S,EAAO1K,KAAAqd,MAAS2D,EAGTnQ,EAAKuH,KAAQA,SAAEjE,GACdpM,EAAQ,CACVoM,MAAKnU,KAAImU,MACTtD,MAAO4L,EACTA,UAAAA,EACA3F,YAAAjG,EAAAiG,YAEF6F,WAAY3c,KAAAmU,MACd4J,QAAAlN,EAAAnS,KACCsY,OAAA,IACE,EACHtM,KAAOqT,QAASqE,oBAAcvE,CAAA,EAC5BhN,EAAIwR,SAAajF,WAAAX,EAAA,WAEb6F,EAAItR,EAAKqM,MAAA,EAGTrM,EAAOmM,OAAKjN,EAEZc,EAAIH,eAAkB5Q,EAAUiI,CAAI,EAGpC8I,EAAIyN,QAAS,mBAAqBZ,CAAA,CAhfpC,CAAC,GAGH7d,KAkfIye,iBAAU3H,EAAoBxN,cAAA,qBAAA,EAjflCtJ,KAkfIsiB,0BAAoBtiB,KAAAuiB,kBAAA5hB,EAAAX,KAAAuiB,iBAAA9Q,WAAA,cAAA,EAExBzR,KAAAkQ,SAAAqJ,UAEAvZ,KAAIwiB,aAAarO,EAAA,CAAA,EACjBnU,KAAIa,aAAgB4hB,EAAAA,CAAAA,GAIpBziB,KAAA0d,wBAAA,EAEA1d,KAAKwiB,YAAO/F,CApfd,CACF,EAAG,CACD1f,IAsfEW,eArfFgN,MAsfI,SAAwByJ,GArf1B,IAmgBFsI,EAMI5L,EACJzL,EACCyY,EArBG6E,EAAU1iB,KApfZ,MAAA,EAwfEye,EAAU/f,GAAIyV,EAAKnU,KAAAoY,SAAA1b,OAAA,GAInB+hB,EAAAA,KAAUM,SAAY5K,EAACnN,GAOtBrG,EAFL8b,EAAAzc,KAAA4d,gBAAAvd,iBAAA,SAAA,EAAA8T,GAEyB,QAAA,KAKzB/O,GADIyL,EAAA7Q,KAAAoY,SAAAjE,IACJ/O,KACCyY,EAAA,CACE1J,MAAEA,EACLzJ,MAAO+R,EACLA,UAASvM,EACP4G,YAAYjG,EAAAiG,YACd6F,WAAAxI,EAEA4J,QAAS7N,EAAQxR,KACfsY,OAAO,IACT,EAEAhX,KAAA+d,QAAY7N,oBAAiB2N,CAAA,EAA7B7d,KAED,UAAAoF,GAAA,aAAAA,EACEuG,WAAE,WACAkF,EAAE3U,SAASymB,WAAclG,EAAG,WAC/BiG,EAAY9E,QAAAA,mBAAgBvd,CAAiB,CAC/C,CAAA,CACC,EAAA,GAAA,EAEDqK,EAAOxO,SAAS8iB,WAAAA,EAAsB,WACpC0D,EAAW3E,QAAM,mBAAAF,CAAA,CACnB,CAAA,GAEA9gB,CA1fF,EA2fE2N,CA1fA3N,IA2fE,YA1fF2N,MA4fO,WA3fL1K,KA4fE4iB,UAAS1S,KAASqQ,MAAAA,CAAS,CA3f/B,CACF,EAAG,CACDxjB,IAAK,YACL2N,MA4fI,WACF1K,KAAA4iB,UAAA5iB,KAAAmU,MAAA,CAAA,CA3fF,CACF,EA6fE,CACDpX,IAAE,YACDA,MAAK,WACL2N,IAAKyJ,EAAmB,EAAjB7W,UAAiBZ,QAAAmC,KAAAA,IAAAvB,UAAA,IAAAA,UAAA,GAKtB,GAJA0C,KAAI6iB,gBAAa7iB,KAAA2d,YAEjB3d,KAAI8e,qBAAY9e,KAAAmU,MAEhB,CAAAnU,KAAAuf,KAAA,IAAApL,EAAA,GAAAA,EAAAnU,KAAAoY,SAAA1b,OAAA,GAEA,MAAIomB,CAAAA,EAIF3O,EAAO1C,EA/fP0C,EAggBE4O,KAAAA,SAAmB9iB,OAAG,EACnBsC,GAAavC,KAAAoY,SAAa1b,SAC/ByX,EAAA,GAIFnU,KAAIgjB,UAAUjb,CAAI,CAhgBpB,CACF,EAAG,CACDhL,IAigBEkmB,cAhgBFvY,MAigBEuY,WACAA,IAAAA,EAA4BhJ,EAAhB3c,UAAG2lB,QAAoCC,KAAAA,IAAf5lB,UAAA,GAAwBA,UAAA,GAAA,GAC5D2lB,EAA0BA,EAAd3lB,UAAGgX,QAAwBzV,KAAAA,IAAbokB,UAAa,GAAA3lB,UAAA,GAAA,CAAA,EAMnCuT,GAJAsS,EAAQ9kB,IACZ8V,EAAUnU,KAAGmjB,SAAKzmB,QAGbof,IAAUhE,EAAGqL,EAAM7Z,KAAAA,CAAc,GAClCvK,EAACmQ,EAAUiU,UAAM7Z,EAEjBmV,EAACb,EAAkBvf,GAAAA,CAAQ,EAE3BigB,EAAWzN,EAAC0N,OAAA,EAEhB9d,EAAc0iB,KAAO/K,SAAA1b,OAAoBwT,EA5frC0O,GA8fJH,EAASvO,MAASoI,EAlgBlBmG,EAmgBOle,KAAO,CAAA,EAlgBdke,EAmgBItf,SAAWmZ,EAlgBfmG,EAmgBIrf,YAAcL,EAlgBlBiB,KAAKoY,SAmgBGpQ,OAAAA,EAAgB,EAAAyW,CAAA,EAEJ,MAChBC,EAAA,KAEJ1e,KAAA4d,kBAEItF,EAAAA,EACFA,KAAAA,gBAAuB2D,YAAY3D,CAAAA,GAG7BqG,EAAa3e,KAAA4d,gBAAAvd,iBAAA,SAAA,EAAA8T,GACnBnU,KAAKO,gBAAiBoC,aAAS2b,EAASK,CAAA,IAGpCtP,KAAErH,SAAAA,SAAgB,GAAAhI,KAAAmU,OAAA,GAAAA,GAAAnU,KAAAmU,MAAA,GAAAA,GAAAnU,KAAAmU,MAAA,GAAAA,IArgBpBnU,KAugBE6iB,aAAgB1O,CAAE,EAGxB,IAAAnU,KAAAmU,OAAA,IAAAA,IAEInU,KAAKmhB,MAAAA,GArgBPnhB,KAwgBEZ,wBAAuBA,EAvgBzBwf,EAwgBM5W,KAAgB4V,gBAAAvd,iBAAA,SAAA,EAAA8T,GAvgBtBuK,EAygBWhK,KAAW+K,uBAAAtL,CAAA,EAxgBtBsK,EAygBEhC,UAAAmC,GAIJ5e,KAAI+d,QAAK7N,iBAAS0J,CAChBzF,MAAK5T,EAzgBLsQ,MA0gBE1R,EAzgBFsd,UA0gBErd,EAzgBF0X,YA0gBS+L,EAzgBTlG,WA0gBW/b,EAzgBXmd,QA0gBQ8E,KAzgBR7L,OA0gBM0H,CAzgBR,CAAC,EA4gBG3e,EAAAC,KAAAkQ,SAAAmP,aAAA,GACJrf,KAAAkQ,SAAAmP,cAAA,CAEIlL,MAAMiE,EACRyK,MAAOjF,EAEP/M,OAAM4L,CACN,CAAA,CA1gBJ,CACF,EAAG,CACD1f,IA8gBE,cA7gBF2N,MA8gBIvL,WA7gBF,IA8gBEC,EAAuBA,EAAvBA,UAAc1C,QAAwBmC,KAAAA,IAAfO,UAAe,GAAA9B,UAAA,GAAA,CAAA,EA5gBxC,GA8gBE6W,EAAA,GAAAA,EAAAnU,KAAAoY,SAAA1b,OAAA,EACA,MAAA,CAAA,EAGH,IAAAmU,EAAA7Q,KAAA4d,iBAAA5d,KAAA4d,gBAAAvd,iBAAA,SAAA,EAAA8T,GAEItD,IACCA,KAAKmO,oBAAsB,GAAK1hB,IAC5B6W,GAASnU,KAAK2d,SAAAA,OAAc9M,EAE/BA,KAAK6D,UAAI/T,EAEdX,KAAA2U,UAAA,GAMA9D,EAAIqE,WAAc+G,YAAKsG,CAAAA,GAIvBviB,KAAIuR,SAAQnR,OAAS+T,EAAA,CAAA,EAjhBrBnU,KAkhBES,QAASpC,gBAAe8V,CAAA,EAExBrR,EAAYzE,KAAAA,SAAe2a,YAAA,GAC7BhZ,KAAAkQ,SAAA8I,aAAA7E,CAAA,CAhhBF,CACF,EAAG,CACDpX,IAohBE,iBAnhBF2N,MAqhBMwK,SAAyBA,EAAAA,GAphB7B,IAqhBEkO,EAAAA,KAGEC,EAAOxS,EAAAvH,cAAA,eAAA,EACTsS,EAAY/K,EAAOvH,cAAE,qBAAA,EACnB2W,EAAW,CACb9L,MAAOnU,KAAIojB,qBArhBXvS,MAshBMyS,KAAAA,gBArhBN7G,UAuhBM8G,KAAQrF,gBAthBdvB,WAwhBWpa,KAAAA,gBAvhBXuU,YAyhBalR,EAACrD,KAAAA,oBAAsB,EAAA,KAAcgH,KAAOga,SAASzF,KAAAA,sBAAoBhH,YACtFiH,QAAAnY,EAAA5F,KAAA8e,oBAAA,EAAA,KAAA9e,KAAAoY,SAAApY,KAAA8e,sBAAApgB,KACFsY,OAAAhX,KAAAyf,uBAAAzf,KAAA8e,oBAAA,CAEA,EACEU,EAAYzX,CAEZoM,MAAKwL,KAAOxL,MA1hBZtD,MA2hBM2S,KAAAA,YA1hBN/G,UA2hBMgH,KAAAA,YA1hBN3M,YA2hBa9W,KAAGwjB,SAAAA,KAAcrP,OAAGsP,YA1hBjC9G,WA2hBY3c,KAACuJ,MACbwU,QAAA/d,KAAAoY,SAAApY,KAAAmU,OAAAzV,KAEAsY,OAAI0M,KAAAA,uBAA6B1jB,KAAAmU,KAAA,CA3hBnC,EA+hB0B,EAAxBnB,EAAa2Q,aAAeA,IA5hB5BhgB,EA6hBEwN,CAAWrK,EA3hBb8U,EA6hBM+H,MAAU/gB,QAAS,IA1hB3BE,EA6hBMqO,EAAWnD,KAAAA,cAAqB2V,EA3hBlCzb,EACFrG,EA6hBSgP,EAAA7Q,KAAAkQ,SAAAqQ,UAAAvgB,KAAAkQ,SAAAoQ,YAAA,GAAA,WACLnP,EAAQjB,SAAGpJ,gBACbgZ,EAAAC,gBAAAlP,CAAA,EAIF+S,EAAS7F,QAAQ8F,gBAAgB,CAE7BT,KAAAA,EACFpV,QAASwR,CACX,CAAA,EAGMsE,EAASC,EAAMjG,SAAWjF,gBAAA,GAC9BiH,EAAIkE,SAAUD,iBAAkBlb,MAAAiX,EAAA,CAAAG,EAAAT,EAAA,CA9hBlC,CAAC,GAqiBEyE,EAAA,UADC5hB,EAAQ2hB,KAAUE,SAAAA,aACnBlkB,KAAAkQ,SAAAqQ,UAAAH,GAAA,GAAAA,EAGDpgB,KAAIojB,qBAAmBpjB,KAAAmU,OACI,SAAzBe,KAAAA,SAAY3S,cACd0hB,EAAAjkB,KAAAkQ,SAAAqQ,UAAAjG,UAAA,IA/hBFzY,EAoiBegP,EAACtO,EAAa,WAC3Bud,EAAA5P,SAAAuI,gBACFqH,EAAAC,gBAAAlP,CAAA,EAGHiP,EAAA/B,QAAA,gBAAA,CACItD,KAAQwF,EACNrZ,QAASud,CACV,CAACC,EAENrkB,EAAA+f,EAAA5P,SAAA2I,gBAAA,GACIiH,EAAA5P,SAAA2I,iBAAyBhQ,MAAAiX,EAAA,CAAAG,EAAAT,EAAA,CAE5B,CAAA,GAMA7T,WAASwI,WACP1T,EAAAA,OAAc0gB,CAAAA,CAtiBhB,EAwiBE1gB,GAAAA,EAtiBFA,EAwiBEA,EAAc0gB,SAAAA,CAviBlB,CACF,EAAG,CACDpkB,IAwiBE,kBACF2N,MAAA,WACC,GAAA,CAAA1K,KAAAke,gBACE,MAAQ,CAAA,EAGTqB,IAAI7K,EAAYxE,KAAAA,gBAMZmU,GAJNvhB,EAAA4R,EAAA1U,KAAAgb,cAAA,EAEAje,EAAK2X,EAAO,MAAA,EAEG1U,KAAIkQ,SAAAyL,aAEbgF,EAAoB,SAAdS,EAAcphB,KAAAkQ,SAAAqQ,UAAAjf,GAAAiZ,IAAAjZ,EAxiBxBtB,KAyiBEskB,iBAAiB5P,CAAA,EAxiBnB1U,KAyiBI+d,QAAShhB,sBAAoB,CAxiB/B0d,KAyiBI,CAxiBFtG,MAyiBInU,KAAKO,qBAxiBTsQ,MAyiBE7Q,KAAAke,gBACFzB,UAAAzc,KAAAke,gBAEAvB,WAAW3c,KAAG8e,qBAChBhI,YAAAlR,EAAA5F,KAAA8e,oBAAA,EAAA,KAAA9e,KAAAoY,SAAApY,KAAA8e,sBAAAhI,YAEAiH,QAAOnY,EAAK5F,KAAA8e,oBAAA,EAAA,KAAA9e,KAAAoY,SAAApY,KAAA8e,sBAAApgB,KACdsY,OAAAhX,KAAAyf,uBAAAzf,KAAA8e,oBAAA,CAEA,EACElY,QAAO,CACTuN,MAAAnU,KAAAmU,MAEItD,MAAC0T,KAAU5G,YACXlB,UAAC6H,KAAAA,YAED3H,WAAKvB,KAAAA,MACPtE,YAAa9W,KAAOoY,SAACgD,KAAAA,OAAgBtE,YACvCiH,QAAA/d,KAAAoY,SAAApY,KAAAmU,OAAAzV,KAEIsY,OAAKwN,KAAAA,uBAA4BxkB,KAAAmU,KAAA,CACnCzW,CA9iBF,CAAC,EAijBDqC,EAAAC,KAAAkQ,SAAA0I,iBAAA,GAEAnY,KAAAA,SAAc0iB,kBAAOta,MAAmB7I,KAAC,CAAA,CAEzC6B,MAAAA,KAAeid,qBAEfjd,MAAAA,KAAeqc,gBACbmG,OAAO1G,KAAAA,uBAAkB3d,KAAA8e,oBAAA,CACzBuF,EAAAA,CACAA,MAAOnG,KAAAA,MACPmG,MAAOI,KAAK9G,YAEZ3G,OAAUhX,KAACO,uBAAQP,KAAAmU,KAAA,CAljBnB,EAmjBE,EAhjBAnU,KAmjBE8e,qBAAA9e,KAAAmU,OAAA,SAAAnU,KAAAkQ,SAAAyL,cAljBJgF,EAmjBE3gB,KAAAkQ,SAAAqQ,UAAAjG,UAAAC,KAhjBJ1Y,EAqjBaxD,EAAasiB,EAAA,WAExB7d,IAAAA,EAAgB4R,EAAEpL,cAAiB,mBAAA,EAEnCxG,EAAYrB,EAAM6H,cAAA,eAAA,EAElB+a,EAAOlB,EAAM1R,cAAsB,qBAAc,EAEjD4S,EAAOtG,MAAQ1a,UAAQ,GAEvBgD,EAAItG,MAAAA,UAAkBmQ,GAEtBpN,EAAAuD,EAAA,QAAA,EAIAA,EAAIqe,MAAQzU,QAAA,GAEZlC,IAEAsW,EAAOjD,MAAAA,QAAe,IAG1Bte,EAAA4R,EAAA,MAAA,CACC,CAAA,CACD3X,CA5jBF,EA6jBE2N,CA5jBA3N,IA6jBE,gBA5jBF2N,MA6jBOia,WAEL,OAAI3kB,KAAK4kB,YA7jBX,CACF,EAAG,CA+jBD7nB,IAAA,yBACD2N,MAAE,SAAAyJ,GACDpX,IAAKof,EAAI,SAAAhI,EACJmC,EAAcrX,KAAKrB,cAAU,EAGhC,MAAImK,EAAAA,CAAAA,EAAIuO,EAAKvW,CAAWnC,GAAAA,CAAAA,EAAWue,KAC3B7F,EAAc6F,EA3jBxB,CACF,EAAG,CACDpf,IAgkBI,iBACJ2N,MAAA,SAAAmG,GACC1S,EAAA0S,CAAA,IACInS,EAAMmS,EAAAvH,cAAA,iBAAA,KAGXuH,EAAAnS,EAAAsI,aAAA,YAAA,GAIEnB,QAAIgf,IAAM,oDAAO,EARlB,IAUK9lB,EAAOzB,KAAAA,uBAAwBA,CAAW,EAG1C0Z,GAAMmE,EAAW2J,SACnB9N,EAAO+N,MAAGplB,CAhkBd,CACF,EAAG,CACD5C,IAAK,mBACL2N,MAokBM,SAAUmG,GAnkBV1S,EAokBE6mB,CAAAA,IACFtmB,EAAAmS,EAAAvH,cAAA,iBAAA,KAIA0b,EAAAA,EAAahe,aAAS,YAAA,GAzkB1B,IA6kBAgQ,EAAAhX,KAAAyf,uBAAA5O,CAAA,EAEDmG,GAAAA,EAAA8N,SACE9N,EAAE+N,MAAA,CAnkBL,CACF,EAqkBE,CACDhoB,IAAE,iBACDA,MAAK,SAAS8T,GACT1S,EAAE0S,CAASoU,IACdvmB,EAAOsZ,EAAQ1O,cAAA,iBAAA,KAIZoR,EAAAA,EAAa1T,aAAA,YAAA,GAKhB9K,QAAQgpB,IAAG,mDAA0B,EAVvCxa,IAWMsM,EAAOhX,KAAAyf,uBAAA5O,CAAA,EAEjBmG,GAAA,CAAAA,EAAA8N,SAEOK,EAAAA,KAAS,CAtkBZ,CACF,EAAG,CACDpoB,IAAK,kBACL2N,MAAO,SAAyBmG,GAC9B,IAcImG,GAZAiB,CAAAA,GAAgB,OAACmN,EAAwBplB,KAAKkQ,SAAS0M,KAAKnY,SAAwD2gB,EAAsBC,SAI1IlnB,EAAO0S,CAAK,IACVnS,EAAOmS,EAAMvH,cAAc,iBAAiB,KAG9CuH,EAAQnS,EAAKsI,aAAa,YAAY,GAItCgQ,EAAShX,KAAKyf,uBAAuB5O,CAAK,IAEhC,CAACmG,EAAO8N,UACpB9N,EAAOkK,KAAK,EAERlhB,KAAKkQ,SAASwI,kBAChB1B,EAAOoB,SAASvK,UAAUqU,MAAM,CAGtC,CACF,EAAG,CACDnlB,IAAK,cACL2N,MAAO,SAAqB0N,GAC1B,IAAIkN,EAAStlB,KAGTmiB,GADJniB,KAAKkQ,SAASkI,SAAW,CAAA,EACP,IAEdA,GAAYA,EAAS1b,QACvBgB,EAAK0a,EAAU,SAAUnY,EAAIxD,GAC3B,IAAIoU,EAAQ,IAAIiH,EAAM7X,EAAIqlB,EAAQ7oB,CAAC,EAC/BsC,EAAO8R,EAAM0U,UAAU,EAEvB9G,EAAYphB,EAAO,GAAI0B,CAAI,EAE/B0f,EAAU3H,YAAc/X,EACxB0f,EAAUviB,SAAW2U,EACrB4N,EAAUtK,MAAQ1X,EAClB0lB,EAAYrhB,KAAK2d,CAAS,CAC5B,CAAC,EAGHze,KAAKoY,SAAW+J,EAEZniB,KAAKohB,eACPphB,KAAK4d,gBAAgB7a,UAAY,GAE7B/C,KAAKoY,SAAS1b,UAChBgB,EAAKsC,KAAKoY,SAAU,WAClB,IAAIvH,EAAQyD,EAAWgR,EAAOpV,SAAS2O,SAAS,EAEhDyG,EAAO1H,gBAAgBxZ,YAAYyM,CAAK,CAC1C,CAAC,EAED7Q,KAAKwlB,UAAU,EAAG,CAAA,CAAI,EAG5B,CACF,EAAG,CACDzoB,IAAK,kBACL2N,MAAO,SAAyBhM,GAC9B,IAAIyV,EAAQ,CAAA,EASZ,OAPAzW,EAAKsC,KAAKoY,SAAU,SAAUnY,EAAIxD,GAChC,GAAIsL,EAAI9H,EAAI,MAAM,GAAKA,EAAGvB,MAAQA,EAEhC,OADAyV,EAAQ1X,EACD,CAAA,CAEX,CAAC,EAEM0X,CACT,CACF,EAAG,CACDpX,IAAK,cACL2N,MAAO,WACL,IAAI2X,EAASriB,KAETsiB,EAAO,GAkBPE,GAjBJxiB,KAAKoY,SAAWpY,KAAKoY,UAA2B,GAE5C,CAACxS,EAAM5F,KAAKkQ,SAASkI,QAAQ,GAAKxQ,EAAQ5H,KAAKkQ,SAASkI,QAAQ,GAAKpY,KAAKkQ,SAASkI,SAAS1b,QAC9FgB,EAAKsC,KAAKkQ,SAASkI,SAAU,SAAUnY,EAAIxD,GACzC,IAAIoU,EAAQ,IAAIiH,EAAM7X,EAAIoiB,EAAQ5lB,CAAC,EAC/BgpB,EAAS5U,EAAM0U,UAAU,EAEzB9G,EAAYphB,EAAO,GAAIooB,CAAM,EAEjChH,EAAU/f,KAAO,CAAA,EACjB+f,EAAUtK,MAAQ1X,EAClBgiB,EAAUviB,SAAW2U,EACrB4N,EAAU3H,YAAc2O,EACxBnD,EAAKxhB,KAAK2d,CAAS,CACrB,CAAC,EAGS,CAAA,GAyBZ,OArBE+D,EAHaxiB,KAAKyiB,YAAY,EAGtBpkB,SAASgC,iBAAiBL,KAAKyiB,YAAY,CAAC,EAGjDD,IAIL9kB,EAAK8kB,EAAO,SAAUviB,EAAIxD,GACxB,IAAIoU,EAAQ,IAAIiH,EAAM7X,EAAIoiB,EAAQ5lB,CAAC,EAC/BgpB,EAAS5U,EAAM0U,UAAU,EAEzB9G,EAAYphB,EAAO,GAAIooB,CAAM,EAEjChH,EAAU/f,KAAOuB,EACjBwe,EAAUtK,MAAQ1X,EAClBgiB,EAAUviB,SAAW2U,EACrB4N,EAAU3H,YAAc2O,EACxBhH,EAAUM,QAAU9e,EAAG+G,aAAa,cAAc,EAClDsb,EAAKxhB,KAAK2d,CAAS,CACrB,CAAC,EAEM6D,CACT,CACF,EAAG,CACDvlB,IAAK,qBACL2N,MAAO,SAA4B4X,EAAMvD,GACvC,OAAOuD,EAAKpZ,OAAO,SAAUjJ,GAC3B,OAAOA,EAAG8e,SAAWA,CACvB,CAAC,CACH,CACF,EAAG,CACDhiB,IAAK,cACL2N,MAAO,WACL,MAAI1K,CAAAA,KAAKkQ,SAASkI,WAIdpY,KAAKkQ,SAASrP,UAAsD,SAA1Cb,KAAKkQ,SAASrP,SAASuZ,UAAU,EAAG,CAAC,EAC1D,KAAK7Q,OAAOvJ,KAAKkQ,SAASrP,SAAU,GAAG,EAGzCb,KAAKkQ,SAASrP,SACvB,CACF,EAAG,CACD9D,IAAK,iBACL2N,MAAO,WACL,OAAO1K,KAAK4d,gBAAgBvd,iBAAiB,SAAS,EAAEL,KAAKmU,MAC/D,CACF,EAAG,CACDpX,IAAK,sBACL2N,MAAO,WACL,OAAO1K,KAAKmU,KACd,CACF,EAAG,CACDpX,IAAK,sBACL2N,MAAO,WACL,IAES3N,EAEDqY,EAJJsQ,EAAU,GAEd,IAAS3oB,KAAOiD,KAAKkQ,SAASqQ,UACxBvgB,KAAKkQ,SAASqQ,UAAUxiB,eAAehB,CAAG,IACxCqY,EAASpV,KAAKkQ,SAASqQ,UAAUxjB,GACrC2oB,EAAQ5kB,KAAK,IAAIyI,OAAO6L,EAAW,EAAC,CAAC,EACrCsQ,EAAQ5kB,KAAK,IAAIyI,OAAO6L,EAAOmF,GAAG,CAAC,GAIvC,OAAOmL,EAAQ/L,KAAK,GAAG,CACzB,CACF,EAAG,CACD5c,IAAK,QACL2N,MAAO,WACL,IAAImY,EAAS7iB,KAEb,GAAIA,KAAKykB,MACP,MAAO,CAAA,EAGT,IAAI3B,EAAWzkB,SAASoD,KAAKkkB,WACzB5C,EAAgB,GAShB6C,GAPJloB,EAAKolB,EAAU,SAAU7iB,GACnBA,EAAGwR,YAAcpT,SAASoD,MAAkC,MAA1BxB,EAAG2L,SAASia,OAAO,CAAC,GAAa5lB,EAAG6lB,cAAgB,CAAC7lB,EAAG6lB,aAAa,aAAa,IACtH/C,EAAcjiB,KAAKb,CAAE,EACrBA,EAAGsC,aAAa,cAAe,MAAM,EAEzC,CAAC,EAEawF,EAAI/H,KAAKkQ,SAASuQ,IAAK,MAAM,EAAIzgB,KAAKkQ,SAASuQ,IAAIC,KAAO,IACpEsC,EAAUjb,EAAI/H,KAAKkQ,SAASuQ,IAAK,MAAM,EAAIzgB,KAAKkQ,SAASuQ,IAAIhG,KAAO,GACpEyI,EAAWnb,EAAI/H,KAAKkQ,SAASuQ,IAAK,OAAO,EAAIzgB,KAAKkQ,SAASuQ,IAAI7P,MAAQ,GACvEqS,EAAejjB,KAAKkQ,SAAS+S,aAM7BE,GAFJF,EAAe3O,EADf2O,GADAA,GAAeA,EADAA,EAAahJ,QAAQ,aAAc2L,CAAO,GAC7B3L,QAAQ,aAAc+I,CAAO,GAC7B/I,QAAQ,cAAeiJ,CAAQ,CACrB,EACtC7kB,SAASoD,KAAK2C,YAAY6e,CAAY,EAC1B5kB,SAASmW,eAAe,gBAAgB,GAEhD8D,GADJtY,KAAKmjB,MAAQA,GACW7Z,cAAc,SAAS,EAC/CtJ,KAAKmhB,WAAagC,EAAM7Z,cAAc,QAAQ,EAC9CtJ,KAAK8b,WAAaqH,EAAM7Z,cAAc,QAAQ,EAC9CtJ,KAAKkP,QAAUiU,EAAM7Z,cAAc,WAAW,EAC9CtJ,KAAKqd,OAAS8F,EAAM7Z,cAAc,UAAU,EAC5CtJ,KAAK4d,gBAAkBvf,SAASmW,eAAe,kBAAkB,EACjExU,KAAKwkB,oBAAsBzB,EAC3B/iB,KAAKO,OAAS,GAEdE,EAAST,KAAKmjB,MAAO,aAAenjB,KAAKkQ,SAASiP,IAAI,EAElDnf,KAAKkQ,SAASoI,aAAeA,IAC/BtY,KAAKO,OAAc,MAAIoC,EAAS,QAAS,CACvCxD,UAAWmZ,EACXlZ,aAAc,SAAsBiQ,EAAG/S,GACrC+S,EAAErH,eAAe,EAEjB6a,EAAOjS,MAAM,CACf,CACF,CAAC,GAGC0H,GAAe,CAACtY,KAAKkQ,SAASoI,aAChCA,EAAY7G,WAAWwK,YAAY3D,CAAW,EAG5CtY,KAAK8b,aACP9b,KAAKO,OAAa,KAAIoC,EAAS,QAAS,CACtCxD,UAAWa,KAAK8b,WAChB1c,aAAc,SAAsBiQ,EAAG/S,GACrC+S,EAAErH,eAAe,EAEjB6a,EAAOlO,UAAU,CACnB,CACF,CAAC,GAGC3U,KAAKmhB,aACPnhB,KAAKO,OAAa,KAAIoC,EAAS,QAAS,CACtCxD,UAAWa,KAAKmhB,WAChB/hB,aAAc,SAAsBiQ,EAAG/S,GACrC+S,EAAErH,eAAe,EAEjB6a,EAAOnO,UAAU,CACnB,CACF,CAAC,GAGC1U,KAAKkQ,SAAS0J,sBAChB5Z,KAAKO,OAAiB,SAAIoC,EAAS,QAAS,CAC1CxD,UAAWgkB,EACX/jB,aAAc,SAAsBiQ,EAAG/S,GAChCumB,EAAOrQ,qBAAwB7R,EAAStC,SAASoD,KAAM,kBAAkB,GAAMb,EAAQyO,EAAE/S,OAAQ,mBAAmB,GAClHsE,EAAQyO,EAAE/S,OAAQ,OAAO,GAAMqE,EAAS0O,EAAE/S,OAAQ,OAAO,GAAMqE,EAAS0O,EAAE/S,OAAQ,OAAO,GAC5FumB,EAAOjS,MAAM,CAGnB,CACF,CAAC,GAGHlT,EAAKsC,KAAKoY,SAAU,SAAUvH,EAAOpU,GACnComB,EAAOjF,gBAAgBxZ,YAAYyM,EAAM3U,SAASqiB,OAAO,CAAC,EAE1D1N,EAAM4L,UAAYoG,EAAOjF,gBAAgBvd,iBAAiB,SAAS,EAAE5D,EACvE,CAAC,EAEGuf,GACFvb,EAASpC,SAASoD,KAAM,iBAAiB,EAG3CzB,KAAKO,OAAe,OAAIoC,EAAS,SAAU,CACzCxD,UAAWf,OACXgB,aAAc,WACZyjB,EAAO1F,OAAO,CAChB,CACF,CAAC,EACDnd,KAAKykB,MAAQ,CAAA,CACf,CACF,EAAG,CACD1nB,IAAK,SACL2N,MAAO,WACL,IASIqZ,EAEA7O,EAEAlH,EAYAoV,EAyDIY,EAEAE,EAVNN,EAzEF/S,GAASA,EADsB,EAAnBvT,UAAUZ,QAA+BmC,KAAAA,IAAjBvB,UAAU,GAAmBA,UAAU,GAAK,OAC/D0C,KAAK2d,YAEjB9M,GAASlQ,CAAAA,EAASkQ,EAAO,QAAQ,IAIlC5C,EAAU/K,EAAW,EAErB6gB,EAAQlT,EAAMvH,cAAc,iBAAiB,EAC7C+Z,EAAQxS,EAAMvH,cAAc,eAAe,EAC3C4L,EAAclV,KAAKuiB,iBACnBhR,EAAWtD,EAAQ7L,MACnB4L,EAAYC,EAAQ5L,QAEpBkP,GAAY,IACd9Q,EAEAqC,GAFSzE,SAASoD,KAAM,kBAAkB,EAKvCsiB,GAAUV,KAIXD,EAAoB,CAAA,EAEpBlO,IAAgBvU,EAASuU,EAAa,oBAAoB,GAAKvU,EAASuU,EAAa,iBAAiB,IAAM,CAACvU,EAASuU,EAAa,WAAW,IAChJkO,EAAoB,CAAA,GAGlBC,IACE9R,GAAY,IACA8R,EAAM/Z,cAAc,KAAK,EAC9B8Z,IACLE,EAAapO,EAAYqM,cAEzBgC,EAAWF,EAAM/Z,cAAc,KAAK,GAE/B/G,aAAa,QAAS,4BAA4BgH,OAAO+Z,EAAY,KAAK,CAAC,EAEpFpO,EAAY3S,aAAa,QAAS,cAAcgH,OAAOga,EAASzF,YAAa,KAAK,CAAC,IAInFiG,MACEpE,EAAQ5X,EAAI/H,KAAKkQ,SAAS0M,KAAKnY,OAAQ,OAAO,EAAIzE,KAAKkQ,SAAS0M,KAAKnY,OAAOkb,MAAQ,MAGlF6D,EAAiBO,EAAMvf,YACvBif,EAAkBM,EAAMpf,aAE5Bgb,EAAQ,GAAGpW,OAAOia,GADduC,EAAUvC,EAAiBC,GACa,GAAG,EAAEla,OAAOka,EAAkBsC,CAAO,GAG/ErC,EAAa/D,EAAMze,MAAM,GAAG,EAC5ByiB,EAAa3jB,KAAKkQ,SAAS2K,YAC3B1J,EAAWnR,KAAKkQ,SAAS2K,YAgBzB+I,GAAYzS,EAdZzK,EAASid,CAAU,GAAkC,CAAC,IAA9BA,EAAW3d,QAAQ,IAAI,EACtCc,SAAS6c,CAAU,EAEG,CAAC,IAA9BA,EAAW3d,QAAQ,IAAI,EACduL,EAAWzK,SAAS6c,CAAU,EAAI,IACP,CAAC,IAA9BA,EAAW3d,QAAQ,IAAI,EACrBgI,EAAYlH,SAAS6c,CAAU,EAAI,IACT,CAAC,IAA7BA,EAAW3d,QAAQ,GAAG,EACpBuL,EAAWzK,SAAS6c,CAAU,EAAI,IAElC7c,SAASid,EAAMvf,WAAW,IAIbsC,SAAS4c,EAAW,EAAE,EAAI5c,SAAS4c,EAAW,EAAE,GAC5EE,EAAYrb,KAAKsb,MAAMD,CAAS,EAE5BR,IACFpV,GAAwBkH,EAAYqM,cAGvBhQ,EAAXJ,GAAmCnD,EAAZ4V,GAAyB5V,EAAY4V,GAAwBzS,EAAXI,GACvEuS,EAASC,EAAMjG,YACfkG,EAAUD,EAAMxC,aAQpBwC,EAAMtS,WAAWlP,aAAa,QAAS,cAAcgH,QAJjDyc,EAAQ,CACV5jB,MAAO0hB,GAHLI,EAASlW,EAAYgW,GAIvB3hB,OAAQ2hB,EAAUE,CACpB,GACkE9hB,MAAO,IAAI,CAAC,EAE1EghB,GACFlO,EAAY3S,aAAa,QAAS,cAAcgH,OAAOyc,EAAM5jB,MAAO,KAAK,CAAC,IAG5E2hB,EAAMtS,WAAW5O,MAAMsO,SAAW,GAAG5H,OAAOoa,CAAU,EAElDP,GACFlO,EAAY3S,aAAa,QAAS,cAAcgH,OAAOoa,EAAY,GAAG,CAAC,GAI/E,CACF,EAAG,CACD5mB,IAAK,SACL2N,MAAO,WACL1K,KAAKokB,KAAK,CACZ,CACF,EAAG,CACDrnB,IAAK,0BACL2N,MAAO,WACL,IAAI6U,EAAOvf,KAAKuf,KAAK,EAErBzc,EAAY9C,KAAK8b,WAAY,UAAU,EAEvChZ,EAAY9C,KAAKmhB,WAAY,UAAU,EAErB,GAAdnhB,KAAKmU,OAAcnU,KAAKoY,SAAS1b,OAAS,GAAK,GACjD+D,EAAST,KAAKmhB,WAAY,UAAU,EAEpC1gB,EAAST,KAAK8b,WAAY,UAAU,GACZ,IAAf9b,KAAKmU,OAAgBoL,EAErBvf,KAAKmU,QAAUnU,KAAKoY,SAAS1b,OAAS,GAAM6iB,GACrD9e,EAAST,KAAK8b,WAAY,UAAU,EAFpCrb,EAAST,KAAKmhB,WAAY,UAAU,CAIxC,CACF,EAAG,CACDpkB,IAAK,OACL2N,MAAO,WACL,IAAI6U,EAAOxX,EAAI/H,KAAKkQ,SAAU,WAAW,EAAIlQ,KAAKkQ,SAAS+V,UAAY,KAEvE,OADA1G,EAAOxX,EAAI/H,KAAKkQ,SAAU,MAAM,EAAIlQ,KAAKkQ,SAASqP,KAAOA,CAE3D,CACF,EAAG,CACDxiB,IAAK,QACL2N,MAAO,WACL,IAAI2Z,EAASrkB,KAEb,GAAI,CAACA,KAAKohB,aAAc,CACtB,GAAIphB,KAAKO,OAAQ,CACf,IAAK,IAAIxD,KAAOiD,KAAKO,OACfP,KAAKO,OAAOxC,eAAehB,CAAG,GAChCiD,KAAKO,OAAOxD,GAAKuD,QAAQ,EAI7BN,KAAKO,OAAS,IAChB,CAEA,MAAO,CAAA,CACT,CAEA,GAAIP,KAAKukB,QACP,MAAO,CAAA,EAGTvkB,KAAKukB,QAAU,CAAA,EACfvkB,KAAKskB,iBAAiBtkB,KAAK2d,WAAW,EAElC3d,KAAKob,mBACPpb,KAAKoY,SAAWpY,KAAKob,kBAGnBpb,KAAKwkB,oBAAoB9nB,QAC3BgB,EAAKsC,KAAKwkB,oBAAqB,SAAUvkB,GACvCA,EAAGimB,gBAAgB,aAAa,CAClC,CAAC,EAGHzlB,EAAST,KAAKmjB,MAAO,mBAAmB,EAExCthB,EAAe7B,KAAKkP,QAAqC,QAA5BlP,KAAKkQ,SAASoQ,WAAuB,OAAStgB,KAAKkQ,SAASqQ,UAAUxI,KAAKwC,GAAG,EAE3G1Y,EAAe7B,KAAK2d,YAAa3d,KAAKkQ,SAASqQ,UAAUvgB,KAAKkQ,SAASoJ,aAAaiB,IAAK,WAMvF,GALA8J,EAAO1G,YAAc,KACrB0G,EAAOvF,qBAAuB,KAC9BuF,EAAOnG,gBAAkB,KACzBmG,EAAOI,MAAQ,CAAA,EAEXJ,EAAO9jB,OAAQ,CACjB,IAAK,IAAI4lB,KAAQ9B,EAAO9jB,OAClB8jB,EAAO9jB,OAAOxC,eAAeooB,CAAI,GACnC9B,EAAO9jB,OAAO4lB,GAAM7lB,QAAQ,EAIhC+jB,EAAO9jB,OAAS,IAClB,CAEA,IAAIkB,EAAOpD,SAASoD,KAchBijB,GAZJ5hB,EAAY+T,GAAM,gBAAgB,EAElC/T,EAAYrB,EAAM,sFAAsF,EAExG4iB,EAAOlB,MAAM1R,WAAWwK,YAAYoI,EAAOlB,KAAK,EAEhDkB,EAAOtG,QAAQ,OAAO,EAElBhe,EAAWskB,EAAOnU,SAASoP,OAAO,GACpC+E,EAAOnU,SAASoP,QAAQ,EAGbjhB,SAASiL,cAAc,cAAc,GAE9Cob,GACFA,EAAOjT,WAAWwK,YAAYyI,CAAM,EAGtCL,EAAOjD,aAAe,CAAA,EACtBiD,EAAOE,QAAU,IACnB,CAAC,CACH,CACF,EAAG,CACDxnB,IAAK,UACL2N,MAAO,WACL1K,KAAK4Q,MAAM,EACX5Q,KAAK2kB,eAAe,EAEhB3kB,KAAK4kB,YACP5kB,KAAK4kB,WAAWtkB,QAAQ,CAE5B,CACF,EAAG,CACDvD,IAAK,KACL2N,MAAO,SAAYzL,EAAKrB,GACtB,IAAIiC,EAA0B,EAAnBvC,UAAUZ,QAA+BmC,KAAAA,IAAjBvB,UAAU,IAAmBA,UAAU,GAE1E,GAAI,CAAC2B,GAAO,CAACc,EAAWnC,CAAQ,EAC9B,MAAM,IAAIxB,UAAU,yCAAyC,EAG/D4D,KAAKmb,UAAUra,KAAK,CAClB7B,IAAKA,EACLY,KAAMA,EACNjC,SAAUA,CACZ,CAAC,CACH,CACF,EAAG,CACDb,IAAK,OACL2N,MAAO,SAAczL,EAAKrB,GACxBoC,KAAK8M,GAAG7N,EAAKrB,EAAU,CAAA,CAAI,CAC7B,CACF,EAAG,CACDb,IAAK,UACL2N,MAAO,SAAiBlK,GACtB,IAAIqkB,EAAS7kB,KAETjB,EAA0B,EAAnBzB,UAAUZ,QAA+BmC,KAAAA,IAAjBvB,UAAU,GAAmBA,UAAU,GAAK,KAC3E0nB,EAAgB,GAEpBtnB,EAAKsC,KAAKmb,UAAW,SAAUxb,EAAOlD,GACpC,IAAIwC,EAAMU,EAAMV,IACZY,EAAOF,EAAME,KAGbZ,GAAOuB,KACT5C,EAHa+B,EAAM/B,UAGVmB,CAAI,EAETc,IACFmlB,EAAclkB,KAAKrE,CAAC,CAG1B,CAAC,EAEGuoB,EAActoB,QAChBgB,EAAKsnB,EAAe,SAAUvoB,GAC5B,OAAOooB,EAAO1J,UAAU/a,OAAO3D,EAAG,CAAC,CACrC,CAAC,CAEL,CACF,EAAG,CACDM,IAAK,iBACL2N,MAAO,WACL1K,KAAKmb,UAAU/a,OAAO,EAAGJ,KAAKmb,UAAUze,MAAM,CAChD,CACF,EAAG,CACDK,IAAK,UACL2N,MAAO,WACL,MA1yBS,OA2yBX,CACF,EAAE,EAEKgQ,GA9nCP,SA2cQiC,IA1cN,IAAIhC,EA2c0BmE,EAAxBhI,UAAkBpa,QAA8BmC,KAAAA,IAAxBigB,UAAAA,GAAoC1G,UAAS,GAAK0G,GAzchF7iB,EA2cc+D,KAAKyf,CAAAA,EAzcnBzf,KA2cI4G,cAAS+T,EA1cb3a,KAAKkQ,SA2cQ7S,EAAU2X,GAAA2F,CAAA,EA1cvB3a,KAAKgb,eA2ca2C,KAAAA,oBAAW,EA1c7B3d,KAAKsW,aA2cY,GA1cjBtW,KAAKmb,UA2cW,GA1chBnb,KAAKob,iBA2cmBhD,CAAAA,CA1c1B,CA6nCF,OAPA,WACE,IACIlc,EAAW,IAAIwe,GADc,EAAnBpd,UAAUZ,QAA+BmC,KAAAA,IAAjBvB,UAAU,GAAmBA,UAAU,GAAK,EAC1C,EAExC,OADApB,EAASkoB,KAAK,EACPloB,CACT,CAIF,CAAE", "file": "glightbox.min.js", "sourcesContent": ["(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (global = global || self, global.GLightbox = factory());\n}(this, (function () { 'use strict';\n\n  function _typeof(obj) {\n    \"@babel/helpers - typeof\";\n\n    if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n      _typeof = function (obj) {\n        return typeof obj;\n      };\n    } else {\n      _typeof = function (obj) {\n        return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n      };\n    }\n\n    return _typeof(obj);\n  }\n\n  function _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n      throw new TypeError(\"Cannot call a class as a function\");\n    }\n  }\n\n  function _defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  function _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n  }\n\n  var uid = Date.now();\n  function extend() {\n    var extended = {};\n    var deep = true;\n    var i = 0;\n    var length = arguments.length;\n\n    if (Object.prototype.toString.call(arguments[0]) === '[object Boolean]') {\n      deep = arguments[0];\n      i++;\n    }\n\n    var merge = function merge(obj) {\n      for (var prop in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, prop)) {\n          if (deep && Object.prototype.toString.call(obj[prop]) === '[object Object]') {\n            extended[prop] = extend(true, extended[prop], obj[prop]);\n          } else {\n            extended[prop] = obj[prop];\n          }\n        }\n      }\n    };\n\n    for (; i < length; i++) {\n      var obj = arguments[i];\n      merge(obj);\n    }\n\n    return extended;\n  }\n  function each(collection, callback) {\n    if (isNode(collection) || collection === window || collection === document) {\n      collection = [collection];\n    }\n\n    if (!isArrayLike(collection) && !isObject(collection)) {\n      collection = [collection];\n    }\n\n    if (size(collection) == 0) {\n      return;\n    }\n\n    if (isArrayLike(collection) && !isObject(collection)) {\n      var l = collection.length,\n          i = 0;\n\n      for (; i < l; i++) {\n        if (callback.call(collection[i], collection[i], i, collection) === false) {\n          break;\n        }\n      }\n    } else if (isObject(collection)) {\n      for (var key in collection) {\n        if (has(collection, key)) {\n          if (callback.call(collection[key], collection[key], key, collection) === false) {\n            break;\n          }\n        }\n      }\n    }\n  }\n  function getNodeEvents(node) {\n    var name = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    var fn = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    var cache = node[uid] = node[uid] || [];\n    var data = {\n      all: cache,\n      evt: null,\n      found: null\n    };\n\n    if (name && fn && size(cache) > 0) {\n      each(cache, function (cl, i) {\n        if (cl.eventName == name && cl.fn.toString() == fn.toString()) {\n          data.found = true;\n          data.evt = i;\n          return false;\n        }\n      });\n    }\n\n    return data;\n  }\n  function addEvent(eventName) {\n    var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        onElement = _ref.onElement,\n        withCallback = _ref.withCallback,\n        _ref$avoidDuplicate = _ref.avoidDuplicate,\n        avoidDuplicate = _ref$avoidDuplicate === void 0 ? true : _ref$avoidDuplicate,\n        _ref$once = _ref.once,\n        once = _ref$once === void 0 ? false : _ref$once,\n        _ref$useCapture = _ref.useCapture,\n        useCapture = _ref$useCapture === void 0 ? false : _ref$useCapture;\n\n    var thisArg = arguments.length > 2 ? arguments[2] : undefined;\n    var element = onElement || [];\n\n    if (isString(element)) {\n      element = document.querySelectorAll(element);\n    }\n\n    function handler(event) {\n      if (isFunction(withCallback)) {\n        withCallback.call(thisArg, event, this);\n      }\n\n      if (once) {\n        handler.destroy();\n      }\n    }\n\n    handler.destroy = function () {\n      each(element, function (el) {\n        var events = getNodeEvents(el, eventName, handler);\n\n        if (events.found) {\n          events.all.splice(events.evt, 1);\n        }\n\n        if (el.removeEventListener) {\n          el.removeEventListener(eventName, handler, useCapture);\n        }\n      });\n    };\n\n    each(element, function (el) {\n      var events = getNodeEvents(el, eventName, handler);\n\n      if (el.addEventListener && avoidDuplicate && !events.found || !avoidDuplicate) {\n        el.addEventListener(eventName, handler, useCapture);\n        events.all.push({\n          eventName: eventName,\n          fn: handler\n        });\n      }\n    });\n    return handler;\n  }\n  function addClass(node, name) {\n    each(name.split(' '), function (cl) {\n      return node.classList.add(cl);\n    });\n  }\n  function removeClass(node, name) {\n    each(name.split(' '), function (cl) {\n      return node.classList.remove(cl);\n    });\n  }\n  function hasClass(node, name) {\n    return node.classList.contains(name);\n  }\n  function closest(elem, selector) {\n    while (elem !== document.body) {\n      elem = elem.parentElement;\n\n      if (!elem) {\n        return false;\n      }\n\n      var matches = typeof elem.matches == 'function' ? elem.matches(selector) : elem.msMatchesSelector(selector);\n\n      if (matches) {\n        return elem;\n      }\n    }\n  }\n  function animateElement(element) {\n    var animation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    var callback = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n    if (!element || animation === '') {\n      return false;\n    }\n\n    if (animation === 'none') {\n      if (isFunction(callback)) {\n        callback();\n      }\n\n      return false;\n    }\n\n    var animationEnd = whichAnimationEvent();\n    var animationNames = animation.split(' ');\n    each(animationNames, function (name) {\n      addClass(element, 'g' + name);\n    });\n    addEvent(animationEnd, {\n      onElement: element,\n      avoidDuplicate: false,\n      once: true,\n      withCallback: function withCallback(event, target) {\n        each(animationNames, function (name) {\n          removeClass(target, 'g' + name);\n        });\n\n        if (isFunction(callback)) {\n          callback();\n        }\n      }\n    });\n  }\n  function cssTransform(node) {\n    var translate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n\n    if (translate === '') {\n      node.style.webkitTransform = '';\n      node.style.MozTransform = '';\n      node.style.msTransform = '';\n      node.style.OTransform = '';\n      node.style.transform = '';\n      return false;\n    }\n\n    node.style.webkitTransform = translate;\n    node.style.MozTransform = translate;\n    node.style.msTransform = translate;\n    node.style.OTransform = translate;\n    node.style.transform = translate;\n  }\n  function show(element) {\n    element.style.display = 'block';\n  }\n  function hide(element) {\n    element.style.display = 'none';\n  }\n  function createHTML(htmlStr) {\n    var frag = document.createDocumentFragment(),\n        temp = document.createElement('div');\n    temp.innerHTML = htmlStr;\n\n    while (temp.firstChild) {\n      frag.appendChild(temp.firstChild);\n    }\n\n    return frag;\n  }\n  function windowSize() {\n    return {\n      width: window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth,\n      height: window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight\n    };\n  }\n  function whichAnimationEvent() {\n    var t,\n        el = document.createElement('fakeelement');\n    var animations = {\n      animation: 'animationend',\n      OAnimation: 'oAnimationEnd',\n      MozAnimation: 'animationend',\n      WebkitAnimation: 'webkitAnimationEnd'\n    };\n\n    for (t in animations) {\n      if (el.style[t] !== undefined) {\n        return animations[t];\n      }\n    }\n  }\n  function whichTransitionEvent() {\n    var t,\n        el = document.createElement('fakeelement');\n    var transitions = {\n      transition: 'transitionend',\n      OTransition: 'oTransitionEnd',\n      MozTransition: 'transitionend',\n      WebkitTransition: 'webkitTransitionEnd'\n    };\n\n    for (t in transitions) {\n      if (el.style[t] !== undefined) {\n        return transitions[t];\n      }\n    }\n  }\n  function createIframe(config) {\n    var url = config.url,\n        allow = config.allow,\n        callback = config.callback,\n        appendTo = config.appendTo;\n    var iframe = document.createElement('iframe');\n    iframe.className = 'vimeo-video gvideo';\n    iframe.src = url;\n    iframe.style.width = '100%';\n    iframe.style.height = '100%';\n\n    if (allow) {\n      iframe.setAttribute('allow', allow);\n    }\n\n    iframe.onload = function () {\n      iframe.onload = null;\n      addClass(iframe, 'node-ready');\n\n      if (isFunction(callback)) {\n        callback();\n      }\n    };\n\n    if (appendTo) {\n      appendTo.appendChild(iframe);\n    }\n\n    return iframe;\n  }\n  function waitUntil(check, onComplete, delay, timeout) {\n    if (check()) {\n      onComplete();\n      return;\n    }\n\n    if (!delay) {\n      delay = 100;\n    }\n\n    var timeoutPointer;\n    var intervalPointer = setInterval(function () {\n      if (!check()) {\n        return;\n      }\n\n      clearInterval(intervalPointer);\n\n      if (timeoutPointer) {\n        clearTimeout(timeoutPointer);\n      }\n\n      onComplete();\n    }, delay);\n\n    if (timeout) {\n      timeoutPointer = setTimeout(function () {\n        clearInterval(intervalPointer);\n      }, timeout);\n    }\n  }\n  function injectAssets(url, waitFor, callback) {\n    if (isNil(url)) {\n      console.error('Inject assets error');\n      return;\n    }\n\n    if (isFunction(waitFor)) {\n      callback = waitFor;\n      waitFor = false;\n    }\n\n    if (isString(waitFor) && waitFor in window) {\n      if (isFunction(callback)) {\n        callback();\n      }\n\n      return;\n    }\n\n    var found;\n\n    if (url.indexOf('.css') !== -1) {\n      found = document.querySelectorAll('link[href=\"' + url + '\"]');\n\n      if (found && found.length > 0) {\n        if (isFunction(callback)) {\n          callback();\n        }\n\n        return;\n      }\n\n      var head = document.getElementsByTagName('head')[0];\n      var headStyles = head.querySelectorAll('link[rel=\"stylesheet\"]');\n      var link = document.createElement('link');\n      link.rel = 'stylesheet';\n      link.type = 'text/css';\n      link.href = url;\n      link.media = 'all';\n\n      if (headStyles) {\n        head.insertBefore(link, headStyles[0]);\n      } else {\n        head.appendChild(link);\n      }\n\n      if (isFunction(callback)) {\n        callback();\n      }\n\n      return;\n    }\n\n    found = document.querySelectorAll('script[src=\"' + url + '\"]');\n\n    if (found && found.length > 0) {\n      if (isFunction(callback)) {\n        if (isString(waitFor)) {\n          waitUntil(function () {\n            return typeof window[waitFor] !== 'undefined';\n          }, function () {\n            callback();\n          });\n          return false;\n        }\n\n        callback();\n      }\n\n      return;\n    }\n\n    var script = document.createElement('script');\n    script.type = 'text/javascript';\n    script.src = url;\n\n    script.onload = function () {\n      if (isFunction(callback)) {\n        if (isString(waitFor)) {\n          waitUntil(function () {\n            return typeof window[waitFor] !== 'undefined';\n          }, function () {\n            callback();\n          });\n          return false;\n        }\n\n        callback();\n      }\n    };\n\n    document.body.appendChild(script);\n  }\n  function isMobile() {\n    return 'navigator' in window && window.navigator.userAgent.match(/(iPad)|(iPhone)|(iPod)|(Android)|(PlayBook)|(BB10)|(BlackBerry)|(Opera Mini)|(IEMobile)|(webOS)|(MeeGo)/i);\n  }\n  function isTouch() {\n    return isMobile() !== null || document.createTouch !== undefined || 'ontouchstart' in window || 'onmsgesturechange' in window || navigator.msMaxTouchPoints;\n  }\n  function isFunction(f) {\n    return typeof f === 'function';\n  }\n  function isString(s) {\n    return typeof s === 'string';\n  }\n  function isNode(el) {\n    return !!(el && el.nodeType && el.nodeType == 1);\n  }\n  function isArray(ar) {\n    return Array.isArray(ar);\n  }\n  function isArrayLike(ar) {\n    return ar && ar.length && isFinite(ar.length);\n  }\n  function isObject(o) {\n    var type = _typeof(o);\n\n    return type === 'object' && o != null && !isFunction(o) && !isArray(o);\n  }\n  function isNil(o) {\n    return o == null;\n  }\n  function has(obj, key) {\n    return obj !== null && hasOwnProperty.call(obj, key);\n  }\n  function size(o) {\n    if (isObject(o)) {\n      if (o.keys) {\n        return o.keys().length;\n      }\n\n      var l = 0;\n\n      for (var k in o) {\n        if (has(o, k)) {\n          l++;\n        }\n      }\n\n      return l;\n    } else {\n      return o.length;\n    }\n  }\n  function isNumber(n) {\n    return !isNaN(parseFloat(n)) && isFinite(n);\n  }\n\n  function getNextFocusElement() {\n    var current = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : -1;\n    var btns = document.querySelectorAll('.gbtn[data-taborder]:not(.disabled)');\n\n    if (!btns.length) {\n      return false;\n    }\n\n    if (btns.length == 1) {\n      return btns[0];\n    }\n\n    if (typeof current == 'string') {\n      current = parseInt(current);\n    }\n\n    var orders = [];\n    each(btns, function (btn) {\n      orders.push(btn.getAttribute('data-taborder'));\n    });\n    var highestOrder = Math.max.apply(Math, orders.map(function (order) {\n      return parseInt(order);\n    }));\n    var newIndex = current < 0 ? 1 : current + 1;\n\n    if (newIndex > highestOrder) {\n      newIndex = '1';\n    }\n\n    var nextOrders = orders.filter(function (el) {\n      return el >= parseInt(newIndex);\n    });\n    var nextFocus = nextOrders.sort()[0];\n    return document.querySelector(\".gbtn[data-taborder=\\\"\".concat(nextFocus, \"\\\"]\"));\n  }\n\n  function keyboardNavigation(instance) {\n    if (instance.events.hasOwnProperty('keyboard')) {\n      return false;\n    }\n\n    instance.events['keyboard'] = addEvent('keydown', {\n      onElement: window,\n      withCallback: function withCallback(event, target) {\n        event = event || window.event;\n        var key = event.keyCode;\n\n        if (key == 9) {\n          var focusedButton = document.querySelector('.gbtn.focused');\n\n          if (!focusedButton) {\n            var activeElement = document.activeElement && document.activeElement.nodeName ? document.activeElement.nodeName.toLocaleLowerCase() : false;\n\n            if (activeElement == 'input' || activeElement == 'textarea' || activeElement == 'button') {\n              return;\n            }\n          }\n\n          event.preventDefault();\n          var btns = document.querySelectorAll('.gbtn[data-taborder]');\n\n          if (!btns || btns.length <= 0) {\n            return;\n          }\n\n          if (!focusedButton) {\n            var first = getNextFocusElement();\n\n            if (first) {\n              first.focus();\n              addClass(first, 'focused');\n            }\n\n            return;\n          }\n\n          var currentFocusOrder = focusedButton.getAttribute('data-taborder');\n          var nextFocus = getNextFocusElement(currentFocusOrder);\n          removeClass(focusedButton, 'focused');\n\n          if (nextFocus) {\n            nextFocus.focus();\n            addClass(nextFocus, 'focused');\n          }\n        }\n\n        if (key == 39) {\n          instance.nextSlide();\n        }\n\n        if (key == 37) {\n          instance.prevSlide();\n        }\n\n        if (key == 27) {\n          instance.close();\n        }\n      }\n    });\n  }\n\n  function getLen(v) {\n    return Math.sqrt(v.x * v.x + v.y * v.y);\n  }\n\n  function dot(v1, v2) {\n    return v1.x * v2.x + v1.y * v2.y;\n  }\n\n  function getAngle(v1, v2) {\n    var mr = getLen(v1) * getLen(v2);\n\n    if (mr === 0) {\n      return 0;\n    }\n\n    var r = dot(v1, v2) / mr;\n\n    if (r > 1) {\n      r = 1;\n    }\n\n    return Math.acos(r);\n  }\n\n  function cross(v1, v2) {\n    return v1.x * v2.y - v2.x * v1.y;\n  }\n\n  function getRotateAngle(v1, v2) {\n    var angle = getAngle(v1, v2);\n\n    if (cross(v1, v2) > 0) {\n      angle *= -1;\n    }\n\n    return angle * 180 / Math.PI;\n  }\n\n  var EventsHandlerAdmin = function () {\n    function EventsHandlerAdmin(el) {\n      _classCallCheck(this, EventsHandlerAdmin);\n\n      this.handlers = [];\n      this.el = el;\n    }\n\n    _createClass(EventsHandlerAdmin, [{\n      key: \"add\",\n      value: function add(handler) {\n        this.handlers.push(handler);\n      }\n    }, {\n      key: \"del\",\n      value: function del(handler) {\n        if (!handler) {\n          this.handlers = [];\n        }\n\n        for (var i = this.handlers.length; i >= 0; i--) {\n          if (this.handlers[i] === handler) {\n            this.handlers.splice(i, 1);\n          }\n        }\n      }\n    }, {\n      key: \"dispatch\",\n      value: function dispatch() {\n        for (var i = 0, len = this.handlers.length; i < len; i++) {\n          var handler = this.handlers[i];\n\n          if (typeof handler === 'function') {\n            handler.apply(this.el, arguments);\n          }\n        }\n      }\n    }]);\n\n    return EventsHandlerAdmin;\n  }();\n\n  function wrapFunc(el, handler) {\n    var EventshandlerAdmin = new EventsHandlerAdmin(el);\n    EventshandlerAdmin.add(handler);\n    return EventshandlerAdmin;\n  }\n\n  var TouchEvents = function () {\n    function TouchEvents(el, option) {\n      _classCallCheck(this, TouchEvents);\n\n      this.element = typeof el == 'string' ? document.querySelector(el) : el;\n      this.start = this.start.bind(this);\n      this.move = this.move.bind(this);\n      this.end = this.end.bind(this);\n      this.cancel = this.cancel.bind(this);\n      this.element.addEventListener('touchstart', this.start, false);\n      this.element.addEventListener('touchmove', this.move, false);\n      this.element.addEventListener('touchend', this.end, false);\n      this.element.addEventListener('touchcancel', this.cancel, false);\n      this.preV = {\n        x: null,\n        y: null\n      };\n      this.pinchStartLen = null;\n      this.zoom = 1;\n      this.isDoubleTap = false;\n\n      var noop = function noop() {};\n\n      this.rotate = wrapFunc(this.element, option.rotate || noop);\n      this.touchStart = wrapFunc(this.element, option.touchStart || noop);\n      this.multipointStart = wrapFunc(this.element, option.multipointStart || noop);\n      this.multipointEnd = wrapFunc(this.element, option.multipointEnd || noop);\n      this.pinch = wrapFunc(this.element, option.pinch || noop);\n      this.swipe = wrapFunc(this.element, option.swipe || noop);\n      this.tap = wrapFunc(this.element, option.tap || noop);\n      this.doubleTap = wrapFunc(this.element, option.doubleTap || noop);\n      this.longTap = wrapFunc(this.element, option.longTap || noop);\n      this.singleTap = wrapFunc(this.element, option.singleTap || noop);\n      this.pressMove = wrapFunc(this.element, option.pressMove || noop);\n      this.twoFingerPressMove = wrapFunc(this.element, option.twoFingerPressMove || noop);\n      this.touchMove = wrapFunc(this.element, option.touchMove || noop);\n      this.touchEnd = wrapFunc(this.element, option.touchEnd || noop);\n      this.touchCancel = wrapFunc(this.element, option.touchCancel || noop);\n      this.translateContainer = this.element;\n      this._cancelAllHandler = this.cancelAll.bind(this);\n      window.addEventListener('scroll', this._cancelAllHandler);\n      this.delta = null;\n      this.last = null;\n      this.now = null;\n      this.tapTimeout = null;\n      this.singleTapTimeout = null;\n      this.longTapTimeout = null;\n      this.swipeTimeout = null;\n      this.x1 = this.x2 = this.y1 = this.y2 = null;\n      this.preTapPosition = {\n        x: null,\n        y: null\n      };\n    }\n\n    _createClass(TouchEvents, [{\n      key: \"start\",\n      value: function start(evt) {\n        if (!evt.touches) {\n          return;\n        }\n\n        var ignoreDragFor = ['a', 'button', 'input'];\n\n        if (evt.target && evt.target.nodeName && ignoreDragFor.indexOf(evt.target.nodeName.toLowerCase()) >= 0) {\n          console.log('ignore drag for this touched element', evt.target.nodeName.toLowerCase());\n          return;\n        }\n\n        this.now = Date.now();\n        this.x1 = evt.touches[0].pageX;\n        this.y1 = evt.touches[0].pageY;\n        this.delta = this.now - (this.last || this.now);\n        this.touchStart.dispatch(evt, this.element);\n\n        if (this.preTapPosition.x !== null) {\n          this.isDoubleTap = this.delta > 0 && this.delta <= 250 && Math.abs(this.preTapPosition.x - this.x1) < 30 && Math.abs(this.preTapPosition.y - this.y1) < 30;\n\n          if (this.isDoubleTap) {\n            clearTimeout(this.singleTapTimeout);\n          }\n        }\n\n        this.preTapPosition.x = this.x1;\n        this.preTapPosition.y = this.y1;\n        this.last = this.now;\n        var preV = this.preV,\n            len = evt.touches.length;\n\n        if (len > 1) {\n          this._cancelLongTap();\n\n          this._cancelSingleTap();\n\n          var v = {\n            x: evt.touches[1].pageX - this.x1,\n            y: evt.touches[1].pageY - this.y1\n          };\n          preV.x = v.x;\n          preV.y = v.y;\n          this.pinchStartLen = getLen(preV);\n          this.multipointStart.dispatch(evt, this.element);\n        }\n\n        this._preventTap = false;\n        this.longTapTimeout = setTimeout(function () {\n          this.longTap.dispatch(evt, this.element);\n          this._preventTap = true;\n        }.bind(this), 750);\n      }\n    }, {\n      key: \"move\",\n      value: function move(evt) {\n        if (!evt.touches) {\n          return;\n        }\n\n        var preV = this.preV,\n            len = evt.touches.length,\n            currentX = evt.touches[0].pageX,\n            currentY = evt.touches[0].pageY;\n        this.isDoubleTap = false;\n\n        if (len > 1) {\n          var sCurrentX = evt.touches[1].pageX,\n              sCurrentY = evt.touches[1].pageY;\n          var v = {\n            x: evt.touches[1].pageX - currentX,\n            y: evt.touches[1].pageY - currentY\n          };\n\n          if (preV.x !== null) {\n            if (this.pinchStartLen > 0) {\n              evt.zoom = getLen(v) / this.pinchStartLen;\n              this.pinch.dispatch(evt, this.element);\n            }\n\n            evt.angle = getRotateAngle(v, preV);\n            this.rotate.dispatch(evt, this.element);\n          }\n\n          preV.x = v.x;\n          preV.y = v.y;\n\n          if (this.x2 !== null && this.sx2 !== null) {\n            evt.deltaX = (currentX - this.x2 + sCurrentX - this.sx2) / 2;\n            evt.deltaY = (currentY - this.y2 + sCurrentY - this.sy2) / 2;\n          } else {\n            evt.deltaX = 0;\n            evt.deltaY = 0;\n          }\n\n          this.twoFingerPressMove.dispatch(evt, this.element);\n          this.sx2 = sCurrentX;\n          this.sy2 = sCurrentY;\n        } else {\n          if (this.x2 !== null) {\n            evt.deltaX = currentX - this.x2;\n            evt.deltaY = currentY - this.y2;\n            var movedX = Math.abs(this.x1 - this.x2),\n                movedY = Math.abs(this.y1 - this.y2);\n\n            if (movedX > 10 || movedY > 10) {\n              this._preventTap = true;\n            }\n          } else {\n            evt.deltaX = 0;\n            evt.deltaY = 0;\n          }\n\n          this.pressMove.dispatch(evt, this.element);\n        }\n\n        this.touchMove.dispatch(evt, this.element);\n\n        this._cancelLongTap();\n\n        this.x2 = currentX;\n        this.y2 = currentY;\n\n        if (len > 1) {\n          evt.preventDefault();\n        }\n      }\n    }, {\n      key: \"end\",\n      value: function end(evt) {\n        if (!evt.changedTouches) {\n          return;\n        }\n\n        this._cancelLongTap();\n\n        var self = this;\n\n        if (evt.touches.length < 2) {\n          this.multipointEnd.dispatch(evt, this.element);\n          this.sx2 = this.sy2 = null;\n        }\n\n        if (this.x2 && Math.abs(this.x1 - this.x2) > 30 || this.y2 && Math.abs(this.y1 - this.y2) > 30) {\n          evt.direction = this._swipeDirection(this.x1, this.x2, this.y1, this.y2);\n          this.swipeTimeout = setTimeout(function () {\n            self.swipe.dispatch(evt, self.element);\n          }, 0);\n        } else {\n          this.tapTimeout = setTimeout(function () {\n            if (!self._preventTap) {\n              self.tap.dispatch(evt, self.element);\n            }\n\n            if (self.isDoubleTap) {\n              self.doubleTap.dispatch(evt, self.element);\n              self.isDoubleTap = false;\n            }\n          }, 0);\n\n          if (!self.isDoubleTap) {\n            self.singleTapTimeout = setTimeout(function () {\n              self.singleTap.dispatch(evt, self.element);\n            }, 250);\n          }\n        }\n\n        this.touchEnd.dispatch(evt, this.element);\n        this.preV.x = 0;\n        this.preV.y = 0;\n        this.zoom = 1;\n        this.pinchStartLen = null;\n        this.x1 = this.x2 = this.y1 = this.y2 = null;\n      }\n    }, {\n      key: \"cancelAll\",\n      value: function cancelAll() {\n        this._preventTap = true;\n        clearTimeout(this.singleTapTimeout);\n        clearTimeout(this.tapTimeout);\n        clearTimeout(this.longTapTimeout);\n        clearTimeout(this.swipeTimeout);\n      }\n    }, {\n      key: \"cancel\",\n      value: function cancel(evt) {\n        this.cancelAll();\n        this.touchCancel.dispatch(evt, this.element);\n      }\n    }, {\n      key: \"_cancelLongTap\",\n      value: function _cancelLongTap() {\n        clearTimeout(this.longTapTimeout);\n      }\n    }, {\n      key: \"_cancelSingleTap\",\n      value: function _cancelSingleTap() {\n        clearTimeout(this.singleTapTimeout);\n      }\n    }, {\n      key: \"_swipeDirection\",\n      value: function _swipeDirection(x1, x2, y1, y2) {\n        return Math.abs(x1 - x2) >= Math.abs(y1 - y2) ? x1 - x2 > 0 ? 'Left' : 'Right' : y1 - y2 > 0 ? 'Up' : 'Down';\n      }\n    }, {\n      key: \"on\",\n      value: function on(evt, handler) {\n        if (this[evt]) {\n          this[evt].add(handler);\n        }\n      }\n    }, {\n      key: \"off\",\n      value: function off(evt, handler) {\n        if (this[evt]) {\n          this[evt].del(handler);\n        }\n      }\n    }, {\n      key: \"destroy\",\n      value: function destroy() {\n        if (this.singleTapTimeout) {\n          clearTimeout(this.singleTapTimeout);\n        }\n\n        if (this.tapTimeout) {\n          clearTimeout(this.tapTimeout);\n        }\n\n        if (this.longTapTimeout) {\n          clearTimeout(this.longTapTimeout);\n        }\n\n        if (this.swipeTimeout) {\n          clearTimeout(this.swipeTimeout);\n        }\n\n        this.element.removeEventListener('touchstart', this.start);\n        this.element.removeEventListener('touchmove', this.move);\n        this.element.removeEventListener('touchend', this.end);\n        this.element.removeEventListener('touchcancel', this.cancel);\n        this.rotate.del();\n        this.touchStart.del();\n        this.multipointStart.del();\n        this.multipointEnd.del();\n        this.pinch.del();\n        this.swipe.del();\n        this.tap.del();\n        this.doubleTap.del();\n        this.longTap.del();\n        this.singleTap.del();\n        this.pressMove.del();\n        this.twoFingerPressMove.del();\n        this.touchMove.del();\n        this.touchEnd.del();\n        this.touchCancel.del();\n        this.preV = this.pinchStartLen = this.zoom = this.isDoubleTap = this.delta = this.last = this.now = this.tapTimeout = this.singleTapTimeout = this.longTapTimeout = this.swipeTimeout = this.x1 = this.x2 = this.y1 = this.y2 = this.preTapPosition = this.rotate = this.touchStart = this.multipointStart = this.multipointEnd = this.pinch = this.swipe = this.tap = this.doubleTap = this.longTap = this.singleTap = this.pressMove = this.touchMove = this.touchEnd = this.touchCancel = this.twoFingerPressMove = null;\n        window.removeEventListener('scroll', this._cancelAllHandler);\n        return null;\n      }\n    }]);\n\n    return TouchEvents;\n  }();\n\n  function resetSlideMove(slide) {\n    var transitionEnd = whichTransitionEvent();\n    var windowWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;\n    var media = hasClass(slide, 'gslide-media') ? slide : slide.querySelector('.gslide-media');\n    var container = closest(media, '.ginner-container');\n    var desc = slide.querySelector('.gslide-description');\n\n    if (windowWidth > 769) {\n      media = container;\n    }\n\n    addClass(media, 'greset');\n    cssTransform(media, 'translate3d(0, 0, 0)');\n    addEvent(transitionEnd, {\n      onElement: media,\n      once: true,\n      withCallback: function withCallback(event, target) {\n        removeClass(media, 'greset');\n      }\n    });\n    media.style.opacity = '';\n\n    if (desc) {\n      desc.style.opacity = '';\n    }\n  }\n\n  function touchNavigation(instance) {\n    if (instance.events.hasOwnProperty('touch')) {\n      return false;\n    }\n\n    var winSize = windowSize();\n    var winWidth = winSize.width;\n    var winHeight = winSize.height;\n    var process = false;\n    var currentSlide = null;\n    var media = null;\n    var mediaImage = null;\n    var doingMove = false;\n    var initScale = 1;\n    var maxScale = 4.5;\n    var currentScale = 1;\n    var doingZoom = false;\n    var imageZoomed = false;\n    var zoomedPosX = null;\n    var zoomedPosY = null;\n    var lastZoomedPosX = null;\n    var lastZoomedPosY = null;\n    var hDistance;\n    var vDistance;\n    var hDistancePercent = 0;\n    var vDistancePercent = 0;\n    var vSwipe = false;\n    var hSwipe = false;\n    var startCoords = {};\n    var endCoords = {};\n    var xDown = 0;\n    var yDown = 0;\n    var isInlined;\n    var sliderWrapper = document.getElementById('glightbox-slider');\n    var overlay = document.querySelector('.goverlay');\n    var touchInstance = new TouchEvents(sliderWrapper, {\n      touchStart: function touchStart(e) {\n        process = true;\n\n        if (hasClass(e.targetTouches[0].target, 'ginner-container') || closest(e.targetTouches[0].target, '.gslide-desc') || e.targetTouches[0].target.nodeName.toLowerCase() == 'a') {\n          process = false;\n        }\n\n        if (closest(e.targetTouches[0].target, '.gslide-inline') && !hasClass(e.targetTouches[0].target.parentNode, 'gslide-inline')) {\n          process = false;\n        }\n\n        if (process) {\n          endCoords = e.targetTouches[0];\n          startCoords.pageX = e.targetTouches[0].pageX;\n          startCoords.pageY = e.targetTouches[0].pageY;\n          xDown = e.targetTouches[0].clientX;\n          yDown = e.targetTouches[0].clientY;\n          currentSlide = instance.activeSlide;\n          media = currentSlide.querySelector('.gslide-media');\n          isInlined = currentSlide.querySelector('.gslide-inline');\n          mediaImage = null;\n\n          if (hasClass(media, 'gslide-image')) {\n            mediaImage = media.querySelector('img');\n          }\n\n          var windowWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;\n\n          if (windowWidth > 769) {\n            media = currentSlide.querySelector('.ginner-container');\n          }\n\n          removeClass(overlay, 'greset');\n\n          if (e.pageX > 20 && e.pageX < window.innerWidth - 20) {\n            return;\n          }\n\n          e.preventDefault();\n        }\n      },\n      touchMove: function touchMove(e) {\n        if (!process) {\n          return;\n        }\n\n        endCoords = e.targetTouches[0];\n\n        if (doingZoom || imageZoomed) {\n          return;\n        }\n\n        if (isInlined && isInlined.offsetHeight > winHeight) {\n          var moved = startCoords.pageX - endCoords.pageX;\n\n          if (Math.abs(moved) <= 13) {\n            return false;\n          }\n        }\n\n        doingMove = true;\n        var xUp = e.targetTouches[0].clientX;\n        var yUp = e.targetTouches[0].clientY;\n        var xDiff = xDown - xUp;\n        var yDiff = yDown - yUp;\n\n        if (Math.abs(xDiff) > Math.abs(yDiff)) {\n          vSwipe = false;\n          hSwipe = true;\n        } else {\n          hSwipe = false;\n          vSwipe = true;\n        }\n\n        hDistance = endCoords.pageX - startCoords.pageX;\n        hDistancePercent = hDistance * 100 / winWidth;\n        vDistance = endCoords.pageY - startCoords.pageY;\n        vDistancePercent = vDistance * 100 / winHeight;\n        var opacity;\n\n        if (vSwipe && mediaImage) {\n          opacity = 1 - Math.abs(vDistance) / winHeight;\n          overlay.style.opacity = opacity;\n\n          if (instance.settings.touchFollowAxis) {\n            hDistancePercent = 0;\n          }\n        }\n\n        if (hSwipe) {\n          opacity = 1 - Math.abs(hDistance) / winWidth;\n          media.style.opacity = opacity;\n\n          if (instance.settings.touchFollowAxis) {\n            vDistancePercent = 0;\n          }\n        }\n\n        if (!mediaImage) {\n          return cssTransform(media, \"translate3d(\".concat(hDistancePercent, \"%, 0, 0)\"));\n        }\n\n        cssTransform(media, \"translate3d(\".concat(hDistancePercent, \"%, \").concat(vDistancePercent, \"%, 0)\"));\n      },\n      touchEnd: function touchEnd() {\n        if (!process) {\n          return;\n        }\n\n        doingMove = false;\n\n        if (imageZoomed || doingZoom) {\n          lastZoomedPosX = zoomedPosX;\n          lastZoomedPosY = zoomedPosY;\n          return;\n        }\n\n        var v = Math.abs(parseInt(vDistancePercent));\n        var h = Math.abs(parseInt(hDistancePercent));\n\n        if (v > 29 && mediaImage) {\n          instance.close();\n          return;\n        }\n\n        if (v < 29 && h < 25) {\n          addClass(overlay, 'greset');\n          overlay.style.opacity = 1;\n          return resetSlideMove(media);\n        }\n      },\n      multipointEnd: function multipointEnd() {\n        setTimeout(function () {\n          doingZoom = false;\n        }, 50);\n      },\n      multipointStart: function multipointStart() {\n        doingZoom = true;\n        initScale = currentScale ? currentScale : 1;\n      },\n      pinch: function pinch(evt) {\n        if (!mediaImage || doingMove) {\n          return false;\n        }\n\n        doingZoom = true;\n        mediaImage.scaleX = mediaImage.scaleY = initScale * evt.zoom;\n        var scale = initScale * evt.zoom;\n        imageZoomed = true;\n\n        if (scale <= 1) {\n          imageZoomed = false;\n          scale = 1;\n          lastZoomedPosY = null;\n          lastZoomedPosX = null;\n          zoomedPosX = null;\n          zoomedPosY = null;\n          mediaImage.setAttribute('style', '');\n          return;\n        }\n\n        if (scale > maxScale) {\n          scale = maxScale;\n        }\n\n        mediaImage.style.transform = \"scale3d(\".concat(scale, \", \").concat(scale, \", 1)\");\n        currentScale = scale;\n      },\n      pressMove: function pressMove(e) {\n        if (imageZoomed && !doingZoom) {\n          var mhDistance = endCoords.pageX - startCoords.pageX;\n          var mvDistance = endCoords.pageY - startCoords.pageY;\n\n          if (lastZoomedPosX) {\n            mhDistance = mhDistance + lastZoomedPosX;\n          }\n\n          if (lastZoomedPosY) {\n            mvDistance = mvDistance + lastZoomedPosY;\n          }\n\n          zoomedPosX = mhDistance;\n          zoomedPosY = mvDistance;\n          var style = \"translate3d(\".concat(mhDistance, \"px, \").concat(mvDistance, \"px, 0)\");\n\n          if (currentScale) {\n            style += \" scale3d(\".concat(currentScale, \", \").concat(currentScale, \", 1)\");\n          }\n\n          cssTransform(mediaImage, style);\n        }\n      },\n      swipe: function swipe(evt) {\n        if (imageZoomed) {\n          return;\n        }\n\n        if (doingZoom) {\n          doingZoom = false;\n          return;\n        }\n\n        if (evt.direction == 'Left') {\n          if (instance.index == instance.elements.length - 1) {\n            return resetSlideMove(media);\n          }\n\n          instance.nextSlide();\n        }\n\n        if (evt.direction == 'Right') {\n          if (instance.index == 0) {\n            return resetSlideMove(media);\n          }\n\n          instance.prevSlide();\n        }\n      }\n    });\n    instance.events['touch'] = touchInstance;\n  }\n\n  var ZoomImages = function () {\n    function ZoomImages(el, slide) {\n      var _this = this;\n\n      var onclose = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n\n      _classCallCheck(this, ZoomImages);\n\n      this.img = el;\n      this.slide = slide;\n      this.onclose = onclose;\n\n      if (this.img.setZoomEvents) {\n        return false;\n      }\n\n      this.active = false;\n      this.zoomedIn = false;\n      this.dragging = false;\n      this.currentX = null;\n      this.currentY = null;\n      this.initialX = null;\n      this.initialY = null;\n      this.xOffset = 0;\n      this.yOffset = 0;\n      this.img.addEventListener('mousedown', function (e) {\n        return _this.dragStart(e);\n      }, false);\n      this.img.addEventListener('mouseup', function (e) {\n        return _this.dragEnd(e);\n      }, false);\n      this.img.addEventListener('mousemove', function (e) {\n        return _this.drag(e);\n      }, false);\n      this.img.addEventListener('click', function (e) {\n        if (_this.slide.classList.contains('dragging-nav')) {\n          _this.zoomOut();\n\n          return false;\n        }\n\n        if (!_this.zoomedIn) {\n          return _this.zoomIn();\n        }\n\n        if (_this.zoomedIn && !_this.dragging) {\n          _this.zoomOut();\n        }\n      }, false);\n      this.img.setZoomEvents = true;\n    }\n\n    _createClass(ZoomImages, [{\n      key: \"zoomIn\",\n      value: function zoomIn() {\n        var winWidth = this.widowWidth();\n\n        if (this.zoomedIn || winWidth <= 768) {\n          return;\n        }\n\n        var img = this.img;\n        img.setAttribute('data-style', img.getAttribute('style'));\n        img.style.maxWidth = img.naturalWidth + 'px';\n        img.style.maxHeight = img.naturalHeight + 'px';\n\n        if (img.naturalWidth > winWidth) {\n          var centerX = winWidth / 2 - img.naturalWidth / 2;\n          this.setTranslate(this.img.parentNode, centerX, 0);\n        }\n\n        this.slide.classList.add('zoomed');\n        this.zoomedIn = true;\n      }\n    }, {\n      key: \"zoomOut\",\n      value: function zoomOut() {\n        this.img.parentNode.setAttribute('style', '');\n        this.img.setAttribute('style', this.img.getAttribute('data-style'));\n        this.slide.classList.remove('zoomed');\n        this.zoomedIn = false;\n        this.currentX = null;\n        this.currentY = null;\n        this.initialX = null;\n        this.initialY = null;\n        this.xOffset = 0;\n        this.yOffset = 0;\n\n        if (this.onclose && typeof this.onclose == 'function') {\n          this.onclose();\n        }\n      }\n    }, {\n      key: \"dragStart\",\n      value: function dragStart(e) {\n        e.preventDefault();\n\n        if (!this.zoomedIn) {\n          this.active = false;\n          return;\n        }\n\n        if (e.type === 'touchstart') {\n          this.initialX = e.touches[0].clientX - this.xOffset;\n          this.initialY = e.touches[0].clientY - this.yOffset;\n        } else {\n          this.initialX = e.clientX - this.xOffset;\n          this.initialY = e.clientY - this.yOffset;\n        }\n\n        if (e.target === this.img) {\n          this.active = true;\n          this.img.classList.add('dragging');\n        }\n      }\n    }, {\n      key: \"dragEnd\",\n      value: function dragEnd(e) {\n        var _this2 = this;\n\n        e.preventDefault();\n        this.initialX = this.currentX;\n        this.initialY = this.currentY;\n        this.active = false;\n        setTimeout(function () {\n          _this2.dragging = false;\n          _this2.img.isDragging = false;\n\n          _this2.img.classList.remove('dragging');\n        }, 100);\n      }\n    }, {\n      key: \"drag\",\n      value: function drag(e) {\n        if (this.active) {\n          e.preventDefault();\n\n          if (e.type === 'touchmove') {\n            this.currentX = e.touches[0].clientX - this.initialX;\n            this.currentY = e.touches[0].clientY - this.initialY;\n          } else {\n            this.currentX = e.clientX - this.initialX;\n            this.currentY = e.clientY - this.initialY;\n          }\n\n          this.xOffset = this.currentX;\n          this.yOffset = this.currentY;\n          this.img.isDragging = true;\n          this.dragging = true;\n          this.setTranslate(this.img, this.currentX, this.currentY);\n        }\n      }\n    }, {\n      key: \"onMove\",\n      value: function onMove(e) {\n        if (!this.zoomedIn) {\n          return;\n        }\n\n        var xOffset = e.clientX - this.img.naturalWidth / 2;\n        var yOffset = e.clientY - this.img.naturalHeight / 2;\n        this.setTranslate(this.img, xOffset, yOffset);\n      }\n    }, {\n      key: \"setTranslate\",\n      value: function setTranslate(node, xPos, yPos) {\n        node.style.transform = 'translate3d(' + xPos + 'px, ' + yPos + 'px, 0)';\n      }\n    }, {\n      key: \"widowWidth\",\n      value: function widowWidth() {\n        return window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;\n      }\n    }]);\n\n    return ZoomImages;\n  }();\n\n  var DragSlides = function () {\n    function DragSlides() {\n      var _this = this;\n\n      var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n      _classCallCheck(this, DragSlides);\n\n      var dragEl = config.dragEl,\n          _config$toleranceX = config.toleranceX,\n          toleranceX = _config$toleranceX === void 0 ? 40 : _config$toleranceX,\n          _config$toleranceY = config.toleranceY,\n          toleranceY = _config$toleranceY === void 0 ? 65 : _config$toleranceY,\n          _config$slide = config.slide,\n          slide = _config$slide === void 0 ? null : _config$slide,\n          _config$instance = config.instance,\n          instance = _config$instance === void 0 ? null : _config$instance;\n      this.el = dragEl;\n      this.active = false;\n      this.dragging = false;\n      this.currentX = null;\n      this.currentY = null;\n      this.initialX = null;\n      this.initialY = null;\n      this.xOffset = 0;\n      this.yOffset = 0;\n      this.direction = null;\n      this.lastDirection = null;\n      this.toleranceX = toleranceX;\n      this.toleranceY = toleranceY;\n      this.toleranceReached = false;\n      this.dragContainer = this.el;\n      this.slide = slide;\n      this.instance = instance;\n      this.el.addEventListener('mousedown', function (e) {\n        return _this.dragStart(e);\n      }, false);\n      this.el.addEventListener('mouseup', function (e) {\n        return _this.dragEnd(e);\n      }, false);\n      this.el.addEventListener('mousemove', function (e) {\n        return _this.drag(e);\n      }, false);\n    }\n\n    _createClass(DragSlides, [{\n      key: \"dragStart\",\n      value: function dragStart(e) {\n        if (this.slide.classList.contains('zoomed')) {\n          this.active = false;\n          return;\n        }\n\n        if (e.type === 'touchstart') {\n          this.initialX = e.touches[0].clientX - this.xOffset;\n          this.initialY = e.touches[0].clientY - this.yOffset;\n        } else {\n          this.initialX = e.clientX - this.xOffset;\n          this.initialY = e.clientY - this.yOffset;\n        }\n\n        var clicked = e.target.nodeName.toLowerCase();\n        var exludeClicks = ['input', 'select', 'textarea', 'button', 'a'];\n\n        if (e.target.classList.contains('nodrag') || closest(e.target, '.nodrag') || exludeClicks.indexOf(clicked) !== -1) {\n          this.active = false;\n          return;\n        }\n\n        e.preventDefault();\n\n        if (e.target === this.el || clicked !== 'img' && closest(e.target, '.gslide-inline')) {\n          this.active = true;\n          this.el.classList.add('dragging');\n          this.dragContainer = closest(e.target, '.ginner-container');\n        }\n      }\n    }, {\n      key: \"dragEnd\",\n      value: function dragEnd(e) {\n        var _this2 = this;\n\n        e && e.preventDefault();\n        this.initialX = 0;\n        this.initialY = 0;\n        this.currentX = null;\n        this.currentY = null;\n        this.initialX = null;\n        this.initialY = null;\n        this.xOffset = 0;\n        this.yOffset = 0;\n        this.active = false;\n\n        if (this.doSlideChange) {\n          this.instance.preventOutsideClick = true;\n          this.doSlideChange == 'right' && this.instance.prevSlide();\n          this.doSlideChange == 'left' && this.instance.nextSlide();\n        }\n\n        if (this.doSlideClose) {\n          this.instance.close();\n        }\n\n        if (!this.toleranceReached) {\n          this.setTranslate(this.dragContainer, 0, 0, true);\n        }\n\n        setTimeout(function () {\n          _this2.instance.preventOutsideClick = false;\n          _this2.toleranceReached = false;\n          _this2.lastDirection = null;\n          _this2.dragging = false;\n          _this2.el.isDragging = false;\n\n          _this2.el.classList.remove('dragging');\n\n          _this2.slide.classList.remove('dragging-nav');\n\n          _this2.dragContainer.style.transform = '';\n          _this2.dragContainer.style.transition = '';\n        }, 100);\n      }\n    }, {\n      key: \"drag\",\n      value: function drag(e) {\n        if (this.active) {\n          e.preventDefault();\n          this.slide.classList.add('dragging-nav');\n\n          if (e.type === 'touchmove') {\n            this.currentX = e.touches[0].clientX - this.initialX;\n            this.currentY = e.touches[0].clientY - this.initialY;\n          } else {\n            this.currentX = e.clientX - this.initialX;\n            this.currentY = e.clientY - this.initialY;\n          }\n\n          this.xOffset = this.currentX;\n          this.yOffset = this.currentY;\n          this.el.isDragging = true;\n          this.dragging = true;\n          this.doSlideChange = false;\n          this.doSlideClose = false;\n          var currentXInt = Math.abs(this.currentX);\n          var currentYInt = Math.abs(this.currentY);\n\n          if (currentXInt > 0 && currentXInt >= Math.abs(this.currentY) && (!this.lastDirection || this.lastDirection == 'x')) {\n            this.yOffset = 0;\n            this.lastDirection = 'x';\n            this.setTranslate(this.dragContainer, this.currentX, 0);\n            var doChange = this.shouldChange();\n\n            if (!this.instance.settings.dragAutoSnap && doChange) {\n              this.doSlideChange = doChange;\n            }\n\n            if (this.instance.settings.dragAutoSnap && doChange) {\n              this.instance.preventOutsideClick = true;\n              this.toleranceReached = true;\n              this.active = false;\n              this.instance.preventOutsideClick = true;\n              this.dragEnd(null);\n              doChange == 'right' && this.instance.prevSlide();\n              doChange == 'left' && this.instance.nextSlide();\n              return;\n            }\n          }\n\n          if (this.toleranceY > 0 && currentYInt > 0 && currentYInt >= currentXInt && (!this.lastDirection || this.lastDirection == 'y')) {\n            this.xOffset = 0;\n            this.lastDirection = 'y';\n            this.setTranslate(this.dragContainer, 0, this.currentY);\n            var doClose = this.shouldClose();\n\n            if (!this.instance.settings.dragAutoSnap && doClose) {\n              this.doSlideClose = true;\n            }\n\n            if (this.instance.settings.dragAutoSnap && doClose) {\n              this.instance.close();\n            }\n\n            return;\n          }\n        }\n      }\n    }, {\n      key: \"shouldChange\",\n      value: function shouldChange() {\n        var doChange = false;\n        var currentXInt = Math.abs(this.currentX);\n\n        if (currentXInt >= this.toleranceX) {\n          var dragDir = this.currentX > 0 ? 'right' : 'left';\n\n          if (dragDir == 'left' && this.slide !== this.slide.parentNode.lastChild || dragDir == 'right' && this.slide !== this.slide.parentNode.firstChild) {\n            doChange = dragDir;\n          }\n        }\n\n        return doChange;\n      }\n    }, {\n      key: \"shouldClose\",\n      value: function shouldClose() {\n        var doClose = false;\n        var currentYInt = Math.abs(this.currentY);\n\n        if (currentYInt >= this.toleranceY) {\n          doClose = true;\n        }\n\n        return doClose;\n      }\n    }, {\n      key: \"setTranslate\",\n      value: function setTranslate(node, xPos, yPos) {\n        var animated = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n\n        if (animated) {\n          node.style.transition = 'all .2s ease';\n        } else {\n          node.style.transition = '';\n        }\n\n        node.style.transform = \"translate3d(\".concat(xPos, \"px, \").concat(yPos, \"px, 0)\");\n      }\n    }]);\n\n    return DragSlides;\n  }();\n\n  function slideImage(slide, data, index, callback) {\n    var slideMedia = slide.querySelector('.gslide-media');\n    var img = new Image();\n    var titleID = 'gSlideTitle_' + index;\n    var textID = 'gSlideDesc_' + index;\n    img.addEventListener('load', function () {\n      if (isFunction(callback)) {\n        callback();\n      }\n    }, false);\n    img.src = data.href;\n\n    if (data.sizes != '' && data.srcset != '') {\n      img.sizes = data.sizes;\n      img.srcset = data.srcset;\n    }\n\n    img.alt = '';\n\n    if (!isNil(data.alt) && data.alt !== '') {\n      img.alt = data.alt;\n    }\n\n    if (data.title !== '') {\n      img.setAttribute('aria-labelledby', titleID);\n    }\n\n    if (data.description !== '') {\n      img.setAttribute('aria-describedby', textID);\n    }\n\n    if (data.hasOwnProperty('_hasCustomWidth') && data._hasCustomWidth) {\n      img.style.width = data.width;\n    }\n\n    if (data.hasOwnProperty('_hasCustomHeight') && data._hasCustomHeight) {\n      img.style.height = data.height;\n    }\n\n    slideMedia.insertBefore(img, slideMedia.firstChild);\n    return;\n  }\n\n  function slideVideo(slide, data, index, callback) {\n    var _this = this;\n\n    var slideContainer = slide.querySelector('.ginner-container');\n    var videoID = 'gvideo' + index;\n    var slideMedia = slide.querySelector('.gslide-media');\n    var videoPlayers = this.getAllPlayers();\n    addClass(slideContainer, 'gvideo-container');\n    slideMedia.insertBefore(createHTML('<div class=\"gvideo-wrapper\"></div>'), slideMedia.firstChild);\n    var videoWrapper = slide.querySelector('.gvideo-wrapper');\n    injectAssets(this.settings.plyr.css, 'Plyr');\n    var url = data.href;\n    var provider = data === null || data === void 0 ? void 0 : data.videoProvider;\n    var customPlaceholder = false;\n    slideMedia.style.maxWidth = data.width;\n    injectAssets(this.settings.plyr.js, 'Plyr', function () {\n      if (!provider && url.match(/vimeo\\.com\\/([0-9]*)/)) {\n        provider = 'vimeo';\n      }\n\n      if (!provider && (url.match(/(youtube\\.com|youtube-nocookie\\.com)\\/watch\\?v=([a-zA-Z0-9\\-_]+)/) || url.match(/youtu\\.be\\/([a-zA-Z0-9\\-_]+)/) || url.match(/(youtube\\.com|youtube-nocookie\\.com)\\/embed\\/([a-zA-Z0-9\\-_]+)/))) {\n        provider = 'youtube';\n      }\n\n      if (provider === 'local' || !provider) {\n        provider = 'local';\n        var html = '<video id=\"' + videoID + '\" ';\n        html += \"style=\\\"background:#000; max-width: \".concat(data.width, \";\\\" \");\n        html += 'preload=\"metadata\" ';\n        html += 'x-webkit-airplay=\"allow\" ';\n        html += 'playsinline ';\n        html += 'controls ';\n        html += 'class=\"gvideo-local\">';\n        html += \"<source src=\\\"\".concat(url, \"\\\">\");\n        html += '</video>';\n        customPlaceholder = createHTML(html);\n      }\n\n      var placeholder = customPlaceholder ? customPlaceholder : createHTML(\"<div id=\\\"\".concat(videoID, \"\\\" data-plyr-provider=\\\"\").concat(provider, \"\\\" data-plyr-embed-id=\\\"\").concat(url, \"\\\"></div>\"));\n      addClass(videoWrapper, \"\".concat(provider, \"-video gvideo\"));\n      videoWrapper.appendChild(placeholder);\n      videoWrapper.setAttribute('data-id', videoID);\n      videoWrapper.setAttribute('data-index', index);\n      var playerConfig = has(_this.settings.plyr, 'config') ? _this.settings.plyr.config : {};\n      var player = new Plyr('#' + videoID, playerConfig);\n      player.on('ready', function (event) {\n        videoPlayers[videoID] = event.detail.plyr;\n\n        if (isFunction(callback)) {\n          callback();\n        }\n      });\n      waitUntil(function () {\n        return slide.querySelector('iframe') && slide.querySelector('iframe').dataset.ready == 'true';\n      }, function () {\n        _this.resize(slide);\n      });\n      player.on('enterfullscreen', handleMediaFullScreen);\n      player.on('exitfullscreen', handleMediaFullScreen);\n    });\n  }\n\n  function handleMediaFullScreen(event) {\n    var media = closest(event.target, '.gslide-media');\n\n    if (event.type === 'enterfullscreen') {\n      addClass(media, 'fullscreen');\n    }\n\n    if (event.type === 'exitfullscreen') {\n      removeClass(media, 'fullscreen');\n    }\n  }\n\n  function slideInline(slide, data, index, callback) {\n    var _this = this;\n\n    var slideMedia = slide.querySelector('.gslide-media');\n    var hash = has(data, 'href') && data.href ? data.href.split('#').pop().trim() : false;\n    var content = has(data, 'content') && data.content ? data.content : false;\n    var innerContent;\n\n    if (content) {\n      if (isString(content)) {\n        innerContent = createHTML(\"<div class=\\\"ginlined-content\\\">\".concat(content, \"</div>\"));\n      }\n\n      if (isNode(content)) {\n        if (content.style.display == 'none') {\n          content.style.display = 'block';\n        }\n\n        var container = document.createElement('div');\n        container.className = 'ginlined-content';\n        container.appendChild(content);\n        innerContent = container;\n      }\n    }\n\n    if (hash) {\n      var div = document.getElementById(hash);\n\n      if (!div) {\n        return false;\n      }\n\n      var cloned = div.cloneNode(true);\n      cloned.style.height = data.height;\n      cloned.style.maxWidth = data.width;\n      addClass(cloned, 'ginlined-content');\n      innerContent = cloned;\n    }\n\n    if (!innerContent) {\n      console.error('Unable to append inline slide content', data);\n      return false;\n    }\n\n    slideMedia.style.height = data.height;\n    slideMedia.style.width = data.width;\n    slideMedia.appendChild(innerContent);\n    this.events['inlineclose' + hash] = addEvent('click', {\n      onElement: slideMedia.querySelectorAll('.gtrigger-close'),\n      withCallback: function withCallback(e) {\n        e.preventDefault();\n\n        _this.close();\n      }\n    });\n\n    if (isFunction(callback)) {\n      callback();\n    }\n\n    return;\n  }\n\n  function slideIframe(slide, data, index, callback) {\n    var slideMedia = slide.querySelector('.gslide-media');\n    var iframe = createIframe({\n      url: data.href,\n      callback: callback\n    });\n    slideMedia.parentNode.style.maxWidth = data.width;\n    slideMedia.parentNode.style.height = data.height;\n    slideMedia.appendChild(iframe);\n    return;\n  }\n\n  var SlideConfigParser = function () {\n    function SlideConfigParser() {\n      var slideParamas = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n      _classCallCheck(this, SlideConfigParser);\n\n      this.defaults = {\n        href: '',\n        sizes: '',\n        srcset: '',\n        title: '',\n        type: '',\n        videoProvider: '',\n        description: '',\n        alt: '',\n        descPosition: 'bottom',\n        effect: '',\n        width: '',\n        height: '',\n        content: false,\n        zoomable: true,\n        draggable: true\n      };\n\n      if (isObject(slideParamas)) {\n        this.defaults = extend(this.defaults, slideParamas);\n      }\n    }\n\n    _createClass(SlideConfigParser, [{\n      key: \"sourceType\",\n      value: function sourceType(url) {\n        var origin = url;\n        url = url.toLowerCase();\n\n        if (url.match(/\\.(jpeg|jpg|jpe|gif|png|apn|webp|avif|svg)/) !== null) {\n          return 'image';\n        }\n\n        if (url.match(/(youtube\\.com|youtube-nocookie\\.com)\\/watch\\?v=([a-zA-Z0-9\\-_]+)/) || url.match(/youtu\\.be\\/([a-zA-Z0-9\\-_]+)/) || url.match(/(youtube\\.com|youtube-nocookie\\.com)\\/embed\\/([a-zA-Z0-9\\-_]+)/)) {\n          return 'video';\n        }\n\n        if (url.match(/vimeo\\.com\\/([0-9]*)/)) {\n          return 'video';\n        }\n\n        if (url.match(/\\.(mp4|ogg|webm|mov)/) !== null) {\n          return 'video';\n        }\n\n        if (url.match(/\\.(mp3|wav|wma|aac|ogg)/) !== null) {\n          return 'audio';\n        }\n\n        if (url.indexOf('#') > -1) {\n          var hash = origin.split('#').pop();\n\n          if (hash.trim() !== '') {\n            return 'inline';\n          }\n        }\n\n        if (url.indexOf('goajax=true') > -1) {\n          return 'ajax';\n        }\n\n        return 'external';\n      }\n    }, {\n      key: \"parseConfig\",\n      value: function parseConfig(element, settings) {\n        var _this = this;\n\n        var data = extend({\n          descPosition: settings.descPosition\n        }, this.defaults);\n\n        if (isObject(element) && !isNode(element)) {\n          if (!has(element, 'type')) {\n            if (has(element, 'content') && element.content) {\n              element.type = 'inline';\n            } else if (has(element, 'href')) {\n              element.type = this.sourceType(element.href);\n            }\n          }\n\n          var objectData = extend(data, element);\n          this.setSize(objectData, settings);\n          return objectData;\n        }\n\n        var url = '';\n        var config = element.getAttribute('data-glightbox');\n        var nodeType = element.nodeName.toLowerCase();\n\n        if (nodeType === 'a') {\n          url = element.href;\n        }\n\n        if (nodeType === 'img') {\n          url = element.src;\n          data.alt = element.alt;\n        }\n\n        data.href = url;\n        each(data, function (val, key) {\n          if (has(settings, key) && key !== 'width') {\n            data[key] = settings[key];\n          }\n\n          var nodeData = element.dataset[key];\n\n          if (!isNil(nodeData)) {\n            data[key] = _this.sanitizeValue(nodeData);\n          }\n        });\n\n        if (data.content) {\n          data.type = 'inline';\n        }\n\n        if (!data.type && url) {\n          data.type = this.sourceType(url);\n        }\n\n        if (!isNil(config)) {\n          var cleanKeys = [];\n          each(data, function (v, k) {\n            cleanKeys.push(';\\\\s?' + k);\n          });\n          cleanKeys = cleanKeys.join('\\\\s?:|');\n\n          if (config.trim() !== '') {\n            each(data, function (val, key) {\n              var str = config;\n              var match = 's?' + key + 's?:s?(.*?)(' + cleanKeys + 's?:|$)';\n              var regex = new RegExp(match);\n              var matches = str.match(regex);\n\n              if (matches && matches.length && matches[1]) {\n                var value = matches[1].trim().replace(/;\\s*$/, '');\n                data[key] = _this.sanitizeValue(value);\n              }\n            });\n          }\n        } else {\n          if (!data.title && nodeType == 'a') {\n            var title = element.title;\n\n            if (!isNil(title) && title !== '') {\n              data.title = title;\n            }\n          }\n\n          if (!data.title && nodeType == 'img') {\n            var alt = element.alt;\n\n            if (!isNil(alt) && alt !== '') {\n              data.title = alt;\n            }\n          }\n        }\n\n        if (data.description && data.description.substring(0, 1) === '.') {\n          var description;\n\n          try {\n            description = document.querySelector(data.description).innerHTML;\n          } catch (error) {\n            if (!(error instanceof DOMException)) {\n              throw error;\n            }\n          }\n\n          if (description) {\n            data.description = description;\n          }\n        }\n\n        if (!data.description) {\n          var nodeDesc = element.querySelector('.glightbox-desc');\n\n          if (nodeDesc) {\n            data.description = nodeDesc.innerHTML;\n          }\n        }\n\n        this.setSize(data, settings, element);\n        this.slideConfig = data;\n        return data;\n      }\n    }, {\n      key: \"setSize\",\n      value: function setSize(data, settings) {\n        var element = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n        var defaultWith = data.type == 'video' ? this.checkSize(settings.videosWidth) : this.checkSize(settings.width);\n        var defaultHeight = this.checkSize(settings.height);\n        data.width = has(data, 'width') && data.width !== '' ? this.checkSize(data.width) : defaultWith;\n        data.height = has(data, 'height') && data.height !== '' ? this.checkSize(data.height) : defaultHeight;\n\n        if (element && data.type == 'image') {\n          data._hasCustomWidth = element.dataset.width ? true : false;\n          data._hasCustomHeight = element.dataset.height ? true : false;\n        }\n\n        return data;\n      }\n    }, {\n      key: \"checkSize\",\n      value: function checkSize(size) {\n        return isNumber(size) ? \"\".concat(size, \"px\") : size;\n      }\n    }, {\n      key: \"sanitizeValue\",\n      value: function sanitizeValue(val) {\n        if (val !== 'true' && val !== 'false') {\n          return val;\n        }\n\n        return val === 'true';\n      }\n    }]);\n\n    return SlideConfigParser;\n  }();\n\n  var Slide = function () {\n    function Slide(el, instance, index) {\n      _classCallCheck(this, Slide);\n\n      this.element = el;\n      this.instance = instance;\n      this.index = index;\n    }\n\n    _createClass(Slide, [{\n      key: \"setContent\",\n      value: function setContent() {\n        var _this = this;\n\n        var slide = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n        var callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n        if (hasClass(slide, 'loaded')) {\n          return false;\n        }\n\n        var settings = this.instance.settings;\n        var slideConfig = this.slideConfig;\n        var isMobileDevice = isMobile();\n\n        if (isFunction(settings.beforeSlideLoad)) {\n          settings.beforeSlideLoad({\n            index: this.index,\n            slide: slide,\n            player: false\n          });\n        }\n\n        var type = slideConfig.type;\n        var position = slideConfig.descPosition;\n        var slideMedia = slide.querySelector('.gslide-media');\n        var slideTitle = slide.querySelector('.gslide-title');\n        var slideText = slide.querySelector('.gslide-desc');\n        var slideDesc = slide.querySelector('.gdesc-inner');\n        var finalCallback = callback;\n        var titleID = 'gSlideTitle_' + this.index;\n        var textID = 'gSlideDesc_' + this.index;\n\n        if (isFunction(settings.afterSlideLoad)) {\n          finalCallback = function finalCallback() {\n            if (isFunction(callback)) {\n              callback();\n            }\n\n            settings.afterSlideLoad({\n              index: _this.index,\n              slide: slide,\n              player: _this.instance.getSlidePlayerInstance(_this.index)\n            });\n          };\n        }\n\n        if (slideConfig.title == '' && slideConfig.description == '') {\n          if (slideDesc) {\n            slideDesc.parentNode.parentNode.removeChild(slideDesc.parentNode);\n          }\n        } else {\n          if (slideTitle && slideConfig.title !== '') {\n            slideTitle.id = titleID;\n            slideTitle.innerHTML = slideConfig.title;\n          } else {\n            slideTitle.parentNode.removeChild(slideTitle);\n          }\n\n          if (slideText && slideConfig.description !== '') {\n            slideText.id = textID;\n\n            if (isMobileDevice && settings.moreLength > 0) {\n              slideConfig.smallDescription = this.slideShortDesc(slideConfig.description, settings.moreLength, settings.moreText);\n              slideText.innerHTML = slideConfig.smallDescription;\n              this.descriptionEvents(slideText, slideConfig);\n            } else {\n              slideText.innerHTML = slideConfig.description;\n            }\n          } else {\n            slideText.parentNode.removeChild(slideText);\n          }\n\n          addClass(slideMedia.parentNode, \"desc-\".concat(position));\n          addClass(slideDesc.parentNode, \"description-\".concat(position));\n        }\n\n        addClass(slideMedia, \"gslide-\".concat(type));\n        addClass(slide, 'loaded');\n\n        if (type === 'video') {\n          slideVideo.apply(this.instance, [slide, slideConfig, this.index, finalCallback]);\n          return;\n        }\n\n        if (type === 'external') {\n          slideIframe.apply(this, [slide, slideConfig, this.index, finalCallback]);\n          return;\n        }\n\n        if (type === 'inline') {\n          slideInline.apply(this.instance, [slide, slideConfig, this.index, finalCallback]);\n\n          if (slideConfig.draggable) {\n            new DragSlides({\n              dragEl: slide.querySelector('.gslide-inline'),\n              toleranceX: settings.dragToleranceX,\n              toleranceY: settings.dragToleranceY,\n              slide: slide,\n              instance: this.instance\n            });\n          }\n\n          return;\n        }\n\n        if (type === 'image') {\n          slideImage(slide, slideConfig, this.index, function () {\n            var img = slide.querySelector('img');\n\n            if (slideConfig.draggable) {\n              new DragSlides({\n                dragEl: img,\n                toleranceX: settings.dragToleranceX,\n                toleranceY: settings.dragToleranceY,\n                slide: slide,\n                instance: _this.instance\n              });\n            }\n\n            if (slideConfig.zoomable && img.naturalWidth > img.offsetWidth) {\n              addClass(img, 'zoomable');\n              new ZoomImages(img, slide, function () {\n                _this.instance.resize();\n              });\n            }\n\n            if (isFunction(finalCallback)) {\n              finalCallback();\n            }\n          });\n          return;\n        }\n\n        if (isFunction(finalCallback)) {\n          finalCallback();\n        }\n      }\n    }, {\n      key: \"slideShortDesc\",\n      value: function slideShortDesc(string) {\n        var n = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 50;\n        var wordBoundary = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n        var div = document.createElement('div');\n        div.innerHTML = string;\n        var cleanedString = div.innerText;\n        var useWordBoundary = wordBoundary;\n        string = cleanedString.trim();\n\n        if (string.length <= n) {\n          return string;\n        }\n\n        var subString = string.substr(0, n - 1);\n\n        if (!useWordBoundary) {\n          return subString;\n        }\n\n        div = null;\n        return subString + '... <a href=\"#\" class=\"desc-more\">' + wordBoundary + '</a>';\n      }\n    }, {\n      key: \"descriptionEvents\",\n      value: function descriptionEvents(desc, data) {\n        var _this2 = this;\n\n        var moreLink = desc.querySelector('.desc-more');\n\n        if (!moreLink) {\n          return false;\n        }\n\n        addEvent('click', {\n          onElement: moreLink,\n          withCallback: function withCallback(event, target) {\n            event.preventDefault();\n            var body = document.body;\n            var desc = closest(target, '.gslide-desc');\n\n            if (!desc) {\n              return false;\n            }\n\n            desc.innerHTML = data.description;\n            addClass(body, 'gdesc-open');\n            var shortEvent = addEvent('click', {\n              onElement: [body, closest(desc, '.gslide-description')],\n              withCallback: function withCallback(event, target) {\n                if (event.target.nodeName.toLowerCase() !== 'a') {\n                  removeClass(body, 'gdesc-open');\n                  addClass(body, 'gdesc-closed');\n                  desc.innerHTML = data.smallDescription;\n\n                  _this2.descriptionEvents(desc, data);\n\n                  setTimeout(function () {\n                    removeClass(body, 'gdesc-closed');\n                  }, 400);\n                  shortEvent.destroy();\n                }\n              }\n            });\n          }\n        });\n      }\n    }, {\n      key: \"create\",\n      value: function create() {\n        return createHTML(this.instance.settings.slideHTML);\n      }\n    }, {\n      key: \"getConfig\",\n      value: function getConfig() {\n        if (!isNode(this.element) && !this.element.hasOwnProperty('draggable')) {\n          this.element.draggable = this.instance.settings.draggable;\n        }\n\n        var parser = new SlideConfigParser(this.instance.settings.slideExtraAttributes);\n        this.slideConfig = parser.parseConfig(this.element, this.instance.settings);\n        return this.slideConfig;\n      }\n    }]);\n\n    return Slide;\n  }();\n\n  var _version = '3.1.0';\n\n  var isMobile$1 = isMobile();\n\n  var isTouch$1 = isTouch();\n\n  var html = document.getElementsByTagName('html')[0];\n  var defaults = {\n    selector: '.glightbox',\n    elements: null,\n    skin: 'clean',\n    theme: 'clean',\n    closeButton: true,\n    startAt: null,\n    autoplayVideos: true,\n    autofocusVideos: true,\n    descPosition: 'bottom',\n    width: '900px',\n    height: '506px',\n    videosWidth: '960px',\n    beforeSlideChange: null,\n    afterSlideChange: null,\n    beforeSlideLoad: null,\n    afterSlideLoad: null,\n    slideInserted: null,\n    slideRemoved: null,\n    slideExtraAttributes: null,\n    onOpen: null,\n    onClose: null,\n    loop: false,\n    zoomable: true,\n    draggable: true,\n    dragAutoSnap: false,\n    dragToleranceX: 40,\n    dragToleranceY: 65,\n    preload: true,\n    oneSlidePerOpen: false,\n    touchNavigation: true,\n    touchFollowAxis: true,\n    keyboardNavigation: true,\n    closeOnOutsideClick: true,\n    plugins: false,\n    plyr: {\n      css: 'https://cdn.plyr.io/3.6.12/plyr.css',\n      js: 'https://cdn.plyr.io/3.6.12/plyr.js',\n      config: {\n        ratio: '16:9',\n        fullscreen: {\n          enabled: true,\n          iosNative: true\n        },\n        youtube: {\n          noCookie: true,\n          rel: 0,\n          showinfo: 0,\n          iv_load_policy: 3\n        },\n        vimeo: {\n          byline: false,\n          portrait: false,\n          title: false,\n          transparent: false\n        }\n      }\n    },\n    openEffect: 'zoom',\n    closeEffect: 'zoom',\n    slideEffect: 'slide',\n    moreText: 'See more',\n    moreLength: 60,\n    cssEfects: {\n      fade: {\n        \"in\": 'fadeIn',\n        out: 'fadeOut'\n      },\n      zoom: {\n        \"in\": 'zoomIn',\n        out: 'zoomOut'\n      },\n      slide: {\n        \"in\": 'slideInRight',\n        out: 'slideOutLeft'\n      },\n      slideBack: {\n        \"in\": 'slideInLeft',\n        out: 'slideOutRight'\n      },\n      none: {\n        \"in\": 'none',\n        out: 'none'\n      }\n    },\n    svg: {\n      close: '<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 512 512\" xml:space=\"preserve\"><g><g><path d=\"M505.943,6.058c-8.077-8.077-21.172-8.077-29.249,0L6.058,476.693c-8.077,8.077-8.077,21.172,0,29.249C10.096,509.982,15.39,512,20.683,512c5.293,0,10.586-2.019,14.625-6.059L505.943,35.306C514.019,27.23,514.019,14.135,505.943,6.058z\"/></g></g><g><g><path d=\"M505.942,476.694L35.306,6.059c-8.076-8.077-21.172-8.077-29.248,0c-8.077,8.076-8.077,21.171,0,29.248l470.636,470.636c4.038,4.039,9.332,6.058,14.625,6.058c5.293,0,10.587-2.019,14.624-6.057C514.018,497.866,514.018,484.771,505.942,476.694z\"/></g></g></svg>',\n      next: '<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 477.175 477.175\" xml:space=\"preserve\"> <g><path d=\"M360.731,229.075l-225.1-225.1c-5.3-5.3-13.8-5.3-19.1,0s-5.3,13.8,0,19.1l215.5,215.5l-215.5,215.5c-5.3,5.3-5.3,13.8,0,19.1c2.6,2.6,6.1,4,9.5,4c3.4,0,6.9-1.3,9.5-4l225.1-225.1C365.931,242.875,365.931,234.275,360.731,229.075z\"/></g></svg>',\n      prev: '<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 477.175 477.175\" xml:space=\"preserve\"><g><path d=\"M145.188,238.575l215.5-215.5c5.3-5.3,5.3-13.8,0-19.1s-13.8-5.3-19.1,0l-225.1,225.1c-5.3,5.3-5.3,13.8,0,19.1l225.1,225c2.6,2.6,6.1,4,9.5,4s6.9-1.3,9.5-4c5.3-5.3,5.3-13.8,0-19.1L145.188,238.575z\"/></g></svg>'\n    }\n  };\n  defaults.slideHTML = \"<div class=\\\"gslide\\\">\\n    <div class=\\\"gslide-inner-content\\\">\\n        <div class=\\\"ginner-container\\\">\\n            <div class=\\\"gslide-media\\\">\\n            </div>\\n            <div class=\\\"gslide-description\\\">\\n                <div class=\\\"gdesc-inner\\\">\\n                    <h4 class=\\\"gslide-title\\\"></h4>\\n                    <div class=\\\"gslide-desc\\\"></div>\\n                </div>\\n            </div>\\n        </div>\\n    </div>\\n</div>\";\n  defaults.lightboxHTML = \"<div id=\\\"glightbox-body\\\" class=\\\"glightbox-container\\\" tabindex=\\\"-1\\\" role=\\\"dialog\\\" aria-hidden=\\\"false\\\">\\n    <div class=\\\"gloader visible\\\"></div>\\n    <div class=\\\"goverlay\\\"></div>\\n    <div class=\\\"gcontainer\\\">\\n    <div id=\\\"glightbox-slider\\\" class=\\\"gslider\\\"></div>\\n    <button class=\\\"gclose gbtn\\\" aria-label=\\\"Close\\\" data-taborder=\\\"3\\\">{closeSVG}</button>\\n    <button class=\\\"gprev gbtn\\\" aria-label=\\\"Previous\\\" data-taborder=\\\"2\\\">{prevSVG}</button>\\n    <button class=\\\"gnext gbtn\\\" aria-label=\\\"Next\\\" data-taborder=\\\"1\\\">{nextSVG}</button>\\n</div>\\n</div>\";\n\n  var GlightboxInit = function () {\n    function GlightboxInit() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n      _classCallCheck(this, GlightboxInit);\n\n      this.customOptions = options;\n      this.settings = extend(defaults, options);\n      this.effectsClasses = this.getAnimationClasses();\n      this.videoPlayers = {};\n      this.apiEvents = [];\n      this.fullElementsList = false;\n    }\n\n    _createClass(GlightboxInit, [{\n      key: \"init\",\n      value: function init() {\n        var _this = this;\n\n        var selector = this.getSelector();\n\n        if (selector) {\n          this.baseEvents = addEvent('click', {\n            onElement: selector,\n            withCallback: function withCallback(e, target) {\n              e.preventDefault();\n\n              _this.open(target);\n            }\n          });\n        }\n\n        this.elements = this.getElements();\n      }\n    }, {\n      key: \"open\",\n      value: function open() {\n        var element = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n        var startAt = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n\n        if (this.elements.length === 0) {\n          return false;\n        }\n\n        this.activeSlide = null;\n        this.prevActiveSlideIndex = null;\n        this.prevActiveSlide = null;\n        var index = isNumber(startAt) ? startAt : this.settings.startAt;\n\n        if (isNode(element)) {\n          var gallery = element.getAttribute('data-gallery');\n\n          if (gallery) {\n            this.fullElementsList = this.elements;\n            this.elements = this.getGalleryElements(this.elements, gallery);\n          }\n\n          if (isNil(index)) {\n            index = this.getElementIndex(element);\n\n            if (index < 0) {\n              index = 0;\n            }\n          }\n        }\n\n        if (!isNumber(index)) {\n          index = 0;\n        }\n\n        this.build();\n\n        animateElement(this.overlay, this.settings.openEffect === 'none' ? 'none' : this.settings.cssEfects.fade[\"in\"]);\n\n        var body = document.body;\n        var scrollBar = window.innerWidth - document.documentElement.clientWidth;\n\n        if (scrollBar > 0) {\n          var styleSheet = document.createElement('style');\n          styleSheet.type = 'text/css';\n          styleSheet.className = 'gcss-styles';\n          styleSheet.innerText = \".gscrollbar-fixer {margin-right: \".concat(scrollBar, \"px}\");\n          document.head.appendChild(styleSheet);\n\n          addClass(body, 'gscrollbar-fixer');\n        }\n\n        addClass(body, 'glightbox-open');\n\n        addClass(html, 'glightbox-open');\n\n        if (isMobile$1) {\n          addClass(document.body, 'glightbox-mobile');\n\n          this.settings.slideEffect = 'slide';\n        }\n\n        this.showSlide(index, true);\n\n        if (this.elements.length === 1) {\n          addClass(this.prevButton, 'glightbox-button-hidden');\n\n          addClass(this.nextButton, 'glightbox-button-hidden');\n        } else {\n          removeClass(this.prevButton, 'glightbox-button-hidden');\n\n          removeClass(this.nextButton, 'glightbox-button-hidden');\n        }\n\n        this.lightboxOpen = true;\n        this.trigger('open');\n\n        if (isFunction(this.settings.onOpen)) {\n          this.settings.onOpen();\n        }\n\n        if (isTouch$1 && this.settings.touchNavigation) {\n          touchNavigation(this);\n        }\n\n        if (this.settings.keyboardNavigation) {\n          keyboardNavigation(this);\n        }\n      }\n    }, {\n      key: \"openAt\",\n      value: function openAt() {\n        var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n        this.open(null, index);\n      }\n    }, {\n      key: \"showSlide\",\n      value: function showSlide() {\n        var _this2 = this;\n\n        var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n        var first = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n        show(this.loader);\n\n        this.index = parseInt(index);\n        var current = this.slidesContainer.querySelector('.current');\n\n        if (current) {\n          removeClass(current, 'current');\n        }\n\n        this.slideAnimateOut();\n        var slideNode = this.slidesContainer.querySelectorAll('.gslide')[index];\n\n        if (hasClass(slideNode, 'loaded')) {\n          this.slideAnimateIn(slideNode, first);\n\n          hide(this.loader);\n        } else {\n          show(this.loader);\n\n          var slide = this.elements[index];\n          var slideData = {\n            index: this.index,\n            slide: slideNode,\n            slideNode: slideNode,\n            slideConfig: slide.slideConfig,\n            slideIndex: this.index,\n            trigger: slide.node,\n            player: null\n          };\n          this.trigger('slide_before_load', slideData);\n          slide.instance.setContent(slideNode, function () {\n            hide(_this2.loader);\n\n            _this2.resize();\n\n            _this2.slideAnimateIn(slideNode, first);\n\n            _this2.trigger('slide_after_load', slideData);\n          });\n        }\n\n        this.slideDescription = slideNode.querySelector('.gslide-description');\n        this.slideDescriptionContained = this.slideDescription && hasClass(this.slideDescription.parentNode, 'gslide-media');\n\n        if (this.settings.preload) {\n          this.preloadSlide(index + 1);\n          this.preloadSlide(index - 1);\n        }\n\n        this.updateNavigationClasses();\n        this.activeSlide = slideNode;\n      }\n    }, {\n      key: \"preloadSlide\",\n      value: function preloadSlide(index) {\n        var _this3 = this;\n\n        if (index < 0 || index > this.elements.length - 1) {\n          return false;\n        }\n\n        if (isNil(this.elements[index])) {\n          return false;\n        }\n\n        var slideNode = this.slidesContainer.querySelectorAll('.gslide')[index];\n\n        if (hasClass(slideNode, 'loaded')) {\n          return false;\n        }\n\n        var slide = this.elements[index];\n        var type = slide.type;\n        var slideData = {\n          index: index,\n          slide: slideNode,\n          slideNode: slideNode,\n          slideConfig: slide.slideConfig,\n          slideIndex: index,\n          trigger: slide.node,\n          player: null\n        };\n        this.trigger('slide_before_load', slideData);\n\n        if (type === 'video' || type === 'external') {\n          setTimeout(function () {\n            slide.instance.setContent(slideNode, function () {\n              _this3.trigger('slide_after_load', slideData);\n            });\n          }, 200);\n        } else {\n          slide.instance.setContent(slideNode, function () {\n            _this3.trigger('slide_after_load', slideData);\n          });\n        }\n      }\n    }, {\n      key: \"prevSlide\",\n      value: function prevSlide() {\n        this.goToSlide(this.index - 1);\n      }\n    }, {\n      key: \"nextSlide\",\n      value: function nextSlide() {\n        this.goToSlide(this.index + 1);\n      }\n    }, {\n      key: \"goToSlide\",\n      value: function goToSlide() {\n        var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n        this.prevActiveSlide = this.activeSlide;\n        this.prevActiveSlideIndex = this.index;\n\n        if (!this.loop() && (index < 0 || index > this.elements.length - 1)) {\n          return false;\n        }\n\n        if (index < 0) {\n          index = this.elements.length - 1;\n        } else if (index >= this.elements.length) {\n          index = 0;\n        }\n\n        this.showSlide(index);\n      }\n    }, {\n      key: \"insertSlide\",\n      value: function insertSlide() {\n        var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        var index = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : -1;\n\n        if (index < 0) {\n          index = this.elements.length;\n        }\n\n        var slide = new Slide(config, this, index);\n        var data = slide.getConfig();\n\n        var slideInfo = extend({}, data);\n\n        var newSlide = slide.create();\n        var totalSlides = this.elements.length - 1;\n        slideInfo.index = index;\n        slideInfo.node = false;\n        slideInfo.instance = slide;\n        slideInfo.slideConfig = data;\n        this.elements.splice(index, 0, slideInfo);\n        var addedSlideNode = null;\n        var addedSlidePlayer = null;\n\n        if (this.slidesContainer) {\n          if (index > totalSlides) {\n            this.slidesContainer.appendChild(newSlide);\n          } else {\n            var existingSlide = this.slidesContainer.querySelectorAll('.gslide')[index];\n            this.slidesContainer.insertBefore(newSlide, existingSlide);\n          }\n\n          if (this.settings.preload && this.index == 0 && index == 0 || this.index - 1 == index || this.index + 1 == index) {\n            this.preloadSlide(index);\n          }\n\n          if (this.index === 0 && index === 0) {\n            this.index = 1;\n          }\n\n          this.updateNavigationClasses();\n          addedSlideNode = this.slidesContainer.querySelectorAll('.gslide')[index];\n          addedSlidePlayer = this.getSlidePlayerInstance(index);\n          slideInfo.slideNode = addedSlideNode;\n        }\n\n        this.trigger('slide_inserted', {\n          index: index,\n          slide: addedSlideNode,\n          slideNode: addedSlideNode,\n          slideConfig: data,\n          slideIndex: index,\n          trigger: null,\n          player: addedSlidePlayer\n        });\n\n        if (isFunction(this.settings.slideInserted)) {\n          this.settings.slideInserted({\n            index: index,\n            slide: addedSlideNode,\n            player: addedSlidePlayer\n          });\n        }\n      }\n    }, {\n      key: \"removeSlide\",\n      value: function removeSlide() {\n        var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : -1;\n\n        if (index < 0 || index > this.elements.length - 1) {\n          return false;\n        }\n\n        var slide = this.slidesContainer && this.slidesContainer.querySelectorAll('.gslide')[index];\n\n        if (slide) {\n          if (this.getActiveSlideIndex() == index) {\n            if (index == this.elements.length - 1) {\n              this.prevSlide();\n            } else {\n              this.nextSlide();\n            }\n          }\n\n          slide.parentNode.removeChild(slide);\n        }\n\n        this.elements.splice(index, 1);\n        this.trigger('slide_removed', index);\n\n        if (isFunction(this.settings.slideRemoved)) {\n          this.settings.slideRemoved(index);\n        }\n      }\n    }, {\n      key: \"slideAnimateIn\",\n      value: function slideAnimateIn(slide, first) {\n        var _this4 = this;\n\n        var slideMedia = slide.querySelector('.gslide-media');\n        var slideDesc = slide.querySelector('.gslide-description');\n        var prevData = {\n          index: this.prevActiveSlideIndex,\n          slide: this.prevActiveSlide,\n          slideNode: this.prevActiveSlide,\n          slideIndex: this.prevActiveSlide,\n          slideConfig: isNil(this.prevActiveSlideIndex) ? null : this.elements[this.prevActiveSlideIndex].slideConfig,\n          trigger: isNil(this.prevActiveSlideIndex) ? null : this.elements[this.prevActiveSlideIndex].node,\n          player: this.getSlidePlayerInstance(this.prevActiveSlideIndex)\n        };\n        var nextData = {\n          index: this.index,\n          slide: this.activeSlide,\n          slideNode: this.activeSlide,\n          slideConfig: this.elements[this.index].slideConfig,\n          slideIndex: this.index,\n          trigger: this.elements[this.index].node,\n          player: this.getSlidePlayerInstance(this.index)\n        };\n\n        if (slideMedia.offsetWidth > 0 && slideDesc) {\n          hide(slideDesc);\n\n          slideDesc.style.display = '';\n        }\n\n        removeClass(slide, this.effectsClasses);\n\n        if (first) {\n          animateElement(slide, this.settings.cssEfects[this.settings.openEffect][\"in\"], function () {\n            if (_this4.settings.autoplayVideos) {\n              _this4.slidePlayerPlay(slide);\n            }\n\n            _this4.trigger('slide_changed', {\n              prev: prevData,\n              current: nextData\n            });\n\n            if (isFunction(_this4.settings.afterSlideChange)) {\n              _this4.settings.afterSlideChange.apply(_this4, [prevData, nextData]);\n            }\n          });\n        } else {\n          var effectName = this.settings.slideEffect;\n          var animIn = effectName !== 'none' ? this.settings.cssEfects[effectName][\"in\"] : effectName;\n\n          if (this.prevActiveSlideIndex > this.index) {\n            if (this.settings.slideEffect == 'slide') {\n              animIn = this.settings.cssEfects.slideBack[\"in\"];\n            }\n          }\n\n          animateElement(slide, animIn, function () {\n            if (_this4.settings.autoplayVideos) {\n              _this4.slidePlayerPlay(slide);\n            }\n\n            _this4.trigger('slide_changed', {\n              prev: prevData,\n              current: nextData\n            });\n\n            if (isFunction(_this4.settings.afterSlideChange)) {\n              _this4.settings.afterSlideChange.apply(_this4, [prevData, nextData]);\n            }\n          });\n        }\n\n        setTimeout(function () {\n          _this4.resize(slide);\n        }, 100);\n\n        addClass(slide, 'current');\n      }\n    }, {\n      key: \"slideAnimateOut\",\n      value: function slideAnimateOut() {\n        if (!this.prevActiveSlide) {\n          return false;\n        }\n\n        var prevSlide = this.prevActiveSlide;\n\n        removeClass(prevSlide, this.effectsClasses);\n\n        addClass(prevSlide, 'prev');\n\n        var animation = this.settings.slideEffect;\n        var animOut = animation !== 'none' ? this.settings.cssEfects[animation].out : animation;\n        this.slidePlayerPause(prevSlide);\n        this.trigger('slide_before_change', {\n          prev: {\n            index: this.prevActiveSlideIndex,\n            slide: this.prevActiveSlide,\n            slideNode: this.prevActiveSlide,\n            slideIndex: this.prevActiveSlideIndex,\n            slideConfig: isNil(this.prevActiveSlideIndex) ? null : this.elements[this.prevActiveSlideIndex].slideConfig,\n            trigger: isNil(this.prevActiveSlideIndex) ? null : this.elements[this.prevActiveSlideIndex].node,\n            player: this.getSlidePlayerInstance(this.prevActiveSlideIndex)\n          },\n          current: {\n            index: this.index,\n            slide: this.activeSlide,\n            slideNode: this.activeSlide,\n            slideIndex: this.index,\n            slideConfig: this.elements[this.index].slideConfig,\n            trigger: this.elements[this.index].node,\n            player: this.getSlidePlayerInstance(this.index)\n          }\n        });\n\n        if (isFunction(this.settings.beforeSlideChange)) {\n          this.settings.beforeSlideChange.apply(this, [{\n            index: this.prevActiveSlideIndex,\n            slide: this.prevActiveSlide,\n            player: this.getSlidePlayerInstance(this.prevActiveSlideIndex)\n          }, {\n            index: this.index,\n            slide: this.activeSlide,\n            player: this.getSlidePlayerInstance(this.index)\n          }]);\n        }\n\n        if (this.prevActiveSlideIndex > this.index && this.settings.slideEffect == 'slide') {\n          animOut = this.settings.cssEfects.slideBack.out;\n        }\n\n        animateElement(prevSlide, animOut, function () {\n          var container = prevSlide.querySelector('.ginner-container');\n          var media = prevSlide.querySelector('.gslide-media');\n          var desc = prevSlide.querySelector('.gslide-description');\n          container.style.transform = '';\n          media.style.transform = '';\n\n          removeClass(media, 'greset');\n\n          media.style.opacity = '';\n\n          if (desc) {\n            desc.style.opacity = '';\n          }\n\n          removeClass(prevSlide, 'prev');\n        });\n      }\n    }, {\n      key: \"getAllPlayers\",\n      value: function getAllPlayers() {\n        return this.videoPlayers;\n      }\n    }, {\n      key: \"getSlidePlayerInstance\",\n      value: function getSlidePlayerInstance(index) {\n        var id = 'gvideo' + index;\n        var videoPlayers = this.getAllPlayers();\n\n        if (has(videoPlayers, id) && videoPlayers[id]) {\n          return videoPlayers[id];\n        }\n\n        return false;\n      }\n    }, {\n      key: \"stopSlideVideo\",\n      value: function stopSlideVideo(slide) {\n        if (isNode(slide)) {\n          var node = slide.querySelector('.gvideo-wrapper');\n\n          if (node) {\n            slide = node.getAttribute('data-index');\n          }\n        }\n\n        console.log('stopSlideVideo is deprecated, use slidePlayerPause');\n        var player = this.getSlidePlayerInstance(slide);\n\n        if (player && player.playing) {\n          player.pause();\n        }\n      }\n    }, {\n      key: \"slidePlayerPause\",\n      value: function slidePlayerPause(slide) {\n        if (isNode(slide)) {\n          var node = slide.querySelector('.gvideo-wrapper');\n\n          if (node) {\n            slide = node.getAttribute('data-index');\n          }\n        }\n\n        var player = this.getSlidePlayerInstance(slide);\n\n        if (player && player.playing) {\n          player.pause();\n        }\n      }\n    }, {\n      key: \"playSlideVideo\",\n      value: function playSlideVideo(slide) {\n        if (isNode(slide)) {\n          var node = slide.querySelector('.gvideo-wrapper');\n\n          if (node) {\n            slide = node.getAttribute('data-index');\n          }\n        }\n\n        console.log('playSlideVideo is deprecated, use slidePlayerPlay');\n        var player = this.getSlidePlayerInstance(slide);\n\n        if (player && !player.playing) {\n          player.play();\n        }\n      }\n    }, {\n      key: \"slidePlayerPlay\",\n      value: function slidePlayerPlay(slide) {\n        var _this$settings$plyr$c;\n\n        if (isMobile$1 && !((_this$settings$plyr$c = this.settings.plyr.config) !== null && _this$settings$plyr$c !== void 0 && _this$settings$plyr$c.muted)) {\n          return;\n        }\n\n        if (isNode(slide)) {\n          var node = slide.querySelector('.gvideo-wrapper');\n\n          if (node) {\n            slide = node.getAttribute('data-index');\n          }\n        }\n\n        var player = this.getSlidePlayerInstance(slide);\n\n        if (player && !player.playing) {\n          player.play();\n\n          if (this.settings.autofocusVideos) {\n            player.elements.container.focus();\n          }\n        }\n      }\n    }, {\n      key: \"setElements\",\n      value: function setElements(elements) {\n        var _this5 = this;\n\n        this.settings.elements = false;\n        var newElements = [];\n\n        if (elements && elements.length) {\n          each(elements, function (el, i) {\n            var slide = new Slide(el, _this5, i);\n            var data = slide.getConfig();\n\n            var slideInfo = extend({}, data);\n\n            slideInfo.slideConfig = data;\n            slideInfo.instance = slide;\n            slideInfo.index = i;\n            newElements.push(slideInfo);\n          });\n        }\n\n        this.elements = newElements;\n\n        if (this.lightboxOpen) {\n          this.slidesContainer.innerHTML = '';\n\n          if (this.elements.length) {\n            each(this.elements, function () {\n              var slide = createHTML(_this5.settings.slideHTML);\n\n              _this5.slidesContainer.appendChild(slide);\n            });\n\n            this.showSlide(0, true);\n          }\n        }\n      }\n    }, {\n      key: \"getElementIndex\",\n      value: function getElementIndex(node) {\n        var index = false;\n\n        each(this.elements, function (el, i) {\n          if (has(el, 'node') && el.node == node) {\n            index = i;\n            return true;\n          }\n        });\n\n        return index;\n      }\n    }, {\n      key: \"getElements\",\n      value: function getElements() {\n        var _this6 = this;\n\n        var list = [];\n        this.elements = this.elements ? this.elements : [];\n\n        if (!isNil(this.settings.elements) && isArray(this.settings.elements) && this.settings.elements.length) {\n          each(this.settings.elements, function (el, i) {\n            var slide = new Slide(el, _this6, i);\n            var elData = slide.getConfig();\n\n            var slideInfo = extend({}, elData);\n\n            slideInfo.node = false;\n            slideInfo.index = i;\n            slideInfo.instance = slide;\n            slideInfo.slideConfig = elData;\n            list.push(slideInfo);\n          });\n        }\n\n        var nodes = false;\n        var selector = this.getSelector();\n\n        if (selector) {\n          nodes = document.querySelectorAll(this.getSelector());\n        }\n\n        if (!nodes) {\n          return list;\n        }\n\n        each(nodes, function (el, i) {\n          var slide = new Slide(el, _this6, i);\n          var elData = slide.getConfig();\n\n          var slideInfo = extend({}, elData);\n\n          slideInfo.node = el;\n          slideInfo.index = i;\n          slideInfo.instance = slide;\n          slideInfo.slideConfig = elData;\n          slideInfo.gallery = el.getAttribute('data-gallery');\n          list.push(slideInfo);\n        });\n\n        return list;\n      }\n    }, {\n      key: \"getGalleryElements\",\n      value: function getGalleryElements(list, gallery) {\n        return list.filter(function (el) {\n          return el.gallery == gallery;\n        });\n      }\n    }, {\n      key: \"getSelector\",\n      value: function getSelector() {\n        if (this.settings.elements) {\n          return false;\n        }\n\n        if (this.settings.selector && this.settings.selector.substring(0, 5) == 'data-') {\n          return \"*[\".concat(this.settings.selector, \"]\");\n        }\n\n        return this.settings.selector;\n      }\n    }, {\n      key: \"getActiveSlide\",\n      value: function getActiveSlide() {\n        return this.slidesContainer.querySelectorAll('.gslide')[this.index];\n      }\n    }, {\n      key: \"getActiveSlideIndex\",\n      value: function getActiveSlideIndex() {\n        return this.index;\n      }\n    }, {\n      key: \"getAnimationClasses\",\n      value: function getAnimationClasses() {\n        var effects = [];\n\n        for (var key in this.settings.cssEfects) {\n          if (this.settings.cssEfects.hasOwnProperty(key)) {\n            var effect = this.settings.cssEfects[key];\n            effects.push(\"g\".concat(effect[\"in\"]));\n            effects.push(\"g\".concat(effect.out));\n          }\n        }\n\n        return effects.join(' ');\n      }\n    }, {\n      key: \"build\",\n      value: function build() {\n        var _this7 = this;\n\n        if (this.built) {\n          return false;\n        }\n\n        var children = document.body.childNodes;\n        var bodyChildElms = [];\n\n        each(children, function (el) {\n          if (el.parentNode == document.body && el.nodeName.charAt(0) !== '#' && el.hasAttribute && !el.hasAttribute('aria-hidden')) {\n            bodyChildElms.push(el);\n            el.setAttribute('aria-hidden', 'true');\n          }\n        });\n\n        var nextSVG = has(this.settings.svg, 'next') ? this.settings.svg.next : '';\n        var prevSVG = has(this.settings.svg, 'prev') ? this.settings.svg.prev : '';\n        var closeSVG = has(this.settings.svg, 'close') ? this.settings.svg.close : '';\n        var lightboxHTML = this.settings.lightboxHTML;\n        lightboxHTML = lightboxHTML.replace(/{nextSVG}/g, nextSVG);\n        lightboxHTML = lightboxHTML.replace(/{prevSVG}/g, prevSVG);\n        lightboxHTML = lightboxHTML.replace(/{closeSVG}/g, closeSVG);\n        lightboxHTML = createHTML(lightboxHTML);\n        document.body.appendChild(lightboxHTML);\n        var modal = document.getElementById('glightbox-body');\n        this.modal = modal;\n        var closeButton = modal.querySelector('.gclose');\n        this.prevButton = modal.querySelector('.gprev');\n        this.nextButton = modal.querySelector('.gnext');\n        this.overlay = modal.querySelector('.goverlay');\n        this.loader = modal.querySelector('.gloader');\n        this.slidesContainer = document.getElementById('glightbox-slider');\n        this.bodyHiddenChildElms = bodyChildElms;\n        this.events = {};\n\n        addClass(this.modal, 'glightbox-' + this.settings.skin);\n\n        if (this.settings.closeButton && closeButton) {\n          this.events['close'] = addEvent('click', {\n            onElement: closeButton,\n            withCallback: function withCallback(e, target) {\n              e.preventDefault();\n\n              _this7.close();\n            }\n          });\n        }\n\n        if (closeButton && !this.settings.closeButton) {\n          closeButton.parentNode.removeChild(closeButton);\n        }\n\n        if (this.nextButton) {\n          this.events['next'] = addEvent('click', {\n            onElement: this.nextButton,\n            withCallback: function withCallback(e, target) {\n              e.preventDefault();\n\n              _this7.nextSlide();\n            }\n          });\n        }\n\n        if (this.prevButton) {\n          this.events['prev'] = addEvent('click', {\n            onElement: this.prevButton,\n            withCallback: function withCallback(e, target) {\n              e.preventDefault();\n\n              _this7.prevSlide();\n            }\n          });\n        }\n\n        if (this.settings.closeOnOutsideClick) {\n          this.events['outClose'] = addEvent('click', {\n            onElement: modal,\n            withCallback: function withCallback(e, target) {\n              if (!_this7.preventOutsideClick && !hasClass(document.body, 'glightbox-mobile') && !closest(e.target, '.ginner-container')) {\n                if (!closest(e.target, '.gbtn') && !hasClass(e.target, 'gnext') && !hasClass(e.target, 'gprev')) {\n                  _this7.close();\n                }\n              }\n            }\n          });\n        }\n\n        each(this.elements, function (slide, i) {\n          _this7.slidesContainer.appendChild(slide.instance.create());\n\n          slide.slideNode = _this7.slidesContainer.querySelectorAll('.gslide')[i];\n        });\n\n        if (isTouch$1) {\n          addClass(document.body, 'glightbox-touch');\n        }\n\n        this.events['resize'] = addEvent('resize', {\n          onElement: window,\n          withCallback: function withCallback() {\n            _this7.resize();\n          }\n        });\n        this.built = true;\n      }\n    }, {\n      key: \"resize\",\n      value: function resize() {\n        var slide = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n        slide = !slide ? this.activeSlide : slide;\n\n        if (!slide || hasClass(slide, 'zoomed')) {\n          return;\n        }\n\n        var winSize = windowSize();\n\n        var video = slide.querySelector('.gvideo-wrapper');\n        var image = slide.querySelector('.gslide-image');\n        var description = this.slideDescription;\n        var winWidth = winSize.width;\n        var winHeight = winSize.height;\n\n        if (winWidth <= 768) {\n          addClass(document.body, 'glightbox-mobile');\n        } else {\n          removeClass(document.body, 'glightbox-mobile');\n        }\n\n        if (!video && !image) {\n          return;\n        }\n\n        var descriptionResize = false;\n\n        if (description && (hasClass(description, 'description-bottom') || hasClass(description, 'description-top')) && !hasClass(description, 'gabsolute')) {\n          descriptionResize = true;\n        }\n\n        if (image) {\n          if (winWidth <= 768) {\n            var imgNode = image.querySelector('img');\n          } else if (descriptionResize) {\n            var descHeight = description.offsetHeight;\n\n            var _imgNode = image.querySelector('img');\n\n            _imgNode.setAttribute('style', \"max-height: calc(100vh - \".concat(descHeight, \"px)\"));\n\n            description.setAttribute('style', \"max-width: \".concat(_imgNode.offsetWidth, \"px;\"));\n          }\n        }\n\n        if (video) {\n          var ratio = has(this.settings.plyr.config, 'ratio') ? this.settings.plyr.config.ratio : '';\n\n          if (!ratio) {\n            var containerWidth = video.clientWidth;\n            var containerHeight = video.clientHeight;\n            var divisor = containerWidth / containerHeight;\n            ratio = \"\".concat(containerWidth / divisor, \":\").concat(containerHeight / divisor);\n          }\n\n          var videoRatio = ratio.split(':');\n          var videoWidth = this.settings.videosWidth;\n          var maxWidth = this.settings.videosWidth;\n\n          if (isNumber(videoWidth) || videoWidth.indexOf('px') !== -1) {\n            maxWidth = parseInt(videoWidth);\n          } else {\n            if (videoWidth.indexOf('vw') !== -1) {\n              maxWidth = winWidth * parseInt(videoWidth) / 100;\n            } else if (videoWidth.indexOf('vh') !== -1) {\n              maxWidth = winHeight * parseInt(videoWidth) / 100;\n            } else if (videoWidth.indexOf('%') !== -1) {\n              maxWidth = winWidth * parseInt(videoWidth) / 100;\n            } else {\n              maxWidth = parseInt(video.clientWidth);\n            }\n          }\n\n          var maxHeight = maxWidth / (parseInt(videoRatio[0]) / parseInt(videoRatio[1]));\n          maxHeight = Math.floor(maxHeight);\n\n          if (descriptionResize) {\n            winHeight = winHeight - description.offsetHeight;\n          }\n\n          if (maxWidth > winWidth || maxHeight > winHeight || winHeight < maxHeight && winWidth > maxWidth) {\n            var vwidth = video.offsetWidth;\n            var vheight = video.offsetHeight;\n\n            var _ratio = winHeight / vheight;\n\n            var vsize = {\n              width: vwidth * _ratio,\n              height: vheight * _ratio\n            };\n            video.parentNode.setAttribute('style', \"max-width: \".concat(vsize.width, \"px\"));\n\n            if (descriptionResize) {\n              description.setAttribute('style', \"max-width: \".concat(vsize.width, \"px;\"));\n            }\n          } else {\n            video.parentNode.style.maxWidth = \"\".concat(videoWidth);\n\n            if (descriptionResize) {\n              description.setAttribute('style', \"max-width: \".concat(videoWidth, \";\"));\n            }\n          }\n        }\n      }\n    }, {\n      key: \"reload\",\n      value: function reload() {\n        this.init();\n      }\n    }, {\n      key: \"updateNavigationClasses\",\n      value: function updateNavigationClasses() {\n        var loop = this.loop();\n\n        removeClass(this.nextButton, 'disabled');\n\n        removeClass(this.prevButton, 'disabled');\n\n        if (this.index == 0 && this.elements.length - 1 == 0) {\n          addClass(this.prevButton, 'disabled');\n\n          addClass(this.nextButton, 'disabled');\n        } else if (this.index === 0 && !loop) {\n          addClass(this.prevButton, 'disabled');\n        } else if (this.index === this.elements.length - 1 && !loop) {\n          addClass(this.nextButton, 'disabled');\n        }\n      }\n    }, {\n      key: \"loop\",\n      value: function loop() {\n        var loop = has(this.settings, 'loopAtEnd') ? this.settings.loopAtEnd : null;\n        loop = has(this.settings, 'loop') ? this.settings.loop : loop;\n        return loop;\n      }\n    }, {\n      key: \"close\",\n      value: function close() {\n        var _this8 = this;\n\n        if (!this.lightboxOpen) {\n          if (this.events) {\n            for (var key in this.events) {\n              if (this.events.hasOwnProperty(key)) {\n                this.events[key].destroy();\n              }\n            }\n\n            this.events = null;\n          }\n\n          return false;\n        }\n\n        if (this.closing) {\n          return false;\n        }\n\n        this.closing = true;\n        this.slidePlayerPause(this.activeSlide);\n\n        if (this.fullElementsList) {\n          this.elements = this.fullElementsList;\n        }\n\n        if (this.bodyHiddenChildElms.length) {\n          each(this.bodyHiddenChildElms, function (el) {\n            el.removeAttribute('aria-hidden');\n          });\n        }\n\n        addClass(this.modal, 'glightbox-closing');\n\n        animateElement(this.overlay, this.settings.openEffect == 'none' ? 'none' : this.settings.cssEfects.fade.out);\n\n        animateElement(this.activeSlide, this.settings.cssEfects[this.settings.closeEffect].out, function () {\n          _this8.activeSlide = null;\n          _this8.prevActiveSlideIndex = null;\n          _this8.prevActiveSlide = null;\n          _this8.built = false;\n\n          if (_this8.events) {\n            for (var _key in _this8.events) {\n              if (_this8.events.hasOwnProperty(_key)) {\n                _this8.events[_key].destroy();\n              }\n            }\n\n            _this8.events = null;\n          }\n\n          var body = document.body;\n\n          removeClass(html, 'glightbox-open');\n\n          removeClass(body, 'glightbox-open touching gdesc-open glightbox-touch glightbox-mobile gscrollbar-fixer');\n\n          _this8.modal.parentNode.removeChild(_this8.modal);\n\n          _this8.trigger('close');\n\n          if (isFunction(_this8.settings.onClose)) {\n            _this8.settings.onClose();\n          }\n\n          var styles = document.querySelector('.gcss-styles');\n\n          if (styles) {\n            styles.parentNode.removeChild(styles);\n          }\n\n          _this8.lightboxOpen = false;\n          _this8.closing = null;\n        });\n      }\n    }, {\n      key: \"destroy\",\n      value: function destroy() {\n        this.close();\n        this.clearAllEvents();\n\n        if (this.baseEvents) {\n          this.baseEvents.destroy();\n        }\n      }\n    }, {\n      key: \"on\",\n      value: function on(evt, callback) {\n        var once = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n        if (!evt || !isFunction(callback)) {\n          throw new TypeError('Event name and callback must be defined');\n        }\n\n        this.apiEvents.push({\n          evt: evt,\n          once: once,\n          callback: callback\n        });\n      }\n    }, {\n      key: \"once\",\n      value: function once(evt, callback) {\n        this.on(evt, callback, true);\n      }\n    }, {\n      key: \"trigger\",\n      value: function trigger(eventName) {\n        var _this9 = this;\n\n        var data = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n        var onceTriggered = [];\n\n        each(this.apiEvents, function (event, i) {\n          var evt = event.evt,\n              once = event.once,\n              callback = event.callback;\n\n          if (evt == eventName) {\n            callback(data);\n\n            if (once) {\n              onceTriggered.push(i);\n            }\n          }\n        });\n\n        if (onceTriggered.length) {\n          each(onceTriggered, function (i) {\n            return _this9.apiEvents.splice(i, 1);\n          });\n        }\n      }\n    }, {\n      key: \"clearAllEvents\",\n      value: function clearAllEvents() {\n        this.apiEvents.splice(0, this.apiEvents.length);\n      }\n    }, {\n      key: \"version\",\n      value: function version() {\n        return _version;\n      }\n    }]);\n\n    return GlightboxInit;\n  }();\n\n  function glightbox () {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var instance = new GlightboxInit(options);\n    instance.init();\n    return instance;\n  }\n\n  return glightbox;\n\n})));\n"]}