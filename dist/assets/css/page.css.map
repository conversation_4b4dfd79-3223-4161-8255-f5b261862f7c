{"version": 3, "sources": ["_root.scss", "../../lib/src/assets/scss/_variables.scss", "../../lib/src/assets/scss/mixins/_buttons.scss", "../../lib/src/assets/scss/bootstrap/vendor/_rfs.scss", "../../lib/src/assets/scss/bootstrap/mixins/_border-radius.scss", "../../lib/src/assets/scss/bootstrap/mixins/_breakpoints.scss", "_page-header.scss", "_intro.scss", "_skill-building.scss", "_carousel.scss", "_care.scss", "_page-footer.scss"], "names": [], "mappings": "AAIE,WACE,iBAAA,CACA,aCoES,CDnET,eC+e0B,CD9e1B,kBAAA,CAGF,WACE,gBCof0B,CDnf1B,aC6DS,CD5DT,eCwe0B,CDve1B,kBAAA,CAGF,aEsFA,sBAAA,CCuBI,iBAtCa,CDiBjB,aDmtB4B,CG3yB1B,kBAAA,CC0CA,yBLpCA,WACE,kBAAA,CAGF,WACE,cCkewB,CD/d1B,aEwEF,iBAAA,CCuBI,cAtCa,CDiBjB,aD0sB4B,CGlyB1B,kBAAA,CAAA,CJsBF,8BACE,iBAAA,CAEA,kCACE,4BAAA,CAAA,oBAAA,CACA,0CAAA,CAAA,kCAAA,CAAA,0BAAA,CAAA,mDAAA,CAGF,oCACE,UAAA,CACA,aAAA,CACA,UAAA,CACA,WAAA,CACA,wBAAA,CACA,+7BAAA,CACA,8BAAA,CACA,2BAAA,CACA,oBAAA,CACA,iBAAA,CACA,iBAAA,CACA,OAAA,CACA,QAAA,CACA,mBAAA,CAIJ,wBACE,eAAA,CACA,aC+UsC,CD1UtC,wCACE,8BAAA,CAAA,sBAAA,CAGF,8BACE,aCjCM,CDsCZ,YACE,iBAAA,CACA,MAAA,CACA,UAAA,CACA,SAAA,CAEA,gBACE,QAAA,CACA,WAAA,CAGF,mBACE,WAAA,CACA,WAAA,CAGF,iBACE,SC7FY,CKPhB,aACE,iBAAA,CACA,YAAA,CACA,oCAAA,CACA,iBAAA,CAGA,kCACE,uDAAA,CACA,0BAAA,CACA,2BAAA,CACA,qBAAA,CACA,YAAA,CAEA,mEAPF,kCAQI,0DAAA,CAAA,CAGF,iEACE,iBAAA,CACA,gCAAA,CACA,YAAA,CAEA,yEACE,UAAA,CACA,aAAA,CACA,UAAA,CACA,WAAA,CACA,iBAAA,CACA,UAAA,CACA,WAAA,CACA,oFAAA,CACA,gCAAA,CAAA,wBAAA,CACA,UAAA,CACA,yBAAA,CAGF,wEACE,UAAA,CACA,aAAA,CACA,UAAA,CACA,WAAA,CACA,iBAAA,CACA,aAAA,CACA,YAAA,CACA,wFAAA,CACA,UAAA,CACA,yBAAA,CAGF,oEACE,iDAAA,CACA,eLkcsB,CKjctB,aLqBK,CKhBX,8BACE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,2BAAA,CAAA,4BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,wBAAA,CAAA,qBAAA,CAAA,kBAAA,CACA,sBAAA,CAAA,mBAAA,CAAA,0BAAA,CACA,SAAA,CAGF,uBACE,UAAA,CACA,WAAA,CACA,mBAAA,CAAA,gBAAA,CAGF,8BACE,iBAAA,CACA,aAAA,CACA,YAAA,CACA,WAAA,CACA,WAAA,CAEA,kCACE,UAAA,CACA,WAAA,CAIJ,kBJmBA,sBAAA,CCuBI,iBAtCa,CDiBjB,aDmtB4B,CG3yB1B,kBAAA,CC0CA,yBC5DJ,aA0FI,oCAAA,CACA,iCAAA,CAEA,kCACE,oBAAA,CACA,iBAAA,CAIE,oEACE,gBAAA,CAKN,qBACE,oBAAA,CACA,iBAAA,CAGF,uBACE,WAAA,CAAA,CDnDF,yBCyDA,kCACE,WAAA,CACA,YAAA,CACA,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,6BAAA,CAAA,4BAAA,CAAA,sBAAA,CAAA,kBAAA,CACA,uBAAA,CAAA,oBAAA,CAAA,sBAAA,CACA,wBAAA,CAAA,qBAAA,CAAA,kBAAA,CAIE,oEACE,iBAAA,CAAA,CDpEN,0BC4EA,kCACE,uDAAA,CAAA,CAEA,iHAHF,kCAII,0DAAA,CAAA,CDhFJ,0BCqFI,oEACE,iBAAA,CAGF,kFACE,6BAAA,CAAA,4BAAA,CAAA,sBAAA,CAAA,kBAAA,CACA,wBAAA,CAAA,qBAAA,CAAA,uBAAA,CACA,uBAAA,CAAA,oBAAA,CAAA,sBAAA,CAAA,CD5FN,0BC5DJ,aA+JI,0BAAA,CAAA,uBAAA,CAAA,kBAAA,CACA,qCAAA,CACA,iCAAA,CAEA,kCACE,oBAAA,CACA,iBAAA,CAIE,oEACE,gBAAA,CAGF,kFACE,6BAAA,CAAA,4BAAA,CAAA,sBAAA,CAAA,kBAAA,CACA,wBAAA,CAAA,qBAAA,CAAA,uBAAA,CACA,uBAAA,CAAA,oBAAA,CAAA,sBAAA,CAEA,uFACE,kBAAA,CAAA,gBAAA,CAAA,YAAA,CAMR,qBACE,oBAAA,CACA,iBAAA,CAAA,CAIJ,0BAEE,kCACE,oBAAA,CACA,iBAAA,CAEA,iEACE,aAAA,CAEA,yEACE,WAAA,CACA,YAAA,CACA,SAAA,CACA,UAAA,CAGF,wEACE,WAAA,CACA,YAAA,CACA,YAAA,CACA,WAAA,CAGF,oEACE,+CAAA,CACA,gDAAA,CAKA,uFACE,kBAAA,CAAA,gBAAA,CAAA,YAAA,CAMR,kBJ5HF,iBAAA,CCuBI,cAtCa,CDiBjB,aD0sB4B,CGlyB1B,kBAAA,CAAA,CGhBF,iBACE,YAAA,CAGF,aACE,WAAA,CACA,eAAA,CAEA,iBACE,eAAA,CFiDF,yBElDA,iBAII,UAAA,CACA,gBAAA,CACA,mBAAA,CAAA,gBAAA,CAAA,CF4CJ,0BElDA,iBAUI,gBAAA,CAAA,CFwCJ,0BElDA,iBAcI,iBAAA,CAAA,CCxBR,gBACE,iBAAA,CAEA,0CACE,iBAAA,CACA,2IAAA,CACA,6BAAA,CACA,+CAAA,CAEA,mEANF,0CAOI,gGAAA,CAAA,CAGF,2DACE,iBAAA,CACA,SAAA,CAGF,kEACE,iBAAA,CACA,UPbU,COcV,WAAA,CACA,0BAAA,CAAA,uBAAA,CAAA,kBAAA,CAEA,0EACE,UAAA,CACA,aAAA,CACA,8CAAA,CACA,+CAAA,CACA,iBAAA,CACA,KAAA,CACA,MAAA,CACA,mFAAA,CACA,gCAAA,CAAA,wBAAA,CACA,yBAAA,CAGF,yEACE,UAAA,CACA,aAAA,CACA,8CAAA,CACA,+CAAA,CACA,iBAAA,CACA,QAAA,CACA,OAAA,CACA,uFAAA,CACA,yBAAA,CAGF,qEACE,UP3CQ,CO4CR,eAAA,CACA,+CAAA,CAEA,0EACE,aAAA,CACA,aAAA,CACA,oBAAA,CAKN,qDACE,cAAA,CACA,UPzDU,CO0DV,kBAAA,CAEA,yDACE,wBAAA,CACA,kBAAA,CHTJ,yBGgBA,0CACE,4CAAA,CACA,2BAAA,CACA,qDAAA,CAIE,0DACE,WAAA,CAGF,6DACE,WAAA,CAIJ,kEACE,cAAA,CAEA,0EACE,UAAA,CACA,WAAA,CAGF,yEACE,UAAA,CACA,WAAA,CAGF,qEACE,cAAA,CAAA,CH9CN,yBGsDA,0CACE,qCAAA,CAAA,CAEA,+GAHF,0CAII,gGAAA,CAAA,CH1DJ,yBG+DI,0DACE,WAAA,CAGF,6DACE,WAAA,CAIJ,kEACE,cAAA,CAEA,0EACE,UAAA,CACA,WAAA,CAGF,yEACE,UAAA,CACA,WAAA,CAGF,qEACE,gBAAA,CAIJ,qDACE,eAAA,CAAA,CH3FJ,0BGkGA,0CACE,6IAAA,CACA,qCAAA,CAAA,CAEA,iHAJF,0CAKI,gGAAA,CAAA,CHvGJ,0BG0GE,kEACE,YAAA,CACA,yBAAA,CAAA,sBAAA,CAAA,iBAAA,CAEA,0EACE,UAAA,CACA,WAAA,CAGF,yEACE,UAAA,CACA,WAAA,CAGF,qEACE,cAAA,CAKN,2BACE,oBAAA,CAEA,+BACE,wBAAA,CACA,eAAA,CAAA,CHnIJ,0BG2IA,0CACE,mDAAA,CACA,2BAAA,CACA,gCAAA,CAEA,kEACE,YAAA,CAEA,0EACE,UAAA,CACA,WAAA,CAGF,yEACE,UAAA,CACA,WAAA,CAGF,qEACE,iDAAA,CAAA,CCrNN,+CACE,iYAAA,CACA,sBAAA,CAEA,kDACE,mDAAA,CAIJ,4CACE,gBAAA,CAEA,4DACE,iBAAA,CACA,OAAA,CACA,MAAA,CACA,kCAAA,CAAA,0BAAA,CACA,UAAA,CACA,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,wBAAA,CAAA,qBAAA,CAAA,6BAAA,CACA,UAAA,CAEA,mEACE,+BAAA,CACA,WAAA,CACA,iBAAA,CACA,cAAA,CACA,SAAA,CACA,QAAA,CACA,YAAA,CACA,qBAAA,CACA,uDAAA,CAAA,+CAAA,CAEA,yEACE,gCAAA,CACA,oBAAA,CAGF,0FACE,SAAA,CACA,kBAAA,CAKN,+DACE,iBAAA,CACA,SAAA,CAEA,yFACE,wBAAA,CACA,0BAAA,CACA,UAAA,CACA,WAAA,CACA,SAAA,CACA,wCAAA,CAAA,gCAAA,CAEA,+FACE,4BAAA,CAGF,yHACE,kBAAA,CJPR,yBIsBE,+CACE,sBAAA,CAEA,kDACE,gBAAA,CAAA,CJ1BN,yBIkCA,8BACE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,6BAAA,CAAA,4BAAA,CAAA,wBAAA,CAAA,oBAAA,CACA,wBAAA,CAAA,qBAAA,CAAA,kBAAA,CAGA,+CACE,kBAAA,CAAA,gBAAA,CAAA,YAAA,CACA,4BAAA,CAEA,kDACE,kBAAA,CAIJ,4CACE,kBAAA,CAAA,gBAAA,CAAA,YAAA,CACA,mBAAA,CACA,kCAAA,CAAA,0BAAA,CAIE,yFACE,UAAA,CACA,WAAA,CACA,YAAA,CAAA,CJ3DR,0BIoEA,8BACE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,6BAAA,CAAA,4BAAA,CAAA,wBAAA,CAAA,oBAAA,CACA,wBAAA,CAAA,qBAAA,CAAA,kBAAA,CAGA,+CACE,4BAAA,CAEA,kDACE,kBAAA,CAIJ,4CACE,mBAAA,CACA,kCAAA,CAAA,0BAAA,CAIE,yFACE,UAAA,CACA,WAAA,CACA,YAAA,CAAA,CJ3FR,0BIoGA,8BACE,UAAA,CAEA,+CACE,6BAAA,CAEA,kDACE,kBAAA,CAIJ,4CACE,mBAAA,CACA,kCAAA,CAAA,0BAAA,CAIE,yFACE,UAAA,CACA,WAAA,CACA,YAAA,CAAA,CClLV,sBACE,iBAAA,CACA,0JAAA,CACA,6BAAA,CACA,+CAAA,CAEA,mEANF,sBAOI,wGAAA,CAAA,CAGF,uCACE,iBAAA,CACA,SAAA,CAGF,oCACE,iBAAA,CACA,UTZU,CSaV,WAAA,CACA,0BAAA,CAAA,uBAAA,CAAA,kBAAA,CAEA,4CACE,UAAA,CACA,aAAA,CACA,8CAAA,CACA,+CAAA,CACA,iBAAA,CACA,KAAA,CACA,MAAA,CACA,mFAAA,CACA,gCAAA,CAAA,wBAAA,CACA,yBAAA,CAGF,2CACE,UAAA,CACA,aAAA,CACA,8CAAA,CACA,+CAAA,CACA,iBAAA,CACA,QAAA,CACA,OAAA,CACA,uFAAA,CACA,yBAAA,CAGF,uCACE,UT1CQ,CS2CR,eAAA,CACA,+CAAA,CAEA,4CACE,aAAA,CACA,aAAA,CACA,iBAAA,CLIN,yBKIA,sBACE,2BAAA,CACA,2DAAA,CACA,qDAAA,CAIE,sCACE,WAAA,CAGF,yCACE,WAAA,CAIJ,oCACE,cAAA,CAEA,4CACE,UAAA,CACA,WAAA,CAGF,2CACE,UAAA,CACA,WAAA,CAGF,uCACE,cAAA,CAAA,CLlCN,yBK0CA,uBACE,WAAA,CAEA,4BACE,WAAA,CAIJ,sBACE,oKAAA,CACA,cAAA,CAAA,CAEA,+GAJF,sBAKI,wGAAA,CAAA,CLvDJ,yBK4DI,sCACE,WAAA,CAGF,yCACE,WAAA,CAIJ,oCACE,cAAA,CAEA,4CACE,UAAA,CACA,WAAA,CAGF,2CACE,UAAA,CACA,WAAA,CAGF,uCACE,gBAAA,CAIJ,0BACE,WAAA,CACA,mBAAA,CAAA,gBAAA,CAAA,CLzFJ,0BKgGA,sBACE,uJAAA,CACA,cAAA,CAAA,CAEA,iHAJF,sBAKI,2GAAA,CAAA,CLrGJ,0BKwGE,oCACE,YAAA,CAEA,4CACE,UAAA,CACA,WAAA,CAGF,2CACE,UAAA,CACA,WAAA,CAGF,uCACE,cAAA,CAAA,CLtHN,0BKgIE,oCACE,YAAA,CAEA,4CACE,UAAA,CACA,WAAA,CAGF,2CACE,UAAA,CACA,WAAA,CAGF,uCACE,iDAAA,CAAA,CC1MV,aACE,yEAAA,CACA,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,2BAAA,CAAA,4BAAA,CAAA,2BAAA,CAAA,uBAAA,CACA,oBAAA,CAAA,iBAAA,CAAA,wBAAA,CACA,UAAA,CACA,kBAAA,CACA,iBAAA,CAEA,mEATF,aAUI,4EAAA,CAAA,CAGF,gBACE,2GAAA,CAAA,oFAAA,CACA,2BAAA,CACA,eAAA,CACA,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,oBAAA,CAAA,iBAAA,CAAA,wBAAA,CN0CA,yBM5DJ,aAsBI,+JAAA,CACA,8BAAA,CACA,aAAA,CACA,iBAAA,CACA,gBAAA,CAAA,CAEA,+GA5BJ,aA6BM,kKAAA,CAAA,CN+BF,yBM5BA,gBACE,eAAA,CACA,SAAA,CACA,eAAA,CACA,aAAA,CAAA,CNwBF,yBM5DJ,aAyCI,+JAAA,CACA,iBAAA,CACA,gBAAA,CAAA,CAEA,+GA7CJ,aA8CM,kKAAA,CAAA,CNcF,yBMXA,gBACE,eAAA,CAAA,CNUF,0BM5DJ,aAuDI,iBAAA,CAEA,gBACE,eAAA,CAAA,CNEF,0BM5DJ,aA+DI,+JAAA,CACA,iBAAA,CAAA,CAEA,iHAlEJ,aAmEM,kKAAA,CAAA,CNPF,0BMUA,gBACE,eAAA,CACA,oDAAA,CAAA", "file": "page.css", "sourcesContent": ["// Global page styles\n\nsection {\n\n  h2 {\n    font-size: $h3-font-size * .875;\n    color: $green;\n    font-weight: $font-weight-normal;\n    margin-bottom: 2rem;\n  }\n\n  h3 {\n    font-size: $h4-font-size;\n    color: $green;\n    font-weight: $font-weight-normal;\n    margin-bottom: 2rem;\n  }\n\n  .btn {\n    @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-line-height-sm, $btn-border-radius-sm);\n  }\n\n  @include media-breakpoint-up(md) {\n\n    h2 {\n      font-size: $h2-font-size * .875;\n    }\n\n    h3 {\n      font-size: $h3-font-size;\n    }\n\n    .btn {\n      @include button-size($btn-padding-y, $btn-padding-x, $btn-font-size, $btn-line-height, $btn-border-radius);\n    }\n  }\n}\n\n.card-video {\n  \n  .card-video-thumb {\n    position: relative;\n\n    > img {\n      filter: brightness(1);\n      transition: filter 0.3s ease;\n    }\n\n    &:after {\n      content: '';\n      display: block;\n      width: 58px;\n      height: 58px;\n      background-color: theme-color('secondary');\n      background-image: url('data:image/svg+xml,<svg width=\"29\" height=\"29\" viewBox=\"0 0 29 29\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M3.26915 28.4255C3.21323 28.4255 3.15731 28.4158 3.10139 28.3965C3.06411 28.3965 3.02684 28.3772 2.98956 28.3385C2.89636 28.2998 2.8218 28.2321 2.76588 28.1353C2.7286 28.0386 2.70996 27.9419 2.70996 27.8451V1.03158C2.70996 0.934852 2.7286 0.838122 2.76588 0.741392C2.8218 0.644662 2.89636 0.576951 2.98956 0.538259C3.08275 0.480221 3.17595 0.451202 3.26915 0.451202C3.36235 0.451202 3.45555 0.480221 3.54875 0.538259L25.9163 13.945C26.0095 13.9837 26.0748 14.0514 26.1121 14.1482C26.168 14.2449 26.1959 14.3416 26.1959 14.4384C26.1959 14.5351 26.168 14.6318 26.1121 14.7286C26.0748 14.8253 26.0095 14.893 25.9163 14.9317L3.54875 28.3385C3.51147 28.3772 3.46487 28.3965 3.40895 28.3965C3.37167 28.4158 3.32507 28.4255 3.26915 28.4255ZM3.82834 2.04725V26.8295L24.5184 14.4384L3.82834 2.04725Z\" fill=\"white\"/></svg>');\n      background-position: 60% center;\n      background-repeat: no-repeat;\n      background-size: 28px;\n      border-radius: 50%;\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      translate: -50% -50%;\n    }\n  }\n\n  .card-title {\n    margin-bottom: 0;\n    color: $link-color;\n  }\n\n  &:hover {\n\n    .card-video-thumb > img {\n      filter: brightness(0.5);\n    }\n\n    .card-title {\n      color: $link-hover-color;\n    }\n  } \n}\n\n.svg-border {\n  position: absolute;\n  left: 0;\n  width: 100%;\n  z-index: 1;\n  \n  &-top {\n    top: -1px;\n    height: 36px;\n  }\n  \n  &-bottom {\n    bottom: -1px;\n    height: 21px;\n  }\n  \n  path {\n    fill: $white;\n  }\n}", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n$white:         #fff;\n$gray-100:      #F7F7FC;\n$gray-200:      #F0F0F5;\n$gray-300:      #E6E6ED;\n$gray-400:      #D7D7E0;\n$gray-500:      #C1C1C6;\n$gray-600:      #929299;\n$gray-700:      #616166;\n$gray-800:      #454547;\n$gray-900:      #313133;\n$black:         #000;\n\n$royal-base: #003E52;\n$royal-100:  desaturate(lighten($royal-base, 80%), 74%);\n$royal-200:  lighten(desaturate($royal-base, 72%), 73%);\n$royal-300:  lighten(desaturate($royal-base, 73%), 66%);\n$royal-400:  lighten(desaturate($royal-base, 69%), 53%);\n$royal-500:  lighten(desaturate($royal-base, 69%), 37%);\n$royal-600:  lighten(desaturate($royal-base, 52%), 19%);\n$royal-700:  $royal-base;\n$royal-800:  darken($royal-base, 4%);\n$royal-900:  darken($royal-base, 9%);\n\n$lavender-base: #6786B8;\n$lavender-100:  lighten(desaturate(adjust-hue($lavender-base, 8deg), 7%), 38%);\n$lavender-200:  lighten(desaturate(adjust-hue($lavender-base, 7deg), 5%), 33%);\n$lavender-300:  lighten(desaturate(adjust-hue($lavender-base, 6deg), 4%), 28%);\n$lavender-400:  lighten(desaturate(adjust-hue($lavender-base, 2deg), 2%), 14%);\n$lavender-500:  $lavender-base;\n$lavender-600:  darken($lavender-base, 11%);\n$lavender-700:  darken($lavender-base, 22%);\n$lavender-800:  darken($lavender-base, 33%);\n$lavender-900:  darken($lavender-base, 44%);\n\n$blue-base: #0075C9;\n$blue-100:  lighten(desaturate($blue-base, 36%), 56%);\n$blue-200:  lighten(desaturate($blue-base, 27%), 42%);\n$blue-300:  lighten(desaturate($blue-base, 18%), 28%);\n$blue-400:  lighten(desaturate($blue-base, 9%), 14%);\n$blue-500:  $blue-base;\n$blue-600:  darken($blue-base, 8%);\n$blue-700:  darken($blue-base, 16%);\n$blue-800:  darken($blue-base, 24%);\n$blue-900:  darken($blue-base, 31%);\n\n$purple-base: #8A1A9B;\n$purple-100:  lighten(desaturate($purple-base, 32%), 60%);\n$purple-200:  lighten(desaturate($purple-base, 32%), 45%);\n$purple-300:  lighten(desaturate($purple-base, 32%), 30%);\n$purple-400:  lighten(desaturate($purple-base, 32%), 15%);\n$purple-500:  $purple-base;\n$purple-600:  darken($purple-base, 7%);\n$purple-700:  darken($purple-base, 14%);\n$purple-800:  darken($purple-base, 21%);\n$purple-900:  darken($purple-base, 28%);\n\n$bamboo-base: #B4BD00;\n$bamboo-100:  lighten(desaturate($bamboo-base, 43%), 56%);\n$bamboo-200:  lighten(desaturate($bamboo-base, 41%), 42%);\n$bamboo-300:  lighten(desaturate($bamboo-base, 41%), 28%);\n$bamboo-400:  lighten(desaturate($bamboo-base, 41%), 14%);\n$bamboo-500:  $bamboo-base;\n$bamboo-600:  darken(adjust-hue($bamboo-base, 5deg), 8%);\n$bamboo-700:  darken(adjust-hue($bamboo-base, 16deg), 16%);\n$bamboo-800:  darken(adjust-hue($bamboo-base, 34deg), 24%);\n$bamboo-900:  darken(adjust-hue($bamboo-base, 51deg), 31%);\n\n$green-base: #00953B;\n$green-100:  lighten(desaturate(adjust-hue($green-base, -2deg), 56%), 66%);\n$green-200:  lighten(desaturate(adjust-hue($green-base, -1deg), 55%), 50%);\n$green-300:  lighten(desaturate($green-base, 54%), 33%);\n$green-400:  lighten(desaturate($green-base, 54%), 17%);\n$green-500:  $green-base;\n$green-600:  darken($green-base, 6%);\n$green-700:  darken($green-base, 12%);\n$green-800:  darken($green-base, 18%);\n$green-900:  darken($green-base, 23%);\n\n\n$yellow-base: #FEC600;\n$yellow-100:  lighten($yellow-base, 46%);\n$yellow-200:  lighten($yellow-base, 35%);\n$yellow-300:  lighten($yellow-base, 23%);\n$yellow-400:  lighten($yellow-base, 12%);\n$yellow-500:  $yellow-base;\n$yellow-600:  darken(desaturate(adjust-hue($yellow-base, -6deg), 15%), 7%);\n$yellow-700:  darken(desaturate(adjust-hue($yellow-base, -16deg), 30%), 14%);\n$yellow-800:  darken(desaturate(adjust-hue($yellow-base, -28deg), 46%), 25%);\n$yellow-900:  darken(desaturate(adjust-hue($yellow-base, -40deg), 61%), 34%);\n\n$red-base: #FC2B00;\n$red-100:  lighten(desaturate($red-base, 36%), 46%);\n$red-200:  lighten(desaturate($red-base, 27%), 39%);\n$red-300:  lighten(desaturate($red-base, 18%), 30%);\n$red-400:  lighten(desaturate($red-base, 9%), 17%);\n$red-500:  $red-base;\n$red-600:  darken($red-base, 12%);\n$red-700:  darken($red-base, 22%);\n$red-800:  darken($red-base, 31%);\n$red-900:  darken($red-base, 41%);\n\n\n$grays: ();\n// stylelint-disable-next-line scss/dollar-variable-default\n$grays: map-merge(\n  (\n    \"100\": $gray-100,\n    \"200\": $gray-200,\n    \"300\": $gray-300,\n    \"400\": $gray-400,\n    \"500\": $gray-500,\n    \"600\": $gray-600,\n    \"700\": $gray-700,\n    \"800\": $gray-800,\n    \"900\": $gray-900\n  ),\n  $grays\n);\n\n$royals: ();\n$royals: map-merge(\n  (\n    \"100\": $royal-100,\n    \"200\": $royal-200,\n    \"300\": $royal-300,\n    \"400\": $royal-400,\n    \"500\": $royal-500,\n    \"600\": $royal-600,\n    \"700\": $royal-700,\n    \"800\": $royal-800,\n    \"900\": $royal-900\n  ),\n  $royals\n);\n\n$lavenders: ();\n$lavenders: map-merge(\n  (\n    \"100\": $lavender-100,\n    \"200\": $lavender-200,\n    \"300\": $lavender-300,\n    \"400\": $lavender-400,\n    \"500\": $lavender-500,\n    \"600\": $lavender-600,\n    \"700\": $lavender-700,\n    \"800\": $lavender-800,\n    \"900\": $lavender-900\n  ),\n  $lavenders\n);\n\n$blues: ();\n$blues: map-merge(\n  (\n    \"100\": $blue-100,\n    \"200\": $blue-200,\n    \"300\": $blue-300,\n    \"400\": $blue-400,\n    \"500\": $blue-500,\n    \"600\": $blue-600,\n    \"700\": $blue-700,\n    \"800\": $blue-800,\n    \"900\": $blue-900\n  ),\n  $blues\n);\n\n$purples: ();\n$purples: map-merge(\n  (\n    \"100\": $purple-100,\n    \"200\": $purple-200,\n    \"300\": $purple-300,\n    \"400\": $purple-400,\n    \"500\": $purple-500,\n    \"600\": $purple-600,\n    \"700\": $purple-700,\n    \"800\": $purple-800,\n    \"900\": $purple-900\n  ),\n  $purples\n);\n\n$bamboos: ();\n$bamboos: map-merge(\n  (\n    \"100\": $bamboo-100,\n    \"200\": $bamboo-200,\n    \"300\": $bamboo-300,\n    \"400\": $bamboo-400,\n    \"500\": $bamboo-500,\n    \"600\": $bamboo-600,\n    \"700\": $bamboo-700,\n    \"800\": $bamboo-800,\n    \"900\": $bamboo-900\n  ),\n  $bamboos\n);\n\n$yellows: ();\n$yellows: map-merge(\n  (\n    \"100\": $yellow-100,\n    \"200\": $yellow-200,\n    \"300\": $yellow-300,\n    \"400\": $yellow-400,\n    \"500\": $yellow-500,\n    \"600\": $yellow-600,\n    \"700\": $yellow-700,\n    \"800\": $yellow-800,\n    \"900\": $yellow-900\n  ),\n  $yellows\n);\n\n$reds: ();\n$reds: map-merge(\n  (\n    \"100\": $red-100,\n    \"200\": $red-200,\n    \"300\": $red-300,\n    \"400\": $red-400,\n    \"500\": $red-500,\n    \"600\": $red-600,\n    \"700\": $red-700,\n    \"800\": $red-800,\n    \"900\": $red-900\n  ),\n  $reds\n);\n\n\n$red:       $red-500;\n$bamboo:    $bamboo-500;\n$green:     $green-500;\n$lavender:  $lavender-500;\n$blue:      $blue-500;\n$purple:    $purple-500;\n$sky:       $lavender-100;\n$royal:     $royal-700;\n$yellow:    $yellow-500;\n\n\n$colors: ();\n// stylelint-disable-next-line scss/dollar-variable-default\n$colors: map-merge(\n  (\n    \"blue\":        $blue,\n    \"royal\":       $royal,\n    \"purple\":      $purple,\n    \"lavender\":    $lavender,\n    \"red\":         $red,\n    \"sky\":         $sky,\n    \"bamboo\":      $bamboo,\n    \"green\":       $green,\n    \"yellow\":      $yellow,\n    \"white\":       $white,\n    \"gray-light\":  $gray-300,\n    \"gray\":        $gray-500,\n    \"gray-dark\":   $gray-800\n  ),\n  $colors\n);\n\n$primary:       $royal;\n$secondary:     $purple;\n$success:       $green;\n$info:          $green;\n$warning:       $yellow;\n$danger:        $red;\n$action:        $purple;\n$lavender:      $lavender;\n$blue:          $blue;\n$light:         $sky;\n$dark:          $royal;\n$white:         $white;\n$gray-light:    $gray-300;\n$gray:          $gray-500;\n$gray-dark:     $gray-800;\n\n$theme-colors: ();\n// stylelint-disable-next-line scss/dollar-variable-default\n$theme-colors: map-merge(\n  (\n    \"primary\":    $primary,\n    \"secondary\":  $secondary,\n    \"success\":    $success,\n    \"info\":       $info,\n    \"warning\":    $warning,\n    \"danger\":     $danger,\n    \"action\":     $action,\n    \"lavender\":   $lavender,\n    \"blue\":       $blue,\n    \"light\":      $light,\n    \"dark\":       $dark,\n    \"white\":      $white,\n    \"gray-light\": $gray-light,\n    \"gray\":       $gray,\n    \"gray-dark\":  $gray-dark\n  ),\n  $theme-colors\n);\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\",\"%3c\"),\n  (\">\",\"%3e\"),\n  (\"#\",\"%23\"),\n  (\"(\",\"%28\"),\n  (\")\",\"%29\"),\n) !default;\n\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                false;\n$enable-shadows:                              true;\n\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n$spacer: 1rem;\n$spacers: ();\n// stylelint-disable-next-line scss/dollar-variable-default\n$spacers: map-merge(\n  (\n    0: 0,\n    1: ($spacer * .25),\n    2: ($spacer * .5),\n    3: $spacer,\n    4: ($spacer * 1.5),\n    5: ($spacer * 2),\n    6: ($spacer * 3),\n    7: ($spacer * 4),\n    8: ($spacer * 6),\n    9: ($spacer * 8),\n    10: ($spacer * 10),\n    11: ($spacer * 12),\n    12: ($spacer * 14),\n    13: ($spacer * 16),\n    14: ($spacer * 18),\n    15: ($spacer * 20)\n  ),\n  $spacers\n);\n\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$sizes: map-merge(\n  (\n    10: 10%,\n    15: 15%,\n    20: 25%,\n    25: 25%,\n    30: 30%,\n    35: 35%,\n    40: 40%,\n    45: 45%,\n    50: 50%,\n    55: 55%,\n    60: 60%,\n    65: 65%,\n    70: 70%,\n    75: 75%,\n    80: 80%,\n    85: 85%,\n    90: 90%,\n    95: 95%,\n    100: 100%,\n    auto: auto,\n    none: none\n  ),\n  $sizes\n);\n\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white;\n$body-color:                $gray-900;\n\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              color(\"blue\");\n$link-decoration:                         none;\n$link-hover-color:                        $blue-400;\n$link-hover-decoration:                   underline;\n// Darken percentage for links with `.text-*` class (e.g. `.text-success`)\n$emphasized-link-hover-darken-percentage: 15%;\n$outlined-link-hover-darken-percentage: 15%;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1.5rem;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 768px,\n  md: 992px,\n  lg: 1232px,\n  xl: 1472px\n);\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 732px,\n  md: 960px,\n  lg: 1164px,\n  xl: 1464px\n);\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12;\n$grid-gutter-width:           32px;\n$grid-row-columns:            6;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.25;\n$line-height-sm:              1.5;\n\n$border-width:                1px;\n$border-color:                $gray;\n\n$border-radius:               .5rem;\n$border-radius-lg:            .5rem;\n$border-radius-sm:            .5rem;\n\n$rounded-pill:                50rem;\n\n$box-shadow-sm:               3px 4px 8px rgba($black, .25);\n$box-shadow:                  3px 7px 14px rgba($black, .25);\n$box-shadow-lg:               $box-shadow;\n\n$component-active-color:      $white;\n$component-active-bg:         color(\"purple\");\n\n$transition-base:             all .2s ease-in-out;\n$transition-fade:             opacity .15s linear;\n$transition-collapse:         height .35s ease;\n\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      \"Brandon Text\", Arial, \"Helvetica Neue\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n$font-family-base:            $font-family-sans-serif;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem; // Assumes the browser default, typically `16px`\n$font-size-lg:                $font-size-base * 1.5;\n$font-size-sm:                $font-size-base * .75;\n\n$font-weight-lighter:         lighter;\n$font-weight-light:           300;\n$font-weight-normal:          400;\n$font-weight-bold:            700;\n$font-weight-bolder:          900;\n\n$font-weight-base:            $font-weight-normal;\n$line-height-base:            1.5;\n\n$h1-font-size:                $font-size-base * 4;\n$h2-font-size:                $font-size-base * 3;\n$h3-font-size:                $font-size-base * 2;\n$h4-font-size:                $font-size-base * 1.5;\n$h5-font-size:                $font-size-base * 1.25;\n$h6-font-size:                $font-size-base;\n\n$headings-margin-bottom:      $line-height-base * 1rem;\n$headings-font-family:        null;\n$headings-font-weight:        900;\n$headings-line-height:        1.125;\n$headings-color:              theme-color('primary');\n\n$display1-size:               4rem;\n$display2-size:               3rem;\n$display3-size:               2rem;\n$display4-size:               1.5rem;\n\n$display1-weight:             300;\n$display2-weight:             300;\n$display3-weight:             300;\n$display4-weight:             300;\n$display-line-height:         $headings-line-height;\n\n$lead-font-size:              $font-size-base * 1.5;\n$lead-font-weight:            400;\n\n$small-font-size:             $font-size-base * .75;\n\n$text-muted:                  $gray-600;\n\n$blockquote-padding-y:        2.25rem;\n$blockquote-padding-x:        1rem;\n$blockquote-padding:          $blockquote-padding-y 0 0 $blockquote-padding-x;\n$blockquote-bg:               url('https://images.kaplanco.com/images/css/quote.png') left #{$blockquote-padding-y - 1.25} no-repeat;\n$blockquote-color:            theme-color(\"blue\");\n$blockquote-small-color:      $gray-600;\n$blockquote-small-font-size:  $font-size-base;\n$blockquote-font-size:        $font-size-lg;\n\n$hr-border-color:             $border-color;\n$hr-border-width:             $border-width;\n\n$mark-padding:                .2em;\n\n$dt-font-weight:              $font-weight-bold;\n\n$list-inline-padding:         .5rem;\n\n$letter-spacing:              .1rem;\n\n$mark-bg:                     #fcf8e3;\n\n$hr-margin-y:                 $spacer;\n\n// Kapicons UI\n\n$ui-icons-font-family: \"Kapicons-UI\";\n$ui-icons-font-path: \"../fonts/Kapicons-UI\";\n\n$icon-angle-down: unquote('\"\\\\e800\"');\n$icon-angle-left: unquote('\"\\\\e801\"');\n$icon-angle-right: unquote('\"\\\\e802\"');\n$icon-angle-up: unquote('\"\\\\e803\"');\n$icon-double-angle-left: unquote('\"\\\\e804\"');\n$icon-double-angle-right: unquote('\"\\\\e805\"');\n$icon-minus: unquote('\"\\\\e806\"');\n$icon-plus: unquote('\"\\\\e807\"');\n$icon-cart: unquote('\"\\\\e808\"');\n$icon-chat: unquote('\"\\\\e809\"');\n$icon-hamburger: unquote('\"\\\\e80a\"');\n$icon-user: unquote('\"\\\\e80b\"');\n$icon-book: unquote('\"\\\\e80c\"');\n$icon-bulb: unquote('\"\\\\e80d\"');\n$icon-package: unquote('\"\\\\e80e\"');\n$icon-pencil: unquote('\"\\\\e80f\"');\n$icon-apple-k5: unquote('\"\\\\e810\"');\n$icon-dollar: unquote('\"\\\\e811\"');\n$icon-leaf: unquote('\"\\\\e812\"');\n$icon-star: unquote('\"\\\\e813\"');\n$icon-star-filled: unquote('\"\\\\e90d\"');\n$icon-tree: unquote('\"\\\\e814\"');\n$icon-truck: unquote('\"\\\\e815\"');\n$icon-save: unquote('\"\\\\e816\"');\n$icon-open: unquote('\"\\\\e817\"');\n$icon-trash: unquote('\"\\\\e818\"');\n$icon-facebook: unquote('\"\\\\e819\"');\n$icon-linkedin: unquote('\"\\\\e81a\"');\n$icon-pinterest: unquote('\"\\\\e81b\"');\n$icon-twitter: unquote('\"\\\\e91d\"');\n$icon-x-twitter: unquote('\"\\\\e81c\"');\n$icon-youtube: unquote('\"\\\\e81d\"');\n$icon-instagram: unquote('\"\\\\e81e\"');\n$icon-tiktok: unquote('\"\\\\e969\"');\n$icon-pdf: unquote('\"\\\\e81f\"');\n$icon-excel: unquote('\"\\\\e820\"');\n$icon-search: unquote('\"\\\\e821\"');\n$icon-share: unquote('\"\\\\e901\"');\n$icon-mail: unquote('\"\\\\e822\"');\n$icon-clipboard: unquote('\"\\\\e823\"');\n$icon-question: unquote('\"\\\\e824\"');\n$icon-grid-view: unquote('\"\\\\e825\"');\n$icon-list-view: unquote('\"\\\\e826\"');\n$icon-eye: unquote('\"\\\\e827\"');\n$icon-footsteps: unquote('\"\\\\e93e\"');\n$icon-heart: unquote('\"\\\\e828\"');\n$icon-point: unquote('\"\\\\e829\"');\n$icon-duplicate: unquote('\"\\\\e82a\"');\n$icon-checkmark: unquote('\"\\\\e902\"');\n$icon-circle-a: unquote('\"\\\\e903\"');\n$icon-circle-b: unquote('\"\\\\e908\"');\n$icon-circle-c: unquote('\"\\\\e90a\"');\n$icon-circle-d: unquote('\"\\\\e910\"');\n$icon-circle-e: unquote('\"\\\\e913\"');\n$icon-circle-f: unquote('\"\\\\e917\"');\n$icon-circle-g: unquote('\"\\\\e919\"');\n$icon-circle-h: unquote('\"\\\\e91b\"');\n$icon-circle-i: unquote('\"\\\\e91c\"');\n$icon-circle-j: unquote('\"\\\\e920\"');\n$icon-circle-k: unquote('\"\\\\e921\"');\n$icon-circle-l: unquote('\"\\\\e923\"');\n$icon-circle-m: unquote('\"\\\\e925\"');\n$icon-circle-n: unquote('\"\\\\e928\"');\n$icon-circle-o: unquote('\"\\\\e929\"');\n$icon-circle-p: unquote('\"\\\\e92a\"');\n$icon-circle-q: unquote('\"\\\\e92d\"');\n$icon-circle-r: unquote('\"\\\\e92f\"');\n$icon-circle-s: unquote('\"\\\\e930\"');\n$icon-circle-t: unquote('\"\\\\e934\"');\n$icon-circle-u: unquote('\"\\\\e937\"');\n$icon-circle-v: unquote('\"\\\\e938\"');\n$icon-circle-w: unquote('\"\\\\e939\"');\n$icon-circle-x: unquote('\"\\\\e93a\"');\n$icon-circle-y: unquote('\"\\\\e93b\"');\n$icon-circle-z: unquote('\"\\\\e93d\"');\n$icon-warning: unquote('\"\\\\ea07\"');\n$icon-notification: unquote('\"\\\\ea08\"');\n$icon-qr-code: unquote('\"\\\\e948\"');\n$icon-apple: unquote('\"\\\\e949\"');\n$icon-calendar: unquote('\"\\\\e94a\"');\n$icon-cancel: unquote('\"\\\\e94b\"');\n$icon-cash-bag: unquote('\"\\\\e94c\"');\n$icon-clock: unquote('\"\\\\e94e\"');\n$icon-contract: unquote('\"\\\\e94f\"');\n$icon-credit-card: unquote('\"\\\\e950\"');\n$icon-customer-support: unquote('\"\\\\e951\"');\n$icon-document: unquote('\"\\\\e952\"');\n$icon-earth: unquote('\"\\\\e914\"');\n$icon-fax: unquote('\"\\\\e955\"');\n$icon-gift: unquote('\"\\\\e956\"');\n$icon-graduation: unquote('\"\\\\e957\"');\n$icon-group: unquote('\"\\\\e958\"');\n$icon-map: unquote('\"\\\\e959\"');\n$icon-phone: unquote('\"\\\\e95a\"');\n$icon-cell-phone: unquote('\"\\\\e91e\"');\n$icon-play: unquote('\"\\\\e95b\"');\n$icon-puzzle: unquote('\"\\\\e95c\"');\n$icon-return-shipment: unquote('\"\\\\e95d\"');\n$icon-seesaw: unquote('\"\\\\e95e\"');\n$icon-shield: unquote('\"\\\\e960\"');\n$icon-speech-heart: unquote('\"\\\\e961\"');\n$icon-star-ribbon: unquote('\"\\\\e962\"');\n$icon-sunburst: unquote('\"\\\\e963\"');\n$icon-thought-cloud: unquote('\"\\\\e964\"');\n$icon-video: unquote('\"\\\\e965\"');\n$icon-warranty: unquote('\"\\\\e966\"');\n$icon-world-wide-web: unquote('\"\\\\e967\"');\n$icon-wrench: unquote('\"\\\\e968\"');\n$icon-storefront: unquote('\"\\\\e909\"');\n$icon-recycle: unquote('\"\\\\e90c\"');\n$icon-handicapped: unquote('\"\\\\e90e\"');\n$icon-discount: unquote('\"\\\\e90b\"');\n$icon-kaplan-icon: unquote('\"\\\\e970\"');\n$icon-gryphonhouse-icon: unquote('\"\\\\e900\"');\n$icon-c4l-icon: unquote('\"\\\\e972\"');\n$icon-extendednotes-icon: unquote('\"\\\\e973\"');\n$icon-fully-assembled: unquote('\"\\\\e90f\"');\n$icon-assembly-required: unquote('\"\\\\e911\"');\n$icon-installation-required: unquote('\"\\\\e912\"');\n$icon-surfacing-required: unquote('\"\\\\e915\"');\n$icon-portable: unquote('\"\\\\e916\"');\n$icon-color-options: unquote('\"\\\\e918\"');\n$icon-lift-gate: unquote('\"\\\\e91f\"');\n\n// Kapicons\n\n$icon-lg-font-family: \"Kapicons\";\n$icon-lg-font-path: \"../fonts/Kapicons\";\n\n$icon-lg-cart: unquote('\"\\\\e808\"');\n$icon-lg-chat: unquote('\"\\\\e809\"');\n$icon-lg-user: unquote('\"\\\\e80b\"');\n$icon-lg-book: unquote('\"\\\\e80c\"');\n$icon-lg-bulb: unquote('\"\\\\e80d\"');\n$icon-lg-package: unquote('\"\\\\e80e\"');\n$icon-lg-pencil: unquote('\"\\\\e80f\"');\n$icon-lg-apple-k5: unquote('\"\\\\e810\"');\n$icon-lg-leaf: unquote('\"\\\\e812\"');\n$icon-lg-star: unquote('\"\\\\e813\"');\n$icon-lg-star-filled: unquote('\"\\\\e90d\"');\n$icon-lg-tree: unquote('\"\\\\e814\"');\n$icon-lg-truck: unquote('\"\\\\e815\"');\n$icon-lg-save: unquote('\"\\\\e816\"');\n$icon-lg-open: unquote('\"\\\\e817\"');\n$icon-lg-trash: unquote('\"\\\\e818\"');\n$icon-lg-pdf: unquote('\"\\\\e81f\"');\n$icon-lg-excel: unquote('\"\\\\e820\"');\n$icon-lg-search: unquote('\"\\\\e821\"');\n$icon-lg-mail: unquote('\"\\\\e822\"');\n$icon-lg-clipboard: unquote('\"\\\\e823\"');\n$icon-lg-eye: unquote('\"\\\\e827\"');\n$icon-lg-heart: unquote('\"\\\\e828\"');\n$icon-lg-footsteps: unquote('\"\\\\e93e\"');\n$icon-lg-duplicate: unquote('\"\\\\e82a\"');\n$icon-lg-checkmark: unquote('\"\\\\e902\"');\n$icon-lg-warning: unquote('\"\\\\ea07\"');\n$icon-lg-notification: unquote('\"\\\\ea08\"');\n$icon-lg-qr-code: unquote('\"\\\\e948\"');\n$icon-lg-apple: unquote('\"\\\\e949\"');\n$icon-lg-calendar: unquote('\"\\\\e94a\"');\n$icon-lg-cancel: unquote('\"\\\\e94b\"');\n$icon-lg-cash-bag: unquote('\"\\\\e94c\"');\n$icon-lg-clock: unquote('\"\\\\e94e\"');\n$icon-lg-contract: unquote('\"\\\\e94f\"');\n$icon-lg-credit-card: unquote('\"\\\\e950\"');\n$icon-lg-customer-support: unquote('\"\\\\e951\"');\n$icon-lg-document: unquote('\"\\\\e952\"');\n$icon-lg-double-puzzle: unquote('\"\\\\e953\"');\n$icon-lg-earth: unquote('\"\\\\e914\"');\n$icon-lg-fax: unquote('\"\\\\e955\"');\n$icon-lg-gift: unquote('\"\\\\e956\"');\n$icon-lg-graduation: unquote('\"\\\\e957\"');\n$icon-lg-group: unquote('\"\\\\e958\"');\n$icon-lg-map: unquote('\"\\\\e959\"');\n$icon-lg-phone: unquote('\"\\\\e95a\"');\n$icon-lg-cell-phone: unquote('\"\\\\e91e\"');\n$icon-lg-play: unquote('\"\\\\e95b\"');\n$icon-lg-puzzle: unquote('\"\\\\e95c\"');\n$icon-lg-return-shipment: unquote('\"\\\\e95d\"');\n$icon-lg-seesaw: unquote('\"\\\\e95e\"');\n$icon-lg-shield: unquote('\"\\\\e960\"');\n$icon-lg-speech-heart: unquote('\"\\\\e961\"');\n$icon-lg-star-ribbon: unquote('\"\\\\e962\"');\n$icon-lg-sunburst: unquote('\"\\\\e963\"');\n$icon-lg-thought-cloud: unquote('\"\\\\e964\"');\n$icon-lg-video: unquote('\"\\\\e965\"');\n$icon-lg-warranty: unquote('\"\\\\e966\"');\n$icon-lg-world-wide-web: unquote('\"\\\\e967\"');\n$icon-lg-wrench: unquote('\"\\\\e968\"');\n$icon-lg-storefront: unquote('\"\\\\e909\"');\n$icon-lg-recycle: unquote('\"\\\\e90c\"');\n$icon-lg-handicapped: unquote('\"\\\\e90e\"');\n$icon-lg-discount: unquote('\"\\\\e90b\"');\n$icon-lg-fully-assembled: unquote('\"\\\\e90f\"');\n$icon-lg-assembly-required: unquote('\"\\\\e911\"');\n$icon-lg-installation-required: unquote('\"\\\\e912\"');\n$icon-lg-surfacing-required: unquote('\"\\\\e915\"');\n$icon-lg-portable: unquote('\"\\\\e916\"');\n$icon-lg-color-options: unquote('\"\\\\e918\"');\n$icon-lg-lift-gate: unquote('\"\\\\e91f\"');\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          1rem;\n$table-cell-padding-sm:       .5rem;\n\n$table-color:                 $body-color;\n$table-bg:                    null;\n$table-accent-bg:             rgba($gray-900, .05);\n$table-hover-color:           $table-color;\n$table-hover-bg:              rgba($lavender, .1);\n$table-active-bg:             $table-hover-bg;\n\n$table-border-width:          $border-width;\n$table-border-color:          $border-color;\n\n$table-head-bg:               $white;\n$table-head-color:            $gray-900;\n$table-head-letter-spacing:   .1rem;\n$table-head-font-weight:      700;\n$table-head-font-size:        $font-size-base;\n$table-head-text-transform:   uppercase;\n\n$table-dark-color:            $white;\n$table-dark-bg:               $lavender;\n$table-dark-accent-bg:        rgba($white, .05);\n$table-dark-hover-color:      $table-dark-color;\n$table-dark-hover-bg:         rgba($white, .075);\n$table-dark-border-color:     lighten($table-dark-bg, 7.5%) !default;\n\n$table-striped-order:         odd;\n\n$table-caption-color:         $text-muted !default;\n\n$table-bg-level:              -9 !default;\n$table-border-level:          -6 !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         1rem;\n$input-btn-padding-x:         2rem;\n$input-btn-font-family:       null;\n$input-btn-font-size:         $font-size-base;\n$input-btn-letter-space:      .2rem;\n$input-btn-text-transform:    uppercase;\n$input-btn-line-height:       1;\n\n$input-btn-focus-width:       .2rem;\n$input-btn-focus-color:       rgba($component-active-bg, .25);\n$input-btn-focus-box-shadow:  none;\n\n$input-btn-padding-y-sm:      .75rem;\n$input-btn-padding-x-sm:      1.25rem;\n$input-btn-font-size-sm:      $font-size-sm;\n$input-btn-line-height-sm:    1;\n\n$input-btn-padding-y-lg:      1.25rem;\n$input-btn-padding-x-lg:      2.75rem;\n$input-btn-font-size-lg:      1.25rem;\n$input-btn-line-height-lg:    1;\n\n$input-btn-border-width:      $border-width;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y;\n$btn-padding-x:               $input-btn-padding-x;\n$btn-font-family:             $input-btn-font-family;\n$btn-font-size:               $input-btn-font-size;\n$btn-letter-space:            $input-btn-letter-space;\n$btn-text-transform:          $input-btn-text-transform;\n$btn-line-height:             $input-btn-line-height;\n$btn-white-space:             null; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm;\n$btn-padding-x-sm:            $input-btn-padding-x-sm;\n$btn-font-size-sm:            $input-btn-font-size-sm;\n$btn-line-height-sm:          $input-btn-line-height-sm;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg;\n$btn-padding-x-lg:            $input-btn-padding-x-lg;\n$btn-font-size-lg:            $input-btn-font-size-lg;\n$btn-line-height-lg:          $input-btn-line-height-lg;\n\n$btn-border-width:            $input-btn-border-width;\n\n$btn-font-weight:             900;\n$btn-box-shadow:              none;\n$btn-focus-width:             $input-btn-focus-width;\n$btn-focus-box-shadow:        none;\n$btn-disabled-opacity:        .65;\n$btn-active-box-shadow:       none;\n\n$btn-link-disabled-color:     $gray-600;\n\n$btn-block-spacing-y:         .5rem;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           28px;\n$btn-border-radius-lg:        34px;\n$btn-border-radius-sm:        26px;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n\n// Forms\n\n$label-margin-bottom:                   .5rem;\n\n$input-padding-y:                       $input-btn-padding-y;\n$input-padding-x:                       $input-btn-padding-x;\n$input-font-family:                     $input-btn-font-family;\n$input-font-size:                       $input-btn-font-size;\n$input-font-weight:                     $font-weight-base;\n$input-line-height:                     $input-btn-line-height;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm;\n$input-padding-x-sm:                    $input-btn-padding-x-sm;\n$input-font-size-sm:                    $input-btn-font-size-sm;\n$input-line-height-sm:                  $input-btn-line-height-sm;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg;\n$input-padding-x-lg:                    $input-btn-padding-x-lg;\n$input-font-size-lg:                    $input-btn-font-size-lg;\n$input-line-height-lg:                  $input-btn-line-height-lg;\n\n$input-bg:                              $white;\n$input-disabled-bg:                     $gray-300;\n\n$input-color:                           $gray-900;\n$input-border-color:                    $border-color;\n$input-border-width:                    $input-btn-border-width;\n$input-box-shadow:                      none;\n\n$input-border-radius:                   28px;\n$input-border-radius-lg:                34px;\n$input-border-radius-sm:                26px;\n\n$input-focus-bg:                        $input-bg;\n$input-focus-border-color:              color('blue');\n$input-focus-color:                     $input-color;\n$input-focus-width:                     $input-btn-focus-width;\n$input-focus-box-shadow:                $box-shadow-sm;\n\n$input-placeholder-color:               $gray-700;\n$input-plaintext-color:                 $body-color;\n\n$input-height-border:                   $input-border-width * 2;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2);\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y);\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y / 2);\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false));\n$input-height-sm:                       add($input-line-height-sm * 1em, add($input-padding-y-sm * 2, $input-height-border, false));\n$input-height-lg:                       add($input-line-height-lg * 1em, add($input-padding-y-lg * 2, $input-height-border, false));\n\n$form-text-margin-top:                  .25rem;\n\n$form-check-input-gutter:               1.375rem;\n$form-check-input-margin-y:             .3rem;\n$form-check-input-margin-x:             .25rem;\n\n$form-check-inline-margin-x:            .75rem;\n$form-check-inline-input-margin-x:      .3125rem;\n\n$form-grid-gutter-width:                16px;\n$form-group-margin-bottom:              2rem;\n\n$input-group-addon-color:               $input-color;\n$input-group-addon-bg:                  $gray-200;\n$input-group-addon-border-color:        $input-border-color;\n\n$custom-forms-transition:               background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$custom-control-gutter:                 .5rem;\n$custom-control-spacer-x:               1rem;\n$custom-control-cursor:                 null;\n\n$custom-control-indicator-size:         1.625rem;\n$custom-control-indicator-bg:           $input-bg;\n\n$custom-control-indicator-bg-size:      50% 50%;\n$custom-control-indicator-box-shadow:   $input-box-shadow;\n$custom-control-indicator-border-color: $input-border-color;\n$custom-control-indicator-border-width: $input-border-width;\n\n$custom-control-label-color:            null;\n\n$custom-control-indicator-disabled-bg:          $input-disabled-bg;\n$custom-control-label-disabled-color:           $gray-600;\n\n$custom-control-indicator-checked-color:        $component-active-color;\n$custom-control-indicator-checked-bg:           $component-active-bg;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"secondary\"), .5);\n$custom-control-indicator-checked-box-shadow:   none;\n$custom-control-indicator-checked-border-color: $custom-control-indicator-checked-bg;\n\n$custom-control-indicator-focus-box-shadow:     none;\n$custom-control-indicator-focus-border-color:   $custom-control-indicator-checked-border-color;\n\n$custom-control-indicator-active-color:         $component-active-color;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%);\n$custom-control-indicator-active-box-shadow:    none;\n$custom-control-indicator-active-border-color:  $custom-control-indicator-active-bg;\n\n$custom-checkbox-indicator-border-radius:       0;\n$custom-checkbox-indicator-icon-checked:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'><path fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/></svg>\");\n\n$custom-checkbox-indicator-indeterminate-bg:           $component-active-bg;\n$custom-checkbox-indicator-indeterminate-color:        $custom-control-indicator-checked-color;\n$custom-checkbox-indicator-icon-indeterminate:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'><path stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/></svg>\");\n$custom-checkbox-indicator-indeterminate-box-shadow:   none;\n$custom-checkbox-indicator-indeterminate-border-color: $custom-checkbox-indicator-indeterminate-bg;\n\n$custom-radio-indicator-border-radius:          50%;\n$custom-radio-indicator-icon-checked:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='-4 -4 8 8'><circle r='3' fill='#{$custom-control-indicator-checked-color}'/></svg>\");\n\n$custom-select-padding-y:           $input-padding-y;\n$custom-select-padding-x:           $input-padding-x;\n$custom-select-font-family:         $input-font-family;\n$custom-select-font-size:           $input-font-size;\n$custom-select-height:              $input-height;\n$custom-select-indicator-padding:   1rem; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-font-weight:         $input-font-weight;\n$custom-select-line-height:         $input-line-height;\n$custom-select-color:               $input-color;\n$custom-select-disabled-color:      $gray-600;\n$custom-select-bg:                  $input-bg;\n$custom-select-disabled-bg:         $gray-200;\n$custom-select-bg-size:             8px 10px; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800;\n$custom-select-indicator:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5'><path fill='#{$custom-select-indicator-color}' d='M.22 2.03a.827.827 0 01-.16-.2.533.533 0 01-.06-.24V.33C0 .*********.04.3-.02.41-.02.51.05l.02.02c.01 0 .02.01.02.02l3.27 1.95c.***********.37 0L7.45.09l.04-.04c.11-.06.22-.07.33-.***********.16.18.29v1.26c0 .19-.07.33-.22.44l-3.6 2.89c-.05.05-.1.08-.17.08s-.13-.03-.19-.08L.22 2.03z'/></svg>\");\n$custom-select-background:          escape-svg($custom-select-indicator) no-repeat right $custom-select-padding-x center / $custom-select-bg-size; // Used so we can have multiple background elements (e.g., arrow and feedback icon)\n\n$custom-select-feedback-icon-padding-right: add(1em * .75, (2 * $custom-select-padding-y * .75) + $custom-select-padding-x + $custom-select-indicator-padding);\n$custom-select-feedback-icon-position:      center right ($custom-select-padding-x + $custom-select-indicator-padding);\n$custom-select-feedback-icon-size:          $input-height-inner-half $input-height-inner-half;\n\n$custom-select-border-width:        $input-border-width;\n$custom-select-border-color:        $input-border-color;\n$custom-select-border-radius:       $input-border-radius;\n$custom-select-box-shadow:          none;\n\n$custom-select-focus-border-color:  $input-focus-border-color;\n$custom-select-focus-width:         $input-focus-width;\n$custom-select-focus-box-shadow:    $input-focus-box-shadow;\n\n$custom-select-padding-y-sm:        $input-padding-y-sm;\n$custom-select-padding-x-sm:        $input-padding-x-sm;\n$custom-select-font-size-sm:        $input-font-size-sm;\n$custom-select-height-sm:           $input-height-sm;\n\n$custom-select-padding-y-lg:        $input-padding-y-lg;\n$custom-select-padding-x-lg:        $input-padding-x-lg;\n$custom-select-font-size-lg:        $input-font-size-lg;\n$custom-select-height-lg:           $input-height-lg;\n\n\n// Form validation\n\n$form-feedback-margin-top:          $form-text-margin-top;\n$form-feedback-font-size:           $small-font-size;\n$form-feedback-valid-color:         theme-color(\"success\");\n$form-feedback-invalid-color:       theme-color(\"danger\");\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color;\n$form-feedback-icon-valid:          none;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color;\n$form-feedback-icon-invalid:        none;\n\n$form-validation-states: ();\n// stylelint-disable-next-line scss/dollar-variable-default\n$form-validation-states: map-merge(\n  (\n    \"valid\": (\n      \"color\": $form-feedback-valid-color,\n      \"icon\": $form-feedback-icon-valid\n    ),\n    \"invalid\": (\n      \"color\": $form-feedback-invalid-color,\n      \"icon\": $form-feedback-icon-invalid\n    ),\n  ),\n  $form-validation-states\n);\n\n\n// Navs\n\n$nav-link-padding-y:                .5rem;\n$nav-link-padding-x:                1rem;\n$nav-link-disabled-color:           $gray-600;\n\n$nav-tabs-border-color:             $gray-600;\n$nav-tabs-border-width:             $border-width;\n$nav-tabs-border-radius:            $border-radius;\n$nav-tabs-link-hover-border-color:  transparent transparent $nav-tabs-border-color;\n$nav-tabs-link-active-color:        $component-active-bg;\n$nav-tabs-link-active-bg:           $body-bg;\n$nav-tabs-link-active-border-color: transparent transparent $nav-tabs-border-color;\n\n$nav-pills-border-radius:           $border-radius;\n$nav-pills-link-active-color:       $component-active-color;\n$nav-pills-link-active-bg:          $component-active-bg;\n\n$nav-divider-color:                 $gray-300;\n$nav-divider-margin-y:              $spacer / 2;\n\n$nav-link-font-size:                $font-size-base;\n$nav-link-font-weight:              700;\n$nav-link-line-height:              1em;\n$nav-link-letter-spacing:           .2rem;\n$nav-link-text-transform:           uppercase;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem;\n$dropdown-padding-y:                .5rem;\n$dropdown-spacer:                   .125rem;\n$dropdown-font-size:                $font-size-base;\n$dropdown-color:                    $body-color;\n$dropdown-bg:                       $white;\n$dropdown-border-color:             $gray-300;\n$dropdown-border-radius:            $border-radius;\n$dropdown-border-width:             $border-width;\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width);\n$dropdown-divider-bg:               $gray-500;\n$dropdown-divider-margin-y:         $nav-divider-margin-y;\n$dropdown-box-shadow:               $box-shadow-sm;\n\n$dropdown-link-color:               $gray-900;\n$dropdown-link-hover-color:         $link-hover-color;\n$dropdown-link-hover-bg:            transparent;\n\n$dropdown-link-active-color:        $component-active-color;\n$dropdown-link-active-bg:           $component-active-bg;\n\n$dropdown-link-disabled-color:      $gray-600;\n\n$dropdown-item-padding-y:           .25rem;\n$dropdown-item-padding-x:           1rem;\n\n$dropdown-header-color:             $white;\n$dropdown-header-bg:                color(\"lavender\");\n$dropdown-header-font-size:         .75rem;\n$dropdown-header-font-weight:       700;\n$dropdown-header-text-transform:    uppercase;\n$dropdown-header-letter-spacing:    .2rem;\n$dropdown-header-padding:           $spacer / 2;\n\n\n// Pagination\n\n$pagination-padding-y:              .8125rem;\n$pagination-padding-x:              .8125rem;\n$pagination-padding-y-sm:           .5rem;\n$pagination-padding-x-sm:           .75rem;\n$pagination-padding-y-lg:           1rem;\n$pagination-padding-x-lg:           1.75rem;\n$pagination-line-height:            1;\n\n$pagination-color:                  $link-color;\n$pagination-bg:                     $white;\n$pagination-border-width:           0;\n$pagination-border-color:           transparent;\n\n$pagination-focus-box-shadow:       none;\n$pagination-focus-outline:          0;\n\n$pagination-hover-color:            $link-hover-color;\n$pagination-hover-bg:               $gray-300;\n$pagination-hover-border-color:     transparent;\n\n$pagination-active-color:           $component-active-color;\n$pagination-active-bg:              $component-active-bg;\n$pagination-active-border-color:    transparent;\n\n$pagination-disabled-color:         $gray-600;\n$pagination-disabled-bg:            $white;\n$pagination-disabled-border-color:  transparent;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-color:                   null !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     1rem;\n$card-spacer-x:                     1rem;\n$card-border-width:                 $border-width;\n$card-border-radius:                $border-radius;\n$card-border-color:                 $border-color;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width);\n$card-cap-bg:                       rgba($black, .03);\n$card-cap-color:                    null;\n$card-height:                       null;\n$card-color:                        null;\n$card-bg:                           $white;\n\n$card-img-overlay-padding:          1rem;\n\n$card-group-margin:                 $grid-gutter-width / 2;\n$card-deck-margin:                  $card-group-margin;\n\n$card-columns-count:                3;\n$card-columns-gap:                  1rem;\n$card-columns-margin:               $card-spacer-y;\n\n\n// Tooltips\n\n$tooltip-font-size:                 $font-size-sm;\n$tooltip-max-width:                 200px;\n$tooltip-color:                     color('sky');\n$tooltip-bg:                        color('royal');\n$tooltip-border-radius:             $border-radius;\n$tooltip-opacity:                   1;\n$tooltip-padding-y:                 1rem;\n$tooltip-padding-x:                 1.25rem;\n$tooltip-margin:                    0;\n\n$tooltip-img-color:                  $body-color;\n$tooltip-img-bg:                     color('sky');\n\n$tooltip-arrow-width:               .8rem;\n$tooltip-arrow-height:              .4rem;\n$tooltip-arrow-color:               $tooltip-bg;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x;\n$form-feedback-tooltip-font-size:     $tooltip-font-size;\n$form-feedback-tooltip-line-height:   $line-height-base;\n$form-feedback-tooltip-opacity:       1;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius;\n\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm;\n$popover-bg:                        $white;\n$popover-max-width:                 276px;\n$popover-border-width:              $border-width;\n$popover-border-color:              $dropdown-border-color;\n$popover-border-radius:             $border-radius;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow:                $box-shadow-sm;\n\n$popover-header-bg:                 color('sky');\n$popover-header-color:              $headings-color;\n$popover-header-padding-y:          1rem;\n$popover-header-padding-x:          1.25rem;\n\n$popover-body-color:                $body-color;\n$popover-body-padding-y:            $popover-header-padding-y;\n$popover-body-padding-x:            $popover-header-padding-x;\n\n$popover-arrow-width:               1rem;\n$popover-arrow-height:              .5rem;\n$popover-arrow-color:               $popover-bg;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05);\n\n\n// Badges\n\n$badge-font-size:                   50%;\n$badge-font-weight:                 $font-weight-bold;\n$badge-padding-y:                   .5em;\n$badge-padding-x:                   .8em;\n$badge-border-radius:               50%;\n\n$badge-transition:                  $btn-transition;\n$badge-focus-width:                 $input-btn-focus-width;\n\n$badge-pill-padding-x:              1.25em;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:               1rem;\n\n// Margin between elements in footer, must be lower than or equal to 2 * $modal-inner-padding\n$modal-footer-margin-between:       .5rem;\n\n$modal-dialog-margin:               .5rem;\n$modal-dialog-margin-y-sm-up:       1.75rem;\n\n$modal-title-line-height:           1;\n\n$modal-content-color:               null;\n$modal-content-bg:                  $white;\n$modal-content-border-color:        transparent;\n$modal-content-border-width:        0;\n$modal-content-border-radius:       $border-radius;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width);\n$modal-content-box-shadow-xs:       $box-shadow;\n$modal-content-box-shadow-sm-up:    $box-shadow-sm;\n\n$modal-backdrop-bg:                 $black;\n$modal-backdrop-opacity:            .3;\n$modal-header-border-color:         $gray-500;\n$modal-footer-border-color:         transparent;\n$modal-header-border-width:         1px;\n$modal-footer-border-width:         0;\n$modal-footer-bg:                   color('sky');\n$modal-header-padding-y:            1rem;\n$modal-header-padding-x:            0;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x; // Keep this for backwards compatibility\n$modal-header-margin-y:             0;\n$modal-header-margin-x:             1rem;\n$modal-header-margin:               $modal-header-margin-y $modal-header-margin-x;\n\n$modal-xl:                          1140px;\n$modal-lg:                          800px;\n$modal-md:                          500px;\n$modal-sm:                          300px;\n\n$modal-fade-transform:              translate(0, -50px);\n$modal-show-transform:              none;\n$modal-transition:                  transform .3s ease-out;\n$modal-scale-transform:             scale(1.02);\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   1rem;\n$alert-padding-x:                   1rem;\n$alert-margin-bottom:               1rem;\n$alert-border-radius:               $border-radius;\n$alert-link-font-weight:            $font-weight-bold;\n$alert-line-height:                 1rem;\n$alert-border-width:                0;\n\n$alert-bg-level:                    0;\n$alert-border-level:                0;\n$alert-color-level:                 -12;\n\n\n// List group\n\n$list-group-color:                  null;\n$list-group-bg:                     $white;\n$list-group-border-color:           transparent;\n$list-group-border-width:           0;\n$list-group-border-radius:          $border-radius;\n\n$list-group-item-padding-y:         .25rem;\n$list-group-item-padding-x:         1rem;\n\n$list-group-hover-bg:               $gray-300;\n$list-group-active-color:           $component-active-bg;\n$list-group-active-bg:              $white;\n$list-group-active-border-color:    $list-group-active-bg;\n\n$list-group-disabled-color:         $gray-600;\n$list-group-disabled-bg:            $list-group-bg;\n\n$list-group-action-color:           null;\n$list-group-action-hover-color:     $list-group-action-color;\n\n$list-group-action-active-color:    $component-active-color;\n$list-group-action-active-bg:       $component-active-bg;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .625rem;\n$thumbnail-bg:                      $body-bg;\n$thumbnail-border-width:            0;\n$thumbnail-border-color:            transparent;\n$thumbnail-border-radius:           0;\n$thumbnail-box-shadow:              none;\n\n\n// Figures\n\n$figure-caption-font-size:          $font-size-sm;\n$figure-caption-color:              $body-color;\n\n\n// Breadcrumbs\n\n$breadcrumb-font-size:              null;\n\n$breadcrumb-padding-y:              0;\n$breadcrumb-padding-x:              0;\n$breadcrumb-item-padding:           .5rem;\n\n$breadcrumb-margin-bottom:          0;\n\n$breadcrumb-bg:                     transparent;\n$breadcrumb-divider-color:          $gray-600;\n$breadcrumb-active-color:           $gray-700;\n$breadcrumb-divider-font-family:    kapicons-ui;\n$breadcrumb-divider:                '\\e802';\n\n$breadcrumb-border-radius:          0;\n\n\n// Close\n\n$close-font-size:                   $font-size-base;\n$close-font-weight:                 $font-weight-normal;\n$close-color:                       $gray-700;\n$close-text-shadow:                 none;\n\n\n// Hamburger Button\n\n$hamburger-padding-x           : 0px !default;\n$hamburger-padding-y           : 0px !default;\n$hamburger-layer-width         : 32px !default;\n$hamburger-layer-height        : 4px !default;\n$hamburger-layer-spacing       : 6px !default;\n$hamburger-layer-color         : $lavender !default;\n$hamburger-layer-border-radius : 4px !default;\n$hamburger-hover-opacity       : 1 !default;\n$hamburger-active-layer-color  : $link-hover-color !default;\n$hamburger-active-hover-opacity: $hamburger-hover-opacity !default;\n\n// To use CSS filters as the hover effect instead of opacity,\n// set $hamburger-hover-use-filter as true and\n// change the value of $hamburger-hover-filter accordingly.\n$hamburger-hover-use-filter   : false !default;\n$hamburger-hover-filter       : opacity(50%) !default;\n$hamburger-active-hover-filter: $hamburger-hover-filter !default;\n\n// Types (Remove or comment out what you don’t need)\n$hamburger-types: (\n  elastic,\n  elastic-r,\n) !default;\n\n\n\n// Slick Carousel\n\n// Slick icon entity codes outputs the following\n// \"\\2190\" outputs ascii character \"←\"\n// \"\\2192\" outputs ascii character \"→\"\n// \"\\2022\" outputs ascii character \"•\"\n\n$slick-font-path: \"fonts/\";\n$slick-font-family: \"kapicons\";\n$slick-loader-path: \"./\";\n$slick-arrow-color: $gray-700;\n$slick-dot-color: $white;\n$slick-dot-color-active: $slick-dot-color;\n$slick-prev-character: \"\\e801\";\n$slick-next-character: \"\\e802\";\n$slick-dot-character: \"\\2022\";\n$slick-dot-size: 6px;\n$slick-opacity-default: 0.75;\n$slick-opacity-on-hover: 1;\n$slick-opacity-not-active: 0.25;\n", "// Button variants\r\n//\r\n// Easily pump out default styles, as well as :hover, :focus, :active,\r\n// and disabled options for all buttons\r\n\r\n@mixin button-variant($background, $border, $hover-background: lighten($background, 10%), $hover-border: lighten($border, 10%), $active-background: lighten($background, 10%), $active-border: lighten($border, 10%)) {\r\n  color: color-yiq($background);\r\n  @include gradient-bg($background);\r\n  border-color: $border;\r\n  @include box-shadow($btn-box-shadow);\r\n\r\n  @include hover() {\r\n    color: color-yiq($hover-background);\r\n    @include gradient-bg($hover-background);\r\n    border-color: $hover-border;\r\n  }\r\n\r\n  &:focus,\r\n  &.focus {\r\n    color: color-yiq($hover-background);\r\n    @include gradient-bg($hover-background);\r\n    border-color: $hover-border;\r\n    @if $enable-shadows {\r\n      box-shadow: none;\r\n    } @else {\r\n      // Avoid using mixin so we can pass custom focus shadow properly\r\n      box-shadow: none;\r\n    }\r\n  }\r\n\r\n  // Disabled comes first so active can properly restyle\r\n  &.disabled,\r\n  &:disabled {\r\n    color: color-yiq($background);\r\n    background-color: $background;\r\n    border-color: $border;\r\n    // Remove CSS gradients if they're enabled\r\n    @if $enable-gradients {\r\n      background-image: none;\r\n    }\r\n  }\r\n\r\n  &:not(:disabled):not(.disabled):active,\r\n  &:not(:disabled):not(.disabled).active,\r\n  .show > &.dropdown-toggle {\r\n    color: color-yiq($active-background);\r\n    background-color: $active-background;\r\n    @if $enable-gradients {\r\n      background-image: none; // Remove the gradient for the pressed/active state\r\n    }\r\n    border-color: $active-border;\r\n\r\n    &:focus {\r\n      @if $enable-shadows and $btn-active-box-shadow != none {\r\n        box-shadow: none;\r\n      } @else {\r\n        // Avoid using mixin so we can pass custom focus shadow properly\r\n        box-shadow: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@mixin button-outline-variant($color, $color-hover: color-yiq($color), $active-background: $color, $active-border: $color) {\r\n  color: $color;\r\n  border-color: $color;\r\n\r\n  @include hover() {\r\n    color: $color-hover;\r\n    background-color: $active-background;\r\n    border-color: $active-border;\r\n  }\r\n\r\n  &:focus,\r\n  &.focus {\r\n    box-shadow: none;\r\n  }\r\n\r\n  &.disabled,\r\n  &:disabled {\r\n    color: $color;\r\n    background-color: transparent;\r\n  }\r\n\r\n  &:not(:disabled):not(.disabled):active,\r\n  &:not(:disabled):not(.disabled).active,\r\n  .show > &.dropdown-toggle {\r\n    color: color-yiq($active-background);\r\n    background-color: $active-background;\r\n    border-color: $active-border;\r\n\r\n    &:focus {\r\n      @if $enable-shadows and $btn-active-box-shadow != none {\r\n        box-shadow: none;\r\n      } @else {\r\n        // Avoid using mixin so we can pass custom focus shadow properly\r\n        box-shadow: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Button sizes\r\n@mixin button-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\r\n  padding: $padding-y $padding-x;\r\n  @include font-size($font-size);\r\n  line-height: $line-height;\r\n  // Manually declare to provide an override to the browser default\r\n  @include border-radius($border-radius, 0);\r\n}\r\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "// stylelint-disable property-blacklist\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", ".page-header {\n  position: relative;\n  display: grid;\n  grid-template-columns: repeat(1, 1fr);\n  overflow-x: hidden;\n\n  \n  .page-header-content {\n    background-image: url('assets/images/header-bg-sm.webp');\n    background-position: center;\n    background-repeat: no-repeat;\n    background-size: cover;\n    padding: 2rem;\n\n    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n      background-image: url('assets/images/header-bg-sm-2x.webp');\n    }\n\n    .page-header-content-container {\n      position: relative;\n      background: rgba($white, .75);\n      padding: 3rem;\n\n      &::before {\n        content: '';\n        display: block;\n        width: 96px;\n        height: 96px;\n        position: absolute;\n        top: .75rem;\n        left: .75rem;\n        background: url('assets/images/corner-flare-bamboo.svg') no-repeat top left/100% 100%;\n        transform: rotate(180deg);\n        opacity: .5;\n        mix-blend-mode: luminosity;\n      }\n\n      &::after {\n        content: '';\n        display: block;\n        width: 96px;\n        height: 96px;\n        position: absolute;\n        bottom: .75rem;\n        right: .75rem;\n        background: url('assets/images/corner-flare-bamboo.svg') no-repeat bottom right/100% 100%;\n        opacity: .5;\n        mix-blend-mode: luminosity;\n      }\n\n      h1 {\n        font-size: clamp(2.5rem, 1.1732rem + 5.8968vw, 4rem);\n        font-weight: $font-weight-normal;\n        color: $green;\n      }\n    }\n  }\n\n  .page-header-cta {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: flex-start;\n    gap: .5rem;\n  }\n\n  .hero-img {\n    width: 100%;\n    height: auto;\n    object-fit: cover;\n  }\n\n  .page-header-rta {\n    position: absolute;\n    bottom: .75rem;\n    right: .75rem;\n    width: 150px;\n    height: auto;\n\n    img {\n      width: 100%;\n      height: auto;\n    }\n  }\n\n  .btn {\n    @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-line-height-sm, $btn-border-radius-sm);\n  }\n\n  @include media-breakpoint-up(sm) {\n    grid-template-columns: repeat(2, 1fr);\n    grid-template-rows: repeat(2, 1fr);\n  \n    .page-header-content {\n      grid-column: 1 / span 1;\n      grid-row: 1 / span 2;\n\n      .page-header-content-container {\n\n        h1 {\n          font-size: 2.5rem;\n        }\n      }\n    }\n\n    picture {\n      grid-column: 2 / span 1;\n      grid-row: 1 / span 2;\n    }\n\n    .hero-img {\n      height: 100%;\n    }\n  }\n\n  @include media-breakpoint-up(md) {\n  \n    .page-header-content {\n      height: 100%;\n      padding: 2rem;\n      display: flex;\n      flex-direction: row;\n      justify-content: center;\n      align-items: center;\n\n      .page-header-content-container {\n\n        h1 {\n          font-size: 2.75rem;\n        }\n      }\n    }\n  }\n\n  @include media-breakpoint-up(lg) {\n  \n    .page-header-content {\n      background-image: url('assets/images/header-bg-lg.webp');\n\n      @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n        background-image: url('assets/images/header-bg-lg-2x.webp');\n      }\n\n      .page-header-content-container {\n\n        h1 {\n          font-size: 2.75rem;\n        }\n\n        .page-header-cta {\n          flex-direction: row;\n          justify-content: stretch;\n          align-items: flex-start;\n        }\n      }\n    }\n  }\n\n  @include media-breakpoint-up(xl) {\n    height: fit-content;\n    grid-template-columns: repeat(13, 1fr);\n    grid-template-rows: repeat(2, 1fr);\n  \n    .page-header-content {\n      grid-column: 1 / span 6;\n      grid-row: 1 / span 2;\n\n      .page-header-content-container {\n\n        h1 {\n          font-size: 3.5rem;\n        }\n\n        .page-header-cta {\n          flex-direction: row;\n          justify-content: stretch;\n          align-items: flex-start;\n\n          .btn {\n            flex: 1 1 50%;\n          }\n        }\n      }\n    }\n\n    picture {\n      grid-column: 7 / span 7;\n      grid-row: 1 / span 2;\n    }\n  }\n\n  @media (min-width: 1900px) {\n  \n    .page-header-content {\n      grid-column: 1 / span 6;\n      grid-row: 1 / span 2;\n\n      .page-header-content-container {\n        padding: 2.5vw; \n\n        &::before {\n          width: 5.4vw;\n          height: 5.4vw;\n          top: .83vw;\n          left: .83vw;\n        }\n  \n        &::after {\n          width: 5.4vw;\n          height: 5.4vw;\n          bottom: .83vw;\n          right: .83vw;\n        }\n\n        h1 {\n          font-size: clamp(4rem, 1.1212rem + 2.4242vw, 5rem);\n          padding: 0 clamp(2rem, -0.8788rem + 2.4242vw, 3rem);\n        }\n\n        .page-header-cta {\n          \n          .btn {\n            flex: 1 1 50%;\n          }\n        }\n      }\n    }\n\n    .btn {\n      @include button-size($btn-padding-y, $btn-padding-x, $btn-font-size, $btn-line-height, $btn-border-radius);\n    }\n  }\n\n  \n}", ".intro {\n\n  #vy-image {\n    display: none;\n  }\n\n  .card {\n    border: none;\n    border-radius: 0;\n    \n    img {\n      border-radius: 0;\n\n      @include media-breakpoint-up(md) {\n        width: 100%;\n        aspect-ratio: 1/1;\n        object-fit: cover;\n      }\n\n      @include media-breakpoint-up(lg) {\n        aspect-ratio: 4/3;\n      }\n\n      @include media-breakpoint-up(xl) {\n        aspect-ratio: 16/9;\n      }\n    }\n  }\n}", ".skill-building {\n  overflow-x: hidden;\n\n  .skill-building-container {\n    position: relative;\n    background: url('assets/images/leaf.webp') left -50px bottom/90% no-repeat, url('assets/images/bg-wood-green-sm.webp') center/cover no-repeat;\n    background-blend-mode: overlay;\n    padding: calc(35px + 1rem) 0 calc(20px + 60vw) 0;\n\n    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n      background-image: url('assets/images/leaf-2x.webp'), url('assets/images/bg-wood-green-sm-2x.webp');\n    }\n\n    .container-fluid {\n      position: relative;\n      z-index: 2;\n    }\n\n    .skill-building-content {\n      position: relative;\n      color: $white;\n      padding: 6vw;\n      height: fit-content;\n\n      &::before {\n        content: '';\n        display: block;\n        width: clamp(4.5rem, 1.4042rem + 13.7592vw, 8rem);\n        height: clamp(4.5rem, 1.4042rem + 13.7592vw, 8rem);\n        position: absolute;\n        top: 0;\n        left: 0;\n        background: url('assets/images/corner-flare-white.svg') no-repeat top left/100% 100%;\n        transform: rotate(180deg);\n        mix-blend-mode: hard-light;\n      }\n\n      &::after {\n        content: '';\n        display: block;\n        width: clamp(4.5rem, 1.4042rem + 13.7592vw, 8rem);\n        height: clamp(4.5rem, 1.4042rem + 13.7592vw, 8rem);\n        position: absolute;\n        bottom: 0;\n        right: 0;\n        background: url('assets/images/corner-flare-white.svg') no-repeat bottom right/100% 100%;\n        mix-blend-mode: hard-light;\n      }\n      \n      h2 {\n        color: $white;\n        margin-bottom: 0;\n        font-size: clamp(2.5rem, 0.2887rem + 9.828vw, 5rem);\n\n        span {\n          display: block;\n          font-size: 56%;\n          margin-bottom: .25rem;\n        }\n      }\n    }\n\n    .skill-img {\n      margin: 0 0 0 0;\n      color: $white;\n      margin-bottom: 2rem;\n\n      img {\n        border: .75rem solid $white;\n        margin-bottom: 1rem;\n      }\n    }\n  }\n\n  @include media-breakpoint-up(sm) {\n\n    .skill-building-container {\n      background-position: left -40px bottom, center;\n      background-size: 320px, cover;\n      padding: calc(42px + 1rem) 2rem calc(26px + 2rem) 2rem;\n  \n      .svg-border {\n        \n        &-top {\n          height: 43px;\n        }\n        \n        &-bottom {\n          height: 27px;\n        }\n      }\n  \n      .skill-building-content {\n        padding: 2.5rem;\n  \n        &::before {\n          width: 7rem;\n          height: 7rem;\n        }\n  \n        &::after {\n          width: 7rem;\n          height: 7rem;\n        }\n        \n        h2 {\n          font-size: 4rem;\n        }\n      }\n    }\n  }\n\n  @include media-breakpoint-up(md) {\n\n    .skill-building-container {\n      padding: 0 1rem calc(40px + 4rem) 1rem;\n  \n      @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n        background-image: url('assets/images/leaf-2x.webp'), url('assets/images/bg-wood-green-lg-2x.webp');\n      }\n  \n      .svg-border {\n        \n        &-top {\n          height: 55px;\n        }\n        \n        &-bottom {\n          height: 40px;\n        }\n      }\n  \n      .skill-building-content {\n        padding: 1.5rem;\n  \n        &::before {\n          width: 4rem;\n          height: 4rem;\n        }\n  \n        &::after {\n          width: 4rem;\n          height: 4rem;\n        }\n        \n        h2 {\n          font-size: 2.5rem;\n        }\n      }\n\n      .skill-img {\n        margin-bottom: 0;\n      }\n    }\n  }\n\n  @include media-breakpoint-up(lg) {\n\n    .skill-building-container {\n      background: url('assets/images/leaf.webp') left -40px bottom/300px no-repeat, url('assets/images/bg-wood-green-lg.webp') center/cover no-repeat;\n      padding: 0 1rem calc(40px + 3rem) 1rem;\n  \n      @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n        background-image: url('assets/images/leaf-2x.webp'), url('assets/images/bg-wood-green-lg-2x.webp');\n      }\n  \n      .skill-building-content {\n        padding: 2rem;\n        width: fit-content;\n  \n        &::before {\n          width: 5rem;\n          height: 5rem;\n        }\n  \n        &::after {\n          width: 5rem;\n          height: 5rem;\n        }\n        \n        h2 {\n          font-size: 3rem;\n        }\n      }\n    }\n\n    .skill-img {\n      margin-bottom: 2.5rem;\n\n      img {\n        border: .75rem solid $white;\n        margin-bottom: 0;\n      }\n\n    }\n  }\n\n  @include media-breakpoint-up(xl) {\n\n    .skill-building-container {\n      background-position: left -40px bottom -150px, center;\n      background-size: 500px, cover;\n      padding: 0 4rem calc(40px + 2rem);\n  \n      .skill-building-content {\n        padding: 2rem;\n  \n        &::before {\n          width: 5rem;\n          height: 5rem;\n        }\n  \n        &::after {\n          width: 5rem;\n          height: 5rem;\n        }\n        \n        h2 {\n          font-size: clamp(3.5rem, 1.4706rem + 2.2059vw, 5rem);\n        }\n      }\n    }\n  }\n}", ".carousel {\n\n  .carousel-container {\n    \n\n    .carousel-header {\n      background: url('data:image/svg+xml,<svg viewBox=\"0 0 1062 577\" preserveAspectRatio=\"none\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M940.747 576.676C670.211 531.187 74.6968 579.915 0 556.932L96.9623 0.296753C191.53 9.87327 405.087 29.0263 502.767 29.0263C600.448 29.0263 951.536 82.5214 1061.67 82.5214L940.747 576.676Z\" fill=\"%23ECEEF5\"/></svg>') no-repeat top/100% 100%;\n      padding: 2rem 4rem 8rem;\n\n      h2 {\n        font-size: clamp(1.75rem, 0.2021rem + 6.8796vw, 3.5rem);\n      }\n    }\n\n    .acadiaSwiper {\n      margin-top: -6rem;\n\n      .swiper-buttons {\n        position: absolute;\n        top: 50%;\n        left: 0;\n        transform: translateY(-50%);\n        width: 100%;\n        display: flex;\n        justify-content: space-between;\n        z-index: 10;\n      \n        button {\n          background: rgba($white, .5);\n          border: none;\n          border-radius: 50%;\n          cursor: pointer;\n          padding: 0;\n          margin: 0;\n          outline: none;\n          opacity: .5 !important;\n          transition: opacity .2s ease, background .2s ease;\n      \n          &:hover {\n            background: rgba($white, .75);\n            opacity: 1 !important;\n          }\n      \n          &.swiper-button-disabled {\n            opacity: 0;\n            cursor: not-allowed;\n          }\n        }\n      }\n\n      .swiper-pagination {\n        position: relative;\n        z-index: 1;\n\n        .swiper-pagination-bullet {\n          border: 1px solid $primary;\n          background: rgba($primary, 0);\n          width: 12px;\n          height: 12px;\n          opacity: 1;\n          transition: background 200ms ease;\n\n          &:hover {\n            background: rgba($primary, .25);\n          }\n\n          &.swiper-pagination-bullet-active {\n            background: rgba($primary, 1);\n          }\n        }\n      }\n    }\n  }\n  \n\n  \n\n  @include media-breakpoint-up(sm) {\n\n    .carousel-container {\n    \n\n      .carousel-header {\n        padding: 3rem 6rem 8rem;\n\n        h2 {\n          font-size: 3.5rem;\n        }\n      }\n    }\n  }\n\n  @include media-breakpoint-up(md) {\n\n    .carousel-container {\n      display: flex;\n      flex-flow: row nowrap;\n      align-items: center;\n    \n\n      .carousel-header {\n        flex: 1 1 50%;\n        padding: 2rem 17rem 2rem 5rem;\n\n        h2 {\n          font-size: $h2-font-size * .875;\n        }\n      }\n\n      .acadiaSwiper {\n        flex: 1 1 50%;\n        margin: 0 0 0 -16rem;\n        transform: translateY(1rem);\n\n        .swiper-pagination {\n\n          .swiper-pagination-bullet {\n            width: 16px;\n            height: 16px;\n            margin: 0 6px;\n          }\n        }\n      }\n    }\n  }\n\n  @include media-breakpoint-up(lg) {\n\n    .carousel-container {\n      display: flex;\n      flex-flow: row nowrap;\n      align-items: center;\n    \n\n      .carousel-header {\n        padding: 5rem 20rem 5rem 6rem;\n\n        h2 {\n          font-size: $h2-font-size * .875;\n        }\n      }\n\n      .acadiaSwiper {\n        margin: 0 0 0 -18rem;\n        transform: translateY(1rem);\n\n        .swiper-pagination {\n\n          .swiper-pagination-bullet {\n            width: 18px;\n            height: 18px;\n            margin: 0 8px;\n          }\n        }\n      }\n    }\n  }\n\n  @include media-breakpoint-up(xl) {\n\n    .carousel-container {  \n      width: 100%;  \n\n      .carousel-header {\n        padding: 7rem 28rem 7rem 10rem;\n\n        h2 {\n          font-size: $h2-font-size * .875;\n        }\n      }\n\n      .acadiaSwiper {\n        margin: 0 0 0 -26rem;\n        transform: translateY(1rem);\n\n        .swiper-pagination {\n\n          .swiper-pagination-bullet {\n            width: 18px;\n            height: 18px;\n            margin: 0 8px;\n          }\n        }\n      }\n    }\n  }\n}", ".care {\n\n  .care-container {\n    position: relative;\n    background: url('assets/images/leaf-flipped.webp') right -4rem bottom -2rem/90% no-repeat, url('assets/images/bg-wood-green-sm.webp') center/cover no-repeat;\n    background-blend-mode: overlay;\n    padding: calc(35px + 1rem) 0 calc(20px + 60vw) 0;\n\n    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n      background-image: url('assets/images/leaf-flipped-2x.webp'), url('assets/images/bg-wood-green-sm-2x.webp');\n    }\n\n    .container-fluid {\n      position: relative;\n      z-index: 2;\n    }\n\n    .care-content {\n      position: relative;\n      color: $white;\n      padding: 6vw;\n      height: fit-content;\n\n      &::before {\n        content: '';\n        display: block;\n        width: clamp(4.5rem, 1.4042rem + 13.7592vw, 8rem);\n        height: clamp(4.5rem, 1.4042rem + 13.7592vw, 8rem);\n        position: absolute;\n        top: 0;\n        left: 0;\n        background: url('assets/images/corner-flare-white.svg') no-repeat top left/100% 100%;\n        transform: rotate(180deg);\n        mix-blend-mode: hard-light;\n      }\n\n      &::after {\n        content: '';\n        display: block;\n        width: clamp(4.5rem, 1.4042rem + 13.7592vw, 8rem);\n        height: clamp(4.5rem, 1.4042rem + 13.7592vw, 8rem);\n        position: absolute;\n        bottom: 0;\n        right: 0;\n        background: url('assets/images/corner-flare-white.svg') no-repeat bottom right/100% 100%;\n        mix-blend-mode: hard-light;\n      }\n      \n      h2 {\n        color: $white;\n        margin-bottom: 0;\n        font-size: clamp(2.5rem, 0.2887rem + 9.828vw, 5rem);\n\n        span {\n          display: block;\n          font-size: 56%;\n          margin-top: .25rem;\n        }\n      }\n    }\n  }\n\n  @include media-breakpoint-up(sm) {\n\n    .care-container {\n      background-size: 320px, cover;\n      background-position: left -12rem top calc(50% - 4rem), center;\n      padding: calc(42px + 1rem) 2rem calc(26px + 2rem) 2rem;\n  \n      .svg-border {\n        \n        &-top {\n          height: 43px;\n        }\n        \n        &-bottom {\n          height: 27px;\n        }\n      }\n  \n      .care-content {\n        padding: 2.5rem;\n  \n        &::before {\n          width: 7rem;\n          height: 7rem;\n        }\n  \n        &::after {\n          width: 7rem;\n          height: 7rem;\n        }\n        \n        h2 {\n          font-size: 4rem;\n        }\n      }\n    }\n  }\n\n  @include media-breakpoint-up(md) {\n\n    .container-fluid {\n      height: 100%;\n\n      & > .row {\n        height: 100%;\n      }\n    }\n\n    .care-container {\n      background: url('assets/images/leaf-flipped.webp') left calc(50% - 10rem) top -2rem/320px no-repeat, url('assets/images/bg-wood-green-lg.webp') center/cover no-repeat;\n      padding: 0 1rem;\n\n      @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n        background-image: url('assets/images/leaf-flipped-2x.webp'), url('assets/images/bg-wood-green-lg-2x.webp');\n      }\n  \n      .svg-border {\n        \n        &-top {\n          height: 55px;\n        }\n        \n        &-bottom {\n          height: 40px;\n        }\n      }\n  \n      .care-content {\n        padding: 1.5rem;\n  \n        &::before {\n          width: 4rem;\n          height: 4rem;\n        }\n  \n        &::after {\n          width: 4rem;\n          height: 4rem;\n        }\n        \n        h2 {\n          font-size: 2.5rem;\n        }\n      }\n\n      img {\n        height: 100%;\n        object-fit: cover;\n      }\n    }\n  }\n\n  @include media-breakpoint-up(lg) {\n\n    .care-container {\n      background: url('assets/images/leaf-flipped-lg.webp') left 50% top -2rem/68% no-repeat, url('assets/images/bg-wood-green-lg.webp') center/cover no-repeat;\n      padding: 0 4rem;\n\n      @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n        background-image: url('assets/images/leaf-flipped-lg-2x.webp'), url('assets/images/bg-wood-green-lg-2x.webp');\n      }\n  \n      .care-content {\n        padding: 2rem;\n  \n        &::before {\n          width: 5rem;\n          height: 5rem;\n        }\n  \n        &::after {\n          width: 5rem;\n          height: 5rem;\n        }\n        \n        h2 {\n          font-size: 3rem;\n        }\n      }\n    }\n  }\n\n  @include media-breakpoint-up(xl) {\n\n    .care-container {\n  \n      .care-content {\n        padding: 2rem;\n  \n        &::before {\n          width: 5rem;\n          height: 5rem;\n        }\n  \n        &::after {\n          width: 5rem;\n          height: 5rem;\n        }\n        \n        h2 {\n          font-size: clamp(3.5rem, 1.4706rem + 2.2059vw, 5rem);\n        }\n      }\n    }\n  }\n}", ".page-footer {\n  background: url('assets/images/footer-img-sm.webp') no-repeat center/cover;\n  display: flex;\n  flex-flow: column nowrap;\n  justify-content: flex-end;\n  width: 100%;\n  aspect-ratio: 16/10;\n  overflow-x: hidden;\n\n  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n    background: url('assets/images/footer-img-sm-2x.webp') no-repeat center/cover;\n  }\n\n  h2 {\n    background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.75) 100%);\n    padding: 2rem 1rem 1rem 1rem;\n    margin-bottom: 0;\n    display: flex;\n    justify-content: flex-end;\n  }\n\n  @include media-breakpoint-up(sm) {\n    background: radial-gradient(circle at top right, rgba(0, 149, 60, 0.5) 15%, rgba(0, 149, 60, 0) 40%), url('assets/images/footer-img.webp') no-repeat center/cover;\n    background-blend-mode: multiply;\n    display: block;\n    padding: 4rem 1rem;\n    aspect-ratio: 2/1;\n\n    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n      background: radial-gradient(circle at top right, rgba(0, 149, 60, 0.5) 18%, rgba(0, 149, 60, 0) 35%), url('assets/images/footer-img-2x.webp') no-repeat center/cover;\n    }\n\n    h2 {\n      background: none;\n      padding: 0;\n      margin-left: 80%;\n      display: block;\n    }\n  }\n\n  @include media-breakpoint-up(md) {\n    background: radial-gradient(circle at top right, rgba(0, 149, 60, 0.5) 18%, rgba(0, 149, 60, 0) 35%), url('assets/images/footer-img.webp') no-repeat center/cover;\n    padding: 4rem 1rem;\n    aspect-ratio: 9/4;\n\n    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n      background: radial-gradient(circle at top right, rgba(0, 149, 60, 0.5) 18%, rgba(0, 149, 60, 0) 35%), url('assets/images/footer-img-2x.webp') no-repeat center/cover;\n    }\n\n    h2 {\n      margin-left: 78%;\n    }\n  }\n\n  @include media-breakpoint-up(lg) {\n    padding: 8rem 1rem;\n\n    h2 {\n      margin-left: 69%;\n    }\n  }\n\n  @include media-breakpoint-up(xl) {\n    background: radial-gradient(circle at top right, rgba(0, 149, 60, 0.5) 22%, rgba(0, 149, 60, 0) 35%), url('assets/images/footer-img.webp') no-repeat center/cover;\n    padding: 11vw 1rem;\n\n    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n      background: radial-gradient(circle at top right, rgba(0, 149, 60, 0.5) 22%, rgba(0, 149, 60, 0) 35%), url('assets/images/footer-img-2x.webp') no-repeat center/cover;\n    }\n\n    h2 {\n      margin-left: 74%;\n      font-size: clamp(2.625rem, 0.0882rem + 2.7574vw, 4.5rem);\n    }\n  }\n}"]}