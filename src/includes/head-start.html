<section id="head-start" class="row py-5 py-md-6">
  <div class="col-12">
    <h2>Head Start & Early Head Start Lists</h2>
    <p class="mb-5">Our durable, FSC-Certified, solid maple is the optimal choice for classrooms in early childhood educational environments. With a mix of Maple and our Sense of Place collection, these curated lists are intended to incorporate our natural environment and have a feeling of home to enhance comfort and security for young learners. The furniture pieces and coordinating learning materials were chosen with national Head Start standards in mind and meet common goals for fostering developmental growth and learning.</p>
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 mb-n5">
      <div class="col mb-5">
        <div class="card h-100">
          <div class="card-img-top">
            <img src="../assets/images/hs-infant.webp" alt="Baby sitting outside looking upward">
            <img src="../assets/images/hs-infant-top.webp" alt="Head Start infant classroom floorplan">
          </div>
          <div class="card-body">
            <div class="h5 card-title">Early Head Start</div>
            <div class="card-subtitle mb-4">Infant 6 Weeks-12 Months</div>
            <ul class="list-inline">
              <li class="list-icon-link list-inline-item"><a href="498039" class="list-icon-link"><i class="icon-clipboard mr-1"></i>Go to List</a></li>
              <li class="list-icon-link list-inline-item"><a href="javascript:void(0)" onclick="hsInfant.open();" class="list-icon-link" ><i class="icon-eye mr-1"></i>View Classroom</a></li>
              <li class="list-icon-link list-inline-item"><a href="https://yun.kujiale.com/design/3FO3L7EGQKKV/airoaming" target="_blank" class="list-icon-link"><i class="icon-footsteps mr-1"></i>Virtual Tour</a></li>
            </ul>
          </div>
        </div>
      </div>
      <div class="col mb-5">
        <div class="card h-100">
          <div class="card-img-top">
            <img src="../assets/images/hs-toddler.webp" alt="Toddler child sitting inside an outdoor playset tunnel laughing">
            <img src="../assets/images/hs-toddler-top.webp" alt="Head Start toddler classroom floorplan">
          </div>
          <div class="card-body">
            <div class="h5 card-title">Early Head Start</div>
            <div class="card-subtitle mb-4">Toddler 12-24 Months</div>
            <ul class="list-inline">
              <li class="list-icon-link list-inline-item"><a href="498129" class="list-icon-link"><i class="icon-clipboard mr-1"></i>Go to List</a></li>
              <li class="list-icon-link list-inline-item"><a href="javascript:void(0)" onclick="hsToddler.open();" class="list-icon-link" ><i class="icon-eye mr-1"></i>View Classroom</a></li>
              <li class="list-icon-link list-inline-item"><a href="https://yun.kujiale.com/design/3FO3L7HG881H/airoaming" target="_blank" class="list-icon-link"><i class="icon-footsteps mr-1"></i>Virtual Tour</a></li>
            </ul>
          </div>
        </div>
      </div>
      <div class="col mb-5">
        <div class="card h-100">
          <div class="card-img-top">
            <img src="../assets/images/hs-two-year.webp" alt="child holding a large block smiling">
            <img src="../assets/images/hs-two-year-top.webp" alt="Head Start two year old classroom floorplan">
          </div>
          <div class="card-body">
            <div class="h5 card-title">Early Head Start</div>
            <div class="card-subtitle mb-4">2-3 Year Old</div>
            <ul class="list-inline">
              <li class="list-icon-link list-inline-item"><a href="498170" class="list-icon-link"><i class="icon-clipboard mr-1"></i>Go to List</a></li>
              <li class="list-icon-link list-inline-item"><a href="javascript:void(0)" onclick="hsTwoYear.open();" class="list-icon-link" ><i class="icon-eye mr-1"></i>View Classroom</a></li>
              <li class="list-icon-link list-inline-item"><a href="https://yun.kujiale.com/design/3FO3L7I8AIVS/airoaming" target="_blank" class="list-icon-link"><i class="icon-footsteps mr-1"></i>Virtual Tour</a></li>
            </ul>
          </div>
        </div>
      </div>
      <div class="col mb-5">
        <div class="card h-100">
          <div class="card-img-top">
            <img src="../assets/images/hs-three-four.webp" alt="Children playing in a sandbox">
            <img src="../assets/images/hs-three-four-top.webp" alt="Head Start preschool classroom floorplanner">
          </div>
          <div class="card-body">
            <div class="h5 card-title">Head Start Preschool</div>
            <div class="card-subtitle mb-4">3-4 Year Old</div>
            <ul class="list-inline">
              <li class="list-icon-link list-inline-item"><a href="498182" class="list-icon-link"><i class="icon-clipboard mr-1"></i>Go to List</a></li>
              <li class="list-icon-link list-inline-item"><a href="javascript:void(0)" onclick="hsPreschool.open();" class="list-icon-link" ><i class="icon-eye mr-1"></i>View Classroom</a></li>
              <li class="list-icon-link list-inline-item"><a href="https://yun.kujiale.com/design/3FO3L86O1VG2/airoaming" target="_blank" class="list-icon-link"><i class="icon-footsteps mr-1"></i>Virtual Tour</a></li>
            </ul>
          </div>
        </div>
      </div>
      <div class="col mb-5">
        <div class="card h-100">
          <div class="card-img-top">
            <img src="../assets/images/hs-mixed.webp" alt="Children and teacher playing with fake vegetables">
            <img src="../assets/images/hs-mixed-top.webp" alt="Head Start mixed ages classroom floorplanner">
          </div>
          <div class="card-body">
            <div class="h5 card-title">Early Head Start</div>
            <div class="card-subtitle mb-4">Mixed Ages 0-3 Year Old</div>
            <ul class="list-inline">
              <li class="list-icon-link list-inline-item"><a href="498377" class="list-icon-link"><i class="icon-clipboard mr-1"></i>Go to List</a></li>
              <li class="list-icon-link list-inline-item"><a href="javascript:void(0)" onclick="hsMixed.open();" class="list-icon-link" ><i class="icon-eye mr-1"></i>View Classroom</a></li>
              <li class="list-icon-link list-inline-item"><a href="https://yun.kujiale.com/design/3FO3L7ODJ7MW/airoaming" target="_blank" class="list-icon-link"><i class="icon-footsteps mr-1"></i>Virtual Tour</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>