// Section styles

#stateMap {
  background-image: url('assets/images/state-map.webp');
  background-size: 100% 100%;
  width: 100%;
  position: relative;

  path {
    fill: transparent;
    cursor: pointer;
    transition: fill 0.3s;
  }

  a {

    &:focus,
    &:hover {
      outline: none;

      path {
        fill: rgba(255,255,255,0.5);
        cursor: pointer;
      }
    }
  }
}

.modal {

  .state-list-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    &:not(:first-child) {
      border-top: 1px solid $border-color;
      margin-top: 1rem;
      padding-top: 1rem;
    }

    .state-list-title {
      font-weight: $font-weight-bold;
    }
  }
}