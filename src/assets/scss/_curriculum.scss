/**********************************
/* <PERSON>er
/**********************************/

.curriculum {

  .curriculum-logos {
    display: grid;
    grid-template-columns: repeat(6, 1fr);

    img {

      &:nth-child(-n+6) {
        grid-column: span 2;
      }

      &:nth-child(7) {
        grid-column: 2 / span 2;
      }

      &:nth-child(8) {
        grid-column: 4 / span 2;
      }
    }
  }

  @include media-breakpoint-up(sm) {

    .curriculum-logos {
      grid-template-columns: repeat(4, 1fr);
  
      img {
  
        &:nth-child(-n+6) {
          grid-column: unset;
        }
  
        &:nth-child(7) {
          grid-column: unset;
        }
  
        &:nth-child(8) {
          grid-column: unset;
        }
      }
    }
  }

  @include media-breakpoint-up(md) {

    .curriculum-logos {
      grid-template-columns: repeat(6, 1fr);

      img {

        &:nth-child(7) {
          grid-column: 3 / span 1;
        }
  
        &:nth-child(8) {
          grid-column: 4 / span 1;
        }
      }
    }
  }

  @include media-breakpoint-up(xl) {

    .curriculum-logos {
      grid-template-columns: repeat(8, 1fr);

      img {

        &:nth-child(7) {
          grid-column: unset;
        }
  
        &:nth-child(8) {
          grid-column: unset;
        }
      }
    }
  }
}