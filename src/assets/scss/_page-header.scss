/**********************************
/* Page Header
/**********************************/

.page-header {
  overflow: hidden;
  background-image: url('assets/images/header-img-xs.webp');
  background-position: top center;
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 72.867vw;

  @media (-webkit-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    background-image: url('assets/images/header-img-xs-2x.webp');
  }

  .page-header-content {
    background-image: url('assets/images/header-bg-sm.webp');
    background-position: top center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 2rem 0;

    @media (-webkit-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      background-image: url('assets/images/header-bg-sm-2x.webp');
    }
  }

  h1,
  p {
    color: $white;
  }

  h1 {
    font-size: 3rem;

    span {
      display: block;
      font-size: 75%;
    }
  }

  .page-header-cta {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 1rem;
  }

  @include media-breakpoint-up(sm) {
    background-image: url('assets/images/header-img-sm.webp');
    padding-top: 46.177vw;

    @media (-webkit-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      background-image: url('assets/images/header-img-sm-2x.webp');
    }

    h1 {
      font-size: 3.5rem;
    }

    .page-header-cta {
      flex-direction: row;
      align-items: stretch;
      justify-content: flex-start;
    }
  }

  @include media-breakpoint-up(md) {
    background-image: url('assets/images/header-bg-lg.webp'), url('assets/images/header-img-md.webp');
    background-position: top left, top right;
    background-size: 67% 100%, auto 100% ;
    padding-top: 0;

    @media (-webkit-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      background-image: url('assets/images/header-bg-lg-2x.webp'), url('assets/images/header-img-md-2x.webp');
    }

    .page-header-content {
      background-image: none;
      background-position: unset;
      background-repeat: unset;
      background-size: unset;
      padding: 4rem 0;
  
      @media (-webkit-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        background-image: none;
      }

      h1 {
        font-size: 4.25rem;
      }
    }
  }

  @include media-breakpoint-up(lg) {
    background-image: url('assets/images/header-bg-lg.webp'), url('assets/images/header-img-lg.webp');
    background-position: top left, top 50% right;
    background-size: 54% 100%, 54% auto;

    @media (-webkit-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      background-image: url('assets/images/header-bg-lg-2x.webp'), url('assets/images/header-img-lg-2x.webp');
    }
  }

  @include media-breakpoint-up(xl) {
    background-image: url('assets/images/header-bg-lg.webp'), url('assets/images/header-img-xl.webp');
    background-position: top left, top 50% right;
    background-size: 52% 100%, 52% auto;

    @media (-webkit-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      background-image: url('assets/images/header-bg-lg-2x.webp'), url('assets/images/header-img-xl-2x.webp');
    }

    .page-header-content {

      h1 {
        font-size: 5rem;
      }
    }
  }

  @media (min-width: 1690px) {
    background-position: top left, top 50% right;
    background-size: 52% 100%, 54% auto;
  }
}