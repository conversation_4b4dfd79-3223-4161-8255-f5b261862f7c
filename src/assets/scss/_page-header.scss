.page-header {
  position: relative;
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  overflow-x: hidden;

  
  .page-header-content {
    background-image: url('assets/images/header-bg-sm.webp');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    padding: 2rem;

    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      background-image: url('assets/images/header-bg-sm-2x.webp');
    }

    .page-header-content-container {
      position: relative;
      background: rgba($white, .75);
      padding: 3rem;

      &::before {
        content: '';
        display: block;
        width: 96px;
        height: 96px;
        position: absolute;
        top: .75rem;
        left: .75rem;
        background: url('assets/images/corner-flare-bamboo.svg') no-repeat top left/100% 100%;
        transform: rotate(180deg);
        opacity: .5;
        mix-blend-mode: luminosity;
      }

      &::after {
        content: '';
        display: block;
        width: 96px;
        height: 96px;
        position: absolute;
        bottom: .75rem;
        right: .75rem;
        background: url('assets/images/corner-flare-bamboo.svg') no-repeat bottom right/100% 100%;
        opacity: .5;
        mix-blend-mode: luminosity;
      }

      h1 {
        font-size: clamp(2.5rem, 1.1732rem + 5.8968vw, 4rem);
        font-weight: $font-weight-normal;
        color: $green;
      }
    }
  }

  .page-header-cta {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    gap: .5rem;
  }

  .hero-img {
    width: 100%;
    height: auto;
    object-fit: cover;
  }

  .page-header-rta {
    position: absolute;
    bottom: .75rem;
    right: .75rem;
    width: 150px;
    height: auto;

    img {
      width: 100%;
      height: auto;
    }
  }

  .btn {
    @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-line-height-sm, $btn-border-radius-sm);
  }

  @include media-breakpoint-up(sm) {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
  
    .page-header-content {
      grid-column: 1 / span 1;
      grid-row: 1 / span 2;

      .page-header-content-container {

        h1 {
          font-size: 2.5rem;
        }
      }
    }

    picture {
      grid-column: 2 / span 1;
      grid-row: 1 / span 2;
    }

    .hero-img {
      height: 100%;
    }
  }

  @include media-breakpoint-up(md) {
  
    .page-header-content {
      height: 100%;
      padding: 2rem;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      .page-header-content-container {

        h1 {
          font-size: 2.75rem;
        }
      }
    }
  }

  @include media-breakpoint-up(lg) {
  
    .page-header-content {
      background-image: url('assets/images/header-bg-lg.webp');

      @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        background-image: url('assets/images/header-bg-lg-2x.webp');
      }

      .page-header-content-container {

        h1 {
          font-size: 2.75rem;
        }

        .page-header-cta {
          flex-direction: row;
          justify-content: stretch;
          align-items: flex-start;
        }
      }
    }
  }

  @include media-breakpoint-up(xl) {
    height: fit-content;
    grid-template-columns: repeat(13, 1fr);
    grid-template-rows: repeat(2, 1fr);
  
    .page-header-content {
      grid-column: 1 / span 6;
      grid-row: 1 / span 2;

      .page-header-content-container {

        h1 {
          font-size: 3.5rem;
        }

        .page-header-cta {
          flex-direction: row;
          justify-content: stretch;
          align-items: flex-start;

          .btn {
            flex: 1 1 50%;
          }
        }
      }
    }

    picture {
      grid-column: 7 / span 7;
      grid-row: 1 / span 2;
    }
  }

  @media (min-width: 1900px) {
  
    .page-header-content {
      grid-column: 1 / span 6;
      grid-row: 1 / span 2;

      .page-header-content-container {
        padding: 2.5vw; 

        &::before {
          width: 5.4vw;
          height: 5.4vw;
          top: .83vw;
          left: .83vw;
        }
  
        &::after {
          width: 5.4vw;
          height: 5.4vw;
          bottom: .83vw;
          right: .83vw;
        }

        h1 {
          font-size: clamp(4rem, 1.1212rem + 2.4242vw, 5rem);
          padding: 0 clamp(2rem, -0.8788rem + 2.4242vw, 3rem);
        }

        .page-header-cta {
          
          .btn {
            flex: 1 1 50%;
          }
        }
      }
    }

    .btn {
      @include button-size($btn-padding-y, $btn-padding-x, $btn-font-size, $btn-line-height, $btn-border-radius);
    }
  }

  
}