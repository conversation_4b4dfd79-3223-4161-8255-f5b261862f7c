// Header styles

.page-header {
  position: relative;
  overflow: hidden;

  picture img {
    width: 100%;
    height: 327px;
    object-fit: cover;
  }

  .lp-title {
    padding: 2rem 0;

    &.lp-blue-title {
      background-image: url('assets/images/lp-blue-shape-sm.webp');
      background-position: center;
      background-repeat: no-repeat;
      background-size: 100% 100%;

      h1,
      .lead {
        color: $white;
      } 
    }

    h1 {
      font-size: $h2-font-size;
    }
  }

  .lp-header-cta {
    gap: 1rem;

    .btn {
      @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-line-height-sm, $btn-border-radius-sm);
    }
  }

  @include media-breakpoint-up(sm) {
    height: 300px;

    picture img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .lp-title {
      position: absolute;
      width: 100%;
      min-height: 100%;
      inset: 0;
      padding: 0;

      &.lp-blue-title {
        background-image: none;

        > .container {
          height: 100%;

          .row {
            height: 100%;
            background-image: url('assets/images/lp-blue-shape.webp');
            background-position: left;
            background-repeat: no-repeat;
            background-size: contain;
            margin-left: -4rem;

            div[class*="col-"] {
              display: flex;
              flex-direction: column;
              justify-content: center;
              padding-left: 4rem;
            }
          }
        }
      }
    }
  }

  @include media-breakpoint-up(md) {
    height: 350px;

    .lp-title {

      h1 {
        font-size: $h1-font-size;
      }
    }

    .lp-header-cta {
  
      .btn {
        @include button-size($btn-padding-y, $btn-padding-x, $btn-font-size, $btn-line-height, $btn-border-radius);
      }
    }
  }

  @include media-breakpoint-up(lg) {
    height: 400px;

    .lp-title {

      &.lp-blue-title {

        > .container {

          .row {
            margin-left: -5rem;

            div[class*="col-"] {
              padding-left: 5rem;
            }
          }
        }
      }
    }
  }

  @include media-breakpoint-up(xl) {
    height: 400px;

    .lp-title {

      &.lp-blue-title {

        > .container {

          .row {
            margin-left: -5rem;

            div[class*="col-"] {
              padding-left: 5rem;
            }
          }
        }
      }

      h1 {
        font-size: $h1-font-size * 1.125;
      }
    }
  }
}