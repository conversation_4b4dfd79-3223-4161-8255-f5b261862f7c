// Global page styles

.lead {

  @include media-breakpoint-down(sm) {
    font-size: 1.25rem;
  }
}

.text-large {

  @include media-breakpoint-up(lg) {
    font-size: $font-size-base * 1.25
  }
}

.list-icon-link {
  font-weight: $font-weight-bold;

  &:hover {
    text-decoration: none;
  }
}

#vyLightbox {
  display: none;
}

.lightboxlaunch {
  cursor: pointer;
}

.card-video {
  .card-video-thumb {
    position: relative;

    >img {
      filter: brightness(1);
      transition: filter 0.3s ease;
    }

    &:after {
      content: '';
      display: block;
      width: 48px;
      height: 48px;
      background-color: $lavender;
      background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.99998 37.92C7.73998 37.92 7.47998 37.86 7.23998 37.72C6.77998 37.46 6.47998 36.96 6.47998 36.42V4.07996C6.47998 3.53996 6.75998 3.03996 7.23998 2.77996C7.69998 2.51996 8.27998 2.51996 8.73998 2.77996L36.74 18.94C37.2 19.2 37.5 19.7 37.5 20.24C37.5 20.78 37.22 21.28 36.74 21.54L8.73998 37.7C8.49998 37.84 8.23998 37.9 7.97998 37.9L7.99998 37.92ZM9.49998 6.67996V33.82L33 20.26L9.49998 6.67996Z' fill='white'/%3E%3C/svg%3E%0A");
      background-position: 60% center;
      background-repeat: no-repeat;
      background-size: 28px;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      translate: -50% -50%;

      @include media-breakpoint-up(md) {
        width: 64px;
        height: 64px;
        background-size: 36px;
      }

      @include media-breakpoint-up(lg) {
        width: 64px;
        height: 64px;
        background-size: 36px;
      }
    }
  }

  .card-title {
    margin-bottom: 0;
    color: $link-color;
  }

  &:hover {
    .card-video-thumb>img {
      filter: brightness(0.5);
    }

    .card-title {
      color: $link-hover-color;
    }
  }
}

#vidyard-overlay {
  z-index: 1800;
}
.vidyard-close-container {
  z-index: 2000;
}
#vidyard-content-fixed {
  z-index: 1900;
}

.card {
  overflow: hidden;
}

.card-img-top {
  position: relative;
  display: flex;
  flex-flow: row nowrap;
  overflow: hidden;
  justify-content: flex-start;
  align-items: flex-start;

  img {
    width: 100%;
    height: auto;
    flex-shrink: 0;
    transition: transform .5s ease;

    &:nth-child(2) {
      width: calc(100% + 1px);
      height: 100%;
    }
  }

  .card:hover & img {

    &:nth-child(1) {
      transform: translateX(-50%);
    }

    &:nth-child(2) {
      transform: translateX(-100%);
    }
    
  }

  &:after {
    content: '';
    display: block;
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,.04);
  }
}

.card-body {
  position: relative;
  margin-top: -1px;
  background-color: $white;
}

.glightbox-clean .gslide-title {
  font-size: $h5-font-size;
  color: $headings-color;
  line-height: $headings-line-height;
  font-weight: $headings-font-weight;
  font-family: $font-family-base;
}

.glightbox-clean .gslide-desc {
  font-size: 1rem;
  font-family: $font-family-base;
  line-height: $line-height-base;
}

section {

  h2 {
    font-size: $h3-font-size;
  }

  h3 {
    font-size: $h4-font-size;
  }

  > div[class*="col-"] {
    
    p:last-child,
    ul:last-child,
    ol:last-child {
      margin-bottom: 0;
    }
  }

  .btn {
    @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-line-height-sm, $btn-border-radius-sm);
  }

  @include media-breakpoint-up(md) {

    h2 {
      font-size: $h2-font-size;
    }

    h3 {
      font-size: $h3-font-size;
    }

    .btn {
      @include button-size($btn-padding-y, $btn-padding-x, $btn-font-size, $btn-line-height, $btn-border-radius);
    }
  }

  @include media-breakpoint-up(lg) {
  }

  @include media-breakpoint-up(xl) {

    .btn {
      @include button-size($btn-padding-y-lg, $btn-padding-x-lg, $btn-font-size-lg, $btn-line-height-lg, $btn-border-radius-lg);
    }

    .modal .btn {
      @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-line-height-sm, $btn-border-radius-sm);
    }
  }
}