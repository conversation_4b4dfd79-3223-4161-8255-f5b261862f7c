.homepage-hero-section {
  position: relative;
  overflow: hidden;

  .hero-promo {
    position: relative;
    display: flex;
    flex-flow: column nowrap;

    img {
      width: 100%;
      height: auto;
    }

    .vidyard-player-container {
      width: 100%;
      height: auto;
      aspect-ratio: 16/9;
    }
      

    @include media-breakpoint-up(sm) {
      overflow: hidden;
      flex-flow: row nowrap;
      width: 100%;
      aspect-ratio: 32/9;

      img {
        width: 50%;
        aspect-ratio: 16/9;
        object-fit: cover;
        flex: 1 1 50%;
        transform: scale(1.005);
        transform-origin: center;
      }

      .vidyard-player-container {
        width: 50%;
        flex: 1 1 50%;
      }

    }
  }

  .hero-cta {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    aspect-ratio: 360/210;
    z-index: 1;

    .hs-web-interactive-inline {
      width: 100%;
      height: 100%;

      a {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    @include media-breakpoint-up(sm) {
      width: 50%
    }
  }
}
