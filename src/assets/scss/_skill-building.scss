.skill-building {
  overflow-x: hidden;

  .skill-building-container {
    position: relative;
    background: url('assets/images/leaf.webp') left -50px bottom/90% no-repeat, url('assets/images/bg-wood-green-sm.webp') center/cover no-repeat;
    background-blend-mode: overlay;
    padding: calc(35px + 1rem) 0 calc(20px + 60vw) 0;

    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      background-image: url('assets/images/leaf-2x.webp'), url('assets/images/bg-wood-green-sm-2x.webp');
    }

    .container-fluid {
      position: relative;
      z-index: 2;
    }

    .skill-building-content {
      position: relative;
      color: $white;
      padding: 6vw;
      height: fit-content;

      &::before {
        content: '';
        display: block;
        width: clamp(4.5rem, 1.4042rem + 13.7592vw, 8rem);
        height: clamp(4.5rem, 1.4042rem + 13.7592vw, 8rem);
        position: absolute;
        top: 0;
        left: 0;
        background: url('assets/images/corner-flare-white.svg') no-repeat top left/100% 100%;
        transform: rotate(180deg);
        mix-blend-mode: hard-light;
      }

      &::after {
        content: '';
        display: block;
        width: clamp(4.5rem, 1.4042rem + 13.7592vw, 8rem);
        height: clamp(4.5rem, 1.4042rem + 13.7592vw, 8rem);
        position: absolute;
        bottom: 0;
        right: 0;
        background: url('assets/images/corner-flare-white.svg') no-repeat bottom right/100% 100%;
        mix-blend-mode: hard-light;
      }
      
      h2 {
        color: $white;
        margin-bottom: 0;
        font-size: clamp(2.5rem, 0.2887rem + 9.828vw, 5rem);

        span {
          display: block;
          font-size: 56%;
          margin-bottom: .25rem;
        }
      }
    }

    .skill-img {
      margin: 0 0 0 0;
      color: $white;
      margin-bottom: 2rem;

      img {
        border: .75rem solid $white;
        margin-bottom: 1rem;
      }
    }
  }

  @include media-breakpoint-up(sm) {

    .skill-building-container {
      background-position: left -40px bottom, center;
      background-size: 320px, cover;
      padding: calc(42px + 1rem) 2rem calc(26px + 2rem) 2rem;
  
      .svg-border {
        
        &-top {
          height: 43px;
        }
        
        &-bottom {
          height: 27px;
        }
      }
  
      .skill-building-content {
        padding: 2.5rem;
  
        &::before {
          width: 7rem;
          height: 7rem;
        }
  
        &::after {
          width: 7rem;
          height: 7rem;
        }
        
        h2 {
          font-size: 4rem;
        }
      }
    }
  }

  @include media-breakpoint-up(md) {

    .skill-building-container {
      padding: 0 1rem calc(40px + 4rem) 1rem;
  
      @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        background-image: url('assets/images/leaf-2x.webp'), url('assets/images/bg-wood-green-lg-2x.webp');
      }
  
      .svg-border {
        
        &-top {
          height: 55px;
        }
        
        &-bottom {
          height: 40px;
        }
      }
  
      .skill-building-content {
        padding: 1.5rem;
  
        &::before {
          width: 4rem;
          height: 4rem;
        }
  
        &::after {
          width: 4rem;
          height: 4rem;
        }
        
        h2 {
          font-size: 2.5rem;
        }
      }

      .skill-img {
        margin-bottom: 0;
      }
    }
  }

  @include media-breakpoint-up(lg) {

    .skill-building-container {
      background: url('assets/images/leaf.webp') left -40px bottom/300px no-repeat, url('assets/images/bg-wood-green-lg.webp') center/cover no-repeat;
      padding: 0 1rem calc(40px + 3rem) 1rem;
  
      @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        background-image: url('assets/images/leaf-2x.webp'), url('assets/images/bg-wood-green-lg-2x.webp');
      }
  
      .skill-building-content {
        padding: 2rem;
        width: fit-content;
  
        &::before {
          width: 5rem;
          height: 5rem;
        }
  
        &::after {
          width: 5rem;
          height: 5rem;
        }
        
        h2 {
          font-size: 3rem;
        }
      }
    }

    .skill-img {
      margin-bottom: 2.5rem;

      img {
        border: .75rem solid $white;
        margin-bottom: 0;
      }

    }
  }

  @include media-breakpoint-up(xl) {

    .skill-building-container {
      background-position: left -40px bottom -150px, center;
      background-size: 500px, cover;
      padding: 0 4rem calc(40px + 2rem);
  
      .skill-building-content {
        padding: 2rem;
  
        &::before {
          width: 5rem;
          height: 5rem;
        }
  
        &::after {
          width: 5rem;
          height: 5rem;
        }
        
        h2 {
          font-size: clamp(3.5rem, 1.4706rem + 2.2059vw, 5rem);
        }
      }
    }
  }
}