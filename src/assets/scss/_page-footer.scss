.page-footer {
  background: url('assets/images/footer-img-sm.webp') no-repeat center/cover;
  display: flex;
  flex-flow: column nowrap;
  justify-content: flex-end;
  width: 100%;
  aspect-ratio: 16/10;
  overflow-x: hidden;

  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    background: url('assets/images/footer-img-sm-2x.webp') no-repeat center/cover;
  }

  h2 {
    background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.75) 100%);
    padding: 2rem 1rem 1rem 1rem;
    margin-bottom: 0;
    display: flex;
    justify-content: flex-end;
  }

  @include media-breakpoint-up(sm) {
    background: radial-gradient(circle at top right, rgba(0, 149, 60, 0.5) 15%, rgba(0, 149, 60, 0) 40%), url('assets/images/footer-img.webp') no-repeat center/cover;
    background-blend-mode: multiply;
    display: block;
    padding: 4rem 1rem;
    aspect-ratio: 2/1;

    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      background: radial-gradient(circle at top right, rgba(0, 149, 60, 0.5) 18%, rgba(0, 149, 60, 0) 35%), url('assets/images/footer-img-2x.webp') no-repeat center/cover;
    }

    h2 {
      background: none;
      padding: 0;
      margin-left: 80%;
      display: block;
    }
  }

  @include media-breakpoint-up(md) {
    background: radial-gradient(circle at top right, rgba(0, 149, 60, 0.5) 18%, rgba(0, 149, 60, 0) 35%), url('assets/images/footer-img.webp') no-repeat center/cover;
    padding: 4rem 1rem;
    aspect-ratio: 9/4;

    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      background: radial-gradient(circle at top right, rgba(0, 149, 60, 0.5) 18%, rgba(0, 149, 60, 0) 35%), url('assets/images/footer-img-2x.webp') no-repeat center/cover;
    }

    h2 {
      margin-left: 78%;
    }
  }

  @include media-breakpoint-up(lg) {
    padding: 8rem 1rem;

    h2 {
      margin-left: 69%;
    }
  }

  @include media-breakpoint-up(xl) {
    background: radial-gradient(circle at top right, rgba(0, 149, 60, 0.5) 22%, rgba(0, 149, 60, 0) 35%), url('assets/images/footer-img.webp') no-repeat center/cover;
    padding: 11vw 1rem;

    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      background: radial-gradient(circle at top right, rgba(0, 149, 60, 0.5) 22%, rgba(0, 149, 60, 0) 35%), url('assets/images/footer-img-2x.webp') no-repeat center/cover;
    }

    h2 {
      margin-left: 74%;
      font-size: clamp(2.625rem, 0.0882rem + 2.7574vw, 4.5rem);
    }
  }
}