//
// Collapsible Cards
//

.card.card-collapse {

  .card-header {

    > a:before {
      float: right !important;
      font-family: kapicons;
      font-size: 1rem;
      margin-top: 4px;
      color: $gray-600;
      content: '\e806';
    }

    > a.collapsed:before {
      float: right !important;
      content: '\e807';
    }

    > a {
      display: block;
    }

    > a,
    a:hover,
    a:active,
    a:focus {
      text-decoration: none;
      color: $headings-color;
    }
  }
}

//
// Collapsible Sidebar Filter
//

#sidebar {
  background: $white;

  .sidebar-close {
    font-size: 2rem;
    border: none;
    padding: 0;

    > i:before {
      transform: rotate(45deg);
    }

    &:hover,
    &:active {
      background-color: transparent;
      color: lighten(desaturate(theme-color("primary"), 50), 40);
    }
  }

  @include media-breakpoint-down(sm) {
    display: block;
    position: fixed;
    top: 0;
    left: -100vw;
    bottom: 0;
    width: 100%;
    max-height: 100%;
    height: 100vh;
    overflow-x: auto;
    opacity: .2;
    background: $white;
    padding-top: 4rem;
    padding-bottom: 2rem;
    z-index: 1050;
    transition: .3s ease-in-out;

    &.slide {
      left: 0;
      opacity: 1;
      overflow-y: auto;
    }
  }
}

//
// Content Boxes
//

.content-box.card.card-collapse {

  .list-group {
    margin-bottom: 1rem;
  }

  .nav-link {
    border-radius: 50em;

    &:hover {
      background-color: $gray-300;
    }

    &.active {
      background-color: $component-active-bg;
      color: $component-active-color;
    }
  }

  @include media-breakpoint-up(md) {

    .card-header > a:before {
      display: none;
    }
  }
}

.content-box {
  position: -webkit-sticky;
  position: sticky;
  top: calc(42px + 1.5rem);
  z-index: 20;

  @include media-breakpoint-down(md) {

    &.scroll-fix {
      position: fixed !important;
      top: 1rem;
      left: 50%;
      width: calc(100% - 2rem);
      margin-left: calc((100% - 2rem) / 2 * -1);
      box-shadow: $box-shadow-sm;
    }
  }
}
