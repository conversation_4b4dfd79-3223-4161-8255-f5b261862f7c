// Variables
//
// Variables should follow the `$component-state-property-size` formula for
// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.

// Color system

$white:         #fff;
$gray-100:      #F7F7FC;
$gray-200:      #F0F0F5;
$gray-300:      #E6E6ED;
$gray-400:      #D7D7E0;
$gray-500:      #C1C1C6;
$gray-600:      #929299;
$gray-700:      #616166;
$gray-800:      #454547;
$gray-900:      #313133;
$black:         #000;

$royal-base: #003E52;
$royal-100:  desaturate(lighten($royal-base, 80%), 74%);
$royal-200:  lighten(desaturate($royal-base, 72%), 73%);
$royal-300:  lighten(desaturate($royal-base, 73%), 66%);
$royal-400:  lighten(desaturate($royal-base, 69%), 53%);
$royal-500:  lighten(desaturate($royal-base, 69%), 37%);
$royal-600:  lighten(desaturate($royal-base, 52%), 19%);
$royal-700:  $royal-base;
$royal-800:  darken($royal-base, 4%);
$royal-900:  darken($royal-base, 9%);

$lavender-base: #6786B8;
$lavender-100:  lighten(desaturate(adjust-hue($lavender-base, 8deg), 7%), 38%);
$lavender-200:  lighten(desaturate(adjust-hue($lavender-base, 7deg), 5%), 33%);
$lavender-300:  lighten(desaturate(adjust-hue($lavender-base, 6deg), 4%), 28%);
$lavender-400:  lighten(desaturate(adjust-hue($lavender-base, 2deg), 2%), 14%);
$lavender-500:  $lavender-base;
$lavender-600:  darken($lavender-base, 11%);
$lavender-700:  darken($lavender-base, 22%);
$lavender-800:  darken($lavender-base, 33%);
$lavender-900:  darken($lavender-base, 44%);

$blue-base: #0075C9;
$blue-100:  lighten(desaturate($blue-base, 36%), 56%);
$blue-200:  lighten(desaturate($blue-base, 27%), 42%);
$blue-300:  lighten(desaturate($blue-base, 18%), 28%);
$blue-400:  lighten(desaturate($blue-base, 9%), 14%);
$blue-500:  $blue-base;
$blue-600:  darken($blue-base, 8%);
$blue-700:  darken($blue-base, 16%);
$blue-800:  darken($blue-base, 24%);
$blue-900:  darken($blue-base, 31%);

$purple-base: #8A1A9B;
$purple-100:  lighten(desaturate($purple-base, 32%), 60%);
$purple-200:  lighten(desaturate($purple-base, 32%), 45%);
$purple-300:  lighten(desaturate($purple-base, 32%), 30%);
$purple-400:  lighten(desaturate($purple-base, 32%), 15%);
$purple-500:  $purple-base;
$purple-600:  darken($purple-base, 7%);
$purple-700:  darken($purple-base, 14%);
$purple-800:  darken($purple-base, 21%);
$purple-900:  darken($purple-base, 28%);

$bamboo-base: #B4BD00;
$bamboo-100:  lighten(desaturate($bamboo-base, 43%), 56%);
$bamboo-200:  lighten(desaturate($bamboo-base, 41%), 42%);
$bamboo-300:  lighten(desaturate($bamboo-base, 41%), 28%);
$bamboo-400:  lighten(desaturate($bamboo-base, 41%), 14%);
$bamboo-500:  $bamboo-base;
$bamboo-600:  darken(adjust-hue($bamboo-base, 5deg), 8%);
$bamboo-700:  darken(adjust-hue($bamboo-base, 16deg), 16%);
$bamboo-800:  darken(adjust-hue($bamboo-base, 34deg), 24%);
$bamboo-900:  darken(adjust-hue($bamboo-base, 51deg), 31%);

$green-base: #00953B;
$green-100:  lighten(desaturate(adjust-hue($green-base, -2deg), 56%), 66%);
$green-200:  lighten(desaturate(adjust-hue($green-base, -1deg), 55%), 50%);
$green-300:  lighten(desaturate($green-base, 54%), 33%);
$green-400:  lighten(desaturate($green-base, 54%), 17%);
$green-500:  $green-base;
$green-600:  darken($green-base, 6%);
$green-700:  darken($green-base, 12%);
$green-800:  darken($green-base, 18%);
$green-900:  darken($green-base, 23%);


$yellow-base: #FEC600;
$yellow-100:  lighten($yellow-base, 46%);
$yellow-200:  lighten($yellow-base, 35%);
$yellow-300:  lighten($yellow-base, 23%);
$yellow-400:  lighten($yellow-base, 12%);
$yellow-500:  $yellow-base;
$yellow-600:  darken(desaturate(adjust-hue($yellow-base, -6deg), 15%), 7%);
$yellow-700:  darken(desaturate(adjust-hue($yellow-base, -16deg), 30%), 14%);
$yellow-800:  darken(desaturate(adjust-hue($yellow-base, -28deg), 46%), 25%);
$yellow-900:  darken(desaturate(adjust-hue($yellow-base, -40deg), 61%), 34%);

$red-base: #FC2B00;
$red-100:  lighten(desaturate($red-base, 36%), 46%);
$red-200:  lighten(desaturate($red-base, 27%), 39%);
$red-300:  lighten(desaturate($red-base, 18%), 30%);
$red-400:  lighten(desaturate($red-base, 9%), 17%);
$red-500:  $red-base;
$red-600:  darken($red-base, 12%);
$red-700:  darken($red-base, 22%);
$red-800:  darken($red-base, 31%);
$red-900:  darken($red-base, 41%);


$grays: ();
// stylelint-disable-next-line scss/dollar-variable-default
$grays: map-merge(
  (
    "100": $gray-100,
    "200": $gray-200,
    "300": $gray-300,
    "400": $gray-400,
    "500": $gray-500,
    "600": $gray-600,
    "700": $gray-700,
    "800": $gray-800,
    "900": $gray-900
  ),
  $grays
);

$royals: ();
$royals: map-merge(
  (
    "100": $royal-100,
    "200": $royal-200,
    "300": $royal-300,
    "400": $royal-400,
    "500": $royal-500,
    "600": $royal-600,
    "700": $royal-700,
    "800": $royal-800,
    "900": $royal-900
  ),
  $royals
);

$lavenders: ();
$lavenders: map-merge(
  (
    "100": $lavender-100,
    "200": $lavender-200,
    "300": $lavender-300,
    "400": $lavender-400,
    "500": $lavender-500,
    "600": $lavender-600,
    "700": $lavender-700,
    "800": $lavender-800,
    "900": $lavender-900
  ),
  $lavenders
);

$blues: ();
$blues: map-merge(
  (
    "100": $blue-100,
    "200": $blue-200,
    "300": $blue-300,
    "400": $blue-400,
    "500": $blue-500,
    "600": $blue-600,
    "700": $blue-700,
    "800": $blue-800,
    "900": $blue-900
  ),
  $blues
);

$purples: ();
$purples: map-merge(
  (
    "100": $purple-100,
    "200": $purple-200,
    "300": $purple-300,
    "400": $purple-400,
    "500": $purple-500,
    "600": $purple-600,
    "700": $purple-700,
    "800": $purple-800,
    "900": $purple-900
  ),
  $purples
);

$bamboos: ();
$bamboos: map-merge(
  (
    "100": $bamboo-100,
    "200": $bamboo-200,
    "300": $bamboo-300,
    "400": $bamboo-400,
    "500": $bamboo-500,
    "600": $bamboo-600,
    "700": $bamboo-700,
    "800": $bamboo-800,
    "900": $bamboo-900
  ),
  $bamboos
);

$yellows: ();
$yellows: map-merge(
  (
    "100": $yellow-100,
    "200": $yellow-200,
    "300": $yellow-300,
    "400": $yellow-400,
    "500": $yellow-500,
    "600": $yellow-600,
    "700": $yellow-700,
    "800": $yellow-800,
    "900": $yellow-900
  ),
  $yellows
);

$reds: ();
$reds: map-merge(
  (
    "100": $red-100,
    "200": $red-200,
    "300": $red-300,
    "400": $red-400,
    "500": $red-500,
    "600": $red-600,
    "700": $red-700,
    "800": $red-800,
    "900": $red-900
  ),
  $reds
);


$red:       $red-500;
$bamboo:    $bamboo-500;
$green:     $green-500;
$lavender:  $lavender-500;
$blue:      $blue-500;
$purple:    $purple-500;
$sky:       $lavender-100;
$royal:     $royal-700;
$yellow:    $yellow-500;


$colors: ();
// stylelint-disable-next-line scss/dollar-variable-default
$colors: map-merge(
  (
    "blue":        $blue,
    "royal":       $royal,
    "purple":      $purple,
    "lavender":    $lavender,
    "red":         $red,
    "sky":         $sky,
    "bamboo":      $bamboo,
    "green":       $green,
    "yellow":      $yellow,
    "white":       $white,
    "gray-light":  $gray-300,
    "gray":        $gray-500,
    "gray-dark":   $gray-800
  ),
  $colors
);

$primary:       $royal;
$secondary:     $purple;
$success:       $green;
$info:          $green;
$warning:       $yellow;
$danger:        $red;
$action:        $purple;
$lavender:      $lavender;
$blue:          $blue;
$light:         $sky;
$dark:          $royal;
$white:         $white;
$gray-light:    $gray-300;
$gray:          $gray-500;
$gray-dark:     $gray-800;

$theme-colors: ();
// stylelint-disable-next-line scss/dollar-variable-default
$theme-colors: map-merge(
  (
    "primary":    $primary,
    "secondary":  $secondary,
    "success":    $success,
    "info":       $info,
    "warning":    $warning,
    "danger":     $danger,
    "action":     $action,
    "lavender":   $lavender,
    "blue":       $blue,
    "light":      $light,
    "dark":       $dark,
    "white":      $white,
    "gray-light": $gray-light,
    "gray":       $gray,
    "gray-dark":  $gray-dark
  ),
  $theme-colors
);

// Characters which are escaped by the escape-svg function
$escaped-characters: (
  ("<","%3c"),
  (">","%3e"),
  ("#","%23"),
  ("(","%28"),
  (")","%29"),
) !default;


// Options
//
// Quickly modify global styling by enabling or disabling optional features.

$enable-caret:                                false;
$enable-shadows:                              true;


// Spacing
//
// Control the default styling of most Bootstrap elements by modifying these
// variables. Mostly focused on spacing.
// You can add more entries to the $spacers map, should you need more variation.

$spacer: 1rem;
$spacers: ();
// stylelint-disable-next-line scss/dollar-variable-default
$spacers: map-merge(
  (
    0: 0,
    1: ($spacer * .25),
    2: ($spacer * .5),
    3: $spacer,
    4: ($spacer * 1.5),
    5: ($spacer * 2),
    6: ($spacer * 3),
    7: ($spacer * 4),
    8: ($spacer * 6),
    9: ($spacer * 8),
    10: ($spacer * 10),
    11: ($spacer * 12),
    12: ($spacer * 14),
    13: ($spacer * 16),
    14: ($spacer * 18),
    15: ($spacer * 20)
  ),
  $spacers
);


// This variable affects the `.h-*` and `.w-*` classes.
$sizes: () !default;
// stylelint-disable-next-line scss/dollar-variable-default
$sizes: map-merge(
  (
    10: 10%,
    15: 15%,
    20: 25%,
    25: 25%,
    30: 30%,
    35: 35%,
    40: 40%,
    45: 45%,
    50: 50%,
    55: 55%,
    60: 60%,
    65: 65%,
    70: 70%,
    75: 75%,
    80: 80%,
    85: 85%,
    90: 90%,
    95: 95%,
    100: 100%,
    auto: auto,
    none: none
  ),
  $sizes
);


// Body
//
// Settings for the `<body>` element.

$body-bg:                   $white;
$body-color:                $gray-900;


// Links
//
// Style anchor elements.

$link-color:                              color("blue");
$link-decoration:                         none;
$link-hover-color:                        $blue-400;
$link-hover-decoration:                   underline;
// Darken percentage for links with `.text-*` class (e.g. `.text-success`)
$emphasized-link-hover-darken-percentage: 15%;
$outlined-link-hover-darken-percentage: 15%;

// Paragraphs
//
// Style p element.

$paragraph-margin-bottom:   1.5rem;


// Grid breakpoints
//
// Define the minimum dimensions at which your layout will change,
// adapting to different screen sizes, for use in media queries.

$grid-breakpoints: (
  xs: 0,
  sm: 768px,
  md: 992px,
  lg: 1232px,
  xl: 1472px
);

@include _assert-ascending($grid-breakpoints, "$grid-breakpoints");
@include _assert-starts-at-zero($grid-breakpoints, "$grid-breakpoints");


// Grid containers
//
// Define the maximum width of `.container` for different screen sizes.

$container-max-widths: (
  sm: 732px,
  md: 960px,
  lg: 1164px,
  xl: 1464px
);

@include _assert-ascending($container-max-widths, "$container-max-widths");


// Grid columns
//
// Set the number of columns and specify the width of the gutters.

$grid-columns:                12;
$grid-gutter-width:           32px;
$grid-row-columns:            6;


// Components
//
// Define common padding and border radius sizes and more.

$line-height-lg:              1.25;
$line-height-sm:              1.5;

$border-width:                1px;
$border-color:                $gray;

$border-radius:               .5rem;
$border-radius-lg:            .5rem;
$border-radius-sm:            .5rem;

$rounded-pill:                50rem;

$box-shadow-sm:               3px 4px 8px rgba($black, .25);
$box-shadow:                  3px 7px 14px rgba($black, .25);
$box-shadow-lg:               $box-shadow;

$component-active-color:      $white;
$component-active-bg:         color("purple");

$transition-base:             all .2s ease-in-out;
$transition-fade:             opacity .15s linear;
$transition-collapse:         height .35s ease;


// Typography
//
// Font, line-height, and color for body text, headings, and more.

// stylelint-disable value-keyword-case
$font-family-sans-serif:      "Brandon Text", Arial, "Helvetica Neue", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
$font-family-base:            $font-family-sans-serif;
// stylelint-enable value-keyword-case

$font-size-base:              1rem; // Assumes the browser default, typically `16px`
$font-size-lg:                $font-size-base * 1.5;
$font-size-sm:                $font-size-base * .75;

$font-weight-lighter:         lighter;
$font-weight-light:           300;
$font-weight-normal:          400;
$font-weight-bold:            700;
$font-weight-bolder:          900;

$font-weight-base:            $font-weight-normal;
$line-height-base:            1.5;

$h1-font-size:                $font-size-base * 4;
$h2-font-size:                $font-size-base * 3;
$h3-font-size:                $font-size-base * 2;
$h4-font-size:                $font-size-base * 1.5;
$h5-font-size:                $font-size-base * 1.25;
$h6-font-size:                $font-size-base;

$headings-margin-bottom:      $line-height-base * 1rem;
$headings-font-family:        null;
$headings-font-weight:        900;
$headings-line-height:        1.125;
$headings-color:              theme-color('primary');

$display1-size:               4rem;
$display2-size:               3rem;
$display3-size:               2rem;
$display4-size:               1.5rem;

$display1-weight:             300;
$display2-weight:             300;
$display3-weight:             300;
$display4-weight:             300;
$display-line-height:         $headings-line-height;

$lead-font-size:              $font-size-base * 1.5;
$lead-font-weight:            400;

$small-font-size:             $font-size-base * .75;

$text-muted:                  $gray-600;

$blockquote-padding-y:        2.25rem;
$blockquote-padding-x:        1rem;
$blockquote-padding:          $blockquote-padding-y 0 0 $blockquote-padding-x;
$blockquote-bg:               url('https://images.kaplanco.com/images/css/quote.png') left #{$blockquote-padding-y - 1.25} no-repeat;
$blockquote-color:            theme-color("blue");
$blockquote-small-color:      $gray-600;
$blockquote-small-font-size:  $font-size-base;
$blockquote-font-size:        $font-size-lg;

$hr-border-color:             $border-color;
$hr-border-width:             $border-width;

$mark-padding:                .2em;

$dt-font-weight:              $font-weight-bold;

$list-inline-padding:         .5rem;

$letter-spacing:              .1rem;

$mark-bg:                     #fcf8e3;

$hr-margin-y:                 $spacer;

// Kapicons UI

$ui-icons-font-family: "kapicons-ui";
$ui-icons-font-path: "../fonts/Kapicons-UI";

$icon-angle-down: unquote('"\\e800"');
$icon-angle-left: unquote('"\\e801"');
$icon-angle-right: unquote('"\\e802"');
$icon-angle-up: unquote('"\\e803"');
$icon-double-angle-left: unquote('"\\e804"');
$icon-double-angle-right: unquote('"\\e805"');
$icon-minus: unquote('"\\e806"');
$icon-plus: unquote('"\\e807"');
$icon-cart: unquote('"\\e808"');
$icon-chat: unquote('"\\e809"');
$icon-hamburger: unquote('"\\e80a"');
$icon-user: unquote('"\\e80b"');
$icon-book: unquote('"\\e80c"');
$icon-bulb: unquote('"\\e80d"');
$icon-package: unquote('"\\e80e"');
$icon-pencil: unquote('"\\e80f"');
$icon-apple-k5: unquote('"\\e810"');
$icon-dollar: unquote('"\\e811"');
$icon-leaf: unquote('"\\e812"');
$icon-star: unquote('"\\e813"');
$icon-star-filled: unquote('"\\e90d"');
$icon-tree: unquote('"\\e814"');
$icon-truck: unquote('"\\e815"');
$icon-save: unquote('"\\e816"');
$icon-open: unquote('"\\e817"');
$icon-trash: unquote('"\\e818"');
$icon-facebook: unquote('"\\e819"');
$icon-linkedin: unquote('"\\e81a"');
$icon-pinterest: unquote('"\\e81b"');
$icon-twitter: unquote('"\\e91d"');
$icon-x-twitter: unquote('"\\e81c"');
$icon-youtube: unquote('"\\e81d"');
$icon-instagram: unquote('"\\e81e"');
$icon-tiktok: unquote('"\\e969"');
$icon-pdf: unquote('"\\e81f"');
$icon-excel: unquote('"\\e820"');
$icon-search: unquote('"\\e821"');
$icon-share: unquote('"\\e901"');
$icon-mail: unquote('"\\e822"');
$icon-clipboard: unquote('"\\e823"');
$icon-question: unquote('"\\e824"');
$icon-grid-view: unquote('"\\e825"');
$icon-list-view: unquote('"\\e826"');
$icon-eye: unquote('"\\e827"');
$icon-footsteps: unquote('"\\e93e"');
$icon-heart: unquote('"\\e828"');
$icon-point: unquote('"\\e829"');
$icon-duplicate: unquote('"\\e82a"');
$icon-checkmark: unquote('"\\e902"');
$icon-circle-a: unquote('"\\e903"');
$icon-circle-b: unquote('"\\e908"');
$icon-circle-c: unquote('"\\e90a"');
$icon-circle-d: unquote('"\\e910"');
$icon-circle-e: unquote('"\\e913"');
$icon-circle-f: unquote('"\\e917"');
$icon-circle-g: unquote('"\\e919"');
$icon-circle-h: unquote('"\\e91b"');
$icon-circle-i: unquote('"\\e91c"');
$icon-circle-j: unquote('"\\e920"');
$icon-circle-k: unquote('"\\e921"');
$icon-circle-l: unquote('"\\e923"');
$icon-circle-m: unquote('"\\e925"');
$icon-circle-n: unquote('"\\e928"');
$icon-circle-o: unquote('"\\e929"');
$icon-circle-p: unquote('"\\e92a"');
$icon-circle-q: unquote('"\\e92d"');
$icon-circle-r: unquote('"\\e92f"');
$icon-circle-s: unquote('"\\e930"');
$icon-circle-t: unquote('"\\e934"');
$icon-circle-u: unquote('"\\e937"');
$icon-circle-v: unquote('"\\e938"');
$icon-circle-w: unquote('"\\e939"');
$icon-circle-x: unquote('"\\e93a"');
$icon-circle-y: unquote('"\\e93b"');
$icon-circle-z: unquote('"\\e93d"');
$icon-warning: unquote('"\\ea07"');
$icon-notification: unquote('"\\ea08"');
$icon-qr-code: unquote('"\\e948"');
$icon-apple: unquote('"\\e949"');
$icon-calendar: unquote('"\\e94a"');
$icon-cancel: unquote('"\\e94b"');
$icon-cash-bag: unquote('"\\e94c"');
$icon-clock: unquote('"\\e94e"');
$icon-contract: unquote('"\\e94f"');
$icon-credit-card: unquote('"\\e950"');
$icon-customer-support: unquote('"\\e951"');
$icon-document: unquote('"\\e952"');
$icon-earth: unquote('"\\e914"');
$icon-fax: unquote('"\\e955"');
$icon-gift: unquote('"\\e956"');
$icon-graduation: unquote('"\\e957"');
$icon-group: unquote('"\\e958"');
$icon-map: unquote('"\\e959"');
$icon-phone: unquote('"\\e95a"');
$icon-cell-phone: unquote('"\\e91e"');
$icon-play: unquote('"\\e95b"');
$icon-puzzle: unquote('"\\e95c"');
$icon-return-shipment: unquote('"\\e95d"');
$icon-seesaw: unquote('"\\e95e"');
$icon-shield: unquote('"\\e960"');
$icon-speech-heart: unquote('"\\e961"');
$icon-star-ribbon: unquote('"\\e962"');
$icon-sunburst: unquote('"\\e963"');
$icon-thought-cloud: unquote('"\\e964"');
$icon-video: unquote('"\\e965"');
$icon-warranty: unquote('"\\e966"');
$icon-world-wide-web: unquote('"\\e967"');
$icon-wrench: unquote('"\\e968"');
$icon-storefront: unquote('"\\e909"');
$icon-recycle: unquote('"\\e90c"');
$icon-handicapped: unquote('"\\e90e"');
$icon-discount: unquote('"\\e90b"');
$icon-check-burst: unquote('"\\e904"');
$icon-check-circle: unquote('"\\e905"');
$icon-link: unquote('"\\e906"');
$icon-ready-to-assemble: unquote('"\\e907"');
$icon-kaplan-k: unquote('"\\e91a"');
$icon-kaplan-icon: unquote('"\\e970"');
$icon-gryphonhouse-icon: unquote('"\\e900"');
$icon-c4l-icon: unquote('"\\e972"');
$icon-extendednotes-icon: unquote('"\\e973"');
$icon-fully-assembled: unquote('"\\e90f"');
$icon-assembly-required: unquote('"\\e911"');
$icon-installation-required: unquote('"\\e912"');
$icon-surfacing-required: unquote('"\\e915"');
$icon-portable: unquote('"\\e916"');
$icon-color-options: unquote('"\\e918"');
$icon-lift-gate: unquote('"\\e91f"');
$icon-age-0-plus: unquote('"\\e922"');
$icon-age-1-plus: unquote('"\\e924"');
$icon-age-2-plus: unquote('"\\e926"');
$icon-age-3-plus: unquote('"\\e927"');
$icon-age-5-plus: unquote('"\\e92b"');
$icon-age-8-plus: unquote('"\\e92c"');

// Kapicons

$icon-lg-font-family: "kapicons";
$icon-lg-font-path: "../fonts/Kapicons";

$icon-lg-cart: unquote('"\\e808"');
$icon-lg-chat: unquote('"\\e809"');
$icon-lg-user: unquote('"\\e80b"');
$icon-lg-book: unquote('"\\e80c"');
$icon-lg-bulb: unquote('"\\e80d"');
$icon-lg-package: unquote('"\\e80e"');
$icon-lg-pencil: unquote('"\\e80f"');
$icon-lg-apple-k5: unquote('"\\e810"');
$icon-lg-leaf: unquote('"\\e812"');
$icon-lg-star: unquote('"\\e813"');
$icon-lg-star-filled: unquote('"\\e90d"');
$icon-lg-tree: unquote('"\\e814"');
$icon-lg-truck: unquote('"\\e815"');
$icon-lg-save: unquote('"\\e816"');
$icon-lg-open: unquote('"\\e817"');
$icon-lg-trash: unquote('"\\e818"');
$icon-lg-pdf: unquote('"\\e81f"');
$icon-lg-excel: unquote('"\\e820"');
$icon-lg-search: unquote('"\\e821"');
$icon-lg-mail: unquote('"\\e822"');
$icon-lg-clipboard: unquote('"\\e823"');
$icon-lg-eye: unquote('"\\e827"');
$icon-lg-heart: unquote('"\\e828"');
$icon-lg-footsteps: unquote('"\\e93e"');
$icon-lg-duplicate: unquote('"\\e82a"');
$icon-lg-checkmark: unquote('"\\e902"');
$icon-lg-warning: unquote('"\\ea07"');
$icon-lg-notification: unquote('"\\ea08"');
$icon-lg-qr-code: unquote('"\\e948"');
$icon-lg-apple: unquote('"\\e949"');
$icon-lg-calendar: unquote('"\\e94a"');
$icon-lg-cancel: unquote('"\\e94b"');
$icon-lg-cash-bag: unquote('"\\e94c"');
$icon-lg-clock: unquote('"\\e94e"');
$icon-lg-contract: unquote('"\\e94f"');
$icon-lg-credit-card: unquote('"\\e950"');
$icon-lg-customer-support: unquote('"\\e951"');
$icon-lg-document: unquote('"\\e952"');
$icon-lg-double-puzzle: unquote('"\\e953"');
$icon-lg-earth: unquote('"\\e914"');
$icon-lg-fax: unquote('"\\e955"');
$icon-lg-gift: unquote('"\\e956"');
$icon-lg-graduation: unquote('"\\e957"');
$icon-lg-group: unquote('"\\e958"');
$icon-lg-map: unquote('"\\e959"');
$icon-lg-phone: unquote('"\\e95a"');
$icon-lg-cell-phone: unquote('"\\e91e"');
$icon-lg-play: unquote('"\\e95b"');
$icon-lg-puzzle: unquote('"\\e95c"');
$icon-lg-return-shipment: unquote('"\\e95d"');
$icon-lg-seesaw: unquote('"\\e95e"');
$icon-lg-shield: unquote('"\\e960"');
$icon-lg-speech-heart: unquote('"\\e961"');
$icon-lg-star-ribbon: unquote('"\\e962"');
$icon-lg-sunburst: unquote('"\\e963"');
$icon-lg-thought-cloud: unquote('"\\e964"');
$icon-lg-video: unquote('"\\e965"');
$icon-lg-warranty: unquote('"\\e966"');
$icon-lg-world-wide-web: unquote('"\\e967"');
$icon-lg-wrench: unquote('"\\e968"');
$icon-lg-storefront: unquote('"\\e909"');
$icon-lg-recycle: unquote('"\\e90c"');
$icon-lg-handicapped: unquote('"\\e90e"');
$icon-lg-discount: unquote('"\\e90b"');
$icon-lg-fully-assembled: unquote('"\\e90f"');
$icon-lg-assembly-required: unquote('"\\e911"');
$icon-lg-installation-required: unquote('"\\e912"');
$icon-lg-surfacing-required: unquote('"\\e915"');
$icon-lg-portable: unquote('"\\e916"');
$icon-lg-color-options: unquote('"\\e918"');
$icon-lg-lift-gate: unquote('"\\e91f"');


// Tables
//
// Customizes the `.table` component with basic values, each used across all table variations.

$table-cell-padding:          1rem;
$table-cell-padding-sm:       .5rem;

$table-color:                 $body-color;
$table-bg:                    null;
$table-accent-bg:             rgba($gray-900, .05);
$table-hover-color:           $table-color;
$table-hover-bg:              rgba($lavender, .1);
$table-active-bg:             $table-hover-bg;

$table-border-width:          $border-width;
$table-border-color:          $border-color;

$table-head-bg:               $white;
$table-head-color:            $gray-900;
$table-head-letter-spacing:   .1rem;
$table-head-font-weight:      700;
$table-head-font-size:        $font-size-base;
$table-head-text-transform:   uppercase;

$table-dark-color:            $white;
$table-dark-bg:               $lavender;
$table-dark-accent-bg:        rgba($white, .05);
$table-dark-hover-color:      $table-dark-color;
$table-dark-hover-bg:         rgba($white, .075);
$table-dark-border-color:     lighten($table-dark-bg, 7.5%) !default;

$table-striped-order:         odd;

$table-caption-color:         $text-muted !default;

$table-bg-level:              -9 !default;
$table-border-level:          -6 !default;


// Buttons + Forms
//
// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.

$input-btn-padding-y:         1rem;
$input-btn-padding-x:         2rem;
$input-btn-font-family:       null;
$input-btn-font-size:         $font-size-base;
$input-btn-letter-space:      .2rem;
$input-btn-text-transform:    uppercase;
$input-btn-line-height:       1;

$input-btn-focus-width:       .2rem;
$input-btn-focus-color:       rgba($component-active-bg, .25);
$input-btn-focus-box-shadow:  none;

$input-btn-padding-y-sm:      .75rem;
$input-btn-padding-x-sm:      1.25rem;
$input-btn-font-size-sm:      $font-size-sm;
$input-btn-line-height-sm:    1;

$input-btn-padding-y-lg:      1.25rem;
$input-btn-padding-x-lg:      2.75rem;
$input-btn-font-size-lg:      1.25rem;
$input-btn-line-height-lg:    1;

$input-btn-border-width:      $border-width;


// Buttons
//
// For each of Bootstrap's buttons, define text, background, and border color.

$btn-padding-y:               $input-btn-padding-y;
$btn-padding-x:               $input-btn-padding-x;
$btn-font-family:             $input-btn-font-family;
$btn-font-size:               $input-btn-font-size;
$btn-letter-space:            $input-btn-letter-space;
$btn-text-transform:          $input-btn-text-transform;
$btn-line-height:             $input-btn-line-height;
$btn-white-space:             null; // Set to `nowrap` to prevent text wrapping

$btn-padding-y-sm:            $input-btn-padding-y-sm;
$btn-padding-x-sm:            $input-btn-padding-x-sm;
$btn-font-size-sm:            $input-btn-font-size-sm;
$btn-line-height-sm:          $input-btn-line-height-sm;

$btn-padding-y-lg:            $input-btn-padding-y-lg;
$btn-padding-x-lg:            $input-btn-padding-x-lg;
$btn-font-size-lg:            $input-btn-font-size-lg;
$btn-line-height-lg:          $input-btn-line-height-lg;

$btn-border-width:            $input-btn-border-width;

$btn-font-weight:             900;
$btn-box-shadow:              none;
$btn-focus-width:             $input-btn-focus-width;
$btn-focus-box-shadow:        none;
$btn-disabled-opacity:        .65;
$btn-active-box-shadow:       none;

$btn-link-disabled-color:     $gray-600;

$btn-block-spacing-y:         .5rem;

// Allows for customizing button radius independently from global border radius
$btn-border-radius:           28px;
$btn-border-radius-lg:        34px;
$btn-border-radius-sm:        26px;

$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;


// Forms

$label-margin-bottom:                   .5rem;

$input-padding-y:                       $input-btn-padding-y;
$input-padding-x:                       $input-btn-padding-x;
$input-font-family:                     $input-btn-font-family;
$input-font-size:                       $input-btn-font-size;
$input-font-weight:                     $font-weight-base;
$input-line-height:                     $input-btn-line-height;

$input-padding-y-sm:                    $input-btn-padding-y-sm;
$input-padding-x-sm:                    $input-btn-padding-x-sm;
$input-font-size-sm:                    $input-btn-font-size-sm;
$input-line-height-sm:                  $input-btn-line-height-sm;

$input-padding-y-lg:                    $input-btn-padding-y-lg;
$input-padding-x-lg:                    $input-btn-padding-x-lg;
$input-font-size-lg:                    $input-btn-font-size-lg;
$input-line-height-lg:                  $input-btn-line-height-lg;

$input-bg:                              $white;
$input-disabled-bg:                     $gray-300;

$input-color:                           $gray-900;
$input-border-color:                    $border-color;
$input-border-width:                    $input-btn-border-width;
$input-box-shadow:                      none;

$input-border-radius:                   28px;
$input-border-radius-lg:                34px;
$input-border-radius-sm:                26px;

$input-focus-bg:                        $input-bg;
$input-focus-border-color:              color('blue');
$input-focus-color:                     $input-color;
$input-focus-width:                     $input-btn-focus-width;
$input-focus-box-shadow:                $box-shadow-sm;

$input-placeholder-color:               $gray-700;
$input-plaintext-color:                 $body-color;

$input-height-border:                   $input-border-width * 2;

$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2);
$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y);
$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y / 2);

$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false));
$input-height-sm:                       add($input-line-height-sm * 1em, add($input-padding-y-sm * 2, $input-height-border, false));
$input-height-lg:                       add($input-line-height-lg * 1em, add($input-padding-y-lg * 2, $input-height-border, false));

$form-text-margin-top:                  .25rem;

$form-check-input-gutter:               1.375rem;
$form-check-input-margin-y:             .3rem;
$form-check-input-margin-x:             .25rem;

$form-check-inline-margin-x:            .75rem;
$form-check-inline-input-margin-x:      .3125rem;

$form-grid-gutter-width:                16px;
$form-group-margin-bottom:              2rem;

$input-group-addon-color:               $input-color;
$input-group-addon-bg:                  $gray-200;
$input-group-addon-border-color:        $input-border-color;

$custom-forms-transition:               background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;

$custom-control-gutter:                 .5rem;
$custom-control-spacer-x:               1rem;
$custom-control-cursor:                 null;

$custom-control-indicator-size:         1.625rem;
$custom-control-indicator-bg:           $input-bg;

$custom-control-indicator-bg-size:      50% 50%;
$custom-control-indicator-box-shadow:   $input-box-shadow;
$custom-control-indicator-border-color: $input-border-color;
$custom-control-indicator-border-width: $input-border-width;

$custom-control-label-color:            null;

$custom-control-indicator-disabled-bg:          $input-disabled-bg;
$custom-control-label-disabled-color:           $gray-600;

$custom-control-indicator-checked-color:        $component-active-color;
$custom-control-indicator-checked-bg:           $component-active-bg;
$custom-control-indicator-checked-disabled-bg:  rgba(theme-color("secondary"), .5);
$custom-control-indicator-checked-box-shadow:   none;
$custom-control-indicator-checked-border-color: $custom-control-indicator-checked-bg;

$custom-control-indicator-focus-box-shadow:     none;
$custom-control-indicator-focus-border-color:   $custom-control-indicator-checked-border-color;

$custom-control-indicator-active-color:         $component-active-color;
$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%);
$custom-control-indicator-active-box-shadow:    none;
$custom-control-indicator-active-border-color:  $custom-control-indicator-active-bg;

$custom-checkbox-indicator-border-radius:       0;
$custom-checkbox-indicator-icon-checked:        url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'><path fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/></svg>");

$custom-checkbox-indicator-indeterminate-bg:           $component-active-bg;
$custom-checkbox-indicator-indeterminate-color:        $custom-control-indicator-checked-color;
$custom-checkbox-indicator-icon-indeterminate:         url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'><path stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/></svg>");
$custom-checkbox-indicator-indeterminate-box-shadow:   none;
$custom-checkbox-indicator-indeterminate-border-color: $custom-checkbox-indicator-indeterminate-bg;

$custom-radio-indicator-border-radius:          50%;
$custom-radio-indicator-icon-checked:           url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='-4 -4 8 8'><circle r='3' fill='#{$custom-control-indicator-checked-color}'/></svg>");

$custom-select-padding-y:           $input-padding-y;
$custom-select-padding-x:           $input-padding-x;
$custom-select-font-family:         $input-font-family;
$custom-select-font-size:           $input-font-size;
$custom-select-height:              $input-height;
$custom-select-indicator-padding:   1rem; // Extra padding to account for the presence of the background-image based indicator
$custom-select-font-weight:         $input-font-weight;
$custom-select-line-height:         $input-line-height;
$custom-select-color:               $input-color;
$custom-select-disabled-color:      $gray-600;
$custom-select-bg:                  $input-bg;
$custom-select-disabled-bg:         $gray-200;
$custom-select-bg-size:             8px 10px; // In pixels because image dimensions
$custom-select-indicator-color:     $gray-800;
$custom-select-indicator:           url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5'><path fill='#{$custom-select-indicator-color}' d='M.22 2.03a.827.827 0 01-.16-.2.533.533 0 01-.06-.24V.33C0 .*********.04.3-.02.41-.02.51.05l.02.02c.01 0 .02.01.02.02l3.27 1.95c.***********.37 0L7.45.09l.04-.04c.11-.06.22-.07.33-.***********.16.18.29v1.26c0 .19-.07.33-.22.44l-3.6 2.89c-.05.05-.1.08-.17.08s-.13-.03-.19-.08L.22 2.03z'/></svg>");
$custom-select-background:          escape-svg($custom-select-indicator) no-repeat right $custom-select-padding-x center / $custom-select-bg-size; // Used so we can have multiple background elements (e.g., arrow and feedback icon)

$custom-select-feedback-icon-padding-right: add(1em * .75, (2 * $custom-select-padding-y * .75) + $custom-select-padding-x + $custom-select-indicator-padding);
$custom-select-feedback-icon-position:      center right ($custom-select-padding-x + $custom-select-indicator-padding);
$custom-select-feedback-icon-size:          $input-height-inner-half $input-height-inner-half;

$custom-select-border-width:        $input-border-width;
$custom-select-border-color:        $input-border-color;
$custom-select-border-radius:       $input-border-radius;
$custom-select-box-shadow:          none;

$custom-select-focus-border-color:  $input-focus-border-color;
$custom-select-focus-width:         $input-focus-width;
$custom-select-focus-box-shadow:    $input-focus-box-shadow;

$custom-select-padding-y-sm:        $input-padding-y-sm;
$custom-select-padding-x-sm:        $input-padding-x-sm;
$custom-select-font-size-sm:        $input-font-size-sm;
$custom-select-height-sm:           $input-height-sm;

$custom-select-padding-y-lg:        $input-padding-y-lg;
$custom-select-padding-x-lg:        $input-padding-x-lg;
$custom-select-font-size-lg:        $input-font-size-lg;
$custom-select-height-lg:           $input-height-lg;


// Form validation

$form-feedback-margin-top:          $form-text-margin-top;
$form-feedback-font-size:           $small-font-size;
$form-feedback-valid-color:         theme-color("success");
$form-feedback-invalid-color:       theme-color("danger");

$form-feedback-icon-valid-color:    $form-feedback-valid-color;
$form-feedback-icon-valid:          none;
$form-feedback-icon-invalid-color:  $form-feedback-invalid-color;
$form-feedback-icon-invalid:        none;

$form-validation-states: ();
// stylelint-disable-next-line scss/dollar-variable-default
$form-validation-states: map-merge(
  (
    "valid": (
      "color": $form-feedback-valid-color,
      "icon": $form-feedback-icon-valid
    ),
    "invalid": (
      "color": $form-feedback-invalid-color,
      "icon": $form-feedback-icon-invalid
    ),
  ),
  $form-validation-states
);


// Navs

$nav-link-padding-y:                .5rem;
$nav-link-padding-x:                1rem;
$nav-link-disabled-color:           $gray-600;

$nav-tabs-border-color:             $gray-600;
$nav-tabs-border-width:             $border-width;
$nav-tabs-border-radius:            $border-radius;
$nav-tabs-link-hover-border-color:  transparent transparent $nav-tabs-border-color;
$nav-tabs-link-active-color:        $component-active-bg;
$nav-tabs-link-active-bg:           $body-bg;
$nav-tabs-link-active-border-color: transparent transparent $nav-tabs-border-color;

$nav-pills-border-radius:           $border-radius;
$nav-pills-link-active-color:       $component-active-color;
$nav-pills-link-active-bg:          $component-active-bg;

$nav-divider-color:                 $gray-300;
$nav-divider-margin-y:              $spacer / 2;

$nav-link-font-size:                $font-size-base;
$nav-link-font-weight:              700;
$nav-link-line-height:              1em;
$nav-link-letter-spacing:           .2rem;
$nav-link-text-transform:           uppercase;


// Dropdowns
//
// Dropdown menu container and contents.

$dropdown-min-width:                10rem;
$dropdown-padding-y:                .5rem;
$dropdown-spacer:                   .125rem;
$dropdown-font-size:                $font-size-base;
$dropdown-color:                    $body-color;
$dropdown-bg:                       $white;
$dropdown-border-color:             $gray-300;
$dropdown-border-radius:            $border-radius;
$dropdown-border-width:             $border-width;
$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width);
$dropdown-divider-bg:               $gray-500;
$dropdown-divider-margin-y:         $nav-divider-margin-y;
$dropdown-box-shadow:               $box-shadow-sm;

$dropdown-link-color:               $gray-900;
$dropdown-link-hover-color:         $link-hover-color;
$dropdown-link-hover-bg:            transparent;

$dropdown-link-active-color:        $component-active-color;
$dropdown-link-active-bg:           $component-active-bg;

$dropdown-link-disabled-color:      $gray-600;

$dropdown-item-padding-y:           .25rem;
$dropdown-item-padding-x:           1rem;

$dropdown-header-color:             $white;
$dropdown-header-bg:                color("lavender");
$dropdown-header-font-size:         .75rem;
$dropdown-header-font-weight:       700;
$dropdown-header-text-transform:    uppercase;
$dropdown-header-letter-spacing:    .2rem;
$dropdown-header-padding:           $spacer / 2;


// Pagination

$pagination-padding-y:              .8125rem;
$pagination-padding-x:              .8125rem;
$pagination-padding-y-sm:           .5rem;
$pagination-padding-x-sm:           .75rem;
$pagination-padding-y-lg:           1rem;
$pagination-padding-x-lg:           1.75rem;
$pagination-line-height:            1;

$pagination-color:                  $link-color;
$pagination-bg:                     $white;
$pagination-border-width:           0;
$pagination-border-color:           transparent;

$pagination-focus-box-shadow:       none;
$pagination-focus-outline:          0;

$pagination-hover-color:            $link-hover-color;
$pagination-hover-bg:               $gray-300;
$pagination-hover-border-color:     transparent;

$pagination-active-color:           $component-active-color;
$pagination-active-bg:              $component-active-bg;
$pagination-active-border-color:    transparent;

$pagination-disabled-color:         $gray-600;
$pagination-disabled-bg:            $white;
$pagination-disabled-border-color:  transparent;


// Jumbotron

$jumbotron-padding:                 2rem !default;
$jumbotron-color:                   null !default;
$jumbotron-bg:                      $gray-200 !default;


// Cards

$card-spacer-y:                     1rem;
$card-spacer-x:                     1rem;
$card-border-width:                 $border-width;
$card-border-radius:                $border-radius;
$card-border-color:                 $border-color;
$card-inner-border-radius:          subtract($card-border-radius, $card-border-width);
$card-cap-bg:                       rgba($black, .03);
$card-cap-color:                    null;
$card-height:                       null;
$card-color:                        null;
$card-bg:                           $white;

$card-img-overlay-padding:          1rem;

$card-group-margin:                 $grid-gutter-width / 2;
$card-deck-margin:                  $card-group-margin;

$card-columns-count:                3;
$card-columns-gap:                  1rem;
$card-columns-margin:               $card-spacer-y;


// Tooltips

$tooltip-font-size:                 $font-size-sm;
$tooltip-max-width:                 200px;
$tooltip-color:                     color('sky');
$tooltip-bg:                        color('royal');
$tooltip-border-radius:             $border-radius;
$tooltip-opacity:                   1;
$tooltip-padding-y:                 1rem;
$tooltip-padding-x:                 1.25rem;
$tooltip-margin:                    0;

$tooltip-img-color:                  $body-color;
$tooltip-img-bg:                     color('sky');

$tooltip-arrow-width:               .8rem;
$tooltip-arrow-height:              .4rem;
$tooltip-arrow-color:               $tooltip-bg;

// Form tooltips must come after regular tooltips
$form-feedback-tooltip-padding-y:     $tooltip-padding-y;
$form-feedback-tooltip-padding-x:     $tooltip-padding-x;
$form-feedback-tooltip-font-size:     $tooltip-font-size;
$form-feedback-tooltip-line-height:   $line-height-base;
$form-feedback-tooltip-opacity:       1;
$form-feedback-tooltip-border-radius: $tooltip-border-radius;


// Popovers

$popover-font-size:                 $font-size-sm;
$popover-bg:                        $white;
$popover-max-width:                 276px;
$popover-border-width:              $border-width;
$popover-border-color:              $dropdown-border-color;
$popover-border-radius:             $border-radius;
$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;
$popover-box-shadow:                $box-shadow-sm;

$popover-header-bg:                 color('sky');
$popover-header-color:              $headings-color;
$popover-header-padding-y:          1rem;
$popover-header-padding-x:          1.25rem;

$popover-body-color:                $body-color;
$popover-body-padding-y:            $popover-header-padding-y;
$popover-body-padding-x:            $popover-header-padding-x;

$popover-arrow-width:               1rem;
$popover-arrow-height:              .5rem;
$popover-arrow-color:               $popover-bg;

$popover-arrow-outer-color:         fade-in($popover-border-color, .05);


// Badges

$badge-font-size:                   50%;
$badge-font-weight:                 $font-weight-bold;
$badge-padding-y:                   .5em;
$badge-padding-x:                   .8em;
$badge-border-radius:               50%;

$badge-transition:                  $btn-transition;
$badge-focus-width:                 $input-btn-focus-width;

$badge-pill-padding-x:              1.25em;
// Use a higher than normal value to ensure completely rounded edges when
// customizing padding or font-size on labels.
$badge-pill-border-radius:          10rem;


// Modals

// Padding applied to the modal body
$modal-inner-padding:               1rem;

// Margin between elements in footer, must be lower than or equal to 2 * $modal-inner-padding
$modal-footer-margin-between:       .5rem;

$modal-dialog-margin:               .5rem;
$modal-dialog-margin-y-sm-up:       1.75rem;

$modal-title-line-height:           1;

$modal-content-color:               null;
$modal-content-bg:                  $white;
$modal-content-border-color:        transparent;
$modal-content-border-width:        0;
$modal-content-border-radius:       $border-radius;
$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width);
$modal-content-box-shadow-xs:       $box-shadow;
$modal-content-box-shadow-sm-up:    $box-shadow-sm;

$modal-backdrop-bg:                 $black;
$modal-backdrop-opacity:            .3;
$modal-header-border-color:         $gray-500;
$modal-footer-border-color:         transparent;
$modal-header-border-width:         1px;
$modal-footer-border-width:         0;
$modal-footer-bg:                   color('sky');
$modal-header-padding-y:            1rem;
$modal-header-padding-x:            0;
$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x; // Keep this for backwards compatibility
$modal-header-margin-y:             0;
$modal-header-margin-x:             1rem;
$modal-header-margin:               $modal-header-margin-y $modal-header-margin-x;

$modal-xl:                          1140px;
$modal-lg:                          800px;
$modal-md:                          500px;
$modal-sm:                          300px;

$modal-fade-transform:              translate(0, -50px);
$modal-show-transform:              none;
$modal-transition:                  transform .3s ease-out;
$modal-scale-transform:             scale(1.02);


// Alerts
//
// Define alert colors, border radius, and padding.

$alert-padding-y:                   1rem;
$alert-padding-x:                   1rem;
$alert-margin-bottom:               1rem;
$alert-border-radius:               $border-radius;
$alert-link-font-weight:            $font-weight-bold;
$alert-line-height:                 1rem;
$alert-border-width:                0;

$alert-bg-level:                    0;
$alert-border-level:                0;
$alert-color-level:                 -12;


// List group

$list-group-color:                  null;
$list-group-bg:                     $white;
$list-group-border-color:           transparent;
$list-group-border-width:           0;
$list-group-border-radius:          $border-radius;

$list-group-item-padding-y:         .25rem;
$list-group-item-padding-x:         1rem;

$list-group-hover-bg:               $gray-300;
$list-group-active-color:           $component-active-bg;
$list-group-active-bg:              $white;
$list-group-active-border-color:    $list-group-active-bg;

$list-group-disabled-color:         $gray-600;
$list-group-disabled-bg:            $list-group-bg;

$list-group-action-color:           null;
$list-group-action-hover-color:     $list-group-action-color;

$list-group-action-active-color:    $component-active-color;
$list-group-action-active-bg:       $component-active-bg;


// Image thumbnails

$thumbnail-padding:                 .625rem;
$thumbnail-bg:                      $body-bg;
$thumbnail-border-width:            0;
$thumbnail-border-color:            transparent;
$thumbnail-border-radius:           0;
$thumbnail-box-shadow:              none;


// Figures

$figure-caption-font-size:          $font-size-sm;
$figure-caption-color:              $body-color;


// Breadcrumbs

$breadcrumb-font-size:              null;

$breadcrumb-padding-y:              0;
$breadcrumb-padding-x:              0;
$breadcrumb-item-padding:           .5rem;

$breadcrumb-margin-bottom:          0;

$breadcrumb-bg:                     transparent;
$breadcrumb-divider-color:          $gray-600;
$breadcrumb-active-color:           $gray-700;
$breadcrumb-divider-font-family:    kapicons-ui;
$breadcrumb-divider:                '\e802';

$breadcrumb-border-radius:          0;


// Close

$close-font-size:                   $font-size-base;
$close-font-weight:                 $font-weight-normal;
$close-color:                       $gray-700;
$close-text-shadow:                 none;


// Hamburger Button

$hamburger-padding-x           : 0px !default;
$hamburger-padding-y           : 0px !default;
$hamburger-layer-width         : 32px !default;
$hamburger-layer-height        : 4px !default;
$hamburger-layer-spacing       : 6px !default;
$hamburger-layer-color         : $lavender !default;
$hamburger-layer-border-radius : 4px !default;
$hamburger-hover-opacity       : 1 !default;
$hamburger-active-layer-color  : $link-hover-color !default;
$hamburger-active-hover-opacity: $hamburger-hover-opacity !default;

// To use CSS filters as the hover effect instead of opacity,
// set $hamburger-hover-use-filter as true and
// change the value of $hamburger-hover-filter accordingly.
$hamburger-hover-use-filter   : false !default;
$hamburger-hover-filter       : opacity(50%) !default;
$hamburger-active-hover-filter: $hamburger-hover-filter !default;

// Types (Remove or comment out what you don’t need)
$hamburger-types: (
  elastic,
  elastic-r,
) !default;



// Slick Carousel

// Slick icon entity codes outputs the following
// "\2190" outputs ascii character "←"
// "\2192" outputs ascii character "→"
// "\2022" outputs ascii character "•"

$slick-font-path: "fonts/";
$slick-font-family: "kapicons";
$slick-loader-path: "./";
$slick-arrow-color: $gray-700;
$slick-dot-color: $white;
$slick-dot-color-active: $slick-dot-color;
$slick-prev-character: "\e801";
$slick-next-character: "\e802";
$slick-dot-character: "\2022";
$slick-dot-size: 6px;
$slick-opacity-default: 0.75;
$slick-opacity-on-hover: 1;
$slick-opacity-not-active: 0.25;
