// User Interface Icon Set
//
// Optimized for use in UI components at small sizes. Do not use larger than 64px.

@import "variables";

@font-face {
  font-family: '#{$ui-icons-font-family}';
  src:  url('#{$ui-icons-font-path}/#{$ui-icons-font-family}.eot?kf1wo2');
  src:  url('#{$ui-icons-font-path}/#{$ui-icons-font-family}.eot?kf1wo2#iefix') format('embedded-opentype'),
    url('#{$ui-icons-font-path}/#{$ui-icons-font-family}.ttf?kf1wo2') format('truetype'),
    url('#{$ui-icons-font-path}/#{$ui-icons-font-family}.woff?kf1wo2') format('woff'),
    url('#{$ui-icons-font-path}/#{$ui-icons-font-family}.svg?kf1wo2##{$ui-icons-font-family}') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: '#{$ui-icons-font-family}' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -moz-font-feature-settings: "liga=1";
  -moz-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-angle-down {
  &:before {
    content: $icon-angle-down; 
  }
}
.icon-angle-left {
  &:before {
    content: $icon-angle-left; 
  }
}
.icon-angle-right {
  &:before {
    content: $icon-angle-right; 
  }
}
.icon-angle-up {
  &:before {
    content: $icon-angle-up; 
  }
}
.icon-double-angle-left {
  &:before {
    content: $icon-double-angle-left; 
  }
}
.icon-double-angle-right {
  &:before {
    content: $icon-double-angle-right; 
  }
}
.icon-minus {
  &:before {
    content: $icon-minus; 
  }
}
.icon-plus {
  &:before {
    content: $icon-plus; 
  }
}
.icon-cart {
  &:before {
    content: $icon-cart; 
  }
}
.icon-chat {
  &:before {
    content: $icon-chat; 
  }
}
.icon-hamburger {
  &:before {
    content: $icon-hamburger; 
  }
}
.icon-user {
  &:before {
    content: $icon-user; 
  }
}
.icon-book {
  &:before {
    content: $icon-book; 
  }
}
.icon-bulb {
  &:before {
    content: $icon-bulb; 
  }
}
.icon-package {
  &:before {
    content: $icon-package; 
  }
}
.icon-pencil {
  &:before {
    content: $icon-pencil; 
  }
}
.icon-apple-k5 {
  &:before {
    content: $icon-apple-k5; 
  }
}
.icon-dollar {
  &:before {
    content: $icon-dollar; 
  }
}
.icon-leaf {
  &:before {
    content: $icon-leaf; 
  }
}
.icon-star {
  &:before {
    content: $icon-star; 
  }
}
.icon-star-filled {
  &:before {
    content: $icon-star-filled; 
  }
}
.icon-tree {
  &:before {
    content: $icon-tree; 
  }
}
.icon-truck {
  &:before {
    content: $icon-truck; 
  }
}
.icon-save {
  &:before {
    content: $icon-save; 
  }
}
.icon-open {
  &:before {
    content: $icon-open; 
  }
}
.icon-trash {
  &:before {
    content: $icon-trash; 
  }
}
.icon-facebook {
  &:before {
    content: $icon-facebook; 
  }
}
.icon-linkedin {
  &:before {
    content: $icon-linkedin; 
  }
}
.icon-pinterest {
  &:before {
    content: $icon-pinterest; 
  }
}
.icon-twitter {
  &:before {
    content: $icon-twitter; 
  }
}
.icon-x-twitter {
  &:before {
    content: $icon-x-twitter; 
  }
}
.icon-youtube {
  &:before {
    content: $icon-youtube; 
  }
}
.icon-instagram {
  &:before {
    content: $icon-instagram; 
  }
}
.icon-tiktok {
  &:before {
    content: $icon-tiktok; 
  }
}
.icon-pdf {
  &:before {
    content: $icon-pdf; 
  }
}
.icon-excel {
  &:before {
    content: $icon-excel; 
  }
}
.icon-search {
  &:before {
    content: $icon-search; 
  }
}
.icon-share {
  &:before {
    content: $icon-share; 
  }
}
.icon-mail {
  &:before {
    content: $icon-mail; 
  }
}
.icon-clipboard {
  &:before {
    content: $icon-clipboard; 
  }
}
.icon-question {
  &:before {
    content: $icon-question; 
  }
}
.icon-grid-view {
  &:before {
    content: $icon-grid-view; 
  }
}
.icon-list-view {
  &:before {
    content: $icon-list-view; 
  }
}
.icon-eye {
  &:before {
    content: $icon-eye; 
  }
}
.icon-footsteps {
  &:before {
    content: $icon-footsteps; 
  }
}
.icon-heart {
  &:before {
    content: $icon-heart; 
  }
}
.icon-point {
  &:before {
    content: $icon-point; 
  }
}
.icon-duplicate {
  &:before {
    content: $icon-duplicate; 
  }
}
.icon-checkmark {
  &:before {
    content: $icon-checkmark; 
  }
}
.icon-circle-a {
  &:before {
    content: $icon-circle-a; 
  }
}
.icon-circle-b {
  &:before {
    content: $icon-circle-b; 
  }
}
.icon-circle-c {
  &:before {
    content: $icon-circle-c; 
  }
}
.icon-circle-d {
  &:before {
    content: $icon-circle-d; 
  }
}
.icon-circle-e {
  &:before {
    content: $icon-circle-e; 
  }
}
.icon-circle-f {
  &:before {
    content: $icon-circle-f; 
  }
}
.icon-circle-g {
  &:before {
    content: $icon-circle-g; 
  }
}
.icon-circle-h {
  &:before {
    content: $icon-circle-h; 
  }
}
.icon-circle-i {
  &:before {
    content: $icon-circle-i; 
  }
}
.icon-circle-j {
  &:before {
    content: $icon-circle-j; 
  }
}
.icon-circle-k {
  &:before {
    content: $icon-circle-k; 
  }
}
.icon-circle-l {
  &:before {
    content: $icon-circle-l; 
  }
}
.icon-circle-m {
  &:before {
    content: $icon-circle-m; 
  }
}
.icon-circle-n {
  &:before {
    content: $icon-circle-n; 
  }
}
.icon-circle-o {
  &:before {
    content: $icon-circle-o; 
  }
}
.icon-circle-p {
  &:before {
    content: $icon-circle-p; 
  }
}
.icon-circle-q {
  &:before {
    content: $icon-circle-q; 
  }
}
.icon-circle-r {
  &:before {
    content: $icon-circle-r; 
  }
}
.icon-circle-s {
  &:before {
    content: $icon-circle-s; 
  }
}
.icon-circle-t {
  &:before {
    content: $icon-circle-t; 
  }
}
.icon-circle-u {
  &:before {
    content: $icon-circle-u; 
  }
}
.icon-circle-v {
  &:before {
    content: $icon-circle-v; 
  }
}
.icon-circle-w {
  &:before {
    content: $icon-circle-w; 
  }
}
.icon-circle-x {
  &:before {
    content: $icon-circle-x; 
  }
}
.icon-circle-y {
  &:before {
    content: $icon-circle-y; 
  }
}
.icon-circle-z {
  &:before {
    content: $icon-circle-z; 
  }
}
.icon-warning {
  &:before {
    content: $icon-warning; 
  }
}
.icon-notification {
  &:before {
    content: $icon-notification; 
  }
}
.icon-qr-code {
  &:before {
    content: $icon-qr-code; 
  }
}
.icon-apple {
  &:before {
    content: $icon-apple; 
  }
}
.icon-calendar {
  &:before {
    content: $icon-calendar; 
  }
}
.icon-cancel {
  &:before {
    content: $icon-cancel; 
  }
}
.icon-cash-bag {
  &:before {
    content: $icon-cash-bag; 
  }
}
.icon-clock {
  &:before {
    content: $icon-clock; 
  }
}
.icon-contract {
  &:before {
    content: $icon-contract; 
  }
}
.icon-credit-card {
  &:before {
    content: $icon-credit-card; 
  }
}
.icon-customer-support {
  &:before {
    content: $icon-customer-support; 
  }
}
.icon-document {
  &:before {
    content: $icon-document; 
  }
}
.icon-earth {
  &:before {
    content: $icon-earth; 
  }
}
.icon-fax {
  &:before {
    content: $icon-fax; 
  }
}
.icon-gift {
  &:before {
    content: $icon-gift; 
  }
}
.icon-graduation {
  &:before {
    content: $icon-graduation; 
  }
}
.icon-group {
  &:before {
    content: $icon-group; 
  }
}
.icon-map {
  &:before {
    content: $icon-map; 
  }
}
.icon-phone {
  &:before {
    content: $icon-phone; 
  }
}
.icon-cell-phone {
  &:before {
    content: $icon-cell-phone; 
  }
}
.icon-play {
  &:before {
    content: $icon-play; 
  }
}
.icon-puzzle {
  &:before {
    content: $icon-puzzle; 
  }
}
.icon-return-shipment {
  &:before {
    content: $icon-return-shipment; 
  }
}
.icon-seesaw {
  &:before {
    content: $icon-seesaw; 
  }
}
.icon-shield {
  &:before {
    content: $icon-shield; 
  }
}
.icon-speech-heart {
  &:before {
    content: $icon-speech-heart; 
  }
}
.icon-star-ribbon {
  &:before {
    content: $icon-star-ribbon; 
  }
}
.icon-sunburst {
  &:before {
    content: $icon-sunburst; 
  }
}
.icon-thought-cloud {
  &:before {
    content: $icon-thought-cloud; 
  }
}
.icon-video {
  &:before {
    content: $icon-video; 
  }
}
.icon-warranty {
  &:before {
    content: $icon-warranty; 
  }
}
.icon-world-wide-web {
  &:before {
    content: $icon-world-wide-web; 
  }
}
.icon-wrench {
  &:before {
    content: $icon-wrench; 
  }
}
.icon-storefront {
  &:before {
    content: $icon-storefront; 
  }
}
.icon-recycle {
  &:before {
    content: $icon-recycle; 
  }
}
.icon-handicapped {
  &:before {
    content: $icon-handicapped; 
  }
}
.icon-discount {
  &:before {
    content: $icon-discount; 
  }
}
.icon-check-burst {
  &:before {
    content: $icon-check-burst; 
  }
}
.icon-check-circle {
  &:before {
    content: $icon-check-circle; 
  }
}
.icon-link {
  &:before {
    content: $icon-link; 
  }
}
.icon-ready-to-assemble {
  &:before {
    content: $icon-ready-to-assemble; 
  }
}
.icon-kaplan-k {
  &:before {
    content: $icon-kaplan-k; 
  }
}
.icon-kaplan-icon {
  &:before {
    content: $icon-kaplan-icon; 
  }
}
.icon-gryphonhouse-icon {
  &:before {
    content: $icon-gryphonhouse-icon; 
  }
}
.icon-c4l-icon {
  &:before {
    content: $icon-c4l-icon; 
  }
}
.icon-extendednotes-icon {
  &:before {
    content: $icon-extendednotes-icon; 
  }
}
.icon-fully-assembled {
  &:before {
    content: $icon-fully-assembled; 
  }
}
.icon-assembly-required {
  &:before {
    content: $icon-assembly-required; 
  }
}
.icon-installation-required {
  &:before {
    content: $icon-installation-required; 
  }
}
.icon-surfacing-required {
  &:before {
    content: $icon-surfacing-required; 
  }
}
.icon-portable {
  &:before {
    content: $icon-portable; 
  }
}
.icon-color-options {
  &:before {
    content: $icon-color-options; 
  }
}
.icon-lift-gate {
  &:before {
    content: $icon-lift-gate; 
  }
}
.icon-age-0-plus {
  &:before {
    content: $icon-age-0-plus; 
  }
}
.icon-age-1-plus {
  &:before {
    content: $icon-age-1-plus; 
  }
}
.icon-age-2-plus {
  &:before {
    content: $icon-age-2-plus; 
  }
}
.icon-age-3-plus {
  &:before {
    content: $icon-age-3-plus; 
  }
}
.icon-age-5-plus {
  &:before {
    content: $icon-age-5-plus; 
  }
}
.icon-age-8-plus {
  &:before {
    content: $icon-age-8-plus; 
  }
}


// Large Icon Set
//
// Optimized for illustrative use. Use at 64px and above.

@font-face {
  font-family: '#{$icon-lg-font-family}';
  src:  url('#{$icon-lg-font-path}/#{$icon-lg-font-family}.eot?g8hsc0');
  src:  url('#{$icon-lg-font-path}/#{$icon-lg-font-family}.eot?g8hsc0#iefix') format('embedded-opentype'),
    url('#{$icon-lg-font-path}/#{$icon-lg-font-family}.ttf?g8hsc0') format('truetype'),
    url('#{$icon-lg-font-path}/#{$icon-lg-font-family}.woff?g8hsc0') format('woff'),
    url('#{$icon-lg-font-path}/#{$icon-lg-font-family}.svg?g8hsc0##{$icon-lg-font-family}') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-lg-"], [class*=" icon-lg-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: '#{$icon-lg-font-family}' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -moz-font-feature-settings: "liga=1";
  -moz-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-lg-cart {
  &:before {
    content: $icon-lg-cart; 
  }
}
.icon-lg-chat {
  &:before {
    content: $icon-lg-chat; 
  }
}
.icon-lg-user {
  &:before {
    content: $icon-lg-user; 
  }
}
.icon-lg-book {
  &:before {
    content: $icon-lg-book; 
  }
}
.icon-lg-bulb {
  &:before {
    content: $icon-lg-bulb; 
  }
}
.icon-lg-package {
  &:before {
    content: $icon-lg-package; 
  }
}
.icon-lg-pencil {
  &:before {
    content: $icon-lg-pencil; 
  }
}
.icon-lg-apple-k5 {
  &:before {
    content: $icon-lg-apple-k5; 
  }
}
.icon-lg-leaf {
  &:before {
    content: $icon-lg-leaf; 
  }
}
.icon-lg-star {
  &:before {
    content: $icon-lg-star; 
  }
}
.icon-lg-star-filled {
  &:before {
    content: $icon-lg-star-filled; 
  }
}
.icon-lg-tree {
  &:before {
    content: $icon-lg-tree; 
  }
}
.icon-lg-truck {
  &:before {
    content: $icon-lg-truck; 
  }
}
.icon-lg-save {
  &:before {
    content: $icon-lg-save; 
  }
}
.icon-lg-open {
  &:before {
    content: $icon-lg-open; 
  }
}
.icon-lg-trash {
  &:before {
    content: $icon-lg-trash; 
  }
}
.icon-lg-pdf {
  &:before {
    content: $icon-lg-pdf; 
  }
}
.icon-lg-excel {
  &:before {
    content: $icon-lg-excel; 
  }
}
.icon-lg-search {
  &:before {
    content: $icon-lg-search; 
  }
}
.icon-lg-mail {
  &:before {
    content: $icon-lg-mail; 
  }
}
.icon-lg-clipboard {
  &:before {
    content: $icon-lg-clipboard; 
  }
}
.icon-lg-eye {
  &:before {
    content: $icon-lg-eye; 
  }
}
.icon-lg-heart {
  &:before {
    content: $icon-lg-heart; 
  }
}
.icon-lg-footsteps {
  &:before {
    content: $icon-lg-footsteps; 
  }
}
.icon-lg-duplicate {
  &:before {
    content: $icon-lg-duplicate; 
  }
}
.icon-lg-checkmark {
  &:before {
    content: $icon-lg-checkmark; 
  }
}
.icon-lg-warning {
  &:before {
    content: $icon-lg-warning; 
  }
}
.icon-lg-notification {
  &:before {
    content: $icon-lg-notification; 
  }
}
.icon-lg-qr-code {
  &:before {
    content: $icon-lg-qr-code; 
  }
}
.icon-lg-apple {
  &:before {
    content: $icon-lg-apple; 
  }
}
.icon-lg-calendar {
  &:before {
    content: $icon-lg-calendar; 
  }
}
.icon-lg-cancel {
  &:before {
    content: $icon-lg-cancel; 
  }
}
.icon-lg-cash-bag {
  &:before {
    content: $icon-lg-cash-bag; 
  }
}
.icon-lg-clock {
  &:before {
    content: $icon-lg-clock; 
  }
}
.icon-lg-contract {
  &:before {
    content: $icon-lg-contract; 
  }
}
.icon-lg-credit-card {
  &:before {
    content: $icon-lg-credit-card; 
  }
}
.icon-lg-customer-support {
  &:before {
    content: $icon-lg-customer-support; 
  }
}
.icon-lg-document {
  &:before {
    content: $icon-lg-document; 
  }
}
.icon-lg-double-puzzle {
  &:before {
    content: $icon-lg-double-puzzle; 
  }
}
.icon-lg-earth {
  &:before {
    content: $icon-lg-earth; 
  }
}
.icon-lg-fax {
  &:before {
    content: $icon-lg-fax; 
  }
}
.icon-lg-gift {
  &:before {
    content: $icon-lg-gift; 
  }
}
.icon-lg-graduation {
  &:before {
    content: $icon-lg-graduation; 
  }
}
.icon-lg-group {
  &:before {
    content: $icon-lg-group; 
  }
}
.icon-lg-map {
  &:before {
    content: $icon-lg-map; 
  }
}
.icon-lg-phone {
  &:before {
    content: $icon-lg-phone; 
  }
}
.icon-lg-cell-phone {
  &:before {
    content: $icon-lg-cell-phone; 
  }
}
.icon-lg-play {
  &:before {
    content: $icon-lg-play; 
  }
}
.icon-lg-puzzle {
  &:before {
    content: $icon-lg-puzzle; 
  }
}
.icon-lg-return-shipment {
  &:before {
    content: $icon-lg-return-shipment; 
  }
}
.icon-lg-seesaw {
  &:before {
    content: $icon-lg-seesaw; 
  }
}
.icon-lg-shield {
  &:before {
    content: $icon-lg-shield; 
  }
}
.icon-lg-speech-heart {
  &:before {
    content: $icon-lg-speech-heart; 
  }
}
.icon-lg-star-ribbon {
  &:before {
    content: $icon-lg-star-ribbon; 
  }
}
.icon-lg-sunburst {
  &:before {
    content: $icon-lg-sunburst; 
  }
}
.icon-lg-thought-cloud {
  &:before {
    content: $icon-lg-thought-cloud; 
  }
}
.icon-lg-video {
  &:before {
    content: $icon-lg-video; 
  }
}
.icon-lg-warranty {
  &:before {
    content: $icon-lg-warranty; 
  }
}
.icon-lg-world-wide-web {
  &:before {
    content: $icon-lg-world-wide-web; 
  }
}
.icon-lg-wrench {
  &:before {
    content: $icon-lg-wrench; 
  }
}
.icon-lg-storefront {
  &:before {
    content: $icon-lg-storefront; 
  }
}
.icon-lg-recycle {
  &:before {
    content: $icon-lg-recycle; 
  }
}
.icon-lg-handicapped {
  &:before {
    content: $icon-lg-handicapped; 
  }
}
.icon-lg-discount {
  &:before {
    content: $icon-lg-discount; 
  }
}
.icon-lg-fully-assembled {
  &:before {
    content: $icon-lg-fully-assembled; 
  }
}
.icon-lg-assembly-required {
  &:before {
    content: $icon-lg-assembly-required; 
  }
}
.icon-lg-installation-required {
  &:before {
    content: $icon-lg-installation-required; 
  }
}
.icon-lg-surfacing-required {
  &:before {
    content: $icon-lg-surfacing-required; 
  }
}
.icon-lg-portable {
  &:before {
    content: $icon-lg-portable; 
  }
}
.icon-lg-color-options {
  &:before {
    content: $icon-lg-color-options; 
  }
}
.icon-lg-lift-gate {
  &:before {
    content: $icon-lg-lift-gate; 
  }
}

