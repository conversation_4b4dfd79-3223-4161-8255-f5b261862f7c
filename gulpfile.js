// Load Gulp...of course
const { src, dest, task, watch, series, parallel } = require('gulp');

// CSS related plugins
var sass         = require( 'gulp-dart-sass' );
var autoprefixer = require( 'gulp-autoprefixer' );

// JS related plugins
var uglify       = require( 'gulp-uglify' );
var babel        = require( 'gulp-babel' );
var concat       = require( 'gulp-concat' );

// Utility plugins
var rename       = require( 'gulp-rename' );
var sourcemaps   = require( 'gulp-sourcemaps' );
var plumber      = require( 'gulp-plumber' );
var del          = require( 'del' );
var fileinclude  = require( 'gulp-file-include' );

// Browers related plugins
var browserSync  = require( 'browser-sync' ).create();

// Project related variables
var styleSRC     = ['./src/lib/src/assets/scss/styles.scss', './src/lib/src/assets/scss/ratings.scss', './src/assets/scss/page.scss'];
var styleDIST    = './dist/assets/css/';
var mapDIST       = './';

var includeSRC   = './src/index.html';
var includeDIST  = './dist/';

var jsSRC        = ['./src/lib/src/assets/js/**/*.js', './node_modules/bootstrap/dist/js/bootstrap.bundle.js', '!./src/lib/src/assets/js/swiper-bundle.js', '!./src/lib/src/assets/js/rss-feed.js'];
var jsDIST        = './dist/assets/js/';

var swiperSRC  = './src/lib/src/assets/js/swiper-bundle.js';
var swiperDIST = './dist/assets/js/';

var rssSRC  = './src/lib/src/assets/js/rss-feed.js';
var rssDIST = './dist/assets/js/';

var pagejsSRC        = './src/assets/js/**/*.js';
var pagejsDIST        = './dist/assets/js/';

var imgSRC       = './src/assets/images/**/*';
var imgDIST       = './dist/assets/images/';

var fontsSRC     = './src/lib/src/assets/fonts/**/*';
var fontsDIST     = './dist/assets/fonts/';

var faviconsSRC  = './src/lib/src/assets/favicons/**/*';
var faviconsDIST = './dist/assets/favicons/';

var htmlSRC     = ['./src/**/*.html', '!./src/lib/src/index.html'];
var htmlDIST     = './dist/';

var styleWatch      = ['./src/assets/scss/**/*.scss', './src/lib/src/assets/scss/**/*.scss'];
var jsWatch         = './src/assets/js/**/*.js';
var imgWatch        = './src/assets/images/**/*.*';
var fontsWatch      = './src/assets/fonts/**/*.*';
var faviconsWatch   = './src/assets/favicons/**/*.*';
var htmlWatch       = './src/**/*.html';

// Tasks
function browser_sync() {
	browserSync.init({
		server: {
			baseDir: './dist/',
			index: 'index.html'
		},
		open: false
	});
}

function file_include ( done ) {
	src( includeSRC )
		.pipe( fileinclude({
			prefix: '@@',
			basepath: '@file',
			indent: true,
		}) )
		.pipe( dest( includeDIST ) );
		done();
}

function clean_images( done ) {
	del([
		'dist/assets/images/*'
	]);
	done();
}

function clean_image_txt( done ) {
	del([
		'dist/assets/images/*.txt'
	]);
	done();
}

function reload( done ) {
	browserSync.reload();
	done();
}

function css( done ) {
	src( styleSRC )
		.pipe( sourcemaps.init() )
		.pipe( sass({
			errLogToConsole: true,
			outputStyle: 'compressed'
		}) )
		.on( 'error', console.error.bind( console ) )
		.pipe( autoprefixer() )
		.pipe( sourcemaps.write( mapDIST ) )
		.pipe( dest( styleDIST ) )
		.pipe( browserSync.stream() );
		done();
};

function js( done ) {
  src( jsSRC )
    .pipe( sourcemaps.init())
    .pipe( babel({
      presets: [ '@babel/env' ]
    }))
    .pipe( concat( 'scripts.js' ))
    .pipe( uglify() )
    .pipe( rename({ extname: '.min.js' }) )
    .pipe( sourcemaps.write( '.' ))
    .pipe( dest( jsDIST ) )
    .pipe( browserSync.stream() );
		done();
};

function swiper( done ) {
  src( swiperSRC )
    .pipe( sourcemaps.init())
    .pipe( babel({
      presets: [ '@babel/env' ]
    }))
    .pipe( uglify() )
    .pipe( rename({ extname: '.min.js' }) )
    .pipe( sourcemaps.write( '.' ))
    .pipe( dest( swiperDIST ) )
    .pipe( browserSync.stream() );
		done();
};

function rss( done ) {
  src( rssSRC )
    .pipe( sourcemaps.init())
    .pipe( babel({
      presets: [ '@babel/env' ]
    }))
    .pipe( uglify() )
    .pipe( rename({ extname: '.min.js' }) )
    .pipe( sourcemaps.write( '.' ))
    .pipe( dest( rssDIST ) )
    .pipe( browserSync.stream() );
		done();
};

function pagejs( done ) {
  src( pagejsSRC )
    .pipe( sourcemaps.init())
    .pipe( babel({
      presets: [ '@babel/env' ]
    }))
    .pipe( uglify() )
    .pipe( rename({ extname: '.min.js' }) )
    .pipe( sourcemaps.write( '.' ))
    .pipe( dest( pagejsDIST ) )
    .pipe( browserSync.stream() );
		done();
};

function triggerPlumber( src_file, dest_file ) {
	return src( src_file, { allowEmpty: true } )
		.pipe( plumber({
			errorHandler: function(error) {
					console.log('Error: ' + error.message);
					this.emit('end');
			}
	}) )
		.pipe( dest( dest_file ) );
};

function images() {
	return triggerPlumber( imgSRC, imgDIST );
};

function fonts() {
	return triggerPlumber( fontsSRC, fontsDIST );
};

function favicons() {
	return triggerPlumber( faviconsSRC, faviconsDIST );
};

function html() {
	return triggerPlumber( htmlSRC, htmlDIST );
};

function watch_files() {
	watch(styleWatch, series(css, html, file_include, reload));
	watch(jsWatch, series(js, swiper, rss, pagejs, reload));
	watch(imgWatch, series(clean_images, images, reload));
	watch(fontsWatch, series(fonts, reload));
	watch(faviconsWatch, series(favicons, reload));
	watch(htmlWatch, series(html, file_include, reload));
}

task("css", css);
task("js", js);
task("swiper", swiper);
task("rss", rss);
task("pagejs", pagejs);
task("images", images);
task("fonts", fonts);
task("favicons", favicons);
task("file_include", file_include);
task("html", html);
task("clean_images", clean_images);
task("clean_image_txt", clean_image_txt);
task("default", series(clean_images, css, js, swiper, rss, pagejs, images, fonts, favicons, html, file_include, clean_image_txt));
task("watch", parallel(browser_sync, watch_files));
